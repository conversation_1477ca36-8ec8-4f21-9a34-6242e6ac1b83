@echo off
echo ========================================
echo Fixing InnoDB Data Files
echo ========================================
echo.

echo 1. Stopping any running MySQL processes...
taskkill /f /im mysqld.exe 2>nul
timeout /t 3 /nobreak >nul

echo 2. Backing up current InnoDB files...
cd "C:\xampp\mysql\data"
if exist "ibdata1" (
    copy ibdata1 ibdata1.backup
    echo ✅ ibdata1 backed up
)
if exist "ib_logfile0" (
    copy ib_logfile0 ib_logfile0.backup
    echo ✅ ib_logfile0 backed up
)
if exist "ib_logfile1" (
    copy ib_logfile1 ib_logfile1.backup
    echo ✅ ib_logfile1 backed up
)

echo.
echo 3. Removing corrupted InnoDB files...
del ibdata1 2>nul
del ib_logfile0 2>nul
del ib_logfile1 2>nul
del ib_buffer_pool 2>nul
echo ✅ InnoDB files removed

echo.
echo 4. Creating new MySQL configuration for InnoDB...
cd "C:\xampp\mysql\bin"
echo # MySQL Configuration - InnoDB Fixed > my_fixed.ini
echo [mysqld] >> my_fixed.ini
echo port=3306 >> my_fixed.ini
echo socket=mysql >> my_fixed.ini
echo basedir=C:/xampp/mysql >> my_fixed.ini
echo datadir=C:/xampp/mysql/data >> my_fixed.ini
echo tmpdir=C:/xampp/tmp >> my_fixed.ini
echo skip-external-locking >> my_fixed.ini
echo skip-slave-start >> my_fixed.ini
echo key_buffer_size=16M >> my_fixed.ini
echo max_allowed_packet=1M >> my_fixed.ini
echo table_open_cache=64 >> my_fixed.ini
echo sort_buffer_size=512K >> my_fixed.ini
echo net_buffer_length=8K >> my_fixed.ini
echo read_buffer_size=256K >> my_fixed.ini
echo read_rnd_buffer_size=512K >> my_fixed.ini
echo myisam_sort_buffer_size=8M >> my_fixed.ini
echo log-error=mysql_error.log >> my_fixed.ini
echo server-id=1 >> my_fixed.ini
echo. >> my_fixed.ini
echo # InnoDB Configuration >> my_fixed.ini
echo innodb_data_home_dir=C:/xampp/mysql/data >> my_fixed.ini
echo innodb_log_group_home_dir=C:/xampp/mysql/data >> my_fixed.ini
echo innodb_data_file_path=ibdata1:10M:autoextend >> my_fixed.ini
echo innodb_log_file_size=5M >> my_fixed.ini
echo innodb_log_buffer_size=8M >> my_fixed.ini
echo innodb_flush_log_at_trx_commit=1 >> my_fixed.ini
echo innodb_lock_wait_timeout=50 >> my_fixed.ini
echo innodb_force_recovery=0 >> my_fixed.ini

echo.
echo 5. Backing up current my.ini and applying fix...
if exist "my.ini" copy my.ini my.ini.broken
copy my_fixed.ini my.ini
echo ✅ Fixed configuration applied

echo.
echo ========================================
echo InnoDB fix complete!
echo Now try starting MySQL manually again
echo ========================================
pause
