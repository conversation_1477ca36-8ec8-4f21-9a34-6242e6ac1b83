import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { db } from "@/lib/db";
import { PropertyForm } from "../../_components/property-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

interface EditPropertyPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

async function getProperty(id: string) {
  try {
    const property = await db.property.findUnique({
      where: { id: parseInt(id, 10) },
      include: {
        property_type: true,
        amenities: {
          include: {
            amenity: true,
          },
        },
        owner: true,
      },
    });
    
    if (property) {
      // Convert Decimal fields to strings for client component
      return {
        ...property,
        base_rent: property.base_rent.toString(),
        total_area: property.total_area?.toString() || null,
        // Ensure amenities are serializable
        amenities: property.amenities.map(pa => ({
          ...pa,
          amenity: {
            ...pa.amenity,
          }
        })),
        // Ensure owner is serializable  
        owner: property.owner ? {
          ...property.owner,
          management_fee_percentage: property.owner.management_fee_percentage?.toString() || null,
        } : undefined,
      };
    }
    
    return property;
  } catch (error) {
    console.error("Error fetching property:", error);
    return null;
  }
}

async function getPropertyTypes() {
  try {
    const propertyTypes = await db.propertyType.findMany({
      orderBy: { name_en: "asc" },
    });
    return propertyTypes;
  } catch (error) {
    console.error("Error fetching property types:", error);
    return [];
  }
}

async function getAmenities() {
  try {
    const amenities = await db.amenity.findMany({
      orderBy: { name_en: "asc" },
    });
    return amenities;
  } catch (error) {
    console.error("Error fetching amenities:", error);
    return [];
  }
}

async function getOwners() {
  try {
    const owners = await db.propertyOwner.findMany({
      where: { status: "ACTIVE" },
      orderBy: { name_en: "asc" },
    });
    
    // Convert Decimal fields to strings for client component
    return owners.map(owner => ({
      ...owner,
      management_fee_percentage: owner.management_fee_percentage?.toString() || null,
    }));
  } catch (error) {
    console.error("Error fetching owners:", error);
    return [];
  }
}

export default async function EditPropertyPage({ params }: EditPropertyPageProps) {
  const { id, locale } = await params;
  const t = await getTranslations('properties');

  const [property, propertyTypes, amenities, owners] = await Promise.all([
    getProperty(id),
    getPropertyTypes(),
    getAmenities(),
    getOwners(),
  ]);

  if (!property) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href={`/${locale}/dashboard/properties`}>
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('editProperty')}</h1>
          <p className="text-muted-foreground">{t('editDescription')}</p>
        </div>
      </div>

      <PropertyForm 
        property={property as any} 
        propertyTypes={propertyTypes}
        amenities={amenities}
        owners={owners}
        isEdit 
      />
    </div>
  );
}