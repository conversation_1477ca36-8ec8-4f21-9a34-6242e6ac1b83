import { Suspense } from "react";
import { Metada<PERSON> } from "next";
import { getTranslations } from "next-intl/server";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AmenitiesDataTable } from "./_components/amenities-data-table";
import { amenityColumns } from "./_components/amenity-columns";
import Link from "next/link";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "amenities" });

  return {
    title: t("title"),
    description: t("description"),
  };
}

export default async function AmenitiesPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale });

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t("amenities.title")}
          </h1>
          <p className="text-muted-foreground">
            {t("amenities.description")}
          </p>
        </div>
        <Button asChild>
          <Link href={`/${locale}/dashboard/amenities/new`}>
            <Plus className="mr-2 h-4 w-4" />
            {t("amenities.addAmenity")}
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("amenities.allAmenities")}</CardTitle>
          <CardDescription>
            {t("amenities.viewManageAmenities")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div>Loading...</div>}>
            <AmenitiesDataTable columns={amenityColumns} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}