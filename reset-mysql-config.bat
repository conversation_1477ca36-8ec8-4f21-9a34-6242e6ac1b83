@echo off
echo Creating clean MySQL configuration...

cd "C:\xampp\mysql\bin"

echo # MySQL Configuration for XAMPP > my_clean.ini
echo [mysqld] >> my_clean.ini
echo port=3306 >> my_clean.ini
echo socket=mysql >> my_clean.ini
echo basedir=C:/xampp/mysql >> my_clean.ini
echo datadir=C:/xampp/mysql/data >> my_clean.ini
echo tmpdir=C:/xampp/tmp >> my_clean.ini
echo skip-external-locking >> my_clean.ini
echo skip-slave-start >> my_clean.ini
echo key_buffer_size=16M >> my_clean.ini
echo max_allowed_packet=1M >> my_clean.ini
echo table_open_cache=64 >> my_clean.ini
echo sort_buffer_size=512K >> my_clean.ini
echo net_buffer_length=8K >> my_clean.ini
echo read_buffer_size=256K >> my_clean.ini
echo read_rnd_buffer_size=512K >> my_clean.ini
echo myisam_sort_buffer_size=8M >> my_clean.ini
echo log-error=mysql_error.log >> my_clean.ini
echo server-id=1 >> my_clean.ini
echo innodb_data_home_dir=C:/xampp/mysql/data >> my_clean.ini
echo innodb_log_group_home_dir=C:/xampp/mysql/data >> my_clean.ini
echo innodb_data_file_path=ibdata1:10M:autoextend >> my_clean.ini
echo innodb_log_file_size=5M >> my_clean.ini
echo innodb_log_buffer_size=8M >> my_clean.ini
echo innodb_flush_log_at_trx_commit=1 >> my_clean.ini
echo innodb_lock_wait_timeout=50 >> my_clean.ini
echo.
echo [mysqldump] >> my_clean.ini
echo quick >> my_clean.ini
echo max_allowed_packet=16M >> my_clean.ini
echo.
echo [mysql] >> my_clean.ini
echo no-auto-rehash >> my_clean.ini
echo.
echo [myisamchk] >> my_clean.ini
echo key_buffer_size=20M >> my_clean.ini
echo sort_buffer_size=20M >> my_clean.ini
echo read_buffer=2M >> my_clean.ini
echo write_buffer=2M >> my_clean.ini
echo.
echo [mysqlhotcopy] >> my_clean.ini
echo interactive-timeout >> my_clean.ini

echo ✅ Clean MySQL configuration created as my_clean.ini
echo.
echo Now backup the original my.ini and replace it:
copy my.ini my.ini.backup
copy my_clean.ini my.ini
echo ✅ MySQL configuration updated
pause
