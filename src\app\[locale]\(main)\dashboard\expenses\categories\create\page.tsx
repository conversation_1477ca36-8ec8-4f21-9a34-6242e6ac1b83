import { getTranslations } from "next-intl/server";
import { CategoryForm } from "../_components/category-form";

interface CreateCategoryPageProps {
  params: Promise<{ locale: string }>;
}

export default async function CreateCategoryPage({ params }: CreateCategoryPageProps) {
  const { locale } = await params;

  return (
    <CategoryForm mode="create" locale={locale} />
  );
}

export async function generateMetadata({ params }: CreateCategoryPageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "expenses.categories" });

  return {
    title: t("addCategory"),
    description: t("addCategoryDescription"),
  };
}
