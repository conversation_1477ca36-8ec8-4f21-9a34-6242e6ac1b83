"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { Progress } from "@/components/ui/progress";
import { Building2, Home, Users, DollarSign } from "lucide-react";

interface PropertyData {
  id: string;
  name: string;
  totalUnits: number;
  occupiedUnits: number;
  monthlyRevenue: number;
  maintenanceIssues: number;
}

export function PropertyOverview() {
  const t = useTranslations();
  const [properties, setProperties] = useState<PropertyData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data - in real app, fetch from API
    const mockData: PropertyData[] = [
      {
        id: "1",
        name: "Sunset Apartments",
        totalUnits: 24,
        occupiedUnits: 22,
        monthlyRevenue: 15000,
        maintenanceIssues: 2,
      },
      {
        id: "2",
        name: "Garden Villas",
        totalUnits: 18,
        occupiedUnits: 16,
        monthlyRevenue: 22000,
        maintenanceIssues: 1,
      },
      {
        id: "3",
        name: "Downtown Tower",
        totalUnits: 36,
        occupiedUnits: 30,
        monthlyRevenue: 35000,
        maintenanceIssues: 4,
      },
      {
        id: "4",
        name: "Riverside Complex",
        totalUnits: 12,
        occupiedUnits: 12,
        monthlyRevenue: 18000,
        maintenanceIssues: 0,
      },
    ];

    setProperties(mockData);
    setLoading(false);
  }, []);

  if (loading) {
    return (
      <Card className="col-span-3">
        <CardHeader>
          <CardTitle>{t("dashboard.propertyOverview")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-muted animate-pulse rounded w-1/3" />
                <div className="h-2 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="col-span-3">
      <CardHeader>
        <CardTitle>{t("dashboard.propertyOverview")}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {properties.map((property) => {
            const occupancyRate = Math.round((property.occupiedUnits / property.totalUnits) * 100);
            return (
              <div key={property.id} className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <h4 className="text-sm font-medium">{property.name}</h4>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {occupancyRate}% {t("dashboard.occupied")}
                  </div>
                </div>
                <Progress value={occupancyRate} className="h-2" />
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Home className="h-3 w-3 text-muted-foreground" />
                    <span className="text-muted-foreground">
                      {property.occupiedUnits}/{property.totalUnits}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-3 w-3 text-muted-foreground" />
                    <span className="text-muted-foreground">
                      OMR {property.monthlyRevenue.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-3 w-3 text-muted-foreground" />
                    <span className={property.maintenanceIssues > 0 ? "text-orange-600" : "text-muted-foreground"}>
                      {property.maintenanceIssues} {t("dashboard.issues")}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}