"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from "recharts";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface RevenueData {
  month: string;
  revenue: number;
  expenses: number;
  profit: number;
}

export function RevenueChart() {
  const t = useTranslations();
  const [data, setData] = useState<RevenueData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data - in real app, fetch from API
    const mockData: RevenueData[] = [
      { month: "Jan", revenue: 45000, expenses: 12000, profit: 33000 },
      { month: "Feb", revenue: 48000, expenses: 13000, profit: 35000 },
      { month: "Mar", revenue: 52000, expenses: 14000, profit: 38000 },
      { month: "Apr", revenue: 49000, expenses: 13500, profit: 35500 },
      { month: "May", revenue: 55000, expenses: 15000, profit: 40000 },
      { month: "Jun", revenue: 58000, expenses: 16000, profit: 42000 },
    ];

    setData(mockData);
    setLoading(false);
  }, []);

  const formatCurrency = (value: number) => `OMR ${value.toLocaleString()}`;

  if (loading) {
    return (
      <Card className="col-span-4">
        <CardHeader>
          <CardTitle>{t("dashboard.revenueOverview")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[350px] bg-muted animate-pulse rounded" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="col-span-4">
      <CardHeader>
        <CardTitle>{t("dashboard.revenueOverview")}</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="revenue" className="space-y-4">
          <TabsList>
            <TabsTrigger value="revenue">{t("dashboard.revenue")}</TabsTrigger>
            <TabsTrigger value="comparison">{t("dashboard.comparison")}</TabsTrigger>
          </TabsList>
          <TabsContent value="revenue" className="space-y-4">
            <div className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={formatCurrency} />
                  <Tooltip formatter={formatCurrency} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    name={t("dashboard.revenue")}
                  />
                  <Line
                    type="monotone"
                    dataKey="profit"
                    stroke="#10b981"
                    strokeWidth={2}
                    name={t("dashboard.profit")}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          <TabsContent value="comparison" className="space-y-4">
            <div className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={formatCurrency} />
                  <Tooltip formatter={formatCurrency} />
                  <Legend />
                  <Bar dataKey="revenue" fill="#3b82f6" name={t("dashboard.revenue")} />
                  <Bar dataKey="expenses" fill="#ef4444" name={t("dashboard.expenses")} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}