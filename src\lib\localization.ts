/**
 * Localization utilities for the property management application
 * Handles timezone conversion, currency formatting, and date formatting for Oman
 */

import { format, parseISO, formatISO } from "date-fns";
import { LOCALIZATION_CONFIG } from "@/config/localization";

/**
 * Format currency amount in Omani Rial (OMR) format
 * @param amount - The amount to format
 * @param options - Optional formatting options
 * @returns Formatted currency string (e.g., "OMR 1,234.567")
 */
export function formatCurrency(
  amount: number | string,
  options?: {
    showSymbol?: boolean;
    decimals?: number;
    locale?: string;
  }
): string {
  const numericAmount = typeof amount === "string" ? parseFloat(amount) : amount;
  
  if (isNaN(numericAmount)) {
    return "OMR 0.000";
  }

  const {
    showSymbol = true,
    decimals = LOCALIZATION_CONFIG.currency.decimals,
    locale = LOCALIZATION_CONFIG.currency.locale,
  } = options || {};

  // Format the number with proper decimals and thousands separator
  const formatted = new Intl.NumberFormat(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
    useGrouping: true,
  }).format(numericAmount);

  return showSymbol ? `${LOCALIZATION_CONFIG.currency.symbol} ${formatted}` : formatted;
}

/**
 * Convert a date to Asia/Muscat timezone
 * @param date - Date to convert (can be Date object, ISO string, or timestamp)
 * @returns Date object in Asia/Muscat timezone
 */
export function toMuscatTime(date: Date | string | number): Date {
  const dateObj = typeof date === "string" ? parseISO(date) : new Date(date);
  // Asia/Muscat is UTC+4, so we add 4 hours to UTC time for display
  const muscatTime = new Date(dateObj.getTime() + (4 * 60 * 60 * 1000));
  return muscatTime;
}

/**
 * Convert a date from Asia/Muscat timezone to UTC
 * @param date - Date in Asia/Muscat timezone
 * @returns UTC Date object
 */
export function fromMuscatTime(date: Date | string): Date {
  const dateObj = typeof date === "string" ? parseISO(date) : date;
  // Asia/Muscat is UTC+4, so we subtract 4 hours to get UTC time
  const utcTime = new Date(dateObj.getTime() - (4 * 60 * 60 * 1000));
  return utcTime;
}

/**
 * Format date in DD/MM/YYYY format using Asia/Muscat timezone
 * @param date - Date to format
 * @param options - Optional formatting options
 * @returns Formatted date string
 */
export function formatDate(
  date: Date | string | number,
  options?: {
    includeTime?: boolean;
    timeFormat?: "12h" | "24h";
    timezone?: string;
    customFormat?: string;
  }
): string {
  const {
    includeTime = false,
    timeFormat = "24h",
    timezone = LOCALIZATION_CONFIG.timezone,
    customFormat,
  } = options || {};

  const dateObj = typeof date === "string" ? parseISO(date) : new Date(date);
  const zonedDate = timezone === LOCALIZATION_CONFIG.timezone ? toMuscatTime(dateObj) : dateObj;

  if (customFormat) {
    return format(zonedDate, customFormat);
  }

  let formatString = "dd/MM/yyyy";
  
  if (includeTime) {
    formatString += timeFormat === "12h" ? " hh:mm a" : " HH:mm";
  }

  return format(zonedDate, formatString);
}

/**
 * Format date for HTML date input (YYYY-MM-DD format)
 * @param date - Date to format
 * @returns Date string in YYYY-MM-DD format
 */
export function formatDateForInput(date: Date | string | number): string {
  const dateObj = typeof date === "string" ? parseISO(date) : new Date(date);
  const zonedDate = toMuscatTime(dateObj);
  return format(zonedDate, "yyyy-MM-dd");
}

/**
 * Parse date from DD/MM/YYYY format and convert to UTC for database storage
 * @param dateInput - Date string in DD/MM/YYYY format or Date object
 * @returns UTC Date object for database storage
 */
export function parseDateFromInput(dateInput: string | Date): Date {
  // If already a Date object, return it
  if (dateInput instanceof Date) {
    return dateInput;
  }

  // Handle both DD/MM/YYYY and YYYY-MM-DD formats
  let dateObj: Date;

  if (dateInput.includes("/")) {
    // DD/MM/YYYY format
    const [day, month, year] = dateInput.split("/");
    dateObj = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
  } else {
    // YYYY-MM-DD format (from HTML date input)
    dateObj = parseISO(dateInput);
  }
  
  // Convert from Muscat time to UTC for database storage
  return fromMuscatTime(dateObj);
}

/**
 * Get current date/time in Asia/Muscat timezone
 * @returns Current Date object in Asia/Muscat timezone
 */
export function getCurrentMuscatTime(): Date {
  return toMuscatTime(new Date());
}

/**
 * Format a date for display in various contexts
 * @param date - Date to format
 * @param context - Display context
 * @returns Formatted date string
 */
export function formatDateForDisplay(
  date: Date | string | number,
  context: "short" | "medium" | "long" | "table" | "form" = "medium"
): string {
  switch (context) {
    case "short":
      return formatDate(date, { customFormat: "dd/MM/yy" });
    case "medium":
      return formatDate(date);
    case "long":
      return formatDate(date, { includeTime: true });
    case "table":
      return formatDate(date, { customFormat: "dd MMM yyyy" });
    case "form":
      return formatDateForInput(date);
    default:
      return formatDate(date);
  }
}

/**
 * Validate if a date string is in correct DD/MM/YYYY format
 * @param dateString - Date string to validate
 * @returns Boolean indicating if the format is valid
 */
export function isValidDateFormat(dateString: string): boolean {
  const ddmmyyyyRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
  const match = dateString.match(ddmmyyyyRegex);
  
  if (!match) return false;
  
  const day = parseInt(match[1]);
  const month = parseInt(match[2]);
  const year = parseInt(match[3]);
  
  // Basic validation
  if (month < 1 || month > 12) return false;
  if (day < 1 || day > 31) return false;
  if (year < 1900 || year > 2100) return false;
  
  // Create date and check if it's valid
  const date = new Date(year, month - 1, day);
  return date.getFullYear() === year && 
         date.getMonth() === month - 1 && 
         date.getDate() === day;
}
