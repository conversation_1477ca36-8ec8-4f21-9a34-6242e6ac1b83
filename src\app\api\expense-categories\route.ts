import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { expenseCategoryFormSchema } from "@/types/expense";
import { z } from "zod";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { ApiResponseBuilder } from "@/lib/api-response";

const prisma = new PrismaClient();

// GET /api/expense-categories - Get all expense categories
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for expense-categories
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "expense-categories", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view expense categories");
    }

    console.log("Expense Categories API: GET request received");

    // For development, skip authentication
    if (process.env.NODE_ENV === "development") {
      console.log("Expense Categories API: Development mode - skipping authentication");
    }

    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get("includeInactive") === "true";

    const whereClause = includeInactive ? {} : { is_active: true };

    const categories = await prisma.expenseCategory.findMany({
      where: whereClause,
      orderBy: [
        { sort_order: "asc" },
        { name_en: "asc" }
      ],
      include: {
        _count: {
          select: {
            expenses: true
          }
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });

    console.log(`Expense Categories API: Categories retrieved: ${categories.length} categories`);
    return NextResponse.json(categories);

  } catch (error) {
    console.error("Expense Categories API: Error fetching categories:", error);
    return NextResponse.json(
      { error: "Failed to fetch expense categories" },
      { status: 500 }
    );
  }
}

// POST /api/expense-categories - Create new expense category
export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for expense-categories
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "expense-categories", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create expense categories");
    }

    console.log("Expense Categories API: POST request received");

    // For development, skip authentication
    if (process.env.NODE_ENV === "development") {
      console.log("Expense Categories API: Development mode - skipping authentication");
    }

    const body = await request.json();
    console.log("Expense Categories API: Request body:", body);

    // Validate the request body
    const validationResult = expenseCategoryFormSchema.safeParse(body);
    if (!validationResult.success) {
      console.log("Expense Categories API: Validation failed:", validationResult.error);
      return NextResponse.json(
        { 
          error: "Validation failed", 
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Check if category with same English name already exists
    const existingCategory = await prisma.expenseCategory.findFirst({
      where: {
        OR: [
          { name_en: data.name_en },
          { name_ar: data.name_ar }
        ]
      }
    });

    if (existingCategory) {
      return NextResponse.json(
        { error: "Category with this name already exists" },
        { status: 409 }
      );
    }

    // Create the category
    const category = await prisma.expenseCategory.create({
      data: {
        name_en: data.name_en,
        name_ar: data.name_ar,
        description: data.description || null,
        is_active: data.is_active,
        sort_order: data.sort_order,
        created_by: 1, // TODO: Get from authenticated user
        updated_by: 1, // TODO: Get from authenticated user
      },
      include: {
        _count: {
          select: {
            expenses: true
          }
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });

    console.log("Expense Categories API: Category created:", category.id);
    return NextResponse.json(category, { status: 201 });

  } catch (error) {
    console.error("Expense Categories API: Error creating category:", error);
    return NextResponse.json(
      { error: "Failed to create expense category" },
      { status: 500 }
    );
  }
}

// Initialize predefined categories
async function initializePredefinedCategories() {
  const predefinedCategories = [
    { name_en: "Office Supplies", name_ar: "مستلزمات المكتب", sort_order: 1 },
    { name_en: "Fuel/Petrol", name_ar: "وقود/بنزين", sort_order: 2 },
    { name_en: "Maintenance Tools", name_ar: "أدوات الصيانة", sort_order: 3 },
    { name_en: "Utilities", name_ar: "المرافق", sort_order: 4 },
    { name_en: "Marketing/Advertising", name_ar: "التسويق/الإعلان", sort_order: 5 },
    { name_en: "Salaries", name_ar: "الرواتب", sort_order: 6 },
    { name_en: "Travel", name_ar: "السفر", sort_order: 7 },
    { name_en: "Insurance", name_ar: "التأمين", sort_order: 8 },
    { name_en: "Legal/Professional Services", name_ar: "الخدمات القانونية/المهنية", sort_order: 9 },
    { name_en: "Other", name_ar: "أخرى", sort_order: 10 }
  ];

  for (const category of predefinedCategories) {
    const existing = await prisma.expenseCategory.findFirst({
      where: { name_en: category.name_en }
    });

    if (!existing) {
      await prisma.expenseCategory.create({
        data: {
          ...category,
          is_active: true,
          created_by: 1,
          updated_by: 1
        }
      });
    }
  }
}
