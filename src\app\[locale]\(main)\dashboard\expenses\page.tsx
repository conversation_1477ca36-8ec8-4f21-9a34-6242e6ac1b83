"use client";

import { useState, useEffect, use, useCallback } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTable } from "@/components/data-table/data-table";
import { DataTablePagination } from "@/components/data-table/data-table-pagination";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Search, Filter, Loader2, X, Calendar, ChevronDown, FileText, Printer } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { ExpenseWithDetails, PAYMENT_METHOD_VALUES } from "@/types/expense";
import { getExpenseColumns } from "./_components/expense-columns";
import { ErrorState } from "@/components/error-state";
import { formatCurrency } from "@/lib/localization";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useDataTableInstance } from "@/hooks/use-data-table-instance";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface ExpensesPageProps {
  params: Promise<{
    locale: string;
  }>;
}

export default function ExpensesPage({ params }: ExpensesPageProps) {
  const { locale } = use(params);
  const t = useTranslations("expenses");
  const tCommon = useTranslations("common");
  const router = useRouter();

  const [expenses, setExpenses] = useState<ExpenseWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchInput, setSearchInput] = useState("");

  // Enhanced filter state
  const [filters, setFilters] = useState({
    category_id: undefined as number | undefined,
    payment_method: undefined as string | undefined,
    date_from: undefined as string | undefined,
    date_to: undefined as string | undefined,
    date_preset: undefined as string | undefined,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(false);

  // Sorting state
  const [sortConfig, setSortConfig] = useState({
    column: "created_at",
    direction: "desc" as 'asc' | 'desc'
  });

  // Report generation state
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);

  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState<ExpenseWithDetails | null>(null);
  const [approvalAction, setApprovalAction] = useState<"approve" | "reject" | null>(null);
  const [actionLoading, setActionLoading] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const pageSize = 10;

  // Load expenses with enhanced error handling and filters
  const loadExpenses = async (page = 1, search = "", appliedFilters = filters, sortBy = sortConfig.column, sortOrder = sortConfig.direction) => {
    try {
      setLoading(true);
      setError(null);

      // Sanitize search input
      const sanitizedSearch = typeof search === 'string' ? search.trim() : '';

      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        sortBy: sortBy,
        sortOrder: sortOrder,
      });

      if (sanitizedSearch) {
        params.append("search", sanitizedSearch);
      }

      // Add filter parameters
      if (appliedFilters.category_id) {
        params.append("category_id", appliedFilters.category_id.toString());
      }

      if (appliedFilters.payment_method) {
        params.append("payment_method", appliedFilters.payment_method);
      }

      if (appliedFilters.date_from) {
        params.append("date_from", appliedFilters.date_from);
      }

      if (appliedFilters.date_to) {
        params.append("date_to", appliedFilters.date_to);
      }

      const response = await fetch(`/api/expenses?${params}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(t("messages.loadingError") || "Failed to load expenses");
      }

      const data = await response.json();
      setExpenses(data.expenses || []);
      setCurrentPage(data.pagination?.page || 1);
      setTotalPages(data.pagination?.totalPages || 1);
      setTotalCount(data.pagination?.totalCount || 0);
    } catch (error) {
      console.error("Error loading expenses:", error);
      setError(t("messages.loadingError") || "Failed to load expenses");
      // Fallback to empty state
      setExpenses([]);
      setCurrentPage(1);
      setTotalPages(1);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadExpenses();
    loadCategories();
  }, []);

  // Load expense categories
  const loadCategories = async () => {
    try {
      setCategoriesLoading(true);
      const response = await fetch('/api/expense-categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setCategoriesLoading(false);
    }
  };

  // Debounced search effect with error handling
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchInput !== searchTerm) {
        try {
          setSearchTerm(searchInput);
          setCurrentPage(1);
          loadExpenses(1, searchInput, filters, sortConfig.column, sortConfig.direction);
        } catch (error) {
          console.error('Search error:', error);
        }
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchInput, filters, sortConfig]);

  // Handle search input change with additional safeguards
  const handleSearchInputChange = useCallback((value: string) => {
    try {
      // Ensure the value is a string and handle potential encoding issues
      const cleanValue = typeof value === 'string' ? value : String(value || '');
      setSearchInput(cleanValue);
    } catch (error) {
      console.error('Input change error:', error);
      setSearchInput('');
    }
  }, []);

  // Handle composition events for better IME support
  const handleCompositionStart = useCallback(() => {
    // Disable debounced search during composition
  }, []);

  const handleCompositionEnd = useCallback((e: React.CompositionEvent<HTMLInputElement>) => {
    // Trigger search after composition ends
    const value = e.currentTarget.value;
    handleSearchInputChange(value);
  }, [handleSearchInputChange]);

  // Handle manual search (Enter key or search button)
  const handleSearch = useCallback(() => {
    try {
      setSearchTerm(searchInput);
      setCurrentPage(1);
      loadExpenses(1, searchInput, filters, sortConfig.column, sortConfig.direction);
    } catch (error) {
      console.error('Manual search error:', error);
    }
  }, [searchInput, filters, sortConfig]);

  // Handle filter changes
  const handleFilterChange = useCallback((key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    setCurrentPage(1);
    loadExpenses(1, searchTerm, newFilters, sortConfig.column, sortConfig.direction);
  }, [filters, searchTerm, sortConfig]);

  // Handle date preset changes
  const handleDatePresetChange = useCallback((preset: string) => {
    const today = new Date();
    let fromDate = '';
    let toDate = '';

    switch (preset) {
      case 'today':
        fromDate = today.toISOString().split('T')[0];
        toDate = fromDate;
        break;
      case 'thisWeek':
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        fromDate = startOfWeek.toISOString().split('T')[0];
        toDate = endOfWeek.toISOString().split('T')[0];
        break;
      case 'thisMonth':
        fromDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
        toDate = new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString().split('T')[0];
        break;
      case 'thisYear':
        fromDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
        toDate = new Date(today.getFullYear(), 11, 31).toISOString().split('T')[0];
        break;
      default:
        fromDate = '';
        toDate = '';
    }

    const newFilters = {
      ...filters,
      date_preset: preset === 'custom' ? undefined : preset,
      date_from: fromDate || undefined,
      date_to: toDate || undefined,
    };

    setFilters(newFilters);
    setCurrentPage(1);
    loadExpenses(1, searchTerm, newFilters, sortConfig.column, sortConfig.direction);
  }, [filters, searchTerm, sortConfig]);

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    const clearedFilters = {
      category_id: undefined,
      payment_method: undefined,
      date_from: undefined,
      date_to: undefined,
      date_preset: undefined,
    };
    setFilters(clearedFilters);
    setCurrentPage(1);
    loadExpenses(1, searchTerm, clearedFilters, sortConfig.column, sortConfig.direction);
  }, [searchTerm, sortConfig]);

  // Handle sorting
  const handleSort = useCallback((column: string, direction: 'asc' | 'desc') => {
    const newSortConfig = { column, direction };
    setSortConfig(newSortConfig);
    setCurrentPage(1);
    loadExpenses(1, searchTerm, filters, column, direction);
  }, [searchTerm, filters]);

  // Generate PDF Report
  const generatePDFReport = useCallback(async () => {
    try {
      setIsGeneratingReport(true);

      // Fetch all expenses with current filters (use max allowed pageSize)
      const params = new URLSearchParams({
        page: "1",
        pageSize: "100", // Maximum allowed by API validation
        sortBy: sortConfig.column,
        sortOrder: sortConfig.direction,
      });

      if (searchTerm) {
        params.append("search", searchTerm);
      }

      if (filters.category_id) {
        params.append("category_id", filters.category_id.toString());
      }

      if (filters.payment_method) {
        params.append("payment_method", filters.payment_method);
      }

      if (filters.date_from) {
        params.append("date_from", filters.date_from);
      }

      if (filters.date_to) {
        params.append("date_to", filters.date_to);
      }

      const response = await fetch(`/api/expenses?${params}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Report API error:', errorData);
        throw new Error(`Failed to fetch expenses for report: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const reportExpenses = data.expenses || [];

      // Generate and download the PDF
      await generateExpenseReportPDF(reportExpenses, {
        searchTerm,
        filters,
        locale,
        t,
        tCommon,
        categories,
      });

    } catch (error) {
      console.error('Error generating report:', error);
      setError(t("reports.reportError"));
    } finally {
      setIsGeneratingReport(false);
    }
  }, [searchTerm, filters, sortConfig, locale, t, tCommon, categories]);

  // PDF Generation Helper Function
  const generateExpenseReportPDF = async (
    reportExpenses: ExpenseWithDetails[],
    options: {
      searchTerm: string;
      filters: any;
      locale: string;
      t: (key: string) => string;
      tCommon: (key: string) => string;
      categories: any[];
    }
  ) => {
    // Create a new window for the PDF content
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      throw new Error('Could not open print window');
    }

    const { searchTerm, filters, locale, t, tCommon, categories } = options;
    const isRTL = locale === 'ar';

    // Calculate totals and summaries
    // Convert amounts to numbers to handle Decimal objects from Prisma
    const totalAmount = reportExpenses.reduce((sum, expense) => {
      const amount = typeof expense.amount === 'string' ? parseFloat(expense.amount) : Number(expense.amount);
      return sum + amount;
    }, 0);
    const totalCount = reportExpenses.length;

    // Group expenses based on applied filters
    let groupedExpenses: { [key: string]: ExpenseWithDetails[] } = {};
    let groupingType = 'category'; // default grouping

    if (filters.category_id) {
      // Group by date if category filter is applied
      groupingType = 'date';
      reportExpenses.forEach(expense => {
        const dateKey = new Date(expense.date).toLocaleDateString(locale);
        if (!groupedExpenses[dateKey]) {
          groupedExpenses[dateKey] = [];
        }
        groupedExpenses[dateKey].push(expense);
      });
    } else {
      // Group by category (default)
      reportExpenses.forEach(expense => {
        const categoryName = locale === 'ar' ? expense.category.name_ar : expense.category.name_en;
        if (!groupedExpenses[categoryName]) {
          groupedExpenses[categoryName] = [];
        }
        groupedExpenses[categoryName].push(expense);
      });
    }

    // Payment method breakdown
    const paymentMethodBreakdown: { [key: string]: { count: number; amount: number } } = {};
    reportExpenses.forEach(expense => {
      const method = t(`paymentMethods.${expense.payment_method}`);
      if (!paymentMethodBreakdown[method]) {
        paymentMethodBreakdown[method] = { count: 0, amount: 0 };
      }
      paymentMethodBreakdown[method].count++;
      const amount = typeof expense.amount === 'string' ? parseFloat(expense.amount) : Number(expense.amount);
      paymentMethodBreakdown[method].amount += amount;
    });

    // Status breakdown
    const statusBreakdown: { [key: string]: { count: number; amount: number } } = {};
    reportExpenses.forEach(expense => {
      const status = t(`statuses.${expense.status}`);
      if (!statusBreakdown[status]) {
        statusBreakdown[status] = { count: 0, amount: 0 };
      }
      statusBreakdown[status].count++;
      const amount = typeof expense.amount === 'string' ? parseFloat(expense.amount) : Number(expense.amount);
      statusBreakdown[status].amount += amount;
    });

    // Generate HTML content for the PDF
    const htmlContent = `
      <!DOCTYPE html>
      <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${locale}">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${t("reports.expenseReport")}</title>
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: ${isRTL ? "'Tajawal', sans-serif" : "'Inter', sans-serif"};
            line-height: 1.6;
            color: #333;
            background: white;
            padding: 20px;
            direction: ${isRTL ? 'rtl' : 'ltr'};
          }

          .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 20px;
          }

          .logo {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 10px;
          }

          .report-title {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 10px;
          }

          .report-meta {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 5px;
          }

          .filters-section {
            background: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
          }

          .filters-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #374151;
          }

          .filter-item {
            margin-bottom: 5px;
            font-size: 14px;
            color: #6b7280;
          }

          .section {
            margin-bottom: 30px;
          }

          .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
          }

          .group {
            margin-bottom: 25px;
          }

          .group-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 10px;
            background: #f3f4f6;
            padding: 8px 12px;
            border-radius: 6px;
          }

          .expense-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            font-size: 12px;
          }

          .expense-table th,
          .expense-table td {
            border: 1px solid #e5e7eb;
            padding: 8px;
            text-align: ${isRTL ? 'right' : 'left'};
          }

          .expense-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
          }

          .expense-table .amount {
            text-align: ${isRTL ? 'left' : 'right'};
            font-weight: 500;
          }

          .subtotal {
            text-align: ${isRTL ? 'left' : 'right'};
            font-weight: 600;
            background: #f3f4f6;
            padding: 8px 12px;
            margin-bottom: 10px;
            border-radius: 4px;
          }

          .summary-section {
            background: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
          }

          .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
          }

          .summary-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
          }

          .summary-card-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 10px;
          }

          .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 13px;
          }

          .grand-total {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background: #e5e7eb;
            border-radius: 8px;
          }

          .empty-message {
            text-align: center;
            color: #6b7280;
            font-style: italic;
            padding: 40px;
          }

          @media print {
            body {
              padding: 0;
            }

            .section {
              page-break-inside: avoid;
            }

            .group {
              page-break-inside: avoid;
            }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="logo">CloudTech</div>
          <h1 class="report-title">${t("reports.reportHeader")}</h1>
          <div class="report-meta">${t("reports.generatedOn")}: ${new Date().toLocaleDateString(locale)} ${new Date().toLocaleTimeString(locale)}</div>
        </div>

        <div class="filters-section">
          <div class="filters-title">${t("reports.appliedFilters")}</div>
          ${searchTerm ? `<div class="filter-item"><strong>${t("searchExpenses")}:</strong> ${searchTerm}</div>` : ''}
          ${filters.category_id ? `<div class="filter-item"><strong>${t("filters.category")}:</strong> ${categories.find(c => c.id === filters.category_id)?.[locale === 'ar' ? 'name_ar' : 'name_en'] || 'Unknown'}</div>` : ''}
          ${filters.payment_method ? `<div class="filter-item"><strong>${t("filters.paymentMethod")}:</strong> ${t(`paymentMethods.${filters.payment_method}`)}</div>` : ''}
          ${filters.date_from || filters.date_to ? `<div class="filter-item"><strong>${t("filters.dateRange")}:</strong> ${filters.date_from || ''} ${filters.date_to ? ` - ${filters.date_to}` : ''}</div>` : ''}
          ${!searchTerm && !filters.category_id && !filters.payment_method && !filters.date_from && !filters.date_to ? `<div class="filter-item">${t("reports.noFiltersApplied")}</div>` : ''}
        </div>

        ${totalCount === 0 ? `
          <div class="empty-message">
            <h3>${t("reports.noExpensesFound")}</h3>
            <p>${t("reports.emptyReport")}</p>
          </div>
        ` : `
          <div class="section">
            <h2 class="section-title">${groupingType === 'date' ? t("reports.groupedByDate") : t("reports.groupedByCategory")}</h2>

            ${Object.entries(groupedExpenses).map(([groupName, groupExpenses]) => {
              const groupTotal = groupExpenses.reduce((sum, expense) => {
                const amount = typeof expense.amount === 'string' ? parseFloat(expense.amount) : Number(expense.amount);
                return sum + amount;
              }, 0);

              return `
                <div class="group">
                  <div class="group-title">${groupName}</div>

                  <table class="expense-table">
                    <thead>
                      <tr>
                        <th>${t("fields.date")}</th>
                        <th>${t("fields.description")}</th>
                        <th>${t("fields.amount")}</th>
                        <th>${t("fields.paymentMethod")}</th>
                        <th>${t("fields.status")}</th>
                      </tr>
                    </thead>
                    <tbody>
                      ${groupExpenses.map(expense => `
                        <tr>
                          <td>${new Date(expense.date).toLocaleDateString(locale)}</td>
                          <td>${expense.description}</td>
                          <td class="amount">${formatCurrency(expense.amount)}</td>
                          <td>${t(`paymentMethods.${expense.payment_method}`)}</td>
                          <td>${t(`statuses.${expense.status}`)}</td>
                        </tr>
                      `).join('')}
                    </tbody>
                  </table>

                  <div class="subtotal">
                    ${t("reports.subtotal")}: ${formatCurrency(groupTotal)}
                  </div>
                </div>
              `;
            }).join('')}
          </div>

          <div class="summary-section">
            <h2 class="section-title">${t("reports.summary")}</h2>

            <div class="summary-grid">
              <div class="summary-card">
                <div class="summary-card-title">${t("reports.paymentMethodBreakdown")}</div>
                ${Object.entries(paymentMethodBreakdown).map(([method, data]) => `
                  <div class="summary-item">
                    <span>${method}:</span>
                    <span>${data.count} (${formatCurrency(data.amount)})</span>
                  </div>
                `).join('')}
              </div>

              <div class="summary-card">
                <div class="summary-card-title">${t("reports.statusBreakdown")}</div>
                ${Object.entries(statusBreakdown).map(([status, data]) => `
                  <div class="summary-item">
                    <span>${status}:</span>
                    <span>${data.count} (${formatCurrency(data.amount)})</span>
                  </div>
                `).join('')}
              </div>
            </div>

            <div class="grand-total">
              ${t("reports.totalExpenses")}: ${totalCount} | ${t("reports.grandTotal")}: ${formatCurrency(totalAmount)}
            </div>
          </div>
        `}
      </body>
      </html>
    `;

    // Write content to the new window and trigger print
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    };
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadExpenses(page, searchTerm, filters, sortConfig.column, sortConfig.direction);
  };

  // Handle expense actions
  const handleView = (expense: ExpenseWithDetails) => {
    router.push(`/${locale}/dashboard/expenses/${expense.id}`);
  };

  const handleEdit = (expense: ExpenseWithDetails) => {
    router.push(`/${locale}/dashboard/expenses/${expense.id}/edit`);
  };

  const handleDelete = (expense: ExpenseWithDetails) => {
    // Refresh the expenses list after successful deletion
    loadExpenses(currentPage, searchTerm, filters, sortConfig.column, sortConfig.direction);
  };

  const handleApprove = (expense: ExpenseWithDetails) => {
    setSelectedExpense(expense);
    setApprovalAction("approve");
    setApprovalDialogOpen(true);
  };

  const handleReject = (expense: ExpenseWithDetails) => {
    setSelectedExpense(expense);
    setApprovalAction("reject");
    setApprovalDialogOpen(true);
  };



  // Confirm approval/rejection
  const confirmApproval = async () => {
    if (!selectedExpense || !approvalAction) return;

    try {
      setActionLoading(true);
      const response = await fetch(`/api/expenses/${selectedExpense.id}/approve`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: approvalAction === "approve" ? "APPROVED" : "REJECTED",
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${approvalAction} expense`);
      }

      // Reload expenses
      await loadExpenses(currentPage, searchTerm, filters, sortConfig.column, sortConfig.direction);
      setApprovalDialogOpen(false);
      setSelectedExpense(null);
      setApprovalAction(null);
    } catch (error) {
      console.error(`Error ${approvalAction}ing expense:`, error);
      setError(`Failed to ${approvalAction} expense`);
    } finally {
      setActionLoading(false);
    }
  };

  const columns = getExpenseColumns({
    locale,
    t,
    onView: handleView,
    onEdit: handleEdit,
    onDelete: handleDelete,
    onApprove: handleApprove,
    onReject: handleReject,
    onSort: handleSort,
    currentSort: sortConfig,
  });

  const table = useDataTableInstance({
    data: expenses,
    columns,
    enableRowSelection: true,
    defaultPageSize: pageSize,
    getRowId: (row) => row.id.toString(),
  });

  if (loading && expenses.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">{tCommon("loading")}</span>
      </div>
    );
  }

  if (error && expenses.length === 0) {
    return (
      <ErrorState
        title={t("messages.errorLoadingExpenses")}
        description={error}
        onRetry={() => loadExpenses()}
        retryLabel={t("messages.tryAgain")}
        showRetry={true}
      />
    );
  }

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("title")}</h1>
          <p className="text-muted-foreground">{t("subtitle")}</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/${locale}/dashboard/expenses/categories`)}
          >
            {t("manageCategories")}
          </Button>
          <Button onClick={() => router.push(`/${locale}/dashboard/expenses/create`)}>
            <Plus className="mr-2 h-4 w-4" />
            {t("addExpense")}
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("statistics.totalExpenses")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("ui.pendingApproval")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {expenses.filter(e => e.status === "PENDING").length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("ui.thisMonth")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {expenses.filter(e => {
                const expenseDate = new Date(e.date);
                const now = new Date();
                return expenseDate.getMonth() === now.getMonth() &&
                       expenseDate.getFullYear() === now.getFullYear();
              }).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("ui.totalAmount")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(expenses.reduce((sum, e) => sum + Number(e.amount), 0))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4">
        {/* Search Bar and Filter Toggle */}
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute ltr:left-2 rtl:right-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t("searchExpenses")}
              value={searchInput}
              onChange={(e) => handleSearchInputChange(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              onCompositionStart={handleCompositionStart}
              onCompositionEnd={handleCompositionEnd}
              className="ltr:pl-8 rtl:pr-8"
              disabled={loading}
              autoComplete="off"
              spellCheck="false"
              dir="auto"
              inputMode="search"
              type="search"
            />
          </div>
          <Button
            onClick={handleSearch}
            variant="outline"
            size="sm"
            disabled={loading}
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Search className="ltr:mr-2 rtl:ml-2 h-4 w-4" />
            )}
            {tCommon("search")}
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="ltr:mr-2 rtl:ml-2 h-4 w-4" />
            {showFilters ? t("filters.hideFilters") : t("filters.showFilters")}
            <ChevronDown className={cn(
              "ltr:ml-2 rtl:mr-2 h-4 w-4 transition-transform",
              showFilters && "rotate-180"
            )} />
          </Button>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t("filters.category")}</CardTitle>
              <CardDescription>{t("filterExpenses")}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Category Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">{t("filters.category")}</label>
                  <Select
                    value={filters.category_id?.toString() || "all"}
                    onValueChange={(value) => handleFilterChange("category_id", value === "all" ? undefined : parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("filters.allCategories")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">{t("filters.allCategories")}</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {locale === 'ar' ? category.name_ar : category.name_en}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Payment Method Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">{t("filters.paymentMethod")}</label>
                  <Select
                    value={filters.payment_method || "all"}
                    onValueChange={(value) => handleFilterChange("payment_method", value === "all" ? undefined : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("filters.allPaymentMethods")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">{t("filters.allPaymentMethods")}</SelectItem>
                      {PAYMENT_METHOD_VALUES.map((method) => (
                        <SelectItem key={method} value={method}>
                          {t(`paymentMethods.${method}`)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Date Preset Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">{t("filters.quickDatePresets")}</label>
                  <Select
                    value={filters.date_preset || "custom"}
                    onValueChange={handleDatePresetChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("datePresets.custom")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="custom">{t("datePresets.custom")}</SelectItem>
                      <SelectItem value="today">{t("datePresets.today")}</SelectItem>
                      <SelectItem value="thisWeek">{t("datePresets.thisWeek")}</SelectItem>
                      <SelectItem value="thisMonth">{t("datePresets.thisMonth")}</SelectItem>
                      <SelectItem value="thisYear">{t("datePresets.thisYear")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Actions */}
                <div className="space-y-2">
                  <label className="text-sm font-medium opacity-0">Actions</label>
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      onClick={handleClearFilters}
                      className="w-full"
                    >
                      <X className="ltr:mr-2 rtl:ml-2 h-4 w-4" />
                      {t("filters.clearFilters")}
                    </Button>
                    <Button
                      variant="default"
                      onClick={generatePDFReport}
                      disabled={isGeneratingReport || loading}
                      className="w-full"
                    >
                      {isGeneratingReport ? (
                        <Loader2 className="ltr:mr-2 rtl:ml-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Printer className="ltr:mr-2 rtl:ml-2 h-4 w-4" />
                      )}
                      {isGeneratingReport ? t("reports.generatingReport") : t("reports.printReport")}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Custom Date Range */}
              {(!filters.date_preset || filters.date_preset === 'custom') && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">{t("filters.fromDate")}</label>
                    <Input
                      type="date"
                      value={filters.date_from || ""}
                      onChange={(e) => handleFilterChange("date_from", e.target.value || undefined)}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">{t("filters.toDate")}</label>
                    <Input
                      type="date"
                      value={filters.date_to || ""}
                      onChange={(e) => handleFilterChange("date_to", e.target.value || undefined)}
                      className="w-full"
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t("allExpenses")}</CardTitle>
          <CardDescription>
            {t("viewManageExpenses")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border overflow-hidden">
            <div className="w-full overflow-x-auto [&_[data-slot=table-container]]:overflow-x-visible">
              <DataTable table={table} columns={columns} />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      <DataTablePagination table={table} />



      {/* Approval Confirmation Dialog */}
      <AlertDialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {approvalAction === "approve" ? t("actions.approve") : t("actions.reject")} Expense
            </AlertDialogTitle>
            <AlertDialogDescription>
              {approvalAction === "approve" 
                ? t("messages.confirmApprove")
                : t("messages.confirmReject")
              }
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={actionLoading}>
              {tCommon("cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmApproval}
              disabled={actionLoading}
              className={approvalAction === "approve" 
                ? "bg-green-600 hover:bg-green-700" 
                : "bg-red-600 hover:bg-red-700"
              }
            >
              {actionLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {approvalAction === "approve" ? t("actions.approve") : t("actions.reject")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
