"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { Loader2 } from "lucide-react";
import { z } from "zod";
import { toast } from "sonner";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";

import { MaintenanceRequestWithRelations, MaintenanceStatus } from "@/types/maintenance";

interface UpdateStatusDialogProps {
  request: MaintenanceRequestWithRelations;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const updateStatusSchema = z.object({
  status: z.enum([
    "REPORTED",
    "ACKNOWLEDGED", 
    "IN_PROGRESS",
    "ON_HOLD",
    "COMPLETED",
    "CANCELLED"
  ]),
  notes: z.string().optional(),
  actual_cost: z.number().optional(),
  completion_date: z.string().optional(),
});

type UpdateStatusInput = z.infer<typeof updateStatusSchema>;

export function UpdateStatusDialog({
  request,
  open,
  onOpenChange,
}: UpdateStatusDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const t = useTranslations();

  const form = useForm<UpdateStatusInput>({
    resolver: zodResolver(updateStatusSchema),
    defaultValues: {
      status: request.status,
      notes: "",
      actual_cost: undefined,
      completion_date: "",
    },
  });

  const onSubmit = async (data: UpdateStatusInput) => {
    try {
      setIsLoading(true);

      const response = await fetch(`/api/maintenance/${request.id}/status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(t("maintenance.statusUpdateSuccess"));
        
        onOpenChange(false);
        router.refresh();
      } else {
        toast.error(result.message || t("common.unexpectedError"));
      }
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error(t("common.unexpectedError"));
    } finally {
      setIsLoading(false);
    }
  };

  const selectedStatus = form.watch("status");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t("maintenance.updateStatus")}</DialogTitle>
          <DialogDescription>
            {t("maintenance.updateStatusDescription", {
              requestNumber: request.request_number,
            })}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("maintenance.status")} *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="REPORTED">{t("maintenance.statuses.reported")}</SelectItem>
                      <SelectItem value="ACKNOWLEDGED">{t("maintenance.statuses.acknowledged")}</SelectItem>
                      <SelectItem value="IN_PROGRESS">{t("maintenance.statuses.in_progress")}</SelectItem>
                      <SelectItem value="ON_HOLD">{t("maintenance.statuses.on_hold")}</SelectItem>
                      <SelectItem value="COMPLETED">{t("maintenance.statuses.completed")}</SelectItem>
                      <SelectItem value="CANCELLED">{t("maintenance.statuses.cancelled")}</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedStatus === "COMPLETED" && (
              <>
                <FormField
                  control={form.control}
                  name="actual_cost"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("maintenance.actualCost")}</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.001"
                          min="0"
                          {...field}
                          value={field.value || ""}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="completion_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("maintenance.completionDate")}</FormLabel>
                      <FormControl>
                        <Input type="datetime-local" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("maintenance.statusNotes")}</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      rows={3}
                      placeholder={t("maintenance.statusNotesPlaceholder")}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                {t("common.cancel")}
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t("maintenance.updateStatus")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}