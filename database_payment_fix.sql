-- =====================================================
-- PAYMENT TAB ENHANCEMENT MIGRATION SCRIPT
-- This script fixes the payment tab errors by adding
-- the missing columns and tables for enhanced payment tracking
-- =====================================================

-- Start transaction
START TRANSACTION;

-- Check if enhanced columns already exist
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'subscription_payments' 
  AND COLUMN_NAME = 'amount_due';

-- Only proceed if columns don't exist
IF @column_exists = 0 THEN
  -- Add enhanced tracking fields to subscription_payments table
  ALTER TABLE `subscription_payments` 
  ADD COLUMN `amount_due` DECIMAL(10, 3) NOT NULL AFTER `due_date`,
  ADD COLUMN `amount_paid` DECIMAL(10, 3) NOT NULL DEFAULT 0 AFTER `amount`,
  ADD COLUMN `remaining_balance` DECIMAL(10, 3) GENERATED ALWAYS AS (`amount_due` - `amount_paid`) STORED,
  ADD COLUMN `transaction_id` INT NULL AFTER `reference_number`,
  ADD INDEX `idx_subscription_payments_transaction_id` (`transaction_id`),
  ADD CONSTRAINT `fk_subscription_payment_transaction` 
    FOREIGN KEY (`transaction_id`) REFERENCES `association_transactions`(`id`) ON DELETE SET NULL;

  -- Update existing records to set amount_due from amount
  UPDATE `subscription_payments` SET `amount_due` = `amount` WHERE `amount_due` IS NULL OR `amount_due` = 0;

  -- Set amount_paid based on status
  UPDATE `subscription_payments` 
  SET `amount_paid` = CASE 
    WHEN `status` = 'PAID' THEN `amount`
    ELSE 0
  END
  WHERE `amount_paid` = 0;

  SELECT 'Enhanced columns added to subscription_payments table' as status;
ELSE
  SELECT 'Enhanced columns already exist in subscription_payments table' as status;
END IF;

-- Check if subscription_payment_installments table exists
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'subscription_payment_installments';

-- Create subscription_payment_installments table if it doesn't exist
IF @table_exists = 0 THEN
  CREATE TABLE `subscription_payment_installments` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `subscription_payment_id` INT NOT NULL,
    `transaction_id` INT NOT NULL,
    `amount` DECIMAL(10, 3) NOT NULL,
    `payment_date` DATE NOT NULL,
    `payment_method` ENUM('CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'CHECK', 'OTHER') NOT NULL,
    `reference_number` VARCHAR(100) NULL,
    `notes` TEXT NULL,
    `created_by` INT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_subscription_payment` (`subscription_payment_id`),
    INDEX `idx_transaction` (`transaction_id`),
    INDEX `idx_created_by` (`created_by`),
    CONSTRAINT `fk_installment_subscription_payment` 
      FOREIGN KEY (`subscription_payment_id`) REFERENCES `subscription_payments`(`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_installment_transaction` 
      FOREIGN KEY (`transaction_id`) REFERENCES `association_transactions`(`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_installment_creator` 
      FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

  SELECT 'subscription_payment_installments table created' as status;
ELSE
  SELECT 'subscription_payment_installments table already exists' as status;
END IF;

-- Check if the view exists and create/replace it
DROP VIEW IF EXISTS `member_subscription_payment_summary`;

CREATE VIEW `member_subscription_payment_summary` AS
SELECT 
  m.id AS member_id,
  m.full_name,
  m.unit_number,
  s.id AS subscription_id,
  s.name_en AS subscription_name_en,
  s.name_ar AS subscription_name_ar,
  sp.id AS payment_id,
  sp.due_date,
  sp.amount_due,
  sp.amount_paid,
  sp.remaining_balance,
  sp.status,
  CASE 
    WHEN sp.status = 'PAID' THEN 'Fully Paid'
    WHEN sp.status = 'PARTIALLY_PAID' THEN CONCAT('Paid: ', sp.amount_paid, ' / ', sp.amount_due)
    WHEN sp.status = 'OVERDUE' THEN 'Overdue'
    ELSE 'Unpaid'
  END AS payment_status_description
FROM 
  association_members m
  INNER JOIN subscription_payments sp ON m.id = sp.member_id
  INNER JOIN association_subscriptions s ON sp.subscription_id = s.id;

-- Check if trigger exists and create it
SET @trigger_exists = 0;
SELECT COUNT(*) INTO @trigger_exists 
FROM information_schema.TRIGGERS 
WHERE TRIGGER_SCHEMA = DATABASE() 
  AND TRIGGER_NAME = 'update_payment_status';

IF @trigger_exists = 0 THEN
  -- Create trigger to automatically update payment status based on amount paid
  DELIMITER $$
  CREATE TRIGGER `update_payment_status` 
  BEFORE UPDATE ON `subscription_payments`
  FOR EACH ROW
  BEGIN
    IF NEW.amount_paid >= NEW.amount_due THEN
      SET NEW.status = 'PAID';
    ELSEIF NEW.amount_paid > 0 AND NEW.amount_paid < NEW.amount_due THEN
      SET NEW.status = 'PARTIALLY_PAID';
    ELSEIF NEW.due_date < CURDATE() AND NEW.amount_paid = 0 THEN
      SET NEW.status = 'OVERDUE';
    ELSE
      SET NEW.status = 'UNPAID';
    END IF;
  END$$
  DELIMITER ;

  SELECT 'Payment status update trigger created' as status;
ELSE
  SELECT 'Payment status update trigger already exists' as status;
END IF;

-- Add missing fields to association_transactions if they don't exist
SET @member_id_exists = 0;
SELECT COUNT(*) INTO @member_id_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'association_transactions' 
  AND COLUMN_NAME = 'member_id';

IF @member_id_exists = 0 THEN
  ALTER TABLE `association_transactions` 
  ADD COLUMN `member_id` INT NULL AFTER `association_id`,
  ADD COLUMN `subscription_payment_id` INT NULL AFTER `member_id`,
  ADD COLUMN `payment_installment_id` INT NULL AFTER `subscription_payment_id`,
  ADD INDEX `idx_association_transactions_member_id` (`member_id`),
  ADD INDEX `idx_association_transactions_subscription_payment_id` (`subscription_payment_id`),
  ADD INDEX `idx_association_transactions_payment_installment_id` (`payment_installment_id`),
  ADD CONSTRAINT `fk_transaction_member` 
    FOREIGN KEY (`member_id`) REFERENCES `association_members`(`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_transaction_subscription_payment` 
    FOREIGN KEY (`subscription_payment_id`) REFERENCES `subscription_payments`(`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_transaction_payment_installment` 
    FOREIGN KEY (`payment_installment_id`) REFERENCES `subscription_payment_installments`(`id`) ON DELETE SET NULL;

  SELECT 'Enhanced fields added to association_transactions table' as status;
ELSE
  SELECT 'Enhanced fields already exist in association_transactions table' as status;
END IF;

-- Commit the transaction
COMMIT;

-- Final status check
SELECT 'Database schema migration completed successfully!' as final_status;

-- Show the current structure of key tables
SELECT 'Current subscription_payments table structure:' as info;
DESCRIBE subscription_payments;

SELECT 'Current subscription_payment_installments table structure:' as info;
DESCRIBE subscription_payment_installments;

SELECT 'Current association_transactions table structure:' as info;  
DESCRIBE association_transactions;