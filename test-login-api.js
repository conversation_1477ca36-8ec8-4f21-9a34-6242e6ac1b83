// Test script to verify login API works
const fetch = require('node-fetch');

async function testLoginAPI() {
  try {
    console.log("Testing login API...");
    
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: '123456'
      })
    });
    
    console.log("Response status:", response.status);
    console.log("Response headers:", Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log("Login successful!");
      console.log("Response data:", JSON.stringify(data, null, 2));
      
      // Test the /api/auth/me endpoint with the cookie
      const cookies = response.headers.get('set-cookie');
      console.log("Cookies:", cookies);
      
      if (cookies) {
        const authCookie = cookies.find(cookie => cookie.startsWith('auth-token='));
        if (authCookie) {
          console.log("Testing /api/auth/me with cookie...");
          
          const meResponse = await fetch('http://localhost:3001/api/auth/me', {
            headers: {
              'Cookie': authCookie
            }
          });
          
          console.log("Me response status:", meResponse.status);
          
          if (meResponse.ok) {
            const meData = await meResponse.json();
            console.log("Me endpoint successful!");
            console.log("User data:", JSON.stringify(meData, null, 2));
          } else {
            const errorData = await meResponse.text();
            console.log("Me endpoint failed:", errorData);
          }
        }
      }
    } else {
      const errorData = await response.text();
      console.log("Login failed:", errorData);
    }
  } catch (error) {
    console.error("Test failed:", error);
  }
}

testLoginAPI();
