<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=property_management', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Read the SQL file
    $sql = file_get_contents('seed.sql');
    
    // Split into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "Executing seed data import...\n";
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                echo "✅ Executed: " . substr($statement, 0, 50) . "...\n";
            } catch (PDOException $e) {
                echo "⚠️  Warning: " . $e->getMessage() . "\n";
                echo "   Statement: " . substr($statement, 0, 100) . "...\n";
            }
        }
    }
    
    echo "\n🎉 Seed data import completed!\n";
    
    // Verify the data
    echo "\nVerifying data:\n";
    
    $result = $pdo->query("SELECT COUNT(*) as count FROM property_owners")->fetch();
    echo "Property owners: " . $result['count'] . "\n";
    
    $result = $pdo->query("SELECT COUNT(*) as count FROM property_types")->fetch();
    echo "Property types: " . $result['count'] . "\n";
    
    $result = $pdo->query("SELECT COUNT(*) as count FROM properties")->fetch();
    echo "Properties: " . $result['count'] . "\n";
    
    $result = $pdo->query("SELECT COUNT(*) as count FROM amenities")->fetch();
    echo "Amenities: " . $result['count'] . "\n";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>