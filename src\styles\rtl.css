/* RTL Support Styles - Following next-intl best practices */

/* CSS Logical Properties for better RTL support */
.rtl-aware {
  /* Use logical properties instead of physical ones */
  margin-inline-start: var(--spacing-4);
  margin-inline-end: var(--spacing-4);
  padding-inline-start: var(--spacing-2);
  padding-inline-end: var(--spacing-2);
  border-inline-start: 1px solid var(--border);
  border-inline-end: 1px solid var(--border);
}

/* RTL-specific icon rotations */
[dir="rtl"] .rtl-flip {
  transform: scaleX(-1);
}

/* RTL-specific text alignment */
[dir="rtl"] .rtl-text-start {
  text-align: right;
}

[dir="ltr"] .rtl-text-start {
  text-align: left;
}

[dir="rtl"] .rtl-text-end {
  text-align: left;
}

[dir="ltr"] .rtl-text-end {
  text-align: right;
}

/* RTL-specific flex direction */
[dir="rtl"] .rtl-flex-row {
  flex-direction: row-reverse;
}

[dir="ltr"] .rtl-flex-row {
  flex-direction: row;
}

/* RTL-specific positioning */
[dir="rtl"] .rtl-left {
  right: 0;
  left: auto;
}

[dir="ltr"] .rtl-left {
  left: 0;
  right: auto;
}

[dir="rtl"] .rtl-right {
  left: 0;
  right: auto;
}

[dir="ltr"] .rtl-right {
  right: 0;
  left: auto;
}

/* Sidebar RTL adjustments */
[dir="rtl"] .sidebar {
  border-left: 1px solid var(--sidebar-border);
  border-right: none;
}

[dir="ltr"] .sidebar {
  border-right: 1px solid var(--sidebar-border);
  border-left: none;
}

/* Sidebar positioning for RTL */
[dir="rtl"] [data-slot="sidebar"] {
  right: 0;
  left: auto;
}

[dir="ltr"] [data-slot="sidebar"] {
  left: 0;
  right: auto;
}

/* Sidebar inset positioning for RTL */
[dir="rtl"] [data-slot="sidebar-inset"] {
  margin-left: 0;
  margin-right: var(--sidebar-width);
}

[dir="ltr"] [data-slot="sidebar-inset"] {
  margin-right: 0;
  margin-left: var(--sidebar-width);
}

/* Sidebar trigger positioning for RTL */
[dir="rtl"] [data-slot="sidebar-trigger"] {
  margin-right: -0.25rem;
  margin-left: 0;
}

[dir="ltr"] [data-slot="sidebar-trigger"] {
  margin-left: -0.25rem;
  margin-right: 0;
}

/* Sidebar content RTL adjustments */
[dir="rtl"] [data-slot="sidebar-container"] {
  border-left: 1px solid hsl(var(--border));
  border-right: none;
}

[dir="ltr"] [data-slot="sidebar-container"] {
  border-right: 1px solid hsl(var(--border));
  border-left: none;
}

/* Sidebar inset margin adjustments for RTL */
[dir="rtl"] [data-content-layout="centered"] {
  margin-left: auto !important;
  margin-right: auto !important;
}

[dir="rtl"] .peer-data-\[variant\=inset\]\:\!mr-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}

[dir="rtl"] .peer-data-\[variant\=inset\]\:peer-data-\[state\=collapsed\]\:\!mr-auto {
  margin-right: 0 !important;
  margin-left: auto !important;
}

/* Navigation arrows for RTL */
[dir="rtl"] .nav-arrow-right {
  transform: rotate(180deg);
}

[dir="rtl"] .nav-arrow-left {
  transform: rotate(180deg);
}

/* Dropdown menu alignment */
[dir="rtl"] .dropdown-menu-end {
  right: auto;
  left: 0;
}

[dir="ltr"] .dropdown-menu-end {
  left: auto;
  right: 0;
}

/* Form input RTL support */
[dir="rtl"] .form-input {
  text-align: right;
}

[dir="ltr"] .form-input {
  text-align: left;
}

/* Input field RTL support */
[dir="rtl"] [data-slot="input"] {
  text-align: right;
  direction: rtl;
}

[dir="ltr"] [data-slot="input"] {
  text-align: left;
  direction: ltr;
}

/* Enhanced input stability for RTL */
[dir="rtl"] input[type="search"],
[dir="rtl"] input[inputmode="search"] {
  text-align: right;
  direction: rtl;
  unicode-bidi: plaintext;
}

[dir="ltr"] input[type="search"],
[dir="ltr"] input[inputmode="search"] {
  text-align: left;
  direction: ltr;
  unicode-bidi: plaintext;
}

/* Search input specific RTL support */
[dir="rtl"] .search-input {
  padding-right: 2rem;
  padding-left: 0.75rem;
}

[dir="ltr"] .search-input {
  padding-left: 2rem;
  padding-right: 0.75rem;
}

/* Prevent input focus loss in RTL */
[dir="rtl"] input:focus,
[dir="rtl"] input:focus-visible {
  outline-offset: 2px;
}

/* Input method editor support */
input[inputmode="search"] {
  ime-mode: auto;
}

/* Composition event handling for better IME support */
input[type="search"]:focus {
  composition-start: auto;
  composition-update: auto;
  composition-end: auto;
}

/* Table RTL support */
[dir="rtl"] .table-cell-start {
  text-align: right;
}

[dir="ltr"] .table-cell-start {
  text-align: left;
}

/* Table header and cell alignment for RTL */
[dir="rtl"] [data-slot="table-head"] {
  text-align: right !important;
}

[dir="ltr"] [data-slot="table-head"] {
  text-align: left !important;
}

[dir="rtl"] [data-slot="table-cell"] {
  text-align: right !important;
}

[dir="ltr"] [data-slot="table-cell"] {
  text-align: left !important;
}

/* More specific table alignment for RTL */
[dir="rtl"] table th,
[dir="rtl"] table td {
  text-align: right !important;
}

[dir="ltr"] table th,
[dir="ltr"] table td {
  text-align: left !important;
}

/* Specific alignment for numeric columns in RTL - keep numbers left-aligned */
[dir="rtl"] [data-slot="table-cell"]:has(.font-medium),
[dir="rtl"] [data-slot="table-head"]:has(.font-medium),
[dir="rtl"] td:nth-child(3), /* Amount column */
[dir="rtl"] th:nth-child(3) /* Amount header */ {
  text-align: left !important;
}

/* Action column alignment - center for both RTL and LTR */
[dir="rtl"] [data-slot="table-cell"]:last-child,
[dir="rtl"] [data-slot="table-head"]:last-child,
[dir="rtl"] td:last-child,
[dir="rtl"] th:last-child {
  text-align: center !important;
}

[dir="ltr"] [data-slot="table-cell"]:last-child,
[dir="ltr"] [data-slot="table-head"]:last-child,
[dir="ltr"] td:last-child,
[dir="ltr"] th:last-child {
  text-align: center !important;
}

/* Badge alignment in RTL */
[dir="rtl"] .badge {
  text-align: center;
}

/* DataTable specific RTL fixes */
[dir="rtl"] .data-table th,
[dir="rtl"] .data-table td {
  text-align: right !important;
}

[dir="rtl"] .data-table th:nth-child(3),
[dir="rtl"] .data-table td:nth-child(3) {
  text-align: left !important; /* Amount column */
}

[dir="rtl"] .data-table th:last-child,
[dir="rtl"] .data-table td:last-child {
  text-align: center !important; /* Actions column */
}

/* Property Types table specific RTL alignment */
[dir="rtl"] table[role="table"] th,
[dir="rtl"] table[role="table"] td {
  text-align: right !important;
}

/* Keep checkbox columns left-aligned in RTL */
[dir="rtl"] table[role="table"] th:first-child,
[dir="rtl"] table[role="table"] td:first-child {
  text-align: left !important;
}

/* Center-aligned columns for both RTL and LTR */
/* ID column (2nd column) */
table[role="table"] th:nth-child(2),
table[role="table"] td:nth-child(2) {
  text-align: center !important;
}

/* Properties count column (5th column) */
table[role="table"] th:nth-child(5),
table[role="table"] td:nth-child(5) {
  text-align: center !important;
}

/* Actions column center-aligned */
[dir="rtl"] table[role="table"] th:last-child,
[dir="rtl"] table[role="table"] td:last-child {
  text-align: center !important;
}

/* Breadcrumb RTL support */
[dir="rtl"] .breadcrumb-separator {
  transform: scaleX(-1);
}

/* Chart and data visualization RTL adjustments */
[dir="rtl"] .chart-legend {
  direction: rtl;
}

/* Button group RTL support */
[dir="rtl"] .button-group {
  flex-direction: row-reverse;
}

/* Toast notification RTL positioning */
[dir="rtl"] .toast-container {
  left: 1rem;
  right: auto;
}

[dir="ltr"] .toast-container {
  right: 1rem;
  left: auto;
}
