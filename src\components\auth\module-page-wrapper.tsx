"use client";

import { ReactNode } from "react";
import { PermissionGuard } from "./permission-guard";

interface ModulePageWrapperProps {
  module: string;
  children: ReactNode;
  fallback?: ReactNode;
}

export function ModulePageWrapper({ module, children, fallback }: ModulePageWrapperProps) {
  return (
    <PermissionGuard module={module} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}