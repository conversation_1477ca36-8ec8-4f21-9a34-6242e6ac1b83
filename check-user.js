const { PrismaClient } = require('./src/generated/prisma/client');
const prisma = new PrismaClient();

async function checkUser() {
  try {
    const user = await prisma.user.findFirst({
      where: { username: 'ali' }
    });
    
    if (user) {
      console.log('User found:');
      console.log('ID:', user.id);
      console.log('Username:', user.username);
      console.log('Email:', user.email);
      console.log('Status:', user.status);
      console.log('Password hash length:', user.password_hash?.length || 0);
      console.log('Password hash preview:', user.password_hash?.substring(0, 30) + '...');
    } else {
      console.log('User not found');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUser();