import { z } from "zod";
import type { Tenant, TenantDocument, EmergencyContact, DocumentType } from "@/generated/prisma";

// Tenant Schemas
export const tenantSchema = z.object({
  first_name: z.string().min(2, "First name must be at least 2 characters"),
  last_name: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional().nullable(),
  national_id: z.string().optional().nullable(),
  national_id_expiry: z.string().optional().nullable(),
  date_of_birth: z.string().optional().nullable(),
  nationality: z.string().optional().nullable(),
  occupation: z.string().optional().nullable(),
  company_name: z.string().optional().nullable(),
});

export const tenantUpdateSchema = tenantSchema.partial();

export type TenantInput = z.infer<typeof tenantSchema>;
export type TenantUpdateInput = z.infer<typeof tenantUpdateSchema>;

// Emergency Contact Schemas
export const emergencyContactSchema = z.object({
  name: z.string().min(2, "Name is required"),
  relationship: z.string().min(2, "Relationship is required"),
  phone: z.string().min(1, "Phone number is required"),
  email: z.string().email("Invalid email address").optional().nullable(),
  address: z.string().optional().nullable(),
  is_primary: z.boolean().default(false),
});

export type EmergencyContactInput = z.infer<typeof emergencyContactSchema>;

// Document Schemas
export const tenantDocumentSchema = z.object({
  document_type: z.enum([
    "NATIONAL_ID",
    "PASSPORT", 
    "DRIVING_LICENSE",
    "EMPLOYMENT_CONTRACT",
    "SALARY_CERTIFICATE",
    "BANK_STATEMENT",
    "OTHER"
  ]),
  document_number: z.string().optional().nullable(),
  issue_date: z.string().optional().nullable(),
  expiry_date: z.string().optional().nullable(),
  file: z.any(), // File will be handled separately
});

export type TenantDocumentInput = z.infer<typeof tenantDocumentSchema>;

// Filter types
export interface TenantFilters {
  search?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// Extended types with relations
export interface TenantWithRelations extends Tenant {
  documents?: TenantDocument[];
  emergency_contacts?: EmergencyContact[];
  creator?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
  updater?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
}

export interface TenantDocumentWithUploader extends TenantDocument {
  uploader?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
}