"use client";

import { useState, useEffect, useCallback } from "react";
import { useTranslations, useLocale } from "next-intl";
import { toast } from "sonner";
import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Calculator,
  TrendingUp,
  TrendingDown,
  Filter,
  Search,
  FileText,
  ArrowUpCircle,
  ArrowDownCircle
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useRTL } from "@/hooks/use-rtl";
import { apiClient } from "@/lib/api-client";
import { formatCurrency } from "@/lib/utils";
import { EXPENSE_CATEGORIES, INCOME_CATEGORIES } from "@/types/owners-association";
import type { AssociationTransactionWithRelations } from "@/types/owners-association";
import { AddTransactionForm } from "./add-transaction-form";
import { EditTransactionForm } from "./edit-transaction-form";

interface AssociationTransactionsTableProps {
  associationId: number;
}

export function AssociationTransactionsTable({ associationId }: AssociationTransactionsTableProps) {
  const locale = useLocale();
  const { isRTL } = useRTL();
  const t = useTranslations("ownersAssociations.transactions");
  const tCommon = useTranslations("common");
  const dateLocale = locale === 'ar' ? ar : enUS;
  
  const [transactions, setTransactions] = useState<AssociationTransactionWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState<AssociationTransactionWithRelations | null>(null);

  const fetchTransactions = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        ...(searchTerm && { search: searchTerm }),
        ...(typeFilter && typeFilter !== "all" && { type: typeFilter }),
        ...(categoryFilter && categoryFilter !== "all" && { category: categoryFilter }),
      });

      const response = await apiClient.get(
        `/api/owners-associations/${associationId}/transactions?${params}`
      );
      
      if (response.success) {
        setTransactions(response.data.transactions || response.data);
      }
    } catch (error) {
      console.error("Error fetching transactions:", error);
      toast.error(t("serverError"));
    } finally {
      setLoading(false);
    }
  }, [associationId, searchTerm, typeFilter, categoryFilter, t]);

  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions]);

  const handleDeleteTransaction = async (transactionId: number) => {
    try {
      await apiClient.delete(`/api/owners-associations/${associationId}/transactions/${transactionId}`);
      toast.success(t("deleteSuccess"));
      fetchTransactions();
    } catch (error) {
      console.error("Error deleting transaction:", error);
      toast.error(t("deleteError"));
    }
  };

  const getCategoryLabel = (type: string, category: string) => {
    const categories = type === 'EXPENSE' ? EXPENSE_CATEGORIES : INCOME_CATEGORIES;
    const categoryObj = categories.find(c => c.value === category);
    return categoryObj ? (locale === 'ar' ? categoryObj.label_ar : categoryObj.label_en) : category;
  };

  const handleReset = () => {
    setSearchTerm("");
    setTypeFilter("");
    setCategoryFilter("");
  };

  const columns: ColumnDef<AssociationTransactionWithRelations>[] = [
    {
      accessorKey: "transaction_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("date")} />
      ),
      cell: ({ row }) => {
        const date = new Date(row.original.transaction_date);
        return (
          <div className={cn("text-sm", isRTL && "text-right")}>
            {format(date, "dd/MM/yyyy", { locale: dateLocale })}
          </div>
        );
      },
    },
    {
      accessorKey: "member",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("member")} />
      ),
      cell: ({ row }) => {
        const transaction = row.original;
        const member = transaction.member;
        
        if (!member) {
          return <span className="text-sm text-muted-foreground">-</span>;
        }
        
        return (
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="font-medium text-sm">{member.full_name}</div>
            <div className="text-xs text-muted-foreground">
              {t("unit")}: {member.unit_number}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "type",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("typeLabel")} />
      ),
      cell: ({ row }) => {
        const transaction = row.original;
        const isIncome = transaction.type === 'INCOME';
        
        return (
          <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
            {isIncome ? (
              <ArrowUpCircle className="h-4 w-4 text-green-600" />
            ) : (
              <ArrowDownCircle className="h-4 w-4 text-red-600" />
            )}
            <Badge variant={isIncome ? "default" : "destructive"}>
              {isIncome ? t("type.income") : t("type.expense")}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: "description",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("description")} />
      ),
      cell: ({ row }) => {
        const transaction = row.original;
        
        return (
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="font-medium">{transaction.description}</div>
            <Badge variant="outline" className="text-xs">
              {getCategoryLabel(transaction.type, transaction.category)}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: "amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("amount")} />
      ),
      cell: ({ row }) => {
        const transaction = row.original;
        const amount = parseFloat(transaction.amount.toString());
        const isIncome = transaction.type === 'INCOME';
        
        return (
          <div className={cn(
            "font-medium flex items-center gap-1",
            isRTL && "flex-row-reverse",
            isIncome ? "text-green-600" : "text-red-600"
          )}>
            {isIncome ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
            {isIncome ? '+' : '-'}{formatCurrency(amount)}
          </div>
        );
      },
    },
    {
      accessorKey: "payment_method",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("paymentMethodLabel")} />
      ),
      cell: ({ row }) => {
        const method = row.original.payment_method;
        return (
          <Badge variant="outline" className="text-xs">
            {t(`paymentMethod.${method.toLowerCase()}`)}
          </Badge>
        );
      },
    },
    {
      accessorKey: "attachment",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("attachment")} />
      ),
      cell: ({ row }) => {
        const { attachment_url, attachment_name } = row.original;
        
        if (!attachment_url) {
          return (
            <span className="text-sm text-muted-foreground">{t("noAttachment")}</span>
          );
        }

        return (
          <Button variant="ghost" size="sm" asChild>
            <a 
              href={attachment_url} 
              target="_blank" 
              rel="noopener noreferrer"
              className={cn("flex items-center gap-1", isRTL && "flex-row-reverse")}
            >
              <FileText className="h-4 w-4" />
              <span className="text-xs truncate max-w-[100px]">
                {attachment_name || t("viewAttachment")}
              </span>
            </a>
          </Button>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className={cn("text-right", isRTL && "text-left")}>{tCommon("actions")}</div>,
      cell: ({ row }) => {
        const transaction = row.original;

        return (
          <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditingTransaction(transaction)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeleteTransaction(transaction.id)}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: transactions,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  // Calculate summary stats
  const totalIncome = transactions
    .filter(t => t.type === 'INCOME')
    .reduce((sum, t) => sum + parseFloat(t.amount.toString()), 0);
  
  const totalExpenses = transactions
    .filter(t => t.type === 'EXPENSE')
    .reduce((sum, t) => sum + parseFloat(t.amount.toString()), 0);

  const netBalance = totalIncome - totalExpenses;

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-96" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[400px] w-full" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className={cn("flex items-center justify-between", isRTL && "flex-row-reverse")}>
          <div>
            <CardTitle className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
              <Calculator className="h-5 w-5" />
              {t("title")}
            </CardTitle>
            <CardDescription>
              {t("description")}
            </CardDescription>
          </div>
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                {t("addTransaction")}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t("addTransactionDialog.title")}</DialogTitle>
                <DialogDescription>
                  {t("addTransactionDialog.description")}
                </DialogDescription>
              </DialogHeader>
              <AddTransactionForm 
                associationId={associationId}
                onSuccess={() => {
                  setShowAddDialog(false);
                  fetchTransactions();
                }}
                onCancel={() => setShowAddDialog(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="text-sm font-medium">{t("totalTransactions")}</div>
            <div className="text-2xl font-bold">{transactions.length}</div>
          </div>
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="text-sm font-medium">{t("totalIncome")}</div>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(totalIncome)}</div>
          </div>
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="text-sm font-medium">{t("totalExpenses")}</div>
            <div className="text-2xl font-bold text-red-600">{formatCurrency(totalExpenses)}</div>
          </div>
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="text-sm font-medium">{t("netBalance")}</div>
            <div className={cn(
              "text-2xl font-bold",
              netBalance >= 0 ? "text-green-600" : "text-red-600"
            )}>
              {formatCurrency(netBalance)}
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className={cn(
          "flex flex-col gap-4 sm:flex-row sm:items-center",
          isRTL && "sm:flex-row-reverse"
        )}>
          <div className="relative flex-1 max-w-sm">
            <Search className={cn(
              "absolute top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground",
              isRTL ? "right-3" : "left-3"
            )} />
            <Input
              placeholder={t("searchTransactions")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={cn(isRTL ? "pr-9" : "pl-9")}
            />
          </div>
          
          <div className={cn(
            "flex items-center gap-2",
            isRTL && "flex-row-reverse"
          )}>
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder={t("filterByType")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("allTypes")}</SelectItem>
                <SelectItem value="INCOME">{t("type.income")}</SelectItem>
                <SelectItem value="EXPENSE">{t("type.expense")}</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder={t("filterByCategory")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("allCategories")}</SelectItem>
                {EXPENSE_CATEGORIES.map((category) => (
                  <SelectItem key={`expense-${category.value}`} value={category.value}>
                    {locale === 'ar' ? category.label_ar : category.label_en}
                  </SelectItem>
                ))}
                {INCOME_CATEGORIES.map((category) => (
                  <SelectItem key={`income-${category.value}`} value={category.value}>
                    {locale === 'ar' ? category.label_ar : category.label_en}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              className={cn("shrink-0", isRTL && "ml-2")}
            >
              {tCommon("reset")}
            </Button>
          </div>
        </div>

        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id} className={cn(isRTL && "text-right")}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} className="hover:bg-muted/50">
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className={cn(isRTL && "text-right")}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    {t("noTransactions")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* Edit Transaction Dialog */}
      <Dialog open={!!editingTransaction} onOpenChange={(open) => !open && setEditingTransaction(null)}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>{t("editTransactionDialog.title")}</DialogTitle>
            <DialogDescription>
              {t("editTransactionDialog.description")}
            </DialogDescription>
          </DialogHeader>
          {editingTransaction && (
            <EditTransactionForm 
              associationId={associationId}
              transaction={editingTransaction}
              onSuccess={() => {
                setEditingTransaction(null);
                fetchTransactions();
              }}
              onCancel={() => setEditingTransaction(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
}