import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { verifyToken } from "@/lib/auth";

const prisma = new PrismaClient();

// GET /api/company-settings - Get company settings
export async function GET(request: NextRequest) {
  try {
    console.log("Company Settings API: GET request received");

    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === 'development';

    // Get token from cookie
    const token = request.cookies.get("auth-token")?.value;

    // In development mode, allow access without authentication
    if (!isDevelopment) {
      if (!token) {
        console.log("Company Settings API: No token found in cookies");
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Verify token
      const decoded = verifyToken(token);
      if (!decoded) {
        console.log("Company Settings API: Token verification failed");
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      console.log("Company Settings API: User authenticated:", decoded.username);
    } else {
      console.log("Company Settings API: Development mode - skipping authentication");
    }

    // Get company settings (there should only be one record)
    let companySettings = await prisma.companySettings.findFirst();

    // If no settings exist, create default ones
    if (!companySettings) {
      console.log("Company Settings API: No settings found, creating default");
      companySettings = await prisma.companySettings.create({
        data: {
          company_name: "CloudTech",
          company_name_ar: "كلاود تك",
          logo_url: null,
        },
      });
    }

    console.log("Company Settings API: Settings retrieved:", companySettings);

    return NextResponse.json({
      success: true,
      data: companySettings,
    });
  } catch (error) {
    console.error("Company Settings API Error:", error);
    return NextResponse.json(
      { error: "Failed to fetch company settings" },
      { status: 500 }
    );
  }
}

// PUT /api/company-settings - Update company settings
export async function PUT(request: NextRequest) {
  try {
    console.log("Company Settings API: PUT request received");

    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === 'development';

    // Get token from cookie
    const token = request.cookies.get("auth-token")?.value;

    // In development mode, allow access without authentication
    if (!isDevelopment) {
      if (!token) {
        console.log("Company Settings API: No token found in cookies");
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Verify token
      const decoded = verifyToken(token);
      if (!decoded) {
        console.log("Company Settings API: Token verification failed");
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      console.log("Company Settings API: User authenticated:", decoded.username);
    } else {
      console.log("Company Settings API: Development mode - skipping authentication");
    }

    // Parse request body
    const body = await request.json();
    const { company_name, company_name_ar, logo_url } = body;

    console.log("Company Settings API: Update data:", { company_name, company_name_ar, logo_url });

    // Validate required fields
    if (!company_name || typeof company_name !== "string") {
      return NextResponse.json(
        { error: "Company name is required and must be a string" },
        { status: 400 }
      );
    }

    // Get existing settings or create new ones
    let companySettings = await prisma.companySettings.findFirst();

    if (companySettings) {
      // Update existing settings
      companySettings = await prisma.companySettings.update({
        where: { id: companySettings.id },
        data: {
          company_name,
          company_name_ar: company_name_ar || null,
          logo_url: logo_url || null,
        },
      });
    } else {
      // Create new settings
      companySettings = await prisma.companySettings.create({
        data: {
          company_name,
          company_name_ar: company_name_ar || null,
          logo_url: logo_url || null,
        },
      });
    }

    console.log("Company Settings API: Settings updated:", companySettings);

    return NextResponse.json({
      success: true,
      data: companySettings,
      message: "Company settings updated successfully",
    });
  } catch (error) {
    console.error("Company Settings API Error:", error);
    return NextResponse.json(
      { error: "Failed to update company settings" },
      { status: 500 }
    );
  }
}
