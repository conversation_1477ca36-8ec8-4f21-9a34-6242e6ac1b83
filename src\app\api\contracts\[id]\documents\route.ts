import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ApiResponseBuilder } from "@/lib/api-response";
import { writeFile, mkdir } from "fs/promises";
import { join } from "path";
import { contractDocumentSchema } from "@/types/contract";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for contracts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "contracts", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to read contracts");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const contractId = parseInt(id);
    if (isNaN(contractId)) {
      return ApiResponseBuilder.error("Invalid contract ID", "BAD_REQUEST", 400);
    }

    // Check if contract exists
    const contract = await prisma.contract.findUnique({
      where: { id: contractId },
    });

    if (!contract) {
      return ApiResponseBuilder.error("Contract not found", "NOT_FOUND", 404);
    }

    // Get contract documents
    const documents = await prisma.contractDocument.findMany({
      where: { contract_id: contractId },
      include: {
        uploader: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
      orderBy: { created_at: "desc" },
    });

    return ApiResponseBuilder.success(documents);
  } catch (error) {
    console.error("Error fetching contract documents:", error);
    return ApiResponseBuilder.error("Failed to fetch contract documents", "INTERNAL_ERROR", 500);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for contracts
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "contracts", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create contracts");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const contractId = parseInt(id);
    if (isNaN(contractId)) {
      return ApiResponseBuilder.error("Invalid contract ID", "BAD_REQUEST", 400);
    }

    // Check if contract exists
    const contract = await prisma.contract.findUnique({
      where: { id: contractId },
    });

    if (!contract) {
      return ApiResponseBuilder.error("Contract not found", "NOT_FOUND", 404);
    }

    // Parse multipart form data
    const formData = await request.formData();
    const file = formData.get("file") as File | null;
    const documentType = formData.get("document_type") as string;
    const documentName = formData.get("document_name") as string;

    if (!file) {
      return ApiResponseBuilder.error("File is required", "BAD_REQUEST", 400);
    }

    if (!documentType || !documentName) {
      return ApiResponseBuilder.error("Document type and name are required", "BAD_REQUEST", 400);
    }

    // Validate document type
    const validTypes = ["CONTRACT", "ADDENDUM", "TERMINATION_LETTER", "RENEWAL_AGREEMENT", "PAYMENT_RECEIPT", "OTHER"];
    if (!validTypes.includes(documentType)) {
      return ApiResponseBuilder.error("Invalid document type", "BAD_REQUEST", 400);
    }

    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      return ApiResponseBuilder.error("File size must be less than 10MB", "BAD_REQUEST", 400);
    }

    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), "public", "uploads", "contract-documents");
    await mkdir(uploadDir, { recursive: true });

    // Generate unique filename
    const timestamp = Date.now();
    const originalName = file.name;
    const extension = originalName.substring(originalName.lastIndexOf("."));
    const fileName = `contract_${contractId}_${timestamp}${extension}`;
    const filePath = join(uploadDir, fileName);
    const publicPath = `/uploads/contract-documents/${fileName}`;

    // Write file to disk
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Create document record
    const document = await prisma.contractDocument.create({
      data: {
        contract_id: contractId,
        document_type: documentType as any,
        document_name: documentName,
        file_name: originalName,
        file_path: publicPath,
        file_size: file.size,
        mime_type: file.type,
        uploaded_by: decoded.id,
      },
      include: {
        uploader: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    // Update contract to reflect changes
    await prisma.contract.update({
      where: { id: contractId },
      data: {
        updated_by: decoded.id,
      },
    });

    return ApiResponseBuilder.success(document, undefined, 201);
  } catch (error) {
    console.error("Error uploading contract document:", error);
    return ApiResponseBuilder.error("Failed to upload contract document", "INTERNAL_ERROR", 500);
  }
}