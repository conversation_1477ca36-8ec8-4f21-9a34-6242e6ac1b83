import { getTranslations } from "next-intl/server";
import { ContractDataTable } from "./_components/contract-data-table";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus } from "lucide-react";
import Link from "next/link";

export default async function ContractsPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations("contracts");

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">{t("title")}</h1>
          <p className="text-sm text-muted-foreground sm:text-base">{t("description")}</p>
        </div>
        <Button asChild className="w-full sm:w-auto">
          <Link href={`/${locale}/dashboard/contracts/new`}>
            <Plus className="mr-2 h-4 w-4" />
            <span className="sm:inline">{t("addContract")}</span>
          </Link>
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>{t("allContracts")}</CardTitle>
          <CardDescription>
            {t("viewManageContracts")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ContractDataTable />
        </CardContent>
      </Card>
    </div>
  );
}