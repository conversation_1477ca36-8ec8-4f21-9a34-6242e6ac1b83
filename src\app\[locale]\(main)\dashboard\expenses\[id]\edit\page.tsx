import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { ExpenseWithDetails } from "@/types/expense";
import { ExpenseForm } from "../../_components/expense-form";
import { db } from "@/lib/db";

interface EditExpensePageProps {
  params: Promise<{
    locale: string;
    id: string;
  }>;
}

async function getExpense(id: string): Promise<ExpenseWithDetails | null> {
  try {
    const expenseId = parseInt(id, 10);
    if (isNaN(expenseId)) {
      return null;
    }

    const expense = await db.expense.findUnique({
      where: { id: expenseId },
      include: {
        category: true,
        attachments: true,
        approvals: {
          orderBy: { created_at: "desc" }
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        approver: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });

    // Serialize the expense data to ensure it can be passed to client components
    return expense ? JSON.parse(JSON.stringify(expense)) : null;
  } catch (error) {
    console.error("Error fetching expense:", error);
    return null;
  }
}

export default async function EditExpensePage({ params }: EditExpensePageProps) {
  const { locale, id } = await params;
  const t = await getTranslations("expenses");
  const tCommon = await getTranslations("common");

  const expense = await getExpense(id);

  if (!expense) {
    notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href={`/${locale}/dashboard/expenses/${expense.id}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            {tCommon("back")}
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("editExpense")}</h1>
          <p className="text-muted-foreground">
            {t("ui.updateExpenseDetails")} #{expense.id}
          </p>
        </div>
      </div>

      {/* Form Card */}
      <Card>
        <CardHeader>
          <CardTitle>{t("expenseDetails")}</CardTitle>
          <CardDescription>
            {t("ui.updateInstructions")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ExpenseForm expense={expense} mode="edit" locale={locale} />
        </CardContent>
      </Card>
    </div>
  );
}
