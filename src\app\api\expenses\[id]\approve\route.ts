import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { z } from "zod";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { ApiResponseBuilder } from "@/lib/api-response";

const prisma = new PrismaClient();

const approvalSchema = z.object({
  action: z.enum(["APPROVED", "REJECTED", "REQUESTED_CHANGES"]),
  comments: z.string().optional()
});

// POST /api/expenses/[id]/approve - Approve/Reject expense
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for expenses
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "expenses", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create expenses");
    }

    const { id } = await params;
    console.log(`Expense Approval API: POST request received for ID: ${id}`);

    // For development, skip authentication
    if (process.env.NODE_ENV === "development") {
      console.log("Expense Approval API: Development mode - skipping authentication");
    }

    const expenseId = parseInt(id, 10);
    if (isNaN(expenseId)) {
      return NextResponse.json(
        { error: "Invalid expense ID" },
        { status: 400 }
      );
    }

    const body = await request.json();
    console.log("Expense Approval API: Request body:", body);

    // Validate the request body
    const validationResult = approvalSchema.safeParse(body);
    if (!validationResult.success) {
      console.log("Expense Approval API: Validation failed:", validationResult.error);
      return NextResponse.json(
        { 
          error: "Validation failed", 
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const { action, comments } = validationResult.data;

    // Check if expense exists
    const existingExpense = await prisma.expense.findUnique({
      where: { id: expenseId }
    });

    if (!existingExpense) {
      return NextResponse.json(
        { error: "Expense not found" },
        { status: 404 }
      );
    }

    // Check if expense is already approved or rejected
    if (existingExpense.status !== "PENDING") {
      return NextResponse.json(
        { error: "Expense has already been processed" },
        { status: 409 }
      );
    }

    const currentUserId = 1; // TODO: Get from authenticated user

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create approval record
      await tx.expenseApproval.create({
        data: {
          expense_id: expenseId,
          user_id: currentUserId,
          action,
          comments: comments || null
        }
      });

      // Update expense status and approval info
      const updateData: any = {
        updated_by: currentUserId
      };

      if (action === "APPROVED") {
        updateData.status = "APPROVED";
        updateData.approved_by = currentUserId;
        updateData.approved_at = new Date();
      } else if (action === "REJECTED") {
        updateData.status = "REJECTED";
      }
      // For REQUESTED_CHANGES, status remains PENDING

      const updatedExpense = await tx.expense.update({
        where: { id: expenseId },
        data: updateData,
        include: {
          category: true,
          attachments: true,
          approvals: {
            orderBy: { created_at: "desc" }
          },
          creator: {
            select: {
              id: true,
              first_name: true,
              last_name: true
            }
          },
          updater: {
            select: {
              id: true,
              first_name: true,
              last_name: true
            }
          },
          approver: {
            select: {
              id: true,
              first_name: true,
              last_name: true
            }
          }
        }
      });

      return updatedExpense;
    });

    console.log(`Expense Approval API: Expense ${action.toLowerCase()}: ${expenseId}`);
    return NextResponse.json(result);

  } catch (error) {
    console.error("Expense Approval API: Error processing approval:", error);
    return NextResponse.json(
      { error: "Failed to process expense approval" },
      { status: 500 }
    );
  }
}

// GET /api/expenses/[id]/approve - Get approval history
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for expenses
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "expenses", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to read expenses");
    }

    const { id: idParam } = await params;
    console.log(`Expense Approval API: GET request received for ID: ${idParam}`);

    // For development, skip authentication
    if (process.env.NODE_ENV === "development") {
      console.log("Expense Approval API: Development mode - skipping authentication");
    }

    const expenseId = parseInt(idParam, 10);
    if (isNaN(expenseId)) {
      return NextResponse.json(
        { error: "Invalid expense ID" },
        { status: 400 }
      );
    }

    // Check if expense exists
    const existingExpense = await prisma.expense.findUnique({
      where: { id: expenseId }
    });

    if (!existingExpense) {
      return NextResponse.json(
        { error: "Expense not found" },
        { status: 404 }
      );
    }

    // Get approval history
    const approvals = await prisma.expenseApproval.findMany({
      where: { expense_id: expenseId },
      orderBy: { created_at: "desc" },
      include: {
        // TODO: Add user relation when available
      }
    });

    console.log(`Expense Approval API: Approval history retrieved: ${approvals.length} records`);
    return NextResponse.json(approvals);

  } catch (error) {
    console.error("Expense Approval API: Error fetching approval history:", error);
    return NextResponse.json(
      { error: "Failed to fetch approval history" },
      { status: 500 }
    );
  }
}
