"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import { useLocale } from "next-intl";
import { FileText, UserPlus, Wrench, DollarSign, Home, AlertCircle } from "lucide-react";

interface Activity {
  id: string;
  type: "contract" | "tenant" | "maintenance" | "payment" | "property" | "invoice";
  title: string;
  description: string;
  timestamp: Date;
  user?: string;
  status?: string;
}

export function RecentActivities() {
  const t = useTranslations();
  const locale = useLocale();
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data - in real app, fetch from API
    const mockActivities: Activity[] = [
      {
        id: "1",
        type: "contract",
        title: t("dashboard.newContractSigned"),
        description: "Unit A-101 - John Doe",
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        user: "Ahmed Ali",
        status: "success",
      },
      {
        id: "2",
        type: "maintenance",
        title: t("dashboard.maintenanceRequested"),
        description: "AC repair - Unit B-205",
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        user: "System",
        status: "warning",
      },
      {
        id: "3",
        type: "payment",
        title: t("dashboard.paymentReceived"),
        description: "OMR 1,500 - Sarah Johnson",
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
        user: "System",
        status: "success",
      },
      {
        id: "4",
        type: "tenant",
        title: t("dashboard.newTenantAdded"),
        description: "Mohammed Al-Said - Unit C-302",
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
        user: "Fatima Hassan",
        status: "info",
      },
      {
        id: "5",
        type: "invoice",
        title: t("dashboard.invoiceOverdue"),
        description: "Invoice #INV-2024-045",
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48), // 2 days ago
        user: "System",
        status: "error",
      },
    ];

    setActivities(mockActivities);
    setLoading(false);
  }, [t]);

  const getIcon = (type: Activity["type"]) => {
    switch (type) {
      case "contract":
        return FileText;
      case "tenant":
        return UserPlus;
      case "maintenance":
        return Wrench;
      case "payment":
        return DollarSign;
      case "property":
        return Home;
      case "invoice":
        return AlertCircle;
      default:
        return FileText;
    }
  };

  const getStatusVariant = (status?: string) => {
    switch (status) {
      case "success":
        return "default";
      case "warning":
        return "secondary";
      case "error":
        return "destructive";
      default:
        return "outline";
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("dashboard.recentActivities")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="h-10 w-10 bg-muted animate-pulse rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
                  <div className="h-3 bg-muted animate-pulse rounded w-1/2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("dashboard.recentActivities")}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => {
            const Icon = getIcon(activity.type);
            return (
              <div key={activity.id} className="flex items-start space-x-4">
                <Avatar className="h-9 w-9">
                  <AvatarFallback>
                    <Icon className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {activity.title}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {activity.description}
                  </p>
                  <div className="flex items-center pt-2 gap-2">
                    <Badge variant={getStatusVariant(activity.status)} className="text-xs">
                      {activity.type}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {formatDistanceToNow(activity.timestamp, {
                        addSuffix: true,
                        locale: locale === "ar" ? ar : enUS,
                      })}
                    </span>
                    {activity.user && (
                      <span className="text-xs text-muted-foreground">
                        • {activity.user}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}