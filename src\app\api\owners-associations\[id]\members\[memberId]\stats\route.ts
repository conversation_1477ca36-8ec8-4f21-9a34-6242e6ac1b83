import { NextRequest, NextResponse } from "next/server";
import { verifyToken } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";
import { db } from "@/lib/db";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; memberId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: "Invalid or expired token" },
        { status: 401 }
      );
    }

    // Check if user has READ permission for owners-associations
    const canRead = await hasPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return NextResponse.json(
        { success: false, error: "You don't have permission to view member stats" },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const associationId = parseInt(resolvedParams.id);
    const memberId = parseInt(resolvedParams.memberId);

    if (isNaN(associationId) || isNaN(memberId)) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters" },
        { status: 400 }
      );
    }

    // Get member's subscription payments
    const payments = await db.subscriptionPayment.findMany({
      where: {
        member_id: memberId,
        subscription: {
          association_id: associationId
        }
      },
      include: {
        subscription: true
      }
    });

    // Calculate statistics
    const totalDue = payments.reduce((sum, payment) => {
      const amountDue = payment.amount_due || payment.amount;
      return sum + parseFloat(amountDue.toString());
    }, 0);

    const totalPaid = payments.reduce((sum, payment) => {
      const amountPaid = payment.amount_paid || 0;
      return sum + parseFloat(amountPaid.toString());
    }, 0);

    const paymentsCount = payments.length;
    const paidPayments = payments.filter(p => p.status === 'PAID').length;
    const complianceRate = paymentsCount > 0 ? (paidPayments / paymentsCount) * 100 : 0;

    // Get member's transactions count
    const transactionsCount = await db.associationTransaction.count({
      where: {
        association_id: associationId,
        member_id: memberId
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        totalDue,
        totalPaid,
        totalOutstanding: totalDue - totalPaid,
        paymentsCount,
        paidPayments,
        complianceRate,
        transactionsCount
      }
    });
  } catch (error) {
    console.error("Error fetching member stats:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch member statistics" },
      { status: 500 }
    );
  }
}