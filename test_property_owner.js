// Simple test script to check property owner assignment
const testPropertyCreation = async () => {
  try {
    console.log("Testing property creation with owner assignment...");
    
    // Test data
    const propertyData = {
      name_en: "Test Property",
      name_ar: "عقار تجريبي",
      address_en: "Test Address",
      address_ar: "عنوان تجريبي",
      property_type_id: 1, // Assuming property type 1 exists
      base_rent: "500.000",
      status: "AVAILABLE",
      owner_id: 1 // Assuming owner 1 exists
    };
    
    const response = await fetch('http://localhost:3002/api/properties', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'auth-token=your_auth_token_here' // You'll need to get this from browser
      },
      body: JSON.stringify(propertyData)
    });
    
    const result = await response.json();
    console.log("API Response:", result);
    
    if (result.success) {
      console.log("✅ Property created successfully with ID:", result.data.id);
      console.log("Owner assigned:", result.data.owners);
    } else {
      console.log("❌ Property creation failed:", result.error);
    }
    
  } catch (error) {
    console.error("Test failed:", error);
  }
};

// Note: This is just a template. Run manually from browser console with proper auth token
console.log("Copy this function to browser console and run testPropertyCreation()");