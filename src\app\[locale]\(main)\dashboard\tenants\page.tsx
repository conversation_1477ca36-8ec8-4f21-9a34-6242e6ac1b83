import { Suspense } from "react";

import { Plus } from "lucide-react";
import Link from "next/link";
import { getTranslations } from 'next-intl/server';

// Simplified configuration for Next.js 15 stability
export const dynamic = 'force-dynamic';

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ErrorBoundary } from "@/components/error-boundary";

import { TenantDataTable } from "./_components/tenant-data-table";

export default async function TenantsPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'tenants' });
  const tCommon = await getTranslations({ locale, namespace: 'common' });

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">{t('title')}</h1>
          <p className="text-sm text-muted-foreground sm:text-base">{t('description')}</p>
        </div>
        <Button asChild className="w-full sm:w-auto">
          <Link href={`/${locale}/dashboard/tenants/create`}>
            <Plus className="mr-2 h-4 w-4" />
            <span className="sm:inline">{t('addTenant')}</span>
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('allTenants')}</CardTitle>
          <CardDescription>
            {t('viewManage')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ErrorBoundary>
            <Suspense fallback={<div>{tCommon('loading')}</div>}>
              <TenantDataTable />
            </Suspense>
          </ErrorBoundary>
        </CardContent>
      </Card>
    </div>
  );
}
