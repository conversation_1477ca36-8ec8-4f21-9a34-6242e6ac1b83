# Database Configuration
DATABASE_URL="mysql://root:@localhost:3306/property_management"

# Authentication
JWT_SECRET="your-super-secret-jwt-key-minimum-32-characters-long"
JWT_EXPIRES_IN="7d"

# Node Environment
NODE_ENV="development"

# Application Configuration
NEXT_PUBLIC_APP_NAME="Property Management System"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# File Upload Configuration
MAX_FILE_SIZE="5242880"  # 5MB in bytes
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,application/pdf"

# Email Configuration (Optional - for email notifications)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"
# SMTP_FROM="<EMAIL>"

# SMS Configuration (Optional - for SMS notifications)
# SMS_API_KEY="your-sms-api-key"
# SMS_API_URL="https://api.sms-provider.com/send"
# SMS_SENDER_ID="PROPERTY"

# Redis Configuration (Optional - for caching)
# REDIS_URL="redis://localhost:6379"

# Rate Limiting Configuration
RATE_LIMIT_WINDOW="900000"    # 15 minutes in milliseconds
RATE_LIMIT_MAX_REQUESTS="100"  # Maximum requests per window