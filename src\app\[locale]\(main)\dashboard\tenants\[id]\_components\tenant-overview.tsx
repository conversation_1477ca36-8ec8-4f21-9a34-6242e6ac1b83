"use client";

import { useState, useEffect } from "react";
import { Mail, Phone, Calendar, MapPin, User, FileText, CreditCard, TrendingUp, TrendingDown, DollarSign, AlertCircle, CheckCircle, Clock, XCircle } from "lucide-react";
import { format } from "date-fns";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";
import { getInitials } from "@/lib/utils";
import { formatCurrency } from "@/lib/localization";

interface TenantOverviewProps {
  tenant: any;
}

export function TenantOverview({ tenant }: TenantOverviewProps) {
  const t = useTranslations("tenants");
  const tCommon = useTranslations("common");
  
  const [financialSummary, setFinancialSummary] = useState<any>(null);
  const [loadingFinancials, setLoadingFinancials] = useState(true);
  
  const fullName = `${tenant.first_name} ${tenant.last_name}`;
  const statusVariant = 
    tenant.status === "active" ? "default" :
    tenant.status === "inactive" ? "secondary" :
    "outline";

  useEffect(() => {
    fetchFinancialSummary();
  }, [tenant.id]);

  const fetchFinancialSummary = async () => {
    try {
      setLoadingFinancials(true);
      const response = await fetch(`/api/tenants/${tenant.id}/financial-summary`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch financial summary");
      }

      const result = await response.json();
      if (result.success) {
        setFinancialSummary(result.data);
      }
    } catch (error) {
      console.error("Error fetching financial summary:", error);
      toast.error(t("financials.fetchError"));
    } finally {
      setLoadingFinancials(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Financial Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {loadingFinancials ? (
          <>
            <Skeleton className="h-32" />
            <Skeleton className="h-32" />
            <Skeleton className="h-32" />
            <Skeleton className="h-32" />
          </>
        ) : financialSummary ? (
          <>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("financials.totalInvoiced")}
                </CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(financialSummary.amounts.totalInvoiced)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {financialSummary.invoices.total} {t("financials.invoices")}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("financials.totalPaid")}
                </CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(financialSummary.amounts.totalPaid)}
                </div>
                <Progress 
                  value={financialSummary.statistics.paymentPercentage} 
                  className="mt-2 h-2"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {t("financials.percentageOfTotal", { percentage: financialSummary.statistics.paymentPercentage })}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("financials.pending")}
                </CardTitle>
                <Clock className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {formatCurrency(financialSummary.amounts.totalPending)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {financialSummary.invoices.pending} {t("financials.pendingInvoices")}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("financials.overdue")}
                </CardTitle>
                <AlertCircle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {formatCurrency(financialSummary.amounts.totalOverdue)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {financialSummary.invoices.overdue} {t("financials.overdueInvoices")}
                </p>
              </CardContent>
            </Card>
          </>
        ) : null}
      </div>

      <div className="grid gap-4 md:gap-6 lg:grid-cols-3">
        {/* Personal Information */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>{t("personalInfo")}</CardTitle>
            <CardDescription>{t("personalInfoDescription")}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-start space-x-4">
              <Avatar className="h-20 w-20">
                <AvatarFallback className="text-xl">
                  {getInitials(fullName)}
                </AvatarFallback>
              </Avatar>
              <div className="space-y-1">
                <h3 className="text-2xl font-semibold">{fullName}</h3>
                {tenant.status && (
                  <Badge variant={statusVariant}>
                    {t(`status.${tenant.status}`)}
                  </Badge>
                )}
              </div>
            </div>

            <Separator />

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t("form.email")}</p>
                    <p className="font-medium">{tenant.email}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t("form.phone")}</p>
                    <p className="font-medium">{tenant.phone || tCommon("notAvailable")}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t("form.nationality")}</p>
                    <p className="font-medium">{tenant.nationality || tCommon("notAvailable")}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t("form.nationalId")}</p>
                    <p className="font-medium">{tenant.national_id || tCommon("notAvailable")}</p>
                    {tenant.national_id_expiry && (
                      <p className="text-xs text-muted-foreground">
                        {t("form.expiresOn")}: {format(new Date(tenant.national_id_expiry), "dd/MM/yyyy")}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t("form.passportNumber")}</p>
                    <p className="font-medium">{tenant.passport_number || tCommon("notAvailable")}</p>
                    {tenant.passport_expiry && (
                      <p className="text-xs text-muted-foreground">
                        {t("form.expiresOn")}: {format(new Date(tenant.passport_expiry), "dd/MM/yyyy")}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t("form.currentAddress")}</p>
                    <p className="font-medium">{tenant.current_address || tCommon("notAvailable")}</p>
                  </div>
                </div>
              </div>
            </div>

            {tenant.notes && (
              <>
                <Separator />
                <div>
                  <p className="text-sm text-muted-foreground mb-2">{t("form.notes")}</p>
                  <p className="text-sm">{tenant.notes}</p>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Right Column - Stats and Financial Details */}
        <div className="space-y-4">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>{t("stats.title")}</CardTitle>
              <CardDescription>{t("stats.description")}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">{t("stats.totalContracts")}</p>
                <p className="text-2xl font-bold">{tenant.contracts?.length || 0}</p>
              </div>
              <Separator />
              <div>
                <p className="text-sm text-muted-foreground">{t("stats.activeContracts")}</p>
                <p className="text-2xl font-bold">
                  {financialSummary?.statistics.activeContracts || 0}
                </p>
              </div>
              <Separator />
              <div>
                <p className="text-sm text-muted-foreground">{t("stats.totalDocuments")}</p>
                <p className="text-2xl font-bold">{tenant._count?.documents || tenant.documents?.length || 0}</p>
              </div>
            </CardContent>
          </Card>

          {/* Financial Details */}
          {!loadingFinancials && financialSummary && (
            <Card>
              <CardHeader>
                <CardTitle>{t("financials.details")}</CardTitle>
                <CardDescription>{t("financials.breakdown")}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">{t("financials.balance")}</span>
                  <span className={`font-medium ${financialSummary.amounts.balance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {formatCurrency(Math.abs(financialSummary.amounts.balance))}
                  </span>
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">{t("financials.paidInvoices")}</span>
                    <span>{financialSummary.invoices.paid}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">{t("financials.partiallyPaid")}</span>
                    <span>{financialSummary.invoices.partiallyPaid}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">{t("financials.pending")}</span>
                    <span className="text-yellow-600">{financialSummary.invoices.pending}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">{t("financials.overdue")}</span>
                    <span className="text-red-600">{financialSummary.invoices.overdue}</span>
                  </div>
                </div>
                
                {financialSummary.statistics.lastPaymentDate && (
                  <>
                    <Separator />
                    <div>
                      <p className="text-sm text-muted-foreground">{t("financials.lastPayment")}</p>
                      <p className="text-sm font-medium">
                        {format(new Date(financialSummary.statistics.lastPaymentDate), "dd/MM/yyyy")}
                      </p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          )}

          {/* System Info */}
          <Card>
            <CardHeader>
              <CardTitle>{t("systemInfo")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm text-muted-foreground">{tCommon("createdAt")}</p>
                <p className="text-sm font-medium">
                  {format(new Date(tenant.created_at), "dd/MM/yyyy HH:mm")}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t("form.updatedAt")}</p>
                <p className="text-sm font-medium">
                  {format(new Date(tenant.updated_at), "dd/MM/yyyy HH:mm")}
                </p>
              </div>
              {tenant.created_by && (
                <div>
                  <p className="text-sm text-muted-foreground">{t("form.createdBy")}</p>
                  <p className="text-sm font-medium">{tenant.created_by}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}