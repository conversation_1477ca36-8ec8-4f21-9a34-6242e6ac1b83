---
allowed-tools: <PERSON><PERSON>(*), Read(*), Write(*), <PERSON>rep(*), <PERSON>lob(*), Task(*)
argument-hint: [commit-hash or file-path]
description: Automatically update README, documentation, and CLAUDE.md based on code changes
model: claude-3-5-sonnet-20241022
---

## Context

### Recent Changes
!`git status --porcelain`

### Git Diff (if applicable)
!`git diff HEAD~1..HEAD 2>/dev/null || echo "No git repository found"`

### Current Documentation Files
- README.md: @README.md
- CLAUDE.md: @CLAUDE.md
- Documentation files: !`find . -name "*.md" -type f | grep -E "(doc|README)" | head -20`

## Your Task

Analyze the recent code changes and update all relevant documentation files including:

1. **README.md**: Update with any new features, dependencies, or setup instructions
2. **CLAUDE.md**: Update implementation guidelines and technical requirements based on code changes
3. **Other documentation**: Update any feature-specific documentation files

### Steps to follow:

1. Analyze recent code changes to understand what has been modified
2. Identify which documentation files need updates based on the changes
3. Update each documentation file with:
   - New features or functionality added
   - Changed APIs or interfaces
   - Updated dependencies or requirements
   - Modified configuration options
   - New or changed file structures
   - Updated examples or usage instructions

### Guidelines:

- Preserve existing documentation structure and style
- Add new sections where appropriate
- Update version numbers or dates if present
- Ensure technical accuracy
- Keep explanations clear and concise
- Update code examples to match current implementation
- Maintain consistency across all documentation

### Arguments:
$ARGUMENTS

If arguments are provided, focus the documentation updates on those specific changes or files.