import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { invoiceSchema, invoiceFilterSchema, generateInvoiceNumber, calculateInvoiceTotals, calculateLateFee, getDaysOverdue } from "@/types/invoice";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for invoices
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "invoices", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view invoices");
    }

    // For now, allow public access to view invoices
    // TODO: Add authentication when needed

    const searchParams = request.nextUrl.searchParams;
    
    // Parse filters
    const filters = invoiceFilterSchema.parse({
      contract_id: searchParams.get("contract_id") ? parseInt(searchParams.get("contract_id")!) : undefined,
      tenant_id: searchParams.get("tenant_id") ? parseInt(searchParams.get("tenant_id")!) : searchParams.get("tenantId") ? parseInt(searchParams.get("tenantId")!) : undefined,
      property_id: searchParams.get("property_id") ? parseInt(searchParams.get("property_id")!) : undefined,
      unit_id: searchParams.get("unit_id") ? parseInt(searchParams.get("unit_id")!) : undefined,
      status: searchParams.get("status") || undefined,
      date_from: searchParams.get("date_from") || undefined,
      date_to: searchParams.get("date_to") || undefined,
      due_date_from: searchParams.get("due_date_from") || undefined,
      due_date_to: searchParams.get("due_date_to") || undefined,
      search: searchParams.get("search") || undefined,
    });

    // Build where clause
    const where: any = {};

    if (filters.contract_id) {
      where.contract_id = filters.contract_id;
    }

    if (filters.tenant_id) {
      where.tenant_id = filters.tenant_id;
    }

    if (filters.property_id) {
      where.property_id = filters.property_id;
    }

    if (filters.unit_id) {
      where.unit_id = filters.unit_id;
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.date_from || filters.date_to) {
      where.invoice_date = {};
      if (filters.date_from) {
        where.invoice_date.gte = new Date(filters.date_from);
      }
      if (filters.date_to) {
        where.invoice_date.lte = new Date(filters.date_to);
      }
    }

    if (filters.due_date_from || filters.due_date_to) {
      where.due_date = {};
      if (filters.due_date_from) {
        where.due_date.gte = new Date(filters.due_date_from);
      }
      if (filters.due_date_to) {
        where.due_date.lte = new Date(filters.due_date_to);
      }
    }

    if (filters.search) {
      where.OR = [
        { invoice_number: { contains: filters.search } },
        { notes: { contains: filters.search } },
      ];
    }

    // Pagination
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const skip = (page - 1) * pageSize;

    // Sorting
    const sortBy = searchParams.get("sortBy") || "created_at";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    // Execute query with pagination
    const [invoices, total] = await Promise.all([
      db.invoice.findMany({
        where,
        include: {
          contract: true,
          tenant: true,
          property: true,
          unit: true,
          items: true,
          payments: true,
          allocations: true,
          creator: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
          updater: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
        },
        skip,
        take: pageSize,
        orderBy: { [sortBy]: sortOrder },
      }),
      db.invoice.count({ where }),
    ]);

    // Update overdue status for pending invoices
    const now = new Date();
    for (const invoice of invoices) {
      if (invoice.status === 'PENDING' && new Date(invoice.due_date) < now) {
        // Calculate and apply late fee
        const daysOverdue = getDaysOverdue(invoice.due_date);
        const lateFee = calculateLateFee(invoice.original_amount.toString(), daysOverdue);
        
        if (parseFloat(invoice.late_fee.toString()) !== lateFee) {
          await db.invoice.update({
            where: { id: invoice.id },
            data: {
              status: 'OVERDUE',
              late_fee: lateFee,
              total_amount: parseFloat(invoice.original_amount.toString()) + lateFee,
              balance_amount: parseFloat(invoice.original_amount.toString()) + lateFee - parseFloat(invoice.paid_amount.toString()),
            },
          });
          
          // Update the invoice object for response
          invoice.status = 'OVERDUE';
          invoice.late_fee = lateFee as any;
          invoice.total_amount = (parseFloat(invoice.original_amount.toString()) + lateFee) as any;
          invoice.balance_amount = (parseFloat(invoice.original_amount.toString()) + lateFee - parseFloat(invoice.paid_amount.toString())) as any;
        }
      }
    }

    return ApiResponseBuilder.success(invoices, {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error) {
    console.error("Error fetching invoices:", error);
    return ApiResponseBuilder.error("Failed to fetch invoices", "INTERNAL_ERROR", 500);
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has CREATE permission for invoices
    const canCreate = hasPermission(userPermissions, "invoices", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create invoices");
    }

    // For now, allow public access to create invoices
    // TODO: Add authentication when needed

    const body = await request.json();
    const validatedData = invoiceSchema.parse(body);

    // Check if tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: validatedData.tenant_id },
    });

    if (!tenant) {
      return ApiResponseBuilder.error("Tenant not found", "NOT_FOUND", 404);
    }

    // Check if property exists
    const property = await db.property.findUnique({
      where: { id: validatedData.property_id },
    });

    if (!property) {
      return ApiResponseBuilder.error("Property not found", "NOT_FOUND", 404);
    }

    // Check if unit exists and belongs to the property
    const unit = await db.unit.findFirst({
      where: {
        id: validatedData.unit_id,
        property_id: validatedData.property_id,
      },
    });

    if (!unit) {
      return ApiResponseBuilder.error("Unit not found or doesn't belong to the selected property", "NOT_FOUND", 404);
    }

    // If contract_id is provided, verify it exists
    if (validatedData.contract_id) {
      const contract = await db.contract.findUnique({
        where: { id: validatedData.contract_id },
      });

      if (!contract) {
        return ApiResponseBuilder.error("Contract not found", "NOT_FOUND", 404);
      }
    }

    // Generate invoice number if not provided
    let invoiceNumber = validatedData.invoice_number;
    
    if (!invoiceNumber) {
      const invoiceDate = new Date(validatedData.invoice_date);
      const year = invoiceDate.getFullYear();
      const month = invoiceDate.getMonth() + 1;
      
      // Get the latest invoice for the same year and month
      const latestInvoice = await db.invoice.findFirst({
        where: {
          invoice_number: {
            startsWith: `INV-${year}-${month.toString().padStart(2, '0')}`,
          },
        },
        orderBy: {
          invoice_number: 'desc',
        },
      });
      
      let sequence = 1;
      if (latestInvoice) {
        const parts = latestInvoice.invoice_number.split('-');
        sequence = parseInt(parts[3]) + 1;
      }
      
      invoiceNumber = generateInvoiceNumber(year, month, sequence);
    }

    // Calculate totals from items
    const { total } = calculateInvoiceTotals(validatedData.items);

    // Create invoice with items
    const invoice = await db.invoice.create({
      data: {
        invoice_number: invoiceNumber,
        contract_id: validatedData.contract_id,
        tenant_id: validatedData.tenant_id,
        property_id: validatedData.property_id,
        unit_id: validatedData.unit_id,
        invoice_date: new Date(validatedData.invoice_date),
        due_date: new Date(validatedData.due_date),
        original_amount: total,
        late_fee: 0,
        total_amount: total,
        paid_amount: 0,
        balance_amount: total,
        status: validatedData.status,
        notes: validatedData.notes,
        created_by: decoded.id,
        updated_by: decoded.id,
        items: {
          create: validatedData.items.map(item => ({
            description_en: item.description_en,
            description_ar: item.description_ar,
            quantity: item.quantity,
            unit_price: parseFloat(item.unit_price),
            amount: parseFloat(item.unit_price) * item.quantity,
          })),
        },
      },
      include: {
        contract: true,
        tenant: true,
        property: true,
        unit: true,
        items: true,
        payments: true,
        allocations: true,
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    return ApiResponseBuilder.success(invoice, undefined, 201);
  } catch (error) {
    console.error("Error creating invoice:", error);
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return ApiResponseBuilder.error("Invoice number already exists", "BAD_REQUEST", 400);
    }
    return ApiResponseBuilder.error("Failed to create invoice", "INTERNAL_ERROR", 500);
  }
}