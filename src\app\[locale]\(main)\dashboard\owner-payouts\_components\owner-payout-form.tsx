"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations, useLocale } from "next-intl";
import { Loader2, Plus, Trash2 } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatOMR } from "@/lib/format";
import {
  OwnerPayoutInput,
  ownerPayoutSchema,
  OwnerPayoutWithRelations,
  calculatePayoutTotals,
} from "@/types/owner-payout";
import { calculateOwnerRevenue } from "@/types/property-owner";

interface OwnerPayoutFormProps {
  initialData?: OwnerPayoutWithRelations;
  mode: "create" | "edit";
}

interface PropertyOwner {
  id: number;
  name_en: string;
  name_ar: string;
  management_fee_percentage: number | null;
  primary_properties?: Array<{
    id: number;
    name_en: string;
    name_ar: string;
  }>;
}

export function OwnerPayoutForm({ initialData, mode }: OwnerPayoutFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [owners, setOwners] = useState<PropertyOwner[]>([]);
  const [selectedOwner, setSelectedOwner] = useState<PropertyOwner | null>(null);
  const [propertyDetails, setPropertyDetails] = useState<Array<{
    property_id: number;
    rent_collected: number;
    management_fee: number;
    net_amount: number;
  }>>([]);
  
  const router = useRouter();
  const t = useTranslations();
  const locale = useLocale();

  const form = useForm<OwnerPayoutInput>({
    resolver: zodResolver(ownerPayoutSchema),
    defaultValues: {
      owner_id: initialData?.owner_id || 0,
      payout_date: initialData?.payout_date ? new Date(initialData.payout_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      period_start: initialData?.period_start ? new Date(initialData.period_start).toISOString().split('T')[0] : "",
      period_end: initialData?.period_end ? new Date(initialData.period_end).toISOString().split('T')[0] : "",
      total_rent_collected: initialData?.total_rent_collected ? Number(initialData.total_rent_collected) : 0,
      management_fee: initialData?.management_fee ? Number(initialData.management_fee) : 0,
      other_deductions: initialData?.other_deductions ? Number(initialData.other_deductions) : 0,
      net_amount: initialData?.net_amount ? Number(initialData.net_amount) : 0,
      payment_method: initialData?.payment_method === "CREDIT_CARD" ? "CARD" : (initialData?.payment_method as any) || "BANK_TRANSFER",
      reference_number: initialData?.reference_number || "",
      bank_transfer_ref: initialData?.bank_transfer_ref || "",
      notes: initialData?.notes || "",
      status: initialData?.status || "PENDING",
    },
  });

  // Fetch owners on mount
  useEffect(() => {
    fetchOwners();
  }, []);

  const fetchOwners = async () => {
    try {
      const response = await fetch("/api/property-owners?status=ACTIVE");
      const result = await response.json();
      
      if (result.success) {
        setOwners(result.data);
      }
    } catch (error) {
      console.error("Error fetching owners:", error);
    }
  };

  const handleOwnerChange = (ownerId: string) => {
    const owner = owners.find(o => o.id === parseInt(ownerId));
    setSelectedOwner(owner || null);
    
    if (owner && owner.primary_properties && owner.primary_properties.length > 0) {
      // Initialize property details for this owner
      const details = owner.primary_properties.map(prop => ({
        property_id: prop.id,
        rent_collected: 0,
        management_fee: 0,
        net_amount: 0,
      }));
      setPropertyDetails(details);
    } else {
      // Clear property details if no properties
      setPropertyDetails([]);
    }
  };

  const updatePropertyDetail = (index: number, field: string, value: number) => {
    const newDetails = [...propertyDetails];
    newDetails[index] = { ...newDetails[index], [field]: value };
    
    // Calculate management fee and net amount
    if (field === "rent_collected" && selectedOwner?.management_fee_percentage) {
      const { managementFee, netAmount } = calculateOwnerRevenue(
        value,
        selectedOwner.management_fee_percentage
      );
      newDetails[index].management_fee = managementFee;
      newDetails[index].net_amount = netAmount;
    }
    
    setPropertyDetails(newDetails);
    
    // Update totals
    const totals = calculatePayoutTotals(newDetails);
    form.setValue("total_rent_collected", totals.total_rent_collected);
    form.setValue("management_fee", totals.total_management_fee);
    form.setValue("net_amount", totals.total_net_amount - (form.getValues("other_deductions") || 0));
  };

  const onSubmit = async (data: OwnerPayoutInput) => {
    try {
      setIsLoading(true);

      const url = mode === "create" 
        ? "/api/owner-payouts"
        : `/api/owner-payouts/${initialData?.id}`;
      
      const method = mode === "create" ? "POST" : "PUT";

      const payload = {
        ...data,
        payout_details: propertyDetails.filter(d => d.rent_collected > 0),
      };

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(mode === "create" 
          ? t("ownerPayouts.createSuccess")
          : t("ownerPayouts.updateSuccess")
        );
        
        router.push(`/${locale}/dashboard/owner-payouts`);
        router.refresh();
      } else {
        toast.error(result.message || t("common.unexpectedError"));
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error(t("common.unexpectedError"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {mode === "create" 
            ? t("ownerPayouts.createTitle")
            : t("ownerPayouts.editTitle")
          }
        </h1>
        <p className="text-muted-foreground">
          {mode === "create"
            ? t("ownerPayouts.createDescription")
            : t("ownerPayouts.editDescription")
          }
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t("ownerPayouts.basicInformation")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="owner_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("ownerPayouts.owner")} *</FormLabel>
                      <Select 
                        onValueChange={(value) => {
                          field.onChange(parseInt(value));
                          handleOwnerChange(value);
                        }} 
                        value={field.value?.toString()}
                        disabled={mode === "edit"}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t("ownerPayouts.selectOwner")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {owners.map((owner) => (
                            <SelectItem key={owner.id} value={owner.id.toString()}>
                              {owner.name_en} - {owner.name_ar}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="payout_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("ownerPayouts.payoutDate")} *</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="period_start"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("ownerPayouts.periodStart")} *</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="period_end"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("ownerPayouts.periodEnd")} *</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Property Details */}
          {selectedOwner && propertyDetails.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>{t("ownerPayouts.propertyDetails")}</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("ownerPayouts.property")}</TableHead>
                      <TableHead>{t("ownerPayouts.rentCollected")}</TableHead>
                      <TableHead>{t("ownerPayouts.managementFee")}</TableHead>
                      <TableHead>{t("ownerPayouts.netAmount")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedOwner.primary_properties?.map((prop, index) => (
                      <TableRow key={prop.id}>
                        <TableCell>{prop.name_en}</TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            step="0.001"
                            min="0"
                            value={propertyDetails[index]?.rent_collected || 0}
                            onChange={(e) => updatePropertyDetail(index, "rent_collected", parseFloat(e.target.value) || 0)}
                          />
                        </TableCell>
                        <TableCell>
                          {formatOMR(propertyDetails[index]?.management_fee || 0)}
                        </TableCell>
                        <TableCell>
                          {formatOMR(propertyDetails[index]?.net_amount || 0)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {/* Financial Summary */}
          <Card>
            <CardHeader>
              <CardTitle>{t("ownerPayouts.financialSummary")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="total_rent_collected"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("ownerPayouts.totalRentCollected")}</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.001"
                          {...field}
                          disabled
                          value={field.value || 0}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="management_fee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("ownerPayouts.totalManagementFee")}</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.001"
                          {...field}
                          disabled
                          value={field.value || 0}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="other_deductions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("ownerPayouts.otherDeductions")}</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.001"
                          min="0"
                          {...field}
                          value={field.value || 0}
                          onChange={(e) => {
                            field.onChange(parseFloat(e.target.value) || 0);
                            const totals = calculatePayoutTotals(propertyDetails);
                            form.setValue("net_amount", totals.total_net_amount - (parseFloat(e.target.value) || 0));
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              <FormField
                control={form.control}
                name="net_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-lg font-semibold">
                      {t("ownerPayouts.netPayoutAmount")}
                    </FormLabel>
                    <FormControl>
                      <div className="text-2xl font-bold">
                        {formatOMR(field.value || 0)}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Payment Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t("ownerPayouts.paymentInformation")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="payment_method"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("ownerPayouts.paymentMethod")}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="CASH">{t("common.paymentMethods.cash")}</SelectItem>
                        <SelectItem value="BANK_TRANSFER">{t("common.paymentMethods.bankTransfer")}</SelectItem>
                        <SelectItem value="CHECK">{t("common.paymentMethods.check")}</SelectItem>
                        <SelectItem value="CARD">{t("common.paymentMethods.card")}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="reference_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("ownerPayouts.referenceNumber")}</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="bank_transfer_ref"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("ownerPayouts.bankTransferRef")}</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("ownerPayouts.notes")}</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        value={field.value || ""}
                        rows={4}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Submit Buttons */}
          <div className="flex items-center justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {mode === "create" ? t("common.create") : t("common.save")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}