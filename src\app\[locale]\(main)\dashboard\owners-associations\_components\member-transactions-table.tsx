"use client";

import { useState, useEffect } from "react";
import { useTranslations, useLocale } from "next-intl";
import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import { toast } from "sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { useRTL } from "@/hooks/use-rtl";
import { apiClient } from "@/lib/api-client";

interface MemberTransactionsTableProps {
  associationId: number;
  memberId: number;
}

export function MemberTransactionsTable({ associationId, memberId }: MemberTransactionsTableProps) {
  const locale = useLocale();
  const { isRTL } = useRTL();
  const t = useTranslations("ownersAssociations.memberTransactions");
  const dateLocale = locale === 'ar' ? ar : enUS;

  const [transactions, setTransactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchTransactions();
  }, [associationId, memberId]);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(
        `/api/owners-associations/${associationId}/members/${memberId}/transactions`
      );
      
      if (response.success) {
        setTransactions(response.data.transactions || []);
      }
    } catch (error) {
      console.error("Error fetching transactions:", error);
      toast.error(t("loadError"));
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        {t("noTransactions")}
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className={cn(isRTL && "text-right")}>{t("date")}</TableHead>
            <TableHead className={cn(isRTL && "text-right")}>{t("type")}</TableHead>
            <TableHead className={cn(isRTL && "text-right")}>{t("category")}</TableHead>
            <TableHead className={cn(isRTL && "text-right")}>{t("description")}</TableHead>
            <TableHead className={cn(isRTL && "text-right")}>{t("amount")}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction) => (
            <TableRow key={transaction.id}>
              <TableCell className={cn(isRTL && "text-right")}>
                {format(new Date(transaction.transaction_date), "dd/MM/yyyy", { locale: dateLocale })}
              </TableCell>
              <TableCell>
                <Badge variant={transaction.type === "INCOME" ? "default" : "destructive"}>
                  {t(`type.${transaction.type.toLowerCase()}`)}
                </Badge>
              </TableCell>
              <TableCell className={cn(isRTL && "text-right")}>
                {locale === "ar" ? transaction.category?.name_ar : transaction.category?.name_en}
              </TableCell>
              <TableCell className={cn(isRTL && "text-right")}>
                {transaction.description}
              </TableCell>
              <TableCell className={cn("font-medium", isRTL && "text-right")}>
                {transaction.type === "EXPENSE" ? "-" : "+"}{transaction.amount.toFixed(3)} OMR
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}