import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { associationSubscriptionSchema } from "@/types/owners-association";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, checkUserPermission } from "@/lib/auth";
import { Decimal } from "@prisma/client/runtime/library";

// GET /api/owners-associations/[id]/subscriptions - List all subscriptions for an association
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await checkUserPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view association subscriptions");
    }

    const { id: idParam } = await params;
    const associationId = parseInt(idParam);

    if (isNaN(associationId)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    // Check if association exists
    const association = await db.ownersAssociation.findUnique({
      where: { id: associationId },
    });

    if (!association) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    const searchParams = request.nextUrl.searchParams;
    const isActive = searchParams.get("is_active") === "true" ? true :
                     searchParams.get("is_active") === "false" ? false : undefined;

    // Build where clause
    const where: any = {
      association_id: associationId,
    };

    if (isActive !== undefined) {
      where.is_active = isActive;
    }

    // Fetch subscriptions
    const subscriptions = await db.associationSubscription.findMany({
      where,
      include: {
        _count: {
          select: {
            payments: true,
          },
        },
        payments: {
          where: {
            status: {
              in: ["PAID", "PARTIALLY_PAID"],
            },
          },
          select: {
            amount: true,
          },
        },
      },
      orderBy: {
        created_at: "desc",
      },
    });

    // Transform subscriptions to handle Decimal serialization and calculate totals
    const transformedSubscriptions = subscriptions.map(subscription => {
      const totalCollected = subscription.payments.reduce((sum, payment) => {
        return sum + parseFloat(payment.amount.toString());
      }, 0);

      return {
        ...subscription,
        amount: subscription.amount.toString(),
        total_collected: totalCollected.toFixed(3),
        payments: undefined, // Remove payments array from response
      };
    });

    return ApiResponseBuilder.success(transformedSubscriptions);
  } catch (error: any) {
    console.error("Error fetching association subscriptions:", error);
    return ApiResponseBuilder.error("Failed to fetch association subscriptions", "INTERNAL_ERROR", 500);
  }
}

// POST /api/owners-associations/[id]/subscriptions - Create a new subscription
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for owners-associations
    const canCreate = await checkUserPermission(decoded.id, "owners-associations", "CREATE");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create association subscriptions");
    }

    const { id: idParam } = await params;
    const associationId = parseInt(idParam);

    if (isNaN(associationId)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    // Check if association exists
    const association = await db.ownersAssociation.findUnique({
      where: { id: associationId },
    });

    if (!association) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    const body = await request.json();
    const validatedData = associationSubscriptionSchema.parse({
      ...body,
      association_id: associationId,
    });

    // Create the subscription
    const subscription = await db.associationSubscription.create({
      data: {
        association_id: associationId,
        name_en: validatedData.name_en,
        name_ar: validatedData.name_ar,
        amount: new Decimal(validatedData.amount),
        frequency: validatedData.frequency,
        is_active: validatedData.is_active,
      },
    });

    // Transform subscription to handle Decimal serialization
    const transformedSubscription = {
      ...subscription,
      amount: subscription.amount.toString(),
    };

    return ApiResponseBuilder.success(transformedSubscription);
  } catch (error: any) {
    console.error("Error creating association subscription:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to create association subscription", "INTERNAL_ERROR", 500);
  }
}