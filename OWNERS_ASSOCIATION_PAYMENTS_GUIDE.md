# Owners Association Payments System Guide

## Understanding the Two Types of Financial Records

### 1. Transactions (المعاملات)
- **Purpose**: General income and expense tracking
- **Examples**: 
  - Maintenance expenses
  - Service fees
  - One-time income
  - Any financial activity
- **Location**: Transactions tab
- **Can be linked to**: Members (optional)

### 2. Subscription Payments (مدفوعات الاشتراكات)
- **Purpose**: Track member subscription obligations and payments
- **Examples**:
  - Monthly HOA fees
  - Quarterly maintenance fees
  - Annual membership dues
- **Location**: Payments tab
- **Always linked to**: A member AND a subscription

## How the Payment System Works

### Step 1: Create Subscriptions
1. Go to the **Subscriptions** tab
2. Create subscriptions (e.g., "Monthly HOA Fee - $200")
3. Set the frequency (monthly, quarterly, yearly)
4. Mark as active

### Step 2: Add Members
1. Go to the **Members** tab
2. Add all association members with their unit numbers

### Step 3: Generate Payment Records
1. Go to the **Payments** tab
2. Click **"Generate Payments"** button (top right)
3. Select:
   - Which subscription (e.g., Monthly HOA Fee)
   - Billing month
   - Which members (all or specific)
4. Click Generate

This creates payment obligations that need to be tracked.

### Step 4: Record Actual Payments
When a member pays:
1. Find their payment record in the Payments tab
2. Click **"Record Payment"**
3. Enter the amount paid (can be partial)
4. The system tracks:
   - Amount due
   - Amount paid
   - Remaining balance
   - Payment status

## Key Differences

### Transactions Tab
- Free-form financial records
- Not tied to any specific obligation
- Can optionally link to a member
- Good for:
  - Recording expenses
  - One-time income
  - General bookkeeping

### Payments Tab
- Structured subscription tracking
- Always tied to a subscription and member
- Tracks payment obligations vs actual payments
- Good for:
  - Regular member fees
  - Tracking who paid what
  - Managing collections

## Example Workflow

1. **January 1st**: Create "Monthly Maintenance Fee" subscription for $100
2. **January 5th**: Generate January payments for all 10 members
   - System creates 10 payment records, each for $100
3. **January 10th**: Member Ali pays his $100
   - Record payment for Ali
   - His status changes to "Paid"
4. **January 15th**: Member Said pays $50 (partial)
   - Record partial payment
   - Status shows "Partially Paid (50%)"
   - Remaining balance: $50
5. **End of month**: 
   - Payments tab shows: 1 paid, 1 partial, 8 unpaid
   - Collection rate: 15%

## Why No "Add Payment" Button?

The Payments tab doesn't have an "Add Payment" button because:
- Payments are generated from subscriptions
- This ensures every payment is tied to a valid subscription
- Prevents arbitrary payment records
- Maintains financial integrity

To create new payment obligations:
1. Use "Generate Payments" button
2. Select subscription and members
3. System creates the payment records

## Tips

1. **Set up subscriptions first** - These define what members should pay
2. **Generate payments monthly/quarterly** - Based on your billing cycle
3. **Use transactions for non-subscription items** - Like one-time expenses
4. **Monitor the Payments tab** - To track collection rates and outstanding balances

## Troubleshooting

**Q: Why don't I see any payments?**
A: You need to generate them first using the "Generate Payments" button

**Q: Can I create a payment without a subscription?**
A: No, use the Transactions tab for one-time payments

**Q: How do I handle late fees?**
A: Create a separate "Late Fee" subscription or add as a transaction

**Q: Can I delete a payment record?**
A: Yes, but it's better to mark as cancelled/void to maintain records