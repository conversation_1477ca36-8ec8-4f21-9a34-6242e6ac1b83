import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { propertyTypeUpdateSchema } from "@/types/property";



interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/property-types/:id - Get a single property type
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for property-types
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "property-types", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view property types");
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.badRequest("Invalid property type ID");
    }

    const propertyType = await db.propertyType.findUnique({
      where: { id },
      include: {
        _count: {
          select: { properties: true },
        },
      },
    });

    if (!propertyType) {
      return ApiResponseBuilder.notFound("Property type");
    }

    return ApiResponseBuilder.success(propertyType);
  } catch (error) {
    console.error("Get Property Type Error:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch property type",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// PUT /api/property-types/:id - Update a property type
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify authentication
    const token = request.cookies.get("auth-token")?.value;
    if (!token) {
      return ApiResponseBuilder.unauthorized("No authentication token provided");
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return ApiResponseBuilder.unauthorized("Invalid authentication token");
    }

    // TODO: Check permissions for property type update
    
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.badRequest("Invalid property type ID");
    }

    const body = await request.json();
    
    // Validate the request body
    const validationResult = propertyTypeUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return ApiResponseBuilder.validationError(validationResult.error);
    }

    const validatedData = validationResult.data;

    // Check if property type exists
    const existing = await db.propertyType.findUnique({
      where: { id },
    });

    if (!existing) {
      return ApiResponseBuilder.notFound("Property type");
    }

    // Check if new name conflicts with another property type
    if (validatedData.name_en || validatedData.name_ar) {
      const nameConflict = await db.propertyType.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                validatedData.name_en ? { name_en: validatedData.name_en } : {},
                validatedData.name_ar ? { name_ar: validatedData.name_ar } : {},
              ],
            },
          ],
        },
      });

      if (nameConflict) {
        return ApiResponseBuilder.conflict(
          "A property type with this name already exists",
          { conflicting_names: { name_en: nameConflict.name_en, name_ar: nameConflict.name_ar } },
          request.url
        );
      }
    }

    // Update the property type
    const propertyType = await db.propertyType.update({
      where: { id },
      data: validatedData,
      include: {
        _count: {
          select: { properties: true },
        },
      },
    });

    return ApiResponseBuilder.success(propertyType);
  } catch (error) {
    console.error("Update Property Type Error:", error);
    return ApiResponseBuilder.error(
      "Failed to update property type",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// DELETE /api/property-types/:id - Delete a property type
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify authentication
    const token = request.cookies.get("auth-token")?.value;
    if (!token) {
      return ApiResponseBuilder.unauthorized("No authentication token provided");
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return ApiResponseBuilder.unauthorized("Invalid authentication token");
    }

    // TODO: Check permissions for property type deletion
    
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.badRequest("Invalid property type ID");
    }

    // Check if property type exists
    const existing = await db.propertyType.findUnique({
      where: { id },
      include: {
        _count: {
          select: { properties: true },
        },
      },
    });

    if (!existing) {
      return ApiResponseBuilder.notFound("Property type");
    }

    // Check if property type is in use
    if (existing._count.properties > 0) {
      return ApiResponseBuilder.conflict(
        "Cannot delete property type that is in use",
        { properties_count: existing._count.properties },
        request.url
      );
    }

    // Delete the property type
    await db.propertyType.delete({
      where: { id },
    });

    return ApiResponseBuilder.success({ message: "Property type deleted successfully" });
  } catch (error) {
    console.error("Delete Property Type Error:", error);
    return ApiResponseBuilder.error(
      "Failed to delete property type",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}