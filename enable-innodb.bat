@echo off
echo ========================================
echo Re-enabling InnoDB with Fresh Files
echo ========================================
echo.

cd "C:\xampp\mysql\bin"

echo Creating InnoDB-enabled configuration...
echo # MySQL Configuration with InnoDB > my_innodb.ini
echo [mysqld] >> my_innodb.ini
echo port=3306 >> my_innodb.ini
echo basedir=C:/xampp/mysql >> my_innodb.ini
echo datadir=C:/xampp/mysql/data >> my_innodb.ini
echo skip-grant-tables >> my_innodb.ini
echo skip-networking >> my_innodb.ini
echo skip-slave-start >> my_innodb.ini
echo default-storage-engine=InnoDB >> my_innodb.ini
echo key_buffer_size=16M >> my_innodb.ini
echo max_allowed_packet=1M >> my_innodb.ini
echo table_open_cache=64 >> my_innodb.ini
echo sort_buffer_size=512K >> my_innodb.ini
echo net_buffer_length=8K >> my_innodb.ini
echo read_buffer_size=256K >> my_innodb.ini
echo read_rnd_buffer_size=512K >> my_innodb.ini
echo myisam_sort_buffer_size=8M >> my_innodb.ini
echo log-error=mysql_error.log >> my_innodb.ini
echo. >> my_innodb.ini
echo # InnoDB Configuration >> my_innodb.ini
echo innodb_data_home_dir=C:/xampp/mysql/data >> my_innodb.ini
echo innodb_log_group_home_dir=C:/xampp/mysql/data >> my_innodb.ini
echo innodb_data_file_path=ibdata1:10M:autoextend >> my_innodb.ini
echo innodb_log_file_size=5M >> my_innodb.ini
echo innodb_log_buffer_size=8M >> my_innodb.ini
echo innodb_flush_log_at_trx_commit=1 >> my_innodb.ini
echo innodb_lock_wait_timeout=50 >> my_innodb.ini

copy my_innodb.ini my.ini
echo ✅ InnoDB configuration applied

echo.
echo Now start MySQL again with:
echo mysqld --console --skip-grant-tables --skip-networking
echo.
echo InnoDB files will be created fresh automatically
echo ========================================
pause
