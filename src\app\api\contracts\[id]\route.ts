import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { contractSchema } from "@/types/contract";
import { ApiResponseBuilder } from "@/lib/api-response";

import { serializeDecimal } from "@/lib/decimal";



import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for contracts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "contracts", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view contracts");
    }

    const { id } = await params;


    const contractId = parseInt(id);
    if (isNaN(contractId)) {
      return ApiResponseBuilder.error("Invalid contract ID", "INVALID_ID", 400);
    }

    const contract = await db.contract.findUnique({
      where: { id: contractId },
      include: {
        property: true,
        unit: true,
        tenants: {
          include: {
            tenant: true,
          },
        },
        documents: {
          include: {
            uploader: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
              },
            },
          },
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    if (!contract) {
      return ApiResponseBuilder.error("Contract not found", "NOT_FOUND", 404);
    }

    // Transform contract to ensure all Decimal values are serialized
    const transformedContract = {
      ...serializeDecimal(contract),
      property: contract.property ? serializeDecimal(contract.property) : null,
      unit: contract.unit ? serializeDecimal(contract.unit) : null,
    };

    return ApiResponseBuilder.success(transformedContract);
  } catch (error) {
    console.error("Error fetching contract:", error);
    return ApiResponseBuilder.error("Failed to fetch contract", "INTERNAL_ERROR", 500);
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for contracts
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "contracts", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update contracts");
    }

    const { id } = await params;


    const contractId = parseInt(id);
    if (isNaN(contractId)) {
      return ApiResponseBuilder.error("Invalid contract ID", "INVALID_ID", 400);
    }

    const body = await request.json();
    const validatedData = contractSchema.parse(body);

    // Check if contract exists
    const existingContract = await db.contract.findUnique({
      where: { id: contractId },
      include: { unit: true },
    });

    if (!existingContract) {
      return ApiResponseBuilder.error("Contract not found", "NOT_FOUND", 404);
    }

    // Check if property exists (if provided)
    if (validatedData.property_id) {
      const property = await db.property.findUnique({
        where: { id: validatedData.property_id },
      });

      if (!property) {
        return ApiResponseBuilder.error("Property not found", "NOT_FOUND", 404);
      }
    }

    // Check if unit exists and belongs to the property (if provided)
    if (validatedData.unit_id) {
      const unit = await db.unit.findFirst({
        where: {
          id: validatedData.unit_id,
          ...(validatedData.property_id && { property_id: validatedData.property_id }),
        },
      });

      if (!unit) {
        return ApiResponseBuilder.error("Unit not found or doesn't belong to the selected property", "NOT_FOUND", 404);
      }
    }

    // Check for overlapping active contracts for the same unit (excluding current contract)
    if (validatedData.unit_id) {
      const overlappingContract = await db.contract.findFirst({
        where: {
          id: { not: contractId },
          unit_id: validatedData.unit_id,
          status: "ACTIVE",
          OR: [
            {
              AND: [
                { start_date: { lte: new Date(validatedData.end_date) } },
                { end_date: { gte: new Date(validatedData.start_date) } },
              ],
            },
          ],
        },
      });

      if (overlappingContract) {
        return ApiResponseBuilder.error("There is already an active contract for this unit in the specified period", "BAD_REQUEST", 400);
      }
    }

    // Update contract
    const updatedContract = await db.contract.update({
      where: { id: contractId },
      data: {
        contract_number: validatedData.contract_number,
        property_id: validatedData.property_id,
        unit_id: validatedData.unit_id,
        start_date: new Date(validatedData.start_date),
        end_date: new Date(validatedData.end_date),
        monthly_rent: validatedData.monthly_rent,
        payment_due_day: validatedData.payment_due_day,
        security_deposit: validatedData.security_deposit,
        insurance_amount: validatedData.insurance_amount || null,
        insurance_due_date: validatedData.insurance_due_date ? new Date(validatedData.insurance_due_date) : null,
        status: validatedData.status,
        notes: validatedData.notes || null,
        updated_by: decoded.id,
      },
      include: {
        property: true,
        unit: true,
        tenants: {
          include: {
            tenant: true,
          },
        },
        documents: true,
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    // Update unit status based on contract status
    if (validatedData.status === "ACTIVE" && validatedData.unit_id) {
      await db.unit.update({
        where: { id: validatedData.unit_id },
        data: { status: "RENTED" },
      });
    } else if (existingContract.status === "ACTIVE" && validatedData.status !== "ACTIVE" && validatedData.unit_id) {
      // Check if there are other active contracts for this unit
      const otherActiveContracts = await db.contract.count({
        where: {
          id: { not: contractId },
          unit_id: validatedData.unit_id,
          status: "ACTIVE",
        },
      });

      if (otherActiveContracts === 0) {
        await db.unit.update({
          where: { id: validatedData.unit_id },
          data: { status: "AVAILABLE" },
        });
      }
    }

    return ApiResponseBuilder.success(updatedContract);
  } catch (error) {
    console.error("Error updating contract:", error);
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return ApiResponseBuilder.error("Contract number already exists", "BAD_REQUEST", 400);
    }
    return ApiResponseBuilder.error("Failed to update contract", "INTERNAL_ERROR", 500);
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for contracts
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "contracts", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete contracts");
    }

    const { id } = await params;


    const contractId = parseInt(id);
    if (isNaN(contractId)) {
      return ApiResponseBuilder.error("Invalid contract ID", "INVALID_ID", 400);
    }

    // Check if contract exists
    const contract = await db.contract.findUnique({
      where: { id: contractId },
    });

    if (!contract) {
      return ApiResponseBuilder.error("Contract not found", "NOT_FOUND", 404);
    }

    // Don't allow deletion of active contracts
    if (contract.status === "ACTIVE") {
      return ApiResponseBuilder.error("Cannot delete an active contract. Please terminate it first.", "BAD_REQUEST", 400);
    }

    // Delete contract (cascade will handle related records)
    await db.contract.delete({
      where: { id: contractId },
    });

    // Check if unit should be marked as available
    if (contract.unit_id) {
      const activeContractsForUnit = await db.contract.count({
        where: {
          unit_id: contract.unit_id,
          status: "ACTIVE",
        },
      });

      if (activeContractsForUnit === 0) {
        await db.unit.update({
          where: { id: contract.unit_id },
          data: { status: "AVAILABLE" },
        });
      }
    }

    return ApiResponseBuilder.success({ message: "Contract deleted successfully" });
  } catch (error) {
    console.error("Error deleting contract:", error);
    return ApiResponseBuilder.error("Failed to delete contract", "INTERNAL_ERROR", 500);
  }
}