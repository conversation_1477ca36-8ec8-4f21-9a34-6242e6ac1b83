import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { propertyOwnerSchema } from "@/types/property-owner";
import { ApiResponseBuilder } from "@/lib/api-response";




import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
// GET /api/property-owners/[id] - Get a single property owner
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for property-owners
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "property-owners", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view property owners");
    }

    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.badRequest("Invalid property owner ID");
    }

    const owner = await db.propertyOwner.findUnique({
      where: { id },
      include: {
        primary_properties: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            address_en: true,
            address_ar: true,
            base_rent: true,
            status: true,
          },
        },
        payouts: {
          orderBy: {
            payout_date: "desc",
          },
          take: 10,
          select: {
            id: true,
            payout_number: true,
            payout_date: true,
            period_start: true,
            period_end: true,
            total_rent_collected: true,
            management_fee: true,
            net_amount: true,
            status: true,
          },
        },
        _count: {
          select: {
            payouts: true,
          },
        },
      },
    });

    if (!owner) {
      return ApiResponseBuilder.notFound("Property owner", request.url);
    }

    // Transform owner to ensure all Decimal values are serialized
    const transformedOwner = {
      ...owner,
      management_fee_percentage: owner.management_fee_percentage?.toString() || null,
      primary_properties: owner.primary_properties.map(property => ({
        ...property,
        base_rent: property.base_rent.toString()
      })),
      payouts: owner.payouts.map(payout => ({
        ...payout,
        total_rent_collected: payout.total_rent_collected.toString(),
        management_fee: payout.management_fee?.toString() || null,
        net_amount: payout.net_amount.toString()
      }))
    };

    return ApiResponseBuilder.success(transformedOwner);
  } catch (error) {
    console.error("Error fetching property owner:", error);
    return ApiResponseBuilder.error("Failed to fetch property owner", "INTERNAL_ERROR", 500);
  }
}

// PUT /api/property-owners/[id] - Update a property owner
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    // Verify authentication
        // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for property-owners
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "property-owners", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update property owners");
    }

    const id = parseInt(resolvedParams.id);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.badRequest("Invalid property owner ID", undefined, request.url);
    }

    const body = await request.json();
    const validatedData = propertyOwnerSchema.parse(body);

    const owner = await db.propertyOwner.update({
      where: { id },
      data: {
        ...validatedData,
        updated_by: decoded.id,
      },
      include: {
        primary_properties: true,
      },
    });

    // Transform owner to ensure all Decimal values are serialized
    const transformedOwner = {
      ...owner,
      management_fee_percentage: owner.management_fee_percentage?.toString() || null,
      primary_properties: owner.primary_properties.map(property => ({
        ...property,
        base_rent: property.base_rent.toString()
      }))
    };

    return ApiResponseBuilder.success(transformedOwner);
  } catch (error: any) {
    console.error("Error updating property owner:", error);
    
    if (error.code === "P2025") {
      return ApiResponseBuilder.notFound("Property owner", request.url);
    }
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to update property owner", "INTERNAL_ERROR", 500);
  }
}

// DELETE /api/property-owners/[id] - Delete a property owner
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
){
  try {
    const resolvedParams = await params;
    
    // Verify authentication
        // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for property-owners
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "property-owners", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete property owners");
    }

    const id = parseInt(resolvedParams.id);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.badRequest("Invalid property owner ID", undefined, request.url);
    }

    // Check if owner has active properties or pending payouts
    const owner = await db.propertyOwner.findUnique({
      where: { id },
      include: {
        primary_properties: true,
        payouts: {
          where: {
            status: {
              in: ["PENDING", "APPROVED"],
            },
          },
        },
      },
    });

    if (!owner) {
      return ApiResponseBuilder.notFound("Property owner", request.url);
    }

    if (owner.primary_properties.length > 0) {
      return ApiResponseBuilder.conflict(
        "Cannot delete property owner with active properties",
        { activeProperties: owner.primary_properties.length },
        request.url
      );
    }

    if (owner.payouts.length > 0) {
      return ApiResponseBuilder.conflict(
        "Cannot delete property owner with pending or approved payouts",
        { pendingPayouts: owner.payouts.length },
        request.url
      );
    }

    // Soft delete by setting status to INACTIVE
    const deletedOwner = await db.propertyOwner.update({
      where: { id },
      data: {
        status: "INACTIVE",
        updated_by: decoded.id,
      },
    });

    return ApiResponseBuilder.success(deletedOwner);
  } catch (error) {
    console.error("Error deleting property owner:", error);
    return ApiResponseBuilder.error("Failed to delete property owner", "INTERNAL_ERROR", 500);
  }
}