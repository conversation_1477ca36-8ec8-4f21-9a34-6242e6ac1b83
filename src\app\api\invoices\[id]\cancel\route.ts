import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ApiResponseBuilder } from "@/lib/api-response";
import { z } from "zod";


const cancelSchema = z.object({
  reason: z.string().optional(),
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for invoices
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "invoices", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create invoices");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const invoiceId = parseInt(id);
    if (isNaN(invoiceId)) {
      return ApiResponseBuilder.error("Invalid invoice ID", "BAD_REQUEST", 400);
    }

    const body = await request.json();
    const validatedData = cancelSchema.parse(body);

    // Check if invoice exists
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        payments: true,
        allocations: true,
      },
    });

    if (!invoice) {
      return ApiResponseBuilder.error("Invoice not found", "NOT_FOUND", 404);
    }

    // Check if invoice is already cancelled
    if (invoice.status === 'CANCELLED') {
      return ApiResponseBuilder.error("Invoice is already cancelled", "BAD_REQUEST", 400);
    }

    // Don't allow cancelling paid invoices
    if (invoice.status === 'PAID') {
      return ApiResponseBuilder.error("Cannot cancel a paid invoice", "BAD_REQUEST", 400);
    }

    // If invoice has payments, those need to be refunded first
    if (invoice.payments.length > 0) {
      const activePayments = invoice.payments.filter(p => p.status === 'COMPLETED');
      if (activePayments.length > 0) {
        return ApiResponseBuilder.error("Cannot cancel invoice with active payments. Please refund the payments first.", "BAD_REQUEST", 400);
      }
    }

    // Cancel the invoice
    const cancelledInvoice = await prisma.invoice.update({
      where: { id: invoiceId },
      data: {
        status: 'CANCELLED',
        notes: validatedData.reason 
          ? `${invoice.notes ? invoice.notes + "\n\n" : ""}Cancellation Reason: ${validatedData.reason}`
          : invoice.notes,
        updated_by: decoded.id,
      },
      include: {
        contract: true,
        tenant: true,
        property: true,
        unit: true,
        items: true,
        payments: true,
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    return ApiResponseBuilder.success({
      message: "Invoice cancelled successfully",
      invoice: cancelledInvoice,
    });
  } catch (error) {
    console.error("Error cancelling invoice:", error);
    return ApiResponseBuilder.error("Failed to cancel invoice", "INTERNAL_ERROR", 500);
  }
}