generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model PropertyType {
  id             Int        @id @default(autoincrement())
  name_en        String     @db.VarChar(100)
  name_ar        String     @db.VarChar(100)
  description_en String?    @db.Text
  description_ar String?    @db.Text
  created_at     DateTime   @default(now())
  updated_at     DateTime   @updatedAt
  properties     Property[]

  @@map("property_types")
}

model Property {
  id                   Int                  @id @default(autoincrement())
  name_en              String               @db.VarChar(255)
  name_ar              String               @db.VarChar(255)
  address_en           String               @db.VarChar(500)
  address_ar           String               @db.VarChar(500)
  property_type_id     Int
  owner_id             Int?
  base_rent            Decimal              @db.Decimal(10, 3)
  status               PropertyStatus       @default(AVAILABLE)
  total_area           Decimal?             @db.Decimal(10, 2)
  floors_count         Int?
  parking_spaces       Int?
  created_by           Int?
  updated_by           Int?
  created_at           DateTime             @default(now())
  updated_at           DateTime             @updatedAt
  contracts            Contract[]
  invoices             Invoice[]
  maintenance_requests MaintenanceRequest[]
  payout_details       OwnerPayoutDetail[]
  owners_associations  OwnersAssociation[]
  payments             Payment[]
  creator              User?                @relation("PropertyCreatedBy", fields: [created_by], references: [id])
  owner                PropertyOwner?       @relation("PropertyPrimaryOwner", fields: [owner_id], references: [id])
  property_type        PropertyType         @relation(fields: [property_type_id], references: [id])
  updater              User?                @relation("PropertyUpdatedBy", fields: [updated_by], references: [id])
  amenities            PropertyAmenity[]
  units                Unit[]
  members              AssociationMember[]

  @@index([property_type_id])
  @@index([owner_id])
  @@index([status])
  @@index([created_by], map: "properties_created_by_fkey")
  @@index([updated_by], map: "properties_updated_by_fkey")
  @@map("properties")
}

model Amenity {
  id                 Int               @id @default(autoincrement())
  name_en            String            @db.VarChar(100)
  name_ar            String            @db.VarChar(100)
  icon               String?           @db.VarChar(50)
  created_at         DateTime          @default(now())
  updated_at         DateTime          @updatedAt
  property_amenities PropertyAmenity[]
  unit_amenities     UnitAmenity[]

  @@map("amenities")
}

model PropertyAmenity {
  id          Int      @id @default(autoincrement())
  property_id Int
  amenity_id  Int
  created_at  DateTime @default(now())
  amenity     Amenity  @relation(fields: [amenity_id], references: [id], onDelete: Cascade)
  property    Property @relation(fields: [property_id], references: [id], onDelete: Cascade)

  @@unique([property_id, amenity_id])
  @@index([amenity_id], map: "property_amenities_amenity_id_fkey")
  @@map("property_amenities")
}

model UnitAmenity {
  id         Int      @id @default(autoincrement())
  unit_id    Int
  amenity_id Int
  created_at DateTime @default(now())
  amenity    Amenity  @relation(fields: [amenity_id], references: [id], onDelete: Cascade)
  unit       Unit     @relation(fields: [unit_id], references: [id], onDelete: Cascade)

  @@unique([unit_id, amenity_id])
  @@index([amenity_id], map: "unit_amenities_amenity_id_fkey")
  @@map("unit_amenities")
}

model Unit {
  id                   Int                  @id @default(autoincrement())
  property_id          Int
  unit_number          String               @db.VarChar(50)
  unit_name_en         String?              @db.VarChar(100)
  unit_name_ar         String?              @db.VarChar(100)
  floor_number         Int?
  rooms_count          Int?
  majalis_count        Int?
  bathrooms_count      Int?
  area                 Decimal?             @db.Decimal(10, 2)
  rent_amount          Decimal              @db.Decimal(10, 3)
  status               UnitStatus           @default(AVAILABLE)
  description_en       String?              @db.Text
  description_ar       String?              @db.Text
  created_by           Int?
  updated_by           Int?
  created_at           DateTime             @default(now())
  updated_at           DateTime             @updatedAt
  contracts            Contract[]
  invoices             Invoice[]
  maintenance_requests MaintenanceRequest[]
  payments             Payment[]
  amenities            UnitAmenity[]
  creator              User?                @relation("UnitCreatedBy", fields: [created_by], references: [id])
  property             Property             @relation(fields: [property_id], references: [id], onDelete: Cascade)
  updater              User?                @relation("UnitUpdatedBy", fields: [updated_by], references: [id])

  @@unique([property_id, unit_number])
  @@index([property_id])
  @@index([status])
  @@index([rent_amount])
  @@index([created_by], map: "units_created_by_fkey")
  @@index([updated_by], map: "units_updated_by_fkey")
  @@map("units")
}

model Tenant {
  id                   Int                  @id @default(autoincrement())
  first_name           String               @db.VarChar(100)
  last_name            String               @db.VarChar(100)
  email                String               @unique @db.VarChar(255)
  phone                String?              @db.VarChar(20)
  national_id          String?              @db.VarChar(50)
  national_id_expiry   DateTime?            @db.Date
  date_of_birth        DateTime?            @db.Date
  nationality          String?              @db.VarChar(100)
  occupation           String?              @db.VarChar(100)
  company_name         String?              @db.VarChar(255)
  created_by           Int?
  updated_by           Int?
  created_at           DateTime             @default(now())
  updated_at           DateTime             @updatedAt
  contracts            ContractTenant[]
  emergency_contacts   EmergencyContact[]
  invoices             Invoice[]
  maintenance_requests MaintenanceRequest[]
  payments             Payment[]
  documents            TenantDocument[]
  creator              User?                @relation("TenantCreatedBy", fields: [created_by], references: [id])
  updater              User?                @relation("TenantUpdatedBy", fields: [updated_by], references: [id])

  @@index([created_by], map: "tenants_created_by_fkey")
  @@index([updated_by], map: "tenants_updated_by_fkey")
  @@map("tenants")
}

model TenantDocument {
  id              Int          @id @default(autoincrement())
  tenant_id       Int
  document_type   DocumentType
  document_number String?      @db.VarChar(100)
  issue_date      DateTime?    @db.Date
  expiry_date     DateTime?    @db.Date
  file_name       String       @db.VarChar(255)
  file_path       String       @db.VarChar(500)
  file_size       Int
  mime_type       String       @db.VarChar(100)
  uploaded_by     Int?
  created_at      DateTime     @default(now())
  updated_at      DateTime     @updatedAt
  tenant          Tenant       @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  uploader        User?        @relation("DocumentUploadedBy", fields: [uploaded_by], references: [id])

  @@index([tenant_id])
  @@index([document_type])
  @@index([uploaded_by], map: "tenant_documents_uploaded_by_fkey")
  @@map("tenant_documents")
}

model EmergencyContact {
  id           Int      @id @default(autoincrement())
  tenant_id    Int
  name         String   @db.VarChar(255)
  relationship String   @db.VarChar(100)
  phone        String   @db.VarChar(20)
  email        String?  @db.VarChar(255)
  address      String?  @db.Text
  is_primary   Boolean  @default(false)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
  created_by   Int?
  updated_by   Int?
  creator      User?    @relation("EmergencyContactCreator", fields: [created_by], references: [id])
  tenant       Tenant   @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  updater      User?    @relation("EmergencyContactUpdater", fields: [updated_by], references: [id])

  @@index([tenant_id])
  @@index([created_by], map: "emergency_contacts_created_by_fkey")
  @@index([updated_by], map: "emergency_contacts_updated_by_fkey")
  @@map("emergency_contacts")
}

model User {
  id                               Int                        @id @default(autoincrement())
  username                         String                     @unique @db.VarChar(50)
  email                            String                     @unique @db.VarChar(255)
  password_hash                    String                     @db.VarChar(255)
  first_name                       String                     @db.VarChar(100)
  last_name                        String                     @db.VarChar(100)
  phone                            String?                    @db.VarChar(20)
  status                           UserStatus                 @default(ACTIVE)
  last_login_at                    DateTime?
  email_verified                   Boolean                    @default(false)
  created_at                       DateTime                   @default(now())
  updated_at                       DateTime                   @updatedAt
  created_association_transactions AssociationTransaction[]   @relation("TransactionCreatedBy")
  uploaded_contract_documents      ContractDocument[]         @relation("ContractDocumentUploadedBy")
  created_contracts                Contract[]                 @relation("ContractCreatedBy")
  updated_contracts                Contract[]                 @relation("ContractUpdatedBy")
  created_emergency_contacts       EmergencyContact[]         @relation("EmergencyContactCreator")
  updated_emergency_contacts       EmergencyContact[]         @relation("EmergencyContactUpdater")
  created_categories               ExpenseCategory[]          @relation("CategoryCreatedBy")
  updated_categories               ExpenseCategory[]          @relation("CategoryUpdatedBy")
  approved_expenses                Expense[]                  @relation("ExpenseApprovedBy")
  created_expenses                 Expense[]                  @relation("ExpenseCreatedBy")
  updated_expenses                 Expense[]                  @relation("ExpenseUpdatedBy")
  created_invoices                 Invoice[]                  @relation("InvoiceCreatedBy")
  updated_invoices                 Invoice[]                  @relation("InvoiceUpdatedBy")
  uploaded_maintenance_attachments MaintenanceAttachment[]    @relation("MaintenanceAttachmentUploadedBy")
  assigned_maintenance             MaintenanceRequest[]       @relation("MaintenanceAssignedTo")
  created_maintenance              MaintenanceRequest[]       @relation("MaintenanceCreatedBy")
  reported_maintenance             MaintenanceRequest[]       @relation("MaintenanceReportedBy")
  updated_maintenance              MaintenanceRequest[]       @relation("MaintenanceUpdatedBy")
  changed_maintenance_status       MaintenanceStatusHistory[] @relation("MaintenanceStatusChangedBy")
  approved_payouts                 OwnerPayout[]              @relation("PayoutApprovedBy")
  created_payouts                  OwnerPayout[]              @relation("PayoutCreatedBy")
  paid_payouts                     OwnerPayout[]              @relation("PayoutPaidBy")
  created_associations             OwnersAssociation[]        @relation("AssociationCreatedBy")
  updated_associations             OwnersAssociation[]        @relation("AssociationUpdatedBy")
  created_payments                 Payment[]                  @relation("PaymentCreatedBy")
  created_properties               Property[]                 @relation("PropertyCreatedBy")
  updated_properties               Property[]                 @relation("PropertyUpdatedBy")
  created_owners                   PropertyOwner[]            @relation("OwnerCreatedBy")
  updated_owners                   PropertyOwner[]            @relation("OwnerUpdatedBy")
  created_subscription_payments    SubscriptionPayment[]      @relation("SubscriptionPaymentCreatedBy")
  uploaded_documents               TenantDocument[]           @relation("DocumentUploadedBy")
  created_tenants                  Tenant[]                   @relation("TenantCreatedBy")
  updated_tenants                  Tenant[]                   @relation("TenantUpdatedBy")
  created_units                    Unit[]                     @relation("UnitCreatedBy")
  updated_units                    Unit[]                     @relation("UnitUpdatedBy")
  user_roles                       UserRole[]

  @@map("users")
}

model Role {
  id               Int              @id @default(autoincrement())
  name             String           @unique @db.VarChar(100)
  description      String?          @db.Text
  is_system        Boolean          @default(false)
  created_at       DateTime         @default(now())
  updated_at       DateTime         @updatedAt
  role_permissions RolePermission[]
  user_roles       UserRole[]

  @@map("roles")
}

model Permission {
  id               Int              @id @default(autoincrement())
  module           String           @db.VarChar(50)
  action           PermissionAction
  description      String?          @db.Text
  created_at       DateTime         @default(now())
  role_permissions RolePermission[]

  @@unique([module, action])
  @@map("permissions")
}

model UserRole {
  id          Int      @id @default(autoincrement())
  user_id     Int
  role_id     Int
  assigned_at DateTime @default(now())
  assigned_by Int?
  role        Role     @relation(fields: [role_id], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, role_id])
  @@index([role_id], map: "user_roles_role_id_fkey")
  @@map("user_roles")
}

model RolePermission {
  id            Int        @id @default(autoincrement())
  role_id       Int
  permission_id Int
  granted_at    DateTime   @default(now())
  permission    Permission @relation(fields: [permission_id], references: [id], onDelete: Cascade)
  role          Role       @relation(fields: [role_id], references: [id], onDelete: Cascade)

  @@unique([role_id, permission_id])
  @@index([permission_id], map: "role_permissions_permission_id_fkey")
  @@map("role_permissions")
}

model CompanySettings {
  id              Int      @id @default(autoincrement())
  company_name    String   @db.VarChar(255)
  logo_url        String?  @db.VarChar(500)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  company_name_ar String?  @db.VarChar(255)

  @@map("company_settings")
}

model ExpenseCategory {
  id          Int       @id @default(autoincrement())
  name_en     String    @db.VarChar(100)
  name_ar     String    @db.VarChar(100)
  description String?   @db.Text
  is_active   Boolean   @default(true)
  sort_order  Int       @default(0)
  created_by  Int?
  updated_by  Int?
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  creator     User?     @relation("CategoryCreatedBy", fields: [created_by], references: [id])
  updater     User?     @relation("CategoryUpdatedBy", fields: [updated_by], references: [id])
  expenses    Expense[]

  @@index([created_by], map: "expense_categories_created_by_fkey")
  @@index([updated_by], map: "expense_categories_updated_by_fkey")
  @@map("expense_categories")
}

model Expense {
  id                  Int                 @id @default(autoincrement())
  date                DateTime            @db.Date
  description         String              @db.VarChar(255)
  amount              Decimal             @db.Decimal(10, 3)
  category_id         Int
  payment_method      PaymentMethod
  paid_by             String              @db.VarChar(100)
  notes               String?             @db.Text
  status              ExpenseStatus       @default(PENDING)
  is_recurring        Boolean             @default(false)
  recurring_frequency RecurringFrequency?
  next_due_date       DateTime?           @db.Date
  approved_by         Int?
  approved_at         DateTime?
  created_by          Int?
  updated_by          Int?
  created_at          DateTime            @default(now())
  updated_at          DateTime            @updatedAt
  approvals           ExpenseApproval[]
  attachments         ExpenseAttachment[]
  approver            User?               @relation("ExpenseApprovedBy", fields: [approved_by], references: [id])
  category            ExpenseCategory     @relation(fields: [category_id], references: [id])
  creator             User?               @relation("ExpenseCreatedBy", fields: [created_by], references: [id])
  updater             User?               @relation("ExpenseUpdatedBy", fields: [updated_by], references: [id])

  @@index([approved_by], map: "expenses_approved_by_fkey")
  @@index([category_id], map: "expenses_category_id_fkey")
  @@index([created_by], map: "expenses_created_by_fkey")
  @@index([updated_by], map: "expenses_updated_by_fkey")
  @@map("expenses")
}

model ExpenseAttachment {
  id            Int      @id @default(autoincrement())
  expense_id    Int
  filename      String   @db.VarChar(255)
  original_name String   @db.VarChar(255)
  file_path     String   @db.VarChar(500)
  file_size     Int
  mime_type     String   @db.VarChar(100)
  uploaded_at   DateTime @default(now())
  expense       Expense  @relation(fields: [expense_id], references: [id], onDelete: Cascade)

  @@index([expense_id], map: "expense_attachments_expense_id_fkey")
  @@map("expense_attachments")
}

model ExpenseApproval {
  id         Int            @id @default(autoincrement())
  expense_id Int
  user_id    Int
  action     ApprovalAction
  comments   String?        @db.Text
  created_at DateTime       @default(now())
  expense    Expense        @relation(fields: [expense_id], references: [id], onDelete: Cascade)

  @@index([expense_id], map: "expense_approvals_expense_id_fkey")
  @@map("expense_approvals")
}

model Contract {
  id                   Int                @id @default(autoincrement())
  contract_number      String             @unique @db.VarChar(50)
  property_id          Int?
  unit_id              Int?
  start_date           DateTime           @db.Date
  end_date             DateTime           @db.Date
  monthly_rent         Decimal            @db.Decimal(10, 3)
  payment_due_day      Int                @default(1)
  security_deposit     Decimal            @db.Decimal(10, 3)
  insurance_amount     Decimal?           @db.Decimal(10, 3)
  insurance_due_date   DateTime?          @db.Date
  terms_and_conditions String?            @db.Text
  status               ContractStatus     @default(DRAFT)
  auto_renew           Boolean            @default(false)
  renewal_notice_days  Int                @default(30)
  notes                String?            @db.Text
  created_by           Int?
  updated_by           Int?
  created_at           DateTime           @default(now())
  updated_at           DateTime           @updatedAt
  documents            ContractDocument[]
  renewals             ContractRenewal[]
  tenants              ContractTenant[]
  creator              User?              @relation("ContractCreatedBy", fields: [created_by], references: [id])
  property             Property?          @relation(fields: [property_id], references: [id])
  unit                 Unit?              @relation(fields: [unit_id], references: [id])
  updater              User?              @relation("ContractUpdatedBy", fields: [updated_by], references: [id])
  invoices             Invoice[]

  @@index([property_id])
  @@index([unit_id])
  @@index([status])
  @@index([start_date])
  @@index([end_date])
  @@index([created_by], map: "contracts_created_by_fkey")
  @@index([updated_by], map: "contracts_updated_by_fkey")
  @@map("contracts")
}

model ContractRenewal {
  id                     Int       @id @default(autoincrement())
  contract_id            Int
  original_contract_id   Int
  renewal_date           DateTime  @default(now())
  new_start_date         DateTime  @db.Date
  new_end_date           DateTime  @db.Date
  new_monthly_rent       Decimal   @db.Decimal(10, 3)
  new_security_deposit   Decimal?  @db.Decimal(10, 3)
  new_insurance_amount   Decimal?  @db.Decimal(10, 3)
  new_insurance_due_date DateTime? @db.Date
  renewal_notes          String?   @db.Text
  created_by             Int?
  created_at             DateTime  @default(now())
  contract               Contract  @relation(fields: [contract_id], references: [id], onDelete: Cascade)

  @@index([contract_id])
  @@index([original_contract_id])
  @@map("contract_renewals")
}

model ContractTenant {
  id          Int      @id @default(autoincrement())
  contract_id Int
  tenant_id   Int
  is_primary  Boolean  @default(false)
  created_at  DateTime @default(now())
  contract    Contract @relation(fields: [contract_id], references: [id], onDelete: Cascade)
  tenant      Tenant   @relation(fields: [tenant_id], references: [id], onDelete: Cascade)

  @@unique([contract_id, tenant_id])
  @@index([contract_id])
  @@index([tenant_id])
  @@map("contract_tenants")
}

model ContractDocument {
  id            Int                  @id @default(autoincrement())
  contract_id   Int
  document_type ContractDocumentType
  document_name String               @db.VarChar(255)
  file_name     String               @db.VarChar(255)
  file_path     String               @db.VarChar(500)
  file_size     Int
  mime_type     String               @db.VarChar(100)
  uploaded_by   Int?
  created_at    DateTime             @default(now())
  updated_at    DateTime             @updatedAt
  contract      Contract             @relation(fields: [contract_id], references: [id], onDelete: Cascade)
  uploader      User?                @relation("ContractDocumentUploadedBy", fields: [uploaded_by], references: [id])

  @@index([contract_id])
  @@index([uploaded_by], map: "contract_documents_uploaded_by_fkey")
  @@map("contract_documents")
}

model Invoice {
  id              Int                 @id @default(autoincrement())
  invoice_number  String              @unique @db.VarChar(50)
  contract_id     Int?
  tenant_id       Int
  property_id     Int
  unit_id         Int
  invoice_date    DateTime            @db.Date
  due_date        DateTime            @db.Date
  original_amount Decimal             @db.Decimal(10, 3)
  late_fee        Decimal             @default(0.000) @db.Decimal(10, 3)
  total_amount    Decimal             @db.Decimal(10, 3)
  paid_amount     Decimal             @default(0.000) @db.Decimal(10, 3)
  balance_amount  Decimal             @db.Decimal(10, 3)
  status          InvoiceStatus       @default(PENDING)
  notes           String?             @db.Text
  created_by      Int?
  updated_by      Int?
  created_at      DateTime            @default(now())
  updated_at      DateTime            @updatedAt
  items           InvoiceItem[]
  contract        Contract?           @relation(fields: [contract_id], references: [id])
  creator         User?               @relation("InvoiceCreatedBy", fields: [created_by], references: [id])
  property        Property            @relation(fields: [property_id], references: [id])
  tenant          Tenant              @relation(fields: [tenant_id], references: [id])
  unit            Unit                @relation(fields: [unit_id], references: [id])
  updater         User?               @relation("InvoiceUpdatedBy", fields: [updated_by], references: [id])
  allocations     PaymentAllocation[]
  payments        Payment[]

  @@index([contract_id])
  @@index([tenant_id])
  @@index([property_id])
  @@index([unit_id])
  @@index([status])
  @@index([due_date])
  @@index([created_by], map: "invoices_created_by_fkey")
  @@index([updated_by], map: "invoices_updated_by_fkey")
  @@map("invoices")
}

model InvoiceItem {
  id             Int      @id @default(autoincrement())
  invoice_id     Int
  description_en String   @db.VarChar(255)
  description_ar String   @db.VarChar(255)
  quantity       Int      @default(1)
  unit_price     Decimal  @db.Decimal(10, 3)
  amount         Decimal  @db.Decimal(10, 3)
  created_at     DateTime @default(now())
  invoice        Invoice  @relation(fields: [invoice_id], references: [id], onDelete: Cascade)

  @@index([invoice_id])
  @@map("invoice_items")
}

model Payment {
  id               Int                 @id @default(autoincrement())
  payment_number   String              @unique @db.VarChar(50)
  invoice_id       Int?
  tenant_id        Int
  property_id      Int?
  unit_id          Int?
  amount           Decimal             @db.Decimal(10, 3)
  payment_method   PaymentMethod
  payment_date     DateTime            @db.Date
  reference_number String?             @db.VarChar(100)
  bank_name        String?             @db.VarChar(100)
  notes            String?             @db.Text
  status           PaymentStatus       @default(COMPLETED)
  installment      Int?                @db.Int
  created_by       Int?
  created_at       DateTime            @default(now())
  updated_at       DateTime            @updatedAt
  allocations      PaymentAllocation[]
  creator          User?               @relation("PaymentCreatedBy", fields: [created_by], references: [id])
  invoice          Invoice?            @relation(fields: [invoice_id], references: [id])
  property         Property?           @relation(fields: [property_id], references: [id])
  tenant           Tenant              @relation(fields: [tenant_id], references: [id])
  unit             Unit?               @relation(fields: [unit_id], references: [id])

  @@index([invoice_id])
  @@index([tenant_id])
  @@index([property_id])
  @@index([unit_id])
  @@index([payment_date])
  @@index([status])
  @@index([created_by], map: "payments_created_by_fkey")
  @@map("payments")
}

model PaymentAllocation {
  id               Int      @id @default(autoincrement())
  payment_id       Int
  invoice_id       Int
  allocated_amount Decimal  @db.Decimal(10, 3)
  created_at       DateTime @default(now())
  invoice          Invoice  @relation(fields: [invoice_id], references: [id], onDelete: Cascade)
  payment          Payment  @relation(fields: [payment_id], references: [id], onDelete: Cascade)

  @@unique([payment_id, invoice_id])
  @@index([payment_id])
  @@index([invoice_id])
  @@map("payment_allocations")
}

model PropertyOwner {
  id                        Int           @id @default(autoincrement())
  name_en                   String        @db.VarChar(100)
  name_ar                   String        @db.VarChar(100)
  email                     String?       @db.VarChar(100)
  phone                     String?       @db.VarChar(20)
  mobile                    String?       @db.VarChar(20)
  address_en                String?       @db.Text
  address_ar                String?       @db.Text
  tax_id                    String?       @db.VarChar(50)
  bank_name                 String?       @db.VarChar(100)
  bank_account_number       String?       @db.VarChar(50)
  bank_iban                 String?       @db.VarChar(50)
  management_fee_percentage Decimal?      @db.Decimal(5, 2)
  notes                     String?       @db.Text
  status                    OwnerStatus   @default(ACTIVE)
  created_by                Int?
  updated_by                Int?
  created_at                DateTime      @default(now())
  updated_at                DateTime      @updatedAt
  payouts                   OwnerPayout[]
  primary_properties        Property[]    @relation("PropertyPrimaryOwner")
  creator                   User?         @relation("OwnerCreatedBy", fields: [created_by], references: [id])
  updater                   User?         @relation("OwnerUpdatedBy", fields: [updated_by], references: [id])

  @@index([created_by], map: "property_owners_created_by_fkey")
  @@index([updated_by], map: "property_owners_updated_by_fkey")
  @@map("property_owners")
}

model OwnerPayout {
  id                   Int                 @id @default(autoincrement())
  payout_number        String              @unique @db.VarChar(50)
  owner_id             Int
  payout_date          DateTime            @db.Date
  period_start         DateTime            @db.Date
  period_end           DateTime            @db.Date
  total_rent_collected Decimal             @db.Decimal(10, 3)
  management_fee       Decimal             @db.Decimal(10, 3)
  other_deductions     Decimal             @default(0.000) @db.Decimal(10, 3)
  net_amount           Decimal             @db.Decimal(10, 3)
  payment_method       PaymentMethod
  reference_number     String?             @db.VarChar(100)
  bank_transfer_ref    String?             @db.VarChar(100)
  notes                String?             @db.Text
  status               PayoutStatus        @default(PENDING)
  approved_by          Int?
  approved_at          DateTime?
  paid_by              Int?
  paid_at              DateTime?
  created_by           Int?
  created_at           DateTime            @default(now())
  updated_at           DateTime            @updatedAt
  payout_details       OwnerPayoutDetail[]
  approver             User?               @relation("PayoutApprovedBy", fields: [approved_by], references: [id])
  creator              User?               @relation("PayoutCreatedBy", fields: [created_by], references: [id])
  owner                PropertyOwner       @relation(fields: [owner_id], references: [id])
  payer                User?               @relation("PayoutPaidBy", fields: [paid_by], references: [id])

  @@index([owner_id])
  @@index([payout_date])
  @@index([status])
  @@index([approved_by], map: "owner_payouts_approved_by_fkey")
  @@index([created_by], map: "owner_payouts_created_by_fkey")
  @@index([paid_by], map: "owner_payouts_paid_by_fkey")
  @@map("owner_payouts")
}

model OwnerPayoutDetail {
  id             Int         @id @default(autoincrement())
  payout_id      Int
  property_id    Int
  rent_collected Decimal     @db.Decimal(10, 3)
  management_fee Decimal     @db.Decimal(10, 3)
  net_amount     Decimal     @db.Decimal(10, 3)
  created_at     DateTime    @default(now())
  payout         OwnerPayout @relation(fields: [payout_id], references: [id], onDelete: Cascade)
  property       Property    @relation(fields: [property_id], references: [id])

  @@index([payout_id])
  @@index([property_id])
  @@map("owner_payout_details")
}

model MaintenanceRequest {
  id               Int                        @id @default(autoincrement())
  request_number   String                     @unique @db.VarChar(50)
  property_id      Int
  unit_id          Int?
  tenant_id        Int?
  title            String                     @db.VarChar(200)
  description      String                     @db.Text
  priority         MaintenancePriority
  category         MaintenanceCategory
  status           MaintenanceStatus          @default(REPORTED)
  reported_by      Int?
  reported_date    DateTime                   @default(now())
  assigned_to      Int?
  assigned_date    DateTime?
  scheduled_date   DateTime?
  completed_date   DateTime?
  estimated_cost   Decimal?                   @db.Decimal(10, 3)
  actual_cost      Decimal?                   @db.Decimal(10, 3)
  contractor_name  String?                    @db.VarChar(100)
  contractor_phone String?                    @db.VarChar(20)
  internal_notes   String?                    @db.Text
  resolution_notes String?                    @db.Text
  created_by       Int?
  updated_by       Int?
  created_at       DateTime                   @default(now())
  updated_at       DateTime                   @updatedAt
  attachments      MaintenanceAttachment[]
  assignee         User?                      @relation("MaintenanceAssignedTo", fields: [assigned_to], references: [id])
  creator          User?                      @relation("MaintenanceCreatedBy", fields: [created_by], references: [id])
  property         Property                   @relation(fields: [property_id], references: [id])
  reporter         User?                      @relation("MaintenanceReportedBy", fields: [reported_by], references: [id])
  tenant           Tenant?                    @relation(fields: [tenant_id], references: [id])
  unit             Unit?                      @relation(fields: [unit_id], references: [id])
  updater          User?                      @relation("MaintenanceUpdatedBy", fields: [updated_by], references: [id])
  status_history   MaintenanceStatusHistory[]

  @@index([property_id])
  @@index([unit_id])
  @@index([tenant_id])
  @@index([status])
  @@index([priority])
  @@index([reported_date])
  @@index([assigned_to], map: "maintenance_requests_assigned_to_fkey")
  @@index([created_by], map: "maintenance_requests_created_by_fkey")
  @@index([reported_by], map: "maintenance_requests_reported_by_fkey")
  @@index([updated_by], map: "maintenance_requests_updated_by_fkey")
  @@map("maintenance_requests")
}

model MaintenanceAttachment {
  id          Int                @id @default(autoincrement())
  request_id  Int
  file_name   String             @db.VarChar(255)
  file_path   String             @db.VarChar(500)
  file_size   Int
  mime_type   String             @db.VarChar(100)
  uploaded_by Int?
  created_at  DateTime           @default(now())
  request     MaintenanceRequest @relation(fields: [request_id], references: [id], onDelete: Cascade)
  uploader    User?              @relation("MaintenanceAttachmentUploadedBy", fields: [uploaded_by], references: [id])

  @@index([request_id])
  @@index([uploaded_by], map: "maintenance_attachments_uploaded_by_fkey")
  @@map("maintenance_attachments")
}

model MaintenanceStatusHistory {
  id          Int                @id @default(autoincrement())
  request_id  Int
  from_status MaintenanceStatus?
  to_status   MaintenanceStatus
  notes       String?            @db.Text
  changed_by  Int?
  created_at  DateTime           @default(now())
  changer     User?              @relation("MaintenanceStatusChangedBy", fields: [changed_by], references: [id])
  request     MaintenanceRequest @relation(fields: [request_id], references: [id], onDelete: Cascade)

  @@index([request_id])
  @@index([changed_by], map: "maintenance_status_history_changed_by_fkey")
  @@map("maintenance_status_history")
}

model OwnersAssociation {
  id                       Int                       @id @default(autoincrement())
  name_en                  String                    @db.VarChar(255)
  name_ar                  String                    @db.VarChar(255)
  property_id              Int
  establishment_date       DateTime                  @db.Date
  total_members            Int                       @default(0)
  president_name           String                    @db.VarChar(255)
  management_term_duration Int
  contact_email            String?                   @db.VarChar(255)
  contact_phone            String?                   @db.VarChar(20)
  created_by               Int?
  updated_by               Int?
  created_at               DateTime                  @default(now())
  updated_at               DateTime                  @updatedAt
  members                  AssociationMember[]
  subscriptions            AssociationSubscription[]
  transactions             AssociationTransaction[]
  creator                  User?                     @relation("AssociationCreatedBy", fields: [created_by], references: [id])
  property                 Property                  @relation(fields: [property_id], references: [id])
  updater                  User?                     @relation("AssociationUpdatedBy", fields: [updated_by], references: [id])

  @@index([property_id])
  @@index([created_by], map: "owners_associations_created_by_fkey")
  @@index([updated_by], map: "owners_associations_updated_by_fkey")
  @@map("owners_associations")
}

model AssociationMember {
  id                     Int                      @id @default(autoincrement())
  association_id         Int
  property_id            Int?
  full_name              String                   @db.VarChar(255)
  unit_number            String                   @db.VarChar(50)
  ownership_percentage   Decimal?                 @db.Decimal(5, 2)
  phone                  String?                  @db.VarChar(20)
  email                  String?                  @db.VarChar(255)
  join_date              DateTime?                @db.Date
  status                 MemberStatus             @default(ACTIVE)
  is_board_member        Boolean                  @default(false)
  created_at             DateTime                 @default(now())
  updated_at             DateTime                 @updatedAt
  association            OwnersAssociation        @relation(fields: [association_id], references: [id], onDelete: Cascade)
  property               Property?                @relation(fields: [property_id], references: [id])
  subscription_payments  SubscriptionPayment[]
  transactions           AssociationTransaction[]

  @@index([association_id])
  @@map("association_members")
}

model AssociationSubscription {
  id             Int                      @id @default(autoincrement())
  association_id Int
  name_en        String                   @db.VarChar(255)
  name_ar        String                   @db.VarChar(255)
  amount         Decimal                  @db.Decimal(10, 3)
  frequency      SubscriptionFrequency    @default(MONTHLY)
  is_active      Boolean                  @default(true)
  created_at     DateTime                 @default(now())
  updated_at     DateTime                 @updatedAt
  association    OwnersAssociation        @relation(fields: [association_id], references: [id], onDelete: Cascade)
  payments       SubscriptionPayment[]
  transactions   AssociationTransaction[]

  @@index([association_id])
  @@map("association_subscriptions")
}

model SubscriptionPayment {
  id               Int                       @id @default(autoincrement())
  subscription_id  Int
  member_id        Int
  payment_date     DateTime                  @db.Date
  due_date         DateTime                  @db.Date
  amount           Decimal                   @db.Decimal(10, 3)
  amount_due       Decimal?                  @db.Decimal(10, 3)
  amount_paid      Decimal?                  @default(0) @db.Decimal(10, 3)
  status           SubscriptionPaymentStatus @default(UNPAID)
  payment_method   PaymentMethod?
  reference_number String?                   @db.VarChar(100)
  transaction_id   Int?
  installment      Int?                      @db.Int
  notes            String?                   @db.Text
  created_by       Int?
  created_at       DateTime                  @default(now())
  updated_at       DateTime                  @updatedAt
  creator          User?                     @relation("SubscriptionPaymentCreatedBy", fields: [created_by], references: [id])
  member           AssociationMember         @relation(fields: [member_id], references: [id])
  subscription     AssociationSubscription   @relation(fields: [subscription_id], references: [id])
  transaction      AssociationTransaction?   @relation(fields: [transaction_id], references: [id])
  transactions     AssociationTransaction[]  @relation("TransactionPayments")

  @@index([subscription_id])
  @@index([member_id])
  @@index([status])
  @@index([transaction_id])
  @@index([created_by], map: "subscription_payments_created_by_fkey")
  @@map("subscription_payments")
}

model AssociationTransaction {
  id                        Int                      @id @default(autoincrement())
  association_id            Int
  member_id                 Int?
  transaction_date          DateTime                 @db.Date
  type                      TransactionType
  category                  String                   @db.VarChar(100)
  description               String                   @db.Text
  amount                    Decimal                  @db.Decimal(10, 3)
  payment_method            PaymentMethod
  reference_number          String?                  @db.VarChar(100)
  notes                     String?                  @db.Text
  attachment_url            String?                  @db.VarChar(500)
  attachment_name           String?                  @db.VarChar(255)
  created_by                Int?
  created_at                DateTime                 @default(now())
  updated_at                DateTime                 @updatedAt
  association               OwnersAssociation        @relation(fields: [association_id], references: [id], onDelete: Cascade)
  member                    AssociationMember?       @relation(fields: [member_id], references: [id])
  creator                   User?                    @relation("TransactionCreatedBy", fields: [created_by], references: [id])
  payments                  SubscriptionPayment[]    @relation("TransactionPayments")
  AssociationSubscription   AssociationSubscription? @relation(fields: [associationSubscriptionId], references: [id])
  associationSubscriptionId Int?
  SubscriptionPayment       SubscriptionPayment[]

  @@index([association_id])
  @@index([member_id])
  @@index([type])
  @@index([transaction_date])
  @@index([created_by], map: "association_transactions_created_by_fkey")
  @@map("association_transactions")
}

enum PropertyStatus {
  AVAILABLE
  RENTED
  UNDER_MAINTENANCE
  OUT_OF_SERVICE
}

enum UnitStatus {
  AVAILABLE
  RENTED
  UNDER_MAINTENANCE
}

enum DocumentType {
  NATIONAL_ID
  PASSPORT
  DRIVING_LICENSE
  EMPLOYMENT_CONTRACT
  SALARY_CERTIFICATE
  BANK_STATEMENT
  OTHER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
}

enum PermissionAction {
  CREATE
  READ
  UPDATE
  DELETE
}

enum ContractStatus {
  DRAFT
  ACTIVE
  EXPIRED
  TERMINATED
  RENEWED
}

enum ContractDocumentType {
  CONTRACT
  ADDENDUM
  TERMINATION_LETTER
  RENEWAL_AGREEMENT
  PAYMENT_RECEIPT
  OTHER
}

enum PaymentMethod {
  CASH
  BANK_TRANSFER
  CREDIT_CARD
  DEBIT_CARD
  CHECK
  OTHER
}

enum ExpenseStatus {
  PENDING
  APPROVED
  REJECTED
}

enum RecurringFrequency {
  MONTHLY
  QUARTERLY
  YEARLY
}

enum ApprovalAction {
  APPROVED
  REJECTED
  REQUESTED_CHANGES
}

enum InvoiceStatus {
  DRAFT
  PENDING
  PARTIALLY_PAID
  PAID
  OVERDUE
  CANCELLED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  CANCELLED
}

enum OwnerStatus {
  ACTIVE
  INACTIVE
}

enum PayoutStatus {
  PENDING
  APPROVED
  PAID
  CANCELLED
}

enum MaintenancePriority {
  LOW
  MEDIUM
  HIGH
  EMERGENCY
}

enum MaintenanceCategory {
  ELECTRICAL
  PLUMBING
  HVAC
  STRUCTURAL
  APPLIANCES
  PAINTING
  CLEANING
  LANDSCAPING
  SECURITY
  OTHER
}

enum MaintenanceStatus {
  REPORTED
  ACKNOWLEDGED
  IN_PROGRESS
  ON_HOLD
  COMPLETED
  CANCELLED
}

enum SubscriptionFrequency {
  MONTHLY
  QUARTERLY
  YEARLY
}

enum SubscriptionPaymentStatus {
  PAID
  UNPAID
  OVERDUE
  PARTIALLY_PAID
}

enum TransactionType {
  EXPENSE
  INCOME
}

enum MemberStatus {
  ACTIVE
  INACTIVE
}
