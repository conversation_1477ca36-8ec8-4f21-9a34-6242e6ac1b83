"use client";

import { useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { useTranslations } from 'next-intl';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import type { TenantWithRelations } from "@/types/tenant";

interface DeleteTenantDialogProps {
  tenant: TenantWithRelations;
  children: React.ReactNode;
}

export function DeleteTenantDialog({ tenant, children }: DeleteTenantDialogProps) {
  const t = useTranslations('tenants.delete');
  const tMessages = useTranslations('tenants.messages');

  const [isDeleting, setIsDeleting] = useState(false);
  const [open, setOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      setError(null);

      const response = await fetch(`/api/tenants/${tenant.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        let errorMessage = t('error');

        if (response.status === 404) {
          errorMessage = t('notFound');
        } else if (response.status === 409) {
          errorMessage = t('conflict');
        } else if (response.status === 500) {
          errorMessage = t('serverError');
        } else if (errorData.error) {
          errorMessage = errorData.error;
        }

        throw new Error(errorMessage);
      }

      toast.success(t('success'));
      setOpen(false);

      // Refresh the page to update the list
      window.location.reload();
    } catch (error) {
      console.error("Error deleting tenant:", error);
      const errorMessage = error instanceof Error ? error.message : t('error');
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        {children}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t('title')}</AlertDialogTitle>
          <AlertDialogDescription>
            {t('description')}{" "}
            <span className="font-semibold">
              {tenant.first_name} {tenant.last_name}
            </span>
            ? {t('warning')}
          </AlertDialogDescription>
        </AlertDialogHeader>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive" className="mx-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>{t('cancelButton')}</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                {t('deleting')}
              </div>
            ) : (
              t('confirmButton')
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
