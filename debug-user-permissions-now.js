const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function debugUserPermissions() {
  try {
    // Get the admin user
    const user = await prisma.user.findFirst({
      where: { username: 'admin' },
      include: {
        user_roles: {
          include: {
            role: {
              include: {
                role_permissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!user) {
      console.log('Admin user not found');
      return;
    }

    console.log('User:', user.username);
    console.log('Is Admin:', user.is_admin);
    console.log('Is Superuser:', user.is_superuser);
    console.log('\nRoles:');
    
    user.user_roles.forEach(ur => {
      console.log(`- ${ur.role.name}`);
      console.log('  Permissions:');
      
      // Group permissions by module
      const permissionsByModule = {};
      ur.role.role_permissions.forEach(rp => {
        const moduleName = rp.permission.module;
        if (!permissionsByModule[moduleName]) {
          permissionsByModule[moduleName] = [];
        }
        permissionsByModule[moduleName].push(rp.permission.action);
      });
      
      Object.entries(permissionsByModule).forEach(([module, actions]) => {
        console.log(`    ${module}: ${actions.join(', ')}`);
      });
    });

    // Check specifically for owners-associations permissions
    console.log('\n\nChecking owners-associations permissions:');
    const permissions = await prisma.permission.findMany({
      where: { module: 'owners-associations' }
    });

    if (permissions.length > 0) {
      console.log('Module permissions exist:');
      permissions.forEach(p => {
        console.log(`  - ${p.action}`);
      });
    } else {
      console.log('owners-associations permissions NOT FOUND!');
      
      // Let's check what modules exist
      const allPermissions = await prisma.permission.findMany({
        distinct: ['module'],
        select: { module: true }
      });
      
      console.log('\nAvailable modules:');
      allPermissions.forEach(p => {
        console.log(`  - ${p.module}`);
      });
    }

    // Check if permissions exist in role_permissions
    const rolePermissions = await prisma.rolePermission.findMany({
      where: {
        permission: {
          module: 'owners-associations'
        }
      },
      include: {
        role: true,
        permission: true
      }
    });

    console.log('\n\nRole permissions for owners-associations:');
    if (rolePermissions.length > 0) {
      rolePermissions.forEach(rp => {
        console.log(`  Role: ${rp.role.name}, Action: ${rp.permission.action}`);
      });
    } else {
      console.log('  No role permissions found for owners-associations');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugUserPermissions();