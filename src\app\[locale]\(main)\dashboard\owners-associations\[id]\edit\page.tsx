import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { cookies, headers } from "next/headers";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { OwnersAssociationForm } from "../../_components/owners-association-form";

interface EditOwnersAssociationPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

async function getAssociation(id: string) {
  try {
    // Get dynamic host for multi-port development
    const headersList = await headers();
    const host = headersList.get('host') || 'localhost:3001';
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}`;
    
    // Get cookies for server-side authentication
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');
    
    const response = await fetch(
      `${baseUrl}/api/owners-associations/${id}`,
      { 
        cache: 'no-store',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': authToken ? `auth-token=${authToken.value}` : '',
        }
      }
    );

    if (!response.ok) {
      console.error(`Failed to fetch association: ${response.status} ${response.statusText}`);
      return null;
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error("Error fetching association:", error);
    return null;
  }
}

export default async function EditOwnersAssociationPage({ params }: EditOwnersAssociationPageProps) {
  const { id, locale } = await params;
  const t = await getTranslations({ locale, namespace: 'ownersAssociations' });
  const tCommon = await getTranslations({ locale, namespace: 'common' });

  const association = await getAssociation(id);

  if (!association) {
    notFound();
  }

  const name = locale === 'ar' ? association.name_ar : association.name_en;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href={`/${locale}/dashboard/owners-associations/${association.id}`}>
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">{tCommon('back')}</span>
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('editAssociation')}</h1>
          <p className="text-muted-foreground">{t('editDescription', { name })}</p>
        </div>
      </div>

      <OwnersAssociationForm 
        association={association} 
        isEdit 
      />
    </div>
  );
}