import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ownerPayoutPaymentSchema, canPayPayout } from "@/types/owner-payout";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

// POST /api/owner-payouts/[id]/pay - Mark an owner payout as paid
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for owner-payouts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "owner-payouts", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create owner payouts");
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.error("Invalid payout ID", "BAD_REQUEST", 400);
    }

    const body = await request.json();
    const validatedData = ownerPayoutPaymentSchema.parse(body);

    const payout = await db.ownerPayout.findUnique({
      where: { id },
      include: {
        owner: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            email: true,
          },
        },
      },
    });

    if (!payout) {
      return ApiResponseBuilder.error("Owner payout not found", "NOT_FOUND", 404);
    }

    if (!canPayPayout(payout.status)) {
      return ApiResponseBuilder.error(
        `Cannot mark payout as paid with status ${payout.status}`,
        "BAD_REQUEST",
        400
      );
    }

    const updateData: any = {
      status: "PAID",
      payment_method: validatedData.payment_method,
      reference_number: validatedData.reference_number,
      bank_transfer_ref: validatedData.bank_transfer_ref,
      paid_by: undefined, // TODO: Get from auth
      paid_at: new Date(),
    };

    // Add payment notes
    if (validatedData.notes) {
      updateData.notes = payout.notes 
        ? `${payout.notes}\n\nPayment Notes: ${validatedData.notes}`
        : `Payment Notes: ${validatedData.notes}`;
    }

    const paidPayout = await db.ownerPayout.update({
      where: { id },
      data: updateData,
      include: {
        owner: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            email: true,
          },
        },
        approver: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        payer: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        payout_details: {
          include: {
            property: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
              },
            },
          },
        },
      },
    });

    // Transform payout to handle Decimal serialization
    const transformedPayout = {
      ...paidPayout,
      total_rent_collected: paidPayout.total_rent_collected.toString(),
      management_fee: paidPayout.management_fee.toString(),
      other_deductions: paidPayout.other_deductions.toString(),
      net_amount: paidPayout.net_amount.toString(),
      payout_details: paidPayout.payout_details.map(detail => ({
        ...detail,
        rent_collected: detail.rent_collected.toString(),
        management_fee: detail.management_fee.toString(),
        net_amount: detail.net_amount.toString(),
      })),
    };

    return ApiResponseBuilder.success(transformedPayout);
  } catch (error: any) {
    console.error("Error marking payout as paid:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to mark payout as paid", "INTERNAL_ERROR", 500);
  }
}