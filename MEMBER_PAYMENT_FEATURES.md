# Member Payment Features - User Guide

## How to Access Member Payment Features

### 1. Navigate to Owners Association
- Go to Dashboard → Owners Associations
- Click on any association to view its details

### 2. Find the Payments Tab
- In the association detail page, you'll see tabs: Overview, Members, Subscriptions, Transactions, **Payments**, Reports
- Click on the **"Payments"** tab (or "المدفوعات" in Arabic)

### 3. Member Payments Table Features

#### View Payment Status
- **Status Badges**:
  - 🟢 **Paid** (Green) - Member has fully paid
  - 🔴 **Overdue** (Red) - Payment is past due date
  - 🟡 **Partially Paid** (Yellow) - Member has made partial payment
  - ⚪ **Unpaid** (Gray) - Not yet due

#### Payment Progress
- Visual progress bars show payment completion percentage
- Installment count shows number of partial payments made

#### Actions Available
- **Record Payment** - Record full or partial payment for a member
- **View Details** - View complete payment history and installments

### 4. Recording Payments

When you click "Record Payment":
1. A dialog opens showing:
   - Member name and unit
   - Subscription details
   - Current payment status
   - Remaining balance

2. Enter payment details:
   - Payment amount (can be partial)
   - Payment method
   - Reference number (optional)
   - Notes (optional)

3. Option to create a linked transaction automatically

### 5. Linking Transactions to Members

When adding/editing transactions:
1. Check "Member-related transaction" checkbox
2. Select the member from dropdown
3. The transaction will be linked to that member

### 6. Payment Summary Statistics

At the top of the Payments tab, you'll see:
- **Total Due** - Total amount owed by all members
- **Total Paid** - Total amount collected
- **Outstanding** - Remaining balance
- **Collection Rate** - Percentage of payments collected

## Technical Details

### Database Tables Involved
- `subscription_payments` - Main payment tracking
- `subscription_payment_installments` - Partial payment records
- `association_transactions` - Linked financial transactions

### API Endpoints
- GET `/api/owners-associations/{id}/subscription-payments` - List all payments
- POST `/api/owners-associations/{id}/subscription-payments` - Create payment record
- POST `/api/owners-associations/{id}/subscription-payments/{paymentId}` - Record payment
- GET `/api/owners-associations/{id}/members/{memberId}/payments` - Member payment history

### UI Components
- `member-payments-table.tsx` - Main payments table
- `record-payment-dialog.tsx` - Payment recording dialog
- `payment-details-dialog.tsx` - Payment history viewer
- `add-transaction-form.tsx` - Enhanced with member linking
- `edit-transaction-form.tsx` - Enhanced with member linking

## Troubleshooting

### Error: "You don't have permission to view subscription payments"
- Ensure you have READ permission for owners-associations module
- Check that you're logged in with proper credentials

### Translation Errors
- Fixed by updating `statusLabel` in translation files
- Both English and Arabic translations are properly configured

### Database Errors
- Run the migration script: `node apply-payment-migration.js`
- This adds required columns and relations to the database

## Next Steps

After the database migration is applied:
1. The member relation in transactions will be fully functional
2. You can track which member made each payment
3. Financial reports will include member-level details
4. Payment history will be available for each member