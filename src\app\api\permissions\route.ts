import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { APPLICATION_MODULES } from "@/types/user";

// GET /api/permissions - Get all permissions grouped by module
export async function GET(request: NextRequest) {
  try {
    // Get all permissions from database
    const permissions = await db.permission.findMany({
      orderBy: [
        { module: "asc" },
        { action: "asc" },
      ],
    });

    // Group permissions by module
    const permissionsByModule: Record<string, any[]> = {};
    
    for (const permission of permissions) {
      if (!permissionsByModule[permission.module]) {
        permissionsByModule[permission.module] = [];
      }
      permissionsByModule[permission.module].push(permission);
    }

    // Create structured response with module information
    const modules = APPLICATION_MODULES.map(module => ({
      id: module.id,
      name: module.name,
      description: module.description,
      permissions: permissionsByModule[module.id] || [],
    }));

    return NextResponse.json({
      modules,
      all_permissions: permissions,
    });
  } catch (error) {
    console.error("Error fetching permissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch permissions" },
      { status: 500 }
    );
  }
}

// POST /api/permissions - Create permissions for modules (used for seeding)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { modules } = body;

    if (!modules || !Array.isArray(modules)) {
      return NextResponse.json(
        { error: "Invalid request body. Expected modules array." },
        { status: 400 }
      );
    }

    const createdPermissions = [];

    for (const moduleId of modules) {
      // Find the module info
      const moduleInfo = APPLICATION_MODULES.find(m => m.id === moduleId);
      if (!moduleInfo) {
        continue;
      }

      // Create permissions for each action
      const actions = ["CREATE", "READ", "UPDATE", "DELETE"] as const;
      
      for (const action of actions) {
        const permission = await db.permission.upsert({
          where: {
            module_action: {
              module: moduleId,
              action: action,
            },
          },
          update: {},
          create: {
            module: moduleId,
            action: action,
            description: `${action.toLowerCase()} access for ${moduleInfo.name}`,
          },
        });

        createdPermissions.push(permission);
      }
    }

    return NextResponse.json({
      message: "Permissions created successfully",
      permissions: createdPermissions,
    });
  } catch (error) {
    console.error("Error creating permissions:", error);
    return NextResponse.json(
      { error: "Failed to create permissions" },
      { status: 500 }
    );
  }
}
