"use client";

import { useState, useEffect, useCallback } from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Settings2, Search, Calendar } from "lucide-react";
import { OwnerPayoutWithRelations } from "@/types/owner-payout";
import { useTranslations, useLocale } from "next-intl";
import { apiClient } from "@/lib/api-client";

interface OwnerPayoutsDataTableProps<TData, TValue> {
  columns: (
    t: any,
    locale: string,
    onRefresh?: () => void
  ) => ColumnDef<TData, TValue>[];
}

export function OwnerPayoutsDataTable<TData, TValue>({
  columns,
}: OwnerPayoutsDataTableProps<TData, TValue>) {
  const [data, setData] = useState<TData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [totalPages, setTotalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);

  const t = useTranslations();
  const locale = useLocale();
  
  // Prepare column translations
  const columnTranslations = {
    payoutNumber: t("ownerPayouts.payoutNumber"),
    owner: t("ownerPayouts.owner"),
    payoutDate: t("ownerPayouts.payoutDate"),
    period: t("ownerPayouts.period"),
    rentCollected: t("ownerPayouts.rentCollected"),
    managementFee: t("ownerPayouts.managementFee"),
    netAmount: t("ownerPayouts.netAmount"),
    status: t("common.status"),
    actions: t("common.actions"),
    approve: t("ownerPayouts.approve"),
    reject: t("ownerPayouts.reject"),
    markAsPaid: t("ownerPayouts.markAsPaid"),
    view: t("common.view"),
    statusPending: t("ownerPayouts.status.pending"),
    statusApproved: t("ownerPayouts.status.approved"),
    statusPaid: t("ownerPayouts.status.paid"),
    statusCancelled: t("ownerPayouts.status.cancelled"),
  };

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: (pagination.pageIndex + 1).toString(),
        pageSize: pagination.pageSize.toString(),
      });

      if (globalFilter) {
        params.append("search", globalFilter);
      }

      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }


      if (sorting.length > 0) {
        params.append("sortBy", sorting[0].id);
        params.append("sortOrder", sorting[0].desc ? "desc" : "asc");
      }

      const result = await apiClient.get(`/api/owner-payouts?${params}`);

      if (result.success) {
        setData(result.data);
        setTotalPages(result.pagination?.totalPages || 0);
        setTotalCount(result.pagination?.total || 0);
      } else {
        throw new Error(result.message || "Failed to fetch owner payouts");
      }
    } catch (error) {
      console.error("Error fetching owner payouts:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  }, [pagination, globalFilter, statusFilter, sorting]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const table = useReactTable({
    data,
    columns: columns(columnTranslations, locale, fetchData),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    manualPagination: true,
    pageCount: totalPages,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      pagination,
    },
  });

  if (error) {
    return (
      <div className="rounded-md border p-8 text-center">
        <p className="text-destructive">Error: {error}</p>
        <Button
          variant="outline"
          onClick={fetchData}
          className="mt-4"
        >
          {t("common.retry")}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t("ownerPayouts.searchPlaceholder")}
              value={globalFilter}
              onChange={(event) => setGlobalFilter(event.target.value)}
              className="pl-8 max-w-sm"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t("ownerPayouts.allStatuses")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("ownerPayouts.allStatuses")}</SelectItem>
              <SelectItem value="PENDING">{t("ownerPayouts.status.pending")}</SelectItem>
              <SelectItem value="APPROVED">{t("ownerPayouts.status.approved")}</SelectItem>
              <SelectItem value="PAID">{t("ownerPayouts.status.paid")}</SelectItem>
              <SelectItem value="CANCELLED">{t("ownerPayouts.status.cancelled")}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Settings2 className="mr-2 h-4 w-4" />
                {t("common.columns")}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Results count */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {loading ? (
            t("common.loading")
          ) : (
            t("common.showing", {
              from: pagination.pageIndex * pagination.pageSize + 1,
              to: Math.min(
                (pagination.pageIndex + 1) * pagination.pageSize,
                totalCount
              ),
              total: totalCount,
            })
          )}
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border overflow-hidden">
        <div className="w-full overflow-x-auto [&_[data-slot=table-container]]:overflow-x-visible">
          <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={table.getAllColumns().length}
                  className="h-24 text-center"
                >
                  {t("common.loading")}
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={table.getAllColumns().length}
                  className="h-24 text-center"
                >
                  {t("ownerPayouts.noResults")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="text-sm text-muted-foreground">
          {t("common.page")} {pagination.pageIndex + 1} {t("common.of")}{" "}
          {totalPages}
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage() || loading}
          >
            {t("common.previous")}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage() || loading}
          >
            {t("common.next")}
          </Button>
        </div>
      </div>
    </div>
  );
}