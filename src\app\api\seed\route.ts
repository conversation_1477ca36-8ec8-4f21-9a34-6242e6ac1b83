import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { Decimal } from "@prisma/client/runtime/library";
import bcrypt from "bcryptjs";

export async function POST(request: NextRequest) {
  try {
    console.log('🌱 Starting database seeding...');
    console.log('Database connection test...');
    
    // Test database connection first
    await db.$queryRaw`SELECT 1`;
    console.log('✅ Database connection successful');

    // Create admin role
    console.log('Creating admin role...');
    const adminRole = await db.role.upsert({
      where: { name: 'Admin' },
      update: {},
      create: {
        name: 'Admin',
        description: 'System Administrator',
        is_system: true,
      },
    });
    console.log('✅ Admin role created:', adminRole.name);

    // Create manager role
    const managerRole = await db.role.upsert({
      where: { name: 'Property Manager' },
      update: {},
      create: {
        name: 'Property Manager',
        description: 'Property Management Staff',
        is_system: false,
      },
    });
    console.log('✅ Manager role created:', managerRole.name);

    // Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 10);
    const adminUser = await db.user.upsert({
      where: { username: 'admin' },
      update: {},
      create: {
        username: 'admin',
        email: '<EMAIL>',
        password_hash: hashedPassword,
        first_name: 'System',
        last_name: 'Administrator',
        status: 'ACTIVE',
        email_verified: true,
      },
    });
    console.log('✅ Admin user created:', adminUser.username);

    // Assign admin role to user
    console.log('Assigning admin role to user...');
    const existingUserRole = await db.userRole.findFirst({
      where: {
        user_id: adminUser.id,
        role_id: adminRole.id,
      }
    });

    if (!existingUserRole) {
      await db.userRole.create({
        data: {
          user_id: adminUser.id,
          role_id: adminRole.id,
          assigned_by: adminUser.id, // Self-assigned for bootstrap
        },
      });
      console.log('✅ Admin role assigned to user');
    } else {
      console.log('Admin role already assigned to user');
    }

    // Create property types
    const propertyTypes = [
      {
        name_en: 'Villa',
        name_ar: 'فيلا',
        description_en: 'Standalone villa property',
        description_ar: 'عقار فيلا منفصلة',
      },
      {
        name_en: 'Apartment',
        name_ar: 'شقة',
        description_en: 'Apartment unit',
        description_ar: 'وحدة شقة',
      },
      {
        name_en: 'Commercial Building',
        name_ar: 'مبنى تجاري',
        description_en: 'Commercial office building',
        description_ar: 'مبنى مكاتب تجاري',
      },
      {
        name_en: 'Land',
        name_ar: 'أرض',
        description_en: 'Empty land plot',
        description_ar: 'قطعة أرض فارغة',
      },
    ];

    const createdPropertyTypes = [];
    for (const type of propertyTypes) {
      console.log(`Creating property type: ${type.name_en}...`);
      const existingPropertyType = await db.propertyType.findFirst({
        where: { name_en: type.name_en }
      });

      let propertyType;
      if (existingPropertyType) {
        propertyType = existingPropertyType;
        console.log(`Property type already exists: ${propertyType.name_en}`);
      } else {
        propertyType = await db.propertyType.create({
          data: {
            ...type,
          },
        });
        console.log('✅ Property type created:', propertyType.name_en);
      }
      createdPropertyTypes.push(propertyType);
    }

    // Create property owners
    const propertyOwners = [
      {
        name_en: 'Ahmed Al-Rashid',
        name_ar: 'أحمد الراشد',
        email: '<EMAIL>',
        phone: '+968 2123 4567',
        mobile: '+968 9123 4567',
        address_en: 'Al-Khuwair, Muscat, Oman',
        address_ar: 'الخوير، مسقط، عمان',
        tax_id: 'TAX001',
        bank_name: 'Bank Muscat',
        bank_account_number: '**********',
        bank_iban: '************************',
        status: 'ACTIVE' as const,
      },
      {
        name_en: 'Fatima Al-Zahra',
        name_ar: 'فاطمة الزهراء',
        email: '<EMAIL>',
        phone: '+968 2234 5678',
        mobile: '+968 9234 5678',
        address_en: 'Ruwi, Muscat, Oman',
        address_ar: 'روي، مسقط، عمان',
        tax_id: 'TAX002',
        bank_name: 'HSBC Bank Oman',
        bank_account_number: '**********',
        bank_iban: '************************',
        status: 'ACTIVE' as const,
      },
      {
        name_en: 'Mohammed Al-Balushi',
        name_ar: 'محمد البلوشي',
        email: '<EMAIL>',
        phone: '+968 2345 6789',
        mobile: '+968 9345 6789',
        address_en: 'Seeb, Muscat, Oman',
        address_ar: 'السيب، مسقط، عمان',
        tax_id: 'TAX003',
        bank_name: 'National Bank of Oman',
        bank_account_number: '**********',
        bank_iban: '************************',
        status: 'ACTIVE' as const,
      },
    ];

    const createdPropertyOwners = [];
    for (const owner of propertyOwners) {
      console.log(`Creating property owner: ${owner.name_en}...`);
      const existingPropertyOwner = await db.propertyOwner.findFirst({
        where: { email: owner.email }
      });

      let propertyOwner;
      if (existingPropertyOwner) {
        propertyOwner = existingPropertyOwner;
        console.log(`Property owner already exists: ${propertyOwner.name_en}`);
      } else {
        propertyOwner = await db.propertyOwner.create({
          data: {
            ...owner,
            created_by: adminUser.id,
            updated_by: adminUser.id,
          },
        });
        console.log('✅ Property owner created:', propertyOwner.name_en);
      }
      createdPropertyOwners.push(propertyOwner);
    }

    // Create amenities
    const amenities = [
      {
        name_en: 'Swimming Pool',
        name_ar: 'مسبح',
      },
      {
        name_en: 'Gym',
        name_ar: 'صالة رياضية',
      },
      {
        name_en: 'Parking',
        name_ar: 'موقف سيارات',
      },
      {
        name_en: 'Garden',
        name_ar: 'حديقة',
      },
      {
        name_en: 'Security',
        name_ar: 'أمن',
      },
      {
        name_en: 'Central AC',
        name_ar: 'تكييف مركزي',
      },
    ];

    const createdAmenities = [];
    for (const amenity of amenities) {
      console.log(`Creating amenity: ${amenity.name_en}...`);
      const existingAmenity = await db.amenity.findFirst({
        where: { name_en: amenity.name_en }
      });

      let createdAmenity;
      if (existingAmenity) {
        createdAmenity = existingAmenity;
        console.log(`Amenity already exists: ${createdAmenity.name_en}`);
      } else {
        createdAmenity = await db.amenity.create({
          data: {
            ...amenity,
          },
        });
        console.log('✅ Amenity created:', createdAmenity.name_en);
      }
      createdAmenities.push(createdAmenity);
    }

    // Create sample property
    console.log('Creating sample property...');
    const existingProperty = await db.property.findFirst({
      where: { 
        name_en: 'Luxury Villa in Al-Khuwair',
        address_en: 'Street 123, Al-Khuwair, Muscat, Oman'
      }
    });

    let sampleProperty;
    if (existingProperty) {
      sampleProperty = existingProperty;
      console.log('Sample property already exists:', sampleProperty.name_en);
    } else {
      sampleProperty = await db.property.create({
        data: {
          name_en: 'Luxury Villa in Al-Khuwair',
          name_ar: 'فيلا فاخرة في الخوير',
          address_en: 'Street 123, Al-Khuwair, Muscat, Oman',
          address_ar: 'شارع 123، الخوير، مسقط، عمان',
          property_type_id: createdPropertyTypes[0].id, // Villa
          base_rent: new Decimal('1200.000'),
          status: 'AVAILABLE',
          total_area: new Decimal('500.00'),
          floors_count: 2,
          parking_spaces: 3,
          created_by: adminUser.id,
          updated_by: adminUser.id,
        },
      });
      console.log('Sample property created:', sampleProperty.name_en);
    }
    
    // Assign owner to property
    console.log('Assigning owner to property...');
    if (!sampleProperty.owner_id) {
      await db.property.update({
        where: { id: sampleProperty.id },
        data: {
          owner_id: createdPropertyOwners[0].id, // Ahmed Al-Rashid
        },
      });
      console.log('Owner assigned to property successfully');
    } else {
      console.log('Property already has an owner');
    }

    // Add amenities to property (Swimming Pool, Parking, Garden, Central AC)
    console.log('Adding amenities to property...');
    const amenityNames = ['Swimming Pool', 'Parking', 'Garden', 'Central AC'];
    for (const amenityName of amenityNames) {
      const amenity = createdAmenities.find(a => a.name_en === amenityName);
      if (amenity) {
        const existingAmenity = await db.propertyAmenity.findFirst({
          where: {
            property_id: sampleProperty.id,
            amenity_id: amenity.id,
          }
        });

        if (!existingAmenity) {
          await db.propertyAmenity.create({
            data: {
              property_id: sampleProperty.id,
              amenity_id: amenity.id,
            },
          });
          console.log(`Added amenity: ${amenityName}`);
        } else {
          console.log(`Amenity already exists: ${amenityName}`);
        }
      }
    }

    console.log('✅ Sample property created:', sampleProperty.name_en);
    console.log('🎉 Database seeding completed successfully!');

    return ApiResponseBuilder.success({
      message: 'Database seeded successfully',
      data: {
        roles: 2,
        users: 1,
        userRoles: 1,
        propertyTypes: propertyTypes.length,
        propertyOwners: propertyOwners.length,
        amenities: amenities.length,
        properties: 1,
        propertyOwnerships: 1,
        propertyAmenities: 4,
      },
    });

  } catch (error) {
    console.error('❌ Error during seeding:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      name: error instanceof Error ? error.name : 'Unknown error type'
    });
    
    return ApiResponseBuilder.error(
      `Failed to seed database: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'SEED_ERROR',
      500,
      process.env.NODE_ENV === 'development' ? {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined
      } : undefined
    );
  }
}