"use client";

import { useTranslations, useLocale } from "next-intl";
import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Building2,
  Calendar,
  Crown,
  Mail,
  Phone,
  Users,
  Clock,
  MapPin,
  FileText,
  Banknote,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useRTL } from "@/hooks/use-rtl";
import { formatCurrency } from "@/lib/utils";
import type { OwnersAssociationWithRelations } from "@/types/owners-association";

interface AssociationOverviewProps {
  association: OwnersAssociationWithRelations;
}

export function AssociationOverview({ association }: AssociationOverviewProps) {
  const locale = useLocale();
  const { isRTL } = useRTL();
  const t = useTranslations("ownersAssociations.overview");
  const dateLocale = locale === 'ar' ? ar : enUS;

  const name = locale === 'ar' ? association.name_ar : association.name_en;
  const propertyName = locale === 'ar' 
    ? association.property?.name_ar 
    : association.property?.name_en;

  const establishmentDate = new Date(association.establishment_date);
  const createdDate = new Date(association.created_at);

  // Calculate some stats
  const totalMembers = association._count?.members || 0;
  const totalSubscriptions = association._count?.subscriptions || 0;
  const totalTransactions = association._count?.transactions || 0;

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
            <Building2 className="h-5 w-5" />
            {t("basicInformation")}
          </CardTitle>
          <CardDescription>
            {t("basicInformationDescription")}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className={cn("space-y-2", isRTL && "text-right")}>
              <div className="text-sm font-medium text-muted-foreground">
                {t("associationName")}
              </div>
              <div className="text-lg font-semibold">{name}</div>
            </div>

            <div className={cn("space-y-2", isRTL && "text-right")}>
              <div className="text-sm font-medium text-muted-foreground">
                {t("property")}
              </div>
              <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span>{propertyName}</span>
              </div>
              {(association.property?.address_en || association.property?.address_ar) && (
                <div className="text-sm text-muted-foreground">
                  {locale === 'ar' ? association.property.address_ar : association.property.address_en}
                </div>
              )}
            </div>

            <div className={cn("space-y-2", isRTL && "text-right")}>
              <div className="text-sm font-medium text-muted-foreground">
                {t("establishmentDate")}
              </div>
              <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>{format(establishmentDate, "dd MMMM yyyy", { locale: dateLocale })}</span>
              </div>
            </div>

            <div className={cn("space-y-2", isRTL && "text-right")}>
              <div className="text-sm font-medium text-muted-foreground">
                {t("president")}
              </div>
              <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <Crown className="h-4 w-4 text-yellow-500" />
                <span>{association.president_name}</span>
              </div>
            </div>

            <div className={cn("space-y-2", isRTL && "text-right")}>
              <div className="text-sm font-medium text-muted-foreground">
                {t("managementTermDuration")}
              </div>
              <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>{association.management_term_duration} {t("months")}</span>
              </div>
            </div>

            <div className={cn("space-y-2", isRTL && "text-right")}>
              <div className="text-sm font-medium text-muted-foreground">
                {t("status")}
              </div>
              <Badge variant="outline">
                {t("statusActive")}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
            <Phone className="h-5 w-5" />
            {t("contactInformation")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {association.contact_email || association.contact_phone ? (
            <div className="space-y-3">
              {association.contact_email && (
                <div className={cn("flex items-center gap-3", isRTL && "flex-row-reverse")}>
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span>{association.contact_email}</span>
                </div>
              )}
              {association.contact_phone && (
                <div className={cn("flex items-center gap-3", isRTL && "flex-row-reverse")}>
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{association.contact_phone}</span>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-6 text-muted-foreground">
              {t("noContactInfo")}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("totalMembers")}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalMembers}</div>
            <p className="text-xs text-muted-foreground">
              {t("membersDescription")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("totalSubscriptions")}</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalSubscriptions}</div>
            <p className="text-xs text-muted-foreground">
              {t("subscriptionsDescription")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("totalTransactions")}</CardTitle>
            <Banknote className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTransactions}</div>
            <p className="text-xs text-muted-foreground">
              {t("transactionsDescription")}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Metadata */}
      <Card>
        <CardHeader>
          <CardTitle>{t("metadata")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className={cn("space-y-2", isRTL && "text-right")}>
              <div className="text-sm font-medium text-muted-foreground">
                {t("createdAt")}
              </div>
              <div className="text-sm">
                {format(createdDate, "dd MMMM yyyy, HH:mm", { locale: dateLocale })}
              </div>
            </div>

            <div className={cn("space-y-2", isRTL && "text-right")}>
              <div className="text-sm font-medium text-muted-foreground">
                {t("lastUpdated")}
              </div>
              <div className="text-sm">
                {format(new Date(association.updated_at), "dd MMMM yyyy, HH:mm", { locale: dateLocale })}
              </div>
            </div>

            {association.creator && (
              <div className={cn("space-y-2", isRTL && "text-right")}>
                <div className="text-sm font-medium text-muted-foreground">
                  {t("createdBy")}
                </div>
                <div className="text-sm">
                  {association.creator.first_name} {association.creator.last_name}
                </div>
              </div>
            )}

            {association.updater && (
              <div className={cn("space-y-2", isRTL && "text-right")}>
                <div className="text-sm font-medium text-muted-foreground">
                  {t("updatedBy")}
                </div>
                <div className="text-sm">
                  {association.updater.first_name} {association.updater.last_name}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}