-- This SQL script enhances the subscription payment tracking system
-- Run this after generating the Prisma client

-- First, add the new columns to subscription_payments table
ALTER TABLE `subscription_payments` 
ADD COLUMN IF NOT EXISTS `amount_due` DECIMAL(10, 3) NOT NULL DEFAULT 0 AFTER `due_date`,
ADD COLUMN IF NOT EXISTS `amount_paid` DECIMAL(10, 3) NOT NULL DEFAULT 0 AFTER `amount`,
ADD COLUMN IF NOT EXISTS `remaining_balance` DECIMAL(10, 3) GENERATED ALWAYS AS (`amount_due` - `amount_paid`) STORED,
ADD COLUMN IF NOT EXISTS `transaction_id` INT NULL AFTER `reference_number`;

-- Add foreign key constraint for transaction_id
ALTER TABLE `subscription_payments`
ADD CONSTRAINT `fk_subscription_payment_transaction` 
FOREIGN KEY (`transaction_id`) REFERENCES `association_transactions`(`id`) ON DELETE SET NULL;

-- Update existing records to set amount_due from amount
UPDATE `subscription_payments` 
SET `amount_due` = `amount` 
WHERE `amount_due` = 0;

-- Update amount_paid based on status
UPDATE `subscription_payments` 
SET `amount_paid` = `amount_due` 
WHERE `status` = 'PAID';

UPDATE `subscription_payments` 
SET `amount_paid` = 0 
WHERE `status` IN ('UNPAID', 'OVERDUE');

-- For PARTIALLY_PAID, we'll need to manually update or set to 50% as default
UPDATE `subscription_payments` 
SET `amount_paid` = `amount_due` * 0.5 
WHERE `status` = 'PARTIALLY_PAID';