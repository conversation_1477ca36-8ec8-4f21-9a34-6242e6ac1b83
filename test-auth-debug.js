const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testAuth() {
  try {
    console.log('Testing authentication...');
    
    // First, check what the stored password hash is
    const user = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    });
    
    if (!user) {
      console.log('User not found');
      return;
    }
    
    console.log('User found:', user.username, user.email);
    console.log('Password hash length:', user.password_hash.length);
    console.log('Password hash preview:', user.password_hash.substring(0, 20) + '...');
    
    // Test password verification
    const testPassword = '123456';
    const isValid = await bcrypt.compare(testPassword, user.password_hash);
    console.log('Password verification result:', isValid);
    
    if (!isValid) {
      console.log('Password does not match. Creating new hash...');
      const newHash = await bcrypt.hash('123456', 12);
      console.log('New hash for 123456:', newHash.substring(0, 20) + '...');
      
      // Update the user's password
      await prisma.user.update({
        where: { id: user.id },
        data: { password_hash: newHash }
      });
      console.log('Password updated successfully');
    }
  } catch (error) {
    console.error('Auth test error:', error);
  } finally {
    await prisma.$disconnect();
    process.exit(0);
  }
}

testAuth();