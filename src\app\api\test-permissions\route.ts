import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken } from "@/lib/auth";
import { hasPermission, getUserPermissions } from "@/lib/permissions";

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    console.log("Decoded token:", decoded);

    // Test getUserPermissions
    const permissions = await getUserPermissions(decoded.id);
    console.log("User permissions:", permissions);

    // Test hasPermission with different cases
    const canReadLower = await hasPermission(decoded.id, "owners-associations", "read" as any);
    const canReadUpper = await hasPermission(decoded.id, "owners-associations", "READ");
    
    // Check what's in the database
    const dbPermissions = await db.permission.findMany({
      where: { module: "owners-associations" }
    });

    const rolePermissions = await db.rolePermission.findMany({
      where: {
        permission: {
          module: "owners-associations"
        }
      },
      include: {
        role: true,
        permission: true
      }
    });

    return ApiResponseBuilder.success({
      decoded,
      permissions,
      canReadLower,
      canReadUpper,
      dbPermissions,
      rolePermissions
    });
  } catch (error: any) {
    console.error("Error in test permissions:", error);
    return ApiResponseBuilder.error(error.message || "Internal error", "INTERNAL_ERROR", 500);
  }
}