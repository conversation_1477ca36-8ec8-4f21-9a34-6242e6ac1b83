import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { expenseApiSchema, expenseFilterSchema } from "@/types/expense";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { ApiResponseBuilder } from "@/lib/api-response";

const prisma = new PrismaClient();

// GET /api/expenses - Get all expenses with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for expenses
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "expenses", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view expenses");
    }

    console.log("Expenses API: GET request received");

    // For development, skip authentication
    if (process.env.NODE_ENV === "development") {
      console.log("Expenses API: Development mode - skipping authentication");
    }

    const { searchParams } = new URL(request.url);
    
    // Parse and validate query parameters
    const filterParams = {
      page: parseInt(searchParams.get("page") || "1", 10),
      pageSize: parseInt(searchParams.get("pageSize") || "10", 10),
      sortBy: searchParams.get("sortBy") || "created_at",
      sortOrder: (searchParams.get("sortOrder") || "desc") as "asc" | "desc",
      search: searchParams.get("search") || undefined,
      category_id: searchParams.get("category_id") ? parseInt(searchParams.get("category_id")!, 10) : undefined,
      status: searchParams.get("status") as any || undefined,
      payment_method: searchParams.get("payment_method") as any || undefined,
      date_from: searchParams.get("date_from") || undefined,
      date_to: searchParams.get("date_to") || undefined,
      amount_min: searchParams.get("amount_min") ? parseFloat(searchParams.get("amount_min")!) : undefined,
      amount_max: searchParams.get("amount_max") ? parseFloat(searchParams.get("amount_max")!) : undefined,
      paid_by: searchParams.get("paid_by") || undefined,
    };

    const validationResult = expenseFilterSchema.safeParse(filterParams);
    if (!validationResult.success) {
      console.log("Expenses API: Filter validation failed:", validationResult.error);
      return NextResponse.json(
        { error: "Invalid filter parameters", details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const filters = validationResult.data;

    // Build where clause
    const whereClause: any = {};

    if (filters.search) {
      whereClause.OR = [
        { description: { contains: filters.search } },
        { paid_by: { contains: filters.search } },
        { notes: { contains: filters.search } }
      ];
    }

    if (filters.category_id) {
      whereClause.category_id = filters.category_id;
    }

    if (filters.status) {
      whereClause.status = filters.status;
    }

    if (filters.payment_method) {
      whereClause.payment_method = filters.payment_method;
    }

    if (filters.date_from || filters.date_to) {
      whereClause.date = {};
      if (filters.date_from) {
        whereClause.date.gte = new Date(filters.date_from);
      }
      if (filters.date_to) {
        whereClause.date.lte = new Date(filters.date_to);
      }
    }

    if (filters.amount_min !== undefined || filters.amount_max !== undefined) {
      whereClause.amount = {};
      if (filters.amount_min !== undefined) {
        whereClause.amount.gte = filters.amount_min;
      }
      if (filters.amount_max !== undefined) {
        whereClause.amount.lte = filters.amount_max;
      }
    }

    if (filters.paid_by) {
      whereClause.paid_by = { contains: filters.paid_by };
    }

    // Calculate pagination
    const skip = (filters.page - 1) * filters.pageSize;

    // Get total count
    const totalCount = await prisma.expense.count({ where: whereClause });

    // Get expenses
    const expenses = await prisma.expense.findMany({
      where: whereClause,
      skip,
      take: filters.pageSize,
      orderBy: { [filters.sortBy]: filters.sortOrder },
      include: {
        category: true,
        attachments: true,
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        approver: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });

    const totalPages = Math.ceil(totalCount / filters.pageSize);

    console.log(`Expenses API: Expenses retrieved: ${expenses.length} of ${totalCount} total`);
    
    return NextResponse.json({
      expenses,
      pagination: {
        page: filters.page,
        pageSize: filters.pageSize,
        totalCount,
        totalPages,
        hasNext: filters.page < totalPages,
        hasPrev: filters.page > 1
      }
    });

  } catch (error) {
    console.error("Expenses API: Error fetching expenses:", error);
    return NextResponse.json(
      { error: "Failed to fetch expenses" },
      { status: 500 }
    );
  }
}

// POST /api/expenses - Create new expense
export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for expenses
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "expenses", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create expenses");
    }

    console.log("Expenses API: POST request received");

    // For development, skip authentication
    if (process.env.NODE_ENV === "development") {
      console.log("Expenses API: Development mode - skipping authentication");
    }

    const body = await request.json();
    console.log("Expenses API: Request body:", body);

    // Validate the request body
    const validationResult = expenseApiSchema.safeParse(body);
    if (!validationResult.success) {
      console.log("Expenses API: Validation failed:", validationResult.error);
      return NextResponse.json(
        { 
          error: "Validation failed", 
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Verify category exists
    const category = await prisma.expenseCategory.findUnique({
      where: { id: data.category_id }
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Calculate next due date for recurring expenses
    let nextDueDate = null;
    if (data.is_recurring && data.recurring_frequency) {
      const currentDate = new Date(data.date);
      switch (data.recurring_frequency) {
        case "MONTHLY":
          nextDueDate = new Date(currentDate.setMonth(currentDate.getMonth() + 1));
          break;
        case "QUARTERLY":
          nextDueDate = new Date(currentDate.setMonth(currentDate.getMonth() + 3));
          break;
        case "YEARLY":
          nextDueDate = new Date(currentDate.setFullYear(currentDate.getFullYear() + 1));
          break;
      }
    }

    // Create the expense
    const expense = await prisma.expense.create({
      data: {
        date: data.date,
        description: data.description,
        amount: data.amount,
        category_id: data.category_id,
        payment_method: data.payment_method,
        paid_by: data.paid_by,
        notes: data.notes || null,
        status: data.status,
        is_recurring: data.is_recurring,
        recurring_frequency: data.recurring_frequency || null,
        next_due_date: nextDueDate,
        created_by: 1, // TODO: Get from authenticated user
        updated_by: 1, // TODO: Get from authenticated user
      },
      include: {
        category: true,
        attachments: true,
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });

    console.log("Expenses API: Expense created:", expense.id);
    return NextResponse.json(expense, { status: 201 });

  } catch (error) {
    console.error("Expenses API: Error creating expense:", error);
    return NextResponse.json(
      { error: "Failed to create expense" },
      { status: 500 }
    );
  }
}
