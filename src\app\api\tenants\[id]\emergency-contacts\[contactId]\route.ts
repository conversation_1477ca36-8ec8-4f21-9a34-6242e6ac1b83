import { NextRequest } from "next/server";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { z } from "zod";

const emergencyContactUpdateSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  relationship: z.string().min(1, "Relationship is required").optional(),
  phone: z.string().min(1, "Phone is required").optional(),
  email: z.string().email().optional().or(z.literal("")).optional(),
  is_primary: z.boolean().optional(),
});

interface RouteParams {
  params: Promise<{
    id: string;
    contactId: string;
  }>;
}

// PUT /api/tenants/:id/emergency-contacts/:contactId - Update an emergency contact
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "tenants", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update tenants");
    }

    const { id, contactId: contactIdParam } = await params;
    const tenantId = parseInt(id);
    const contactId = parseInt(contactIdParam);
    
    if (isNaN(tenantId) || isNaN(contactId)) {
      return ApiResponseBuilder.badRequest("Invalid ID");
    }

    // Check if contact exists
    const existingContact = await db.emergencyContact.findFirst({
      where: { 
        id: contactId,
        tenant_id: tenantId 
      },
    });

    if (!existingContact) {
      return ApiResponseBuilder.notFound("Emergency contact");
    }

    const body = await request.json();
    const validationResult = emergencyContactUpdateSchema.safeParse(body);
    
    if (!validationResult.success) {
      return ApiResponseBuilder.validationError(validationResult.error);
    }

    const data = validationResult.data;

    // If this is set as primary, unset other primary contacts
    if (data.is_primary === true) {
      await db.emergencyContact.updateMany({
        where: { 
          tenant_id: tenantId,
          id: { not: contactId }
        },
        data: { is_primary: false },
      });
    }

    const updatedContact = await db.emergencyContact.update({
      where: { id: contactId },
      data: {
        ...data,
        email: data.email === "" ? null : data.email,
        updated_by: decoded.id,
      },
    });

    return ApiResponseBuilder.success(updatedContact);
  } catch (error) {
    console.error("Update Emergency Contact Error:", error);
    return ApiResponseBuilder.error(
      "Failed to update emergency contact",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// DELETE /api/tenants/:id/emergency-contacts/:contactId - Delete an emergency contact
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "tenants", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update tenants");
    }

    const { id, contactId: contactIdParam } = await params;
    const tenantId = parseInt(id);
    const contactId = parseInt(contactIdParam);
    
    if (isNaN(tenantId) || isNaN(contactId)) {
      return ApiResponseBuilder.badRequest("Invalid ID");
    }

    // Check if contact exists
    const existingContact = await db.emergencyContact.findFirst({
      where: { 
        id: contactId,
        tenant_id: tenantId 
      },
    });

    if (!existingContact) {
      return ApiResponseBuilder.notFound("Emergency contact");
    }

    await db.emergencyContact.delete({
      where: { id: contactId },
    });

    return ApiResponseBuilder.success({ message: "Emergency contact deleted successfully" });
  } catch (error) {
    console.error("Delete Emergency Contact Error:", error);
    return ApiResponseBuilder.error(
      "Failed to delete emergency contact",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}