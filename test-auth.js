// Simple test to verify authentication logic
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");

const JWT_SECRET = "your-secret-key-change-in-production";
const JWT_EXPIRES_IN = "7d";

async function testAuth() {
  console.log("Testing authentication logic...");
  
  // Test password hashing
  const password = "123456";
  const hashedPassword = await bcrypt.hash(password, 12);
  console.log("Password hashed successfully");
  
  // Test password verification
  const isValid = await bcrypt.compare(password, hashedPassword);
  console.log("Password verification:", isValid);
  
  // Test JWT token generation
  const payload = {
    id: 1,
    username: "admin",
    email: "<EMAIL>"
  };
  
  const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
  console.log("JWT token generated:", token.substring(0, 50) + "...");
  
  // Test JWT token verification
  const decoded = jwt.verify(token, JWT_SECRET);
  console.log("JWT token verified:", decoded);
  
  console.log("All authentication tests passed!");
}

testAuth().catch(console.error);
