const fetch = require('node-fetch');

// Manual test for the specific route the user is having trouble with
async function testPropertyTypesManual() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🔍 Testing Property Types Routes Manually...\n');

  try {
    // Test the exact problematic route first
    console.log('Testing the route that user reports 404 for:');
    const directResponse = await fetch(`${baseUrl}/property-types`, {
      redirect: 'manual'
    });
    
    console.log(`Direct /property-types: ${directResponse.status}`);
    if (directResponse.status >= 300 && directResponse.status < 400) {
      const location = directResponse.headers.get('location');
      console.log(`  Redirects to: ${location}`);
      
      // Follow the redirect
      if (location) {
        const redirectResponse = await fetch(location, { redirect: 'manual' });
        console.log(`  Redirect target status: ${redirectResponse.status}`);
        
        if (redirectResponse.status >= 300 && redirectResponse.status < 400) {
          const secondLocation = redirectResponse.headers.get('location');
          console.log(`  Second redirect to: ${secondLocation}`);
        }
      }
    }

    // Test with authentication
    console.log('\n2. Testing with login and dashboard route:');
    
    // First login
    const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'admin', password: '123456' })
    });
    
    const loginData = await loginResponse.json();
    const cookies = loginResponse.headers.get('set-cookie');
    
    if (loginResponse.ok && cookies) {
      console.log('✅ Login successful');
      
      // Test the problematic dashboard route
      const dashboardTest = await fetch(`${baseUrl}/en/dashboard/property-types`, {
        headers: { 'Cookie': cookies },
        redirect: 'manual'
      });
      
      console.log(`/en/dashboard/property-types: ${dashboardTest.status}`);
      
      if (dashboardTest.status === 200) {
        console.log('✅ Property types page loads successfully!');
      } else if (dashboardTest.status >= 300 && dashboardTest.status < 400) {
        const location = dashboardTest.headers.get('location');
        console.log(`  Redirects to: ${location}`);
      } else {
        console.log(`❌ Error: ${dashboardTest.statusText}`);
      }
      
      // Also test without locale prefix
      const noLocaleTest = await fetch(`${baseUrl}/dashboard/property-types`, {
        headers: { 'Cookie': cookies },
        redirect: 'manual'
      });
      
      console.log(`/dashboard/property-types: ${noLocaleTest.status}`);
      if (noLocaleTest.status >= 300 && noLocaleTest.status < 400) {
        const location = noLocaleTest.headers.get('location');
        console.log(`  Redirects to: ${location}`);
      }
      
    } else {
      console.log('❌ Login failed');
    }

  } catch (error) {
    console.error('Test error:', error.message);
  }
}

testPropertyTypesManual().catch(console.error);