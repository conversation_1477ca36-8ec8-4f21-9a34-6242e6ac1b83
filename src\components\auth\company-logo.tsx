"use client";

import { useState, useEffect } from "react";
import { Building2 } from "lucide-react";
import { APP_CONFIG } from "@/config/app-config";

interface CompanySettings {
  id: number;
  company_name: string;
  logo_url: string | null;
  created_at: string;
  updated_at: string;
}

interface CompanyLogoProps {
  className?: string;
  showCompanyName?: boolean;
  size?: "sm" | "md" | "lg" | "xl";
}

export function CompanyLogo({ 
  className = "", 
  showCompanyName = true, 
  size = "md" 
}: CompanyLogoProps) {
  const [settings, setSettings] = useState<CompanySettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // Size configurations
  const sizeConfig = {
    sm: {
      logo: "h-8 w-8",
      text: "text-lg",
      container: "gap-2"
    },
    md: {
      logo: "h-12 w-12",
      text: "text-2xl",
      container: "gap-3"
    },
    lg: {
      logo: "h-16 w-16",
      text: "text-3xl",
      container: "gap-4"
    },
    xl: {
      logo: "h-24 w-24",
      text: "text-4xl",
      container: "gap-5"
    }
  };

  const config = sizeConfig[size];

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setIsLoading(true);

        // Try to fetch company settings - in development mode this should work
        const response = await fetch("/api/company-settings", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          // Don't include credentials to avoid CORS issues on login page
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setSettings(result.data);
          }
        } else {
          console.log("Company settings API returned:", response.status);
        }
      } catch (error) {
        console.log("Could not fetch company settings:", error);
        // Silently fail and use defaults
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const companyName = settings?.company_name || APP_CONFIG.name;
  const logoUrl = settings?.logo_url;

  const handleImageError = () => {
    setImageError(true);
  };

  if (isLoading) {
    return (
      <div className={`flex items-center ${config.container} ${className}`}>
        <div className={`${config.logo} bg-muted animate-pulse rounded`} />
        {showCompanyName && (
          <div className={`h-6 w-32 bg-muted animate-pulse rounded ${config.text}`} />
        )}
      </div>
    );
  }

  return (
    <div className={`flex items-center ${config.container} ${className}`}>
      {logoUrl && !imageError ? (
        <img
          src={logoUrl}
          alt={`${companyName} Logo`}
          className={`${config.logo} object-contain`}
          onError={handleImageError}
        />
      ) : (
        <div className={`${config.logo} bg-primary text-primary-foreground rounded-lg flex items-center justify-center`}>
          <Building2 className={size === "sm" ? "h-4 w-4" : size === "md" ? "h-6 w-6" : size === "lg" ? "h-8 w-8" : "h-12 w-12"} />
        </div>
      )}
      
      {showCompanyName && (
        <h1 className={`font-bold text-foreground ${config.text}`}>
          {companyName}
        </h1>
      )}
    </div>
  );
}
