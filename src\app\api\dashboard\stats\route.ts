import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getAuthUser } from "@/lib/auth";
import { verifyToken } from "@/lib/auth";

export async function GET(request: NextRequest) {
  try {
    // Get token from cookie
    const token = request.cookies.get("auth-token")?.value;
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify token
    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    // Get user
    const user = await getAuthUser(decoded.id);
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 401 });
    }

    // Fetch all stats in parallel with optimized queries
    const [
      propertiesCount,
      unitsStats,
      contractsStats,
      maintenanceStats,
      invoicesStats,
      revenueStats
    ] = await Promise.all([
      // Total properties count
      db.properties.count(),
      
      // Units statistics
      db.units.groupBy({
        by: ['status'],
        _count: true,
      }),
      
      // Contracts statistics
      db.contracts.groupBy({
        by: ['status'],
        _count: true,
        _sum: {
          monthly_rent: true,
        },
      }),
      
      // Maintenance statistics
      db.maintenanceRequests.groupBy({
        by: ['status'],
        _count: true,
      }),
      
      // Invoices statistics
      db.invoices.groupBy({
        by: ['status'],
        _count: true,
        _sum: {
          total_amount: true,
        },
      }),
      
      // Revenue from active contracts
      db.contracts.aggregate({
        where: { status: 'ACTIVE' },
        _sum: {
          monthly_rent: true,
        },
        _count: true,
      }),
    ]);

    // Process the results
    const totalUnits = unitsStats.reduce((acc, stat) => acc + stat._count, 0);
    const occupiedUnits = unitsStats.find(stat => stat.status === 'OCCUPIED')?._count || 0;
    const activeContracts = contractsStats.find(stat => stat.status === 'ACTIVE')?._count || 0;
    const totalRevenue = revenueStats._sum.monthly_rent || 0;
    
    const pendingMaintenance = maintenanceStats
      .filter(stat => ['REPORTED', 'IN_PROGRESS'].includes(stat.status))
      .reduce((acc, stat) => acc + stat._count, 0);
    
    const overdueInvoices = invoicesStats.find(stat => stat.status === 'OVERDUE')?._count || 0;
    const totalInvoiceAmount = invoicesStats.reduce((acc, stat) => acc + (stat._sum.total_amount || 0), 0);

    const occupancyRate = totalUnits > 0 ? Math.round((occupiedUnits / totalUnits) * 100) : 0;

    const stats = {
      totalProperties: propertiesCount,
      totalUnits,
      occupiedUnits,
      occupancyRate,
      activeContracts,
      totalRevenue,
      pendingMaintenance,
      overdueInvoices,
      totalInvoiceAmount,
      monthlyGrowth: 12.5, // This would come from historical data comparison
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error("Dashboard stats API error:", error);
    return NextResponse.json(
      { error: "Failed to fetch dashboard statistics" },
      { status: 500 }
    );
  }
}
