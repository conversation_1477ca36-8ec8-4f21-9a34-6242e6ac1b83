import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { associationTransactionSchema, transactionFilterSchema } from "@/types/owners-association";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, checkUserPermission } from "@/lib/auth";
import { Decimal } from "@prisma/client/runtime/library";

// GET /api/owners-associations/[id]/transactions - List all transactions for an association
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await checkUserPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view association transactions");
    }

    const { id: idParam } = await params;
    const associationId = parseInt(idParam);

    if (isNaN(associationId)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    // Check if association exists
    const association = await db.ownersAssociation.findUnique({
      where: { id: associationId },
    });

    if (!association) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    const searchParams = request.nextUrl.searchParams;
    
    // Parse filters
    const filters = transactionFilterSchema.parse({
      search: searchParams.get("search") || undefined,
      type: searchParams.get("type") || undefined,
      category: searchParams.get("category") || undefined,
      date_from: searchParams.get("date_from") || undefined,
      date_to: searchParams.get("date_to") || undefined,
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
      pageSize: searchParams.get("pageSize") ? parseInt(searchParams.get("pageSize")!) : 10,
      sortBy: searchParams.get("sortBy") || "transaction_date",
      sortOrder: searchParams.get("sortOrder") || "desc",
    });

    // Build where clause
    const where: any = {
      association_id: associationId,
    };

    if (filters.search) {
      where.OR = [
        { description: { contains: filters.search, mode: 'insensitive' } },
        { reference_number: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    if (filters.type) {
      where.type = filters.type;
    }

    if (filters.category) {
      where.category = filters.category;
    }

    if (filters.date_from || filters.date_to) {
      where.transaction_date = {};
      if (filters.date_from) {
        where.transaction_date.gte = new Date(filters.date_from);
      }
      if (filters.date_to) {
        where.transaction_date.lte = new Date(filters.date_to);
      }
    }

    // Calculate pagination
    const page = filters.page || 1;
    const pageSize = filters.pageSize || 10;
    const skip = (page - 1) * pageSize;

    // Fetch transactions
    const [transactions, total] = await Promise.all([
      db.associationTransaction.findMany({
        where,
        include: {
          // TODO: Uncomment after running member_transaction_links migration
          // member: {
          //   select: {
          //     id: true,
          //     full_name: true,
          //     unit_number: true,
          //     email: true,
          //     phone: true,
          //   },
          // },
          creator: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || "transaction_date"]: filters.sortOrder || "desc",
        },
        skip,
        take: pageSize,
      }),
      db.associationTransaction.count({ where }),
    ]);

    // Transform transactions to handle Decimal serialization
    const transformedTransactions = transactions.map(transaction => ({
      ...transaction,
      amount: transaction.amount.toString(),
    }));

    // Calculate summary statistics
    const [expenseSum, incomeSum] = await Promise.all([
      db.associationTransaction.aggregate({
        where: {
          ...where,
          type: "EXPENSE",
        },
        _sum: {
          amount: true,
        },
      }),
      db.associationTransaction.aggregate({
        where: {
          ...where,
          type: "INCOME",
        },
        _sum: {
          amount: true,
        },
      }),
    ]);

    const totalExpenses = expenseSum._sum.amount || new Decimal(0);
    const totalIncome = incomeSum._sum.amount || new Decimal(0);
    const netBalance = new Decimal(totalIncome.toString()).minus(totalExpenses.toString());

    const stats = {
      total_expenses: totalExpenses.toString(),
      total_income: totalIncome.toString(),
      net_balance: netBalance.toString(),
    };

    return ApiResponseBuilder.success({
      transactions: transformedTransactions,
      stats,
    }, {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error: any) {
    console.error("Error fetching association transactions:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }
    
    // Check for Prisma errors
    if (error.code === 'P2025') {
      return ApiResponseBuilder.notFound("Record not found");
    }
    
    if (error.code === 'P2021') {
      // Column does not exist error - likely the member relation
      console.error("Database schema mismatch. Please run migrations.");
    }
    
    return ApiResponseBuilder.error(
      error.message || "Failed to fetch association transactions", 
      "INTERNAL_ERROR", 
      500
    );
  }
}

// POST /api/owners-associations/[id]/transactions - Create a new transaction
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for owners-associations
    const canCreate = await checkUserPermission(decoded.id, "owners-associations", "CREATE");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create association transactions");
    }

    const { id: idParam } = await params;
    const associationId = parseInt(idParam);

    if (isNaN(associationId)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    // Check if association exists
    const association = await db.ownersAssociation.findUnique({
      where: { id: associationId },
    });

    if (!association) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    const body = await request.json();
    const validatedData = associationTransactionSchema.parse({
      ...body,
      association_id: associationId,
    });

    // Create the transaction
    const transaction = await db.associationTransaction.create({
      data: {
        association_id: associationId,
        transaction_date: new Date(validatedData.transaction_date),
        type: validatedData.type,
        category: validatedData.category,
        description: validatedData.description,
        amount: new Decimal(validatedData.amount),
        payment_method: validatedData.payment_method,
        notes: validatedData.notes,
        attachment_url: validatedData.attachment_url,
        attachment_name: validatedData.attachment_name,
        created_by: decoded.id,
      },
      include: {
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    // Transform transaction to handle Decimal serialization
    const transformedTransaction = {
      ...transaction,
      amount: transaction.amount.toString(),
    };

    return ApiResponseBuilder.success(transformedTransaction);
  } catch (error: any) {
    console.error("Error creating association transaction:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to create association transaction", "INTERNAL_ERROR", 500);
  }
}