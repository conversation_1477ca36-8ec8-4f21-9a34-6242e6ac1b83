"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import { format } from "date-fns";
import { Receipt, Eye, Download } from "lucide-react";
import { toast } from "sonner";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/localization";

interface TenantPaymentsProps {
  tenantId: string;
}

export function TenantPayments({ tenantId }: TenantPaymentsProps) {
  const locale = useLocale();
  const t = useTranslations("tenants");
  const tPayments = useTranslations("payments");
  const tCommon = useTranslations("common");
  
  const [payments, setPayments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPayments();
  }, [tenantId]);

  const fetchPayments = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/payments?tenantId=${tenantId}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch payments");
      }

      const result = await response.json();
      if (result.success) {
        setPayments(result.data);
      }
    } catch (error) {
      console.error("Error fetching payments:", error);
      toast.error(t("payments.fetchError"));
    } finally {
      setLoading(false);
    }
  };

  const getPaymentMethodBadge = (method: string) => {
    // Convert uppercase snake_case to lowercase camelCase for translation keys
    const methodKey = method.toLowerCase().replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    
    const variant = 
      method === "CASH" ? "default" :
      method === "BANK_TRANSFER" ? "secondary" :
      method === "CHECK" || method === "CHEQUE" ? "outline" :
      method === "CREDIT_CARD" || method === "CARD" ? "default" :
      "default";
    
    return (
      <Badge variant={variant}>
        {tCommon(`paymentMethods.${methodKey}`)}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    // Convert uppercase to lowercase for translation keys
    const statusKey = status.toLowerCase();
    
    const variant = 
      status === "COMPLETED" ? "default" :
      status === "PENDING" ? "secondary" :
      status === "FAILED" ? "destructive" :
      status === "CANCELLED" ? "secondary" :
      "outline";
    
    return (
      <Badge variant={variant}>
        {tPayments(`status.${statusKey}`)}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("payments.title")}</CardTitle>
          <CardDescription>{t("payments.description")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("payments.title")}</CardTitle>
        <CardDescription>{t("payments.description")}</CardDescription>
      </CardHeader>
      <CardContent>
        {payments.length === 0 ? (
          <div className="text-center py-8">
            <Receipt className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">{t("payments.noPayments")}</p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{tPayments("table.receiptNumber")}</TableHead>
                  <TableHead>{tPayments("table.paymentDate")}</TableHead>
                  <TableHead>{tPayments("table.invoice")}</TableHead>
                  <TableHead>{tPayments("table.amount")}</TableHead>
                  <TableHead>{tPayments("table.paymentMethod")}</TableHead>
                  <TableHead>{tPayments("table.status")}</TableHead>
                  <TableHead>{tCommon("actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {payments.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell className="font-medium">
                      {payment.receipt_number}
                    </TableCell>
                    <TableCell>
                      {payment.payment_date ? format(new Date(payment.payment_date), "dd/MM/yyyy") : tCommon("notAvailable")}
                    </TableCell>
                    <TableCell>
                      {payment.invoice?.invoice_number || tCommon("notAvailable")}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(payment.amount)}
                    </TableCell>
                    <TableCell>
                      {getPaymentMethodBadge(payment.payment_method)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(payment.status)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/${locale}/dashboard/payments/${payment.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // Download receipt PDF
                            window.open(`/api/payments/${payment.id}/receipt`, '_blank');
                          }}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}