"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, Edit, Trash2, Check, X, ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";
import { ExpenseWithDetails } from "@/types/expense";
import { formatCurrency, formatDateForDisplay } from "@/lib/localization";
import { DeleteExpenseDialog } from "./delete-expense-dialog";

interface GetExpenseColumnsProps {
  locale: string;
  t: (key: string) => string;
  onView: (expense: ExpenseWithDetails) => void;
  onEdit: (expense: ExpenseWithDetails) => void;
  onDelete: (expense: ExpenseWithDetails) => void;
  onApprove?: (expense: ExpenseWithDetails) => void;
  onReject?: (expense: ExpenseWithDetails) => void;
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  currentSort?: { column: string; direction: 'asc' | 'desc' };
}

// Helper function to create sortable header
const createSortableHeader = (
  title: string,
  column: string,
  onSort?: (column: string, direction: 'asc' | 'desc') => void,
  currentSort?: { column: string; direction: 'asc' | 'desc' }
) => {
  if (!onSort) return title;

  const isCurrentColumn = currentSort?.column === column;
  const currentDirection = isCurrentColumn ? currentSort.direction : null;

  return (
    <Button
      variant="ghost"
      onClick={() => {
        const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
        onSort(column, newDirection);
      }}
      className="h-auto p-0 font-semibold hover:bg-transparent"
    >
      <span className="ltr:mr-2 rtl:ml-2">{title}</span>
      {isCurrentColumn ? (
        currentDirection === 'asc' ? (
          <ArrowUp className="h-4 w-4" />
        ) : (
          <ArrowDown className="h-4 w-4" />
        )
      ) : (
        <ArrowUpDown className="h-4 w-4 opacity-50" />
      )}
    </Button>
  );
};

export function getExpenseColumns({
  locale,
  t,
  onView,
  onEdit,
  onDelete,
  onApprove,
  onReject,
  onSort,
  currentSort,
}: GetExpenseColumnsProps): ColumnDef<ExpenseWithDetails>[] {
  return [
    {
      accessorKey: "date",
      header: () => createSortableHeader(t("fields.date"), "date", onSort, currentSort),
      cell: ({ row }) => {
        const date = row.getValue("date") as Date;
        return formatDateForDisplay(date);
      },
    },
    {
      accessorKey: "description",
      header: () => createSortableHeader(t("fields.description"), "description", onSort, currentSort),
      cell: ({ row }) => {
        const description = row.getValue("description") as string;
        return (
          <div className="max-w-[200px] truncate" title={description}>
            {description}
          </div>
        );
      },
    },
    {
      accessorKey: "amount",
      header: () => createSortableHeader(t("fields.amount"), "amount", onSort, currentSort),
      cell: ({ row }) => {
        const amount = row.getValue("amount") as number;
        return (
          <div className="font-medium">
            {formatCurrency(amount)}
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: () => createSortableHeader(t("fields.category"), "category_id", onSort, currentSort),
      cell: ({ row }) => {
        const category = row.original.category;
        return locale === "ar" ? category.name_ar : category.name_en;
      },
    },
    {
      accessorKey: "payment_method",
      header: () => createSortableHeader(t("fields.paymentMethod"), "payment_method", onSort, currentSort),
      cell: ({ row }) => {
        const paymentMethod = row.getValue("payment_method") as string;
        return t(`paymentMethods.${paymentMethod}`);
      },
    },
    {
      accessorKey: "paid_by",
      header: t("fields.paidBy"),
      cell: ({ row }) => {
        const paidBy = row.getValue("paid_by") as string;
        return (
          <div className="max-w-[150px] truncate" title={paidBy}>
            {paidBy}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: () => createSortableHeader(t("fields.status"), "status", onSort, currentSort),
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        const statusVariant =
          status === "APPROVED" ? "success" :
          status === "REJECTED" ? "destructive" :
          "secondary";

        return (
          <Badge variant={statusVariant as any}>
            {t(`statuses.${status}`)}
          </Badge>
        );
      },
    },
    {
      accessorKey: "is_recurring",
      header: t("fields.isRecurring"),
      cell: ({ row }) => {
        const isRecurring = row.getValue("is_recurring") as boolean;
        return isRecurring ? (
          <Badge variant="outline">{t("frequencies.MONTHLY")}</Badge>
        ) : null;
      },
    },
    {
      accessorKey: "created_at",
      header: () => createSortableHeader(t("fields.createdAt"), "created_at", onSort, currentSort),
      cell: ({ row }) => {
        const createdAt = row.getValue("created_at") as Date;
        return formatDateForDisplay(createdAt);
      },
    },
    {
      id: "actions",
      header: () => <div className="text-center">{t("common.actions")}</div>,
      cell: ({ row }) => {
        const expense = row.original;
        const canApprove = expense.status === "PENDING" && onApprove && onReject;

        return (
          <div className="text-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t("common.actions")}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              
              <DropdownMenuItem onClick={() => onView(expense)}>
                <Eye className="mr-2 h-4 w-4" />
                {t("viewExpense")}
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={() => onEdit(expense)}>
                <Edit className="mr-2 h-4 w-4" />
                {t("editExpense")}
              </DropdownMenuItem>

              {canApprove && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onApprove(expense)}
                    className="text-green-600"
                  >
                    <Check className="mr-2 h-4 w-4" />
                    {t("actions.approve")}
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem 
                    onClick={() => onReject(expense)}
                    className="text-red-600"
                  >
                    <X className="mr-2 h-4 w-4" />
                    {t("actions.reject")}
                  </DropdownMenuItem>
                </>
              )}

              <DropdownMenuSeparator />
              <DeleteExpenseDialog expense={expense} onSuccess={onDelete ? () => onDelete(expense) : undefined}>
                <DropdownMenuItem
                  className="text-red-600"
                  onSelect={(e) => e.preventDefault()}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  {t("deleteExpense")}
                </DropdownMenuItem>
              </DeleteExpenseDialog>
            </DropdownMenuContent>
          </DropdownMenu>
          </div>
        );
      },
    },
  ];
}
