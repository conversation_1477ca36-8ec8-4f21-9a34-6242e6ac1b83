const { PrismaClient } = require('./src/generated/prisma');
const prisma = new PrismaClient();

async function checkDBSchema() {
  try {
    console.log('=== Checking Database Schema ===\n');
    
    // Test SubscriptionPayment model fields
    console.log('1. Testing SubscriptionPayment model...');
    
    // Try to query with the new fields that might be missing
    try {
      const testQuery = await prisma.subscriptionPayment.findFirst({
        select: {
          id: true,
          amount: true,
          // These fields might not exist in the current DB
          amount_due: true,
          amount_paid: true,
          remaining_balance: true,
          transaction_id: true,
        }
      });
      console.log('✓ All expected fields exist in subscription_payments table');
      console.log('Sample data structure:', testQuery);
    } catch (error) {
      console.log('❌ Error querying subscription_payments with new fields:');
      console.log('Error code:', error.code);
      console.log('Error message:', error.message);
      
      // Try querying with only basic fields
      try {
        const basicQuery = await prisma.subscriptionPayment.findFirst({
          select: {
            id: true,
            amount: true,
            status: true,
            due_date: true,
            payment_date: true,
          }
        });
        console.log('✓ Basic fields work fine:', basicQuery);
      } catch (basicError) {
        console.log('❌ Even basic query failed:', basicError.message);
      }
    }
    
    // Test if subscription_payment_installments table exists
    console.log('\n2. Testing subscription_payment_installments table...');
    try {
      const installmentCount = await prisma.subscriptionPaymentInstallment.count();
      console.log('✓ subscription_payment_installments table exists, count:', installmentCount);
    } catch (error) {
      console.log('❌ subscription_payment_installments table does not exist');
      console.log('Error:', error.message);
    }
    
    // Check if migration was applied
    console.log('\n3. Checking if enhanced schema migration was applied...');
    try {
      // Try to find a subscription payment with the enhanced schema
      const enhancedQuery = await prisma.$queryRaw`
        DESCRIBE subscription_payments;
      `;
      console.log('✓ subscription_payments table structure:');
      console.table(enhancedQuery);
    } catch (error) {
      console.log('❌ Cannot describe table:', error.message);
    }
    
  } catch (error) {
    console.error('❌ General error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDBSchema().catch(console.error);