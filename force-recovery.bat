@echo off
echo ========================================
echo InnoDB Force Recovery
echo ========================================
echo.

echo Creating recovery configuration...
cd "C:\xampp\mysql\bin"

echo # MySQL Configuration - Force Recovery > my_recovery.ini
echo [mysqld] >> my_recovery.ini
echo port=3306 >> my_recovery.ini
echo socket=mysql >> my_recovery.ini
echo basedir=C:/xampp/mysql >> my_recovery.ini
echo datadir=C:/xampp/mysql/data >> my_recovery.ini
echo tmpdir=C:/xampp/tmp >> my_recovery.ini
echo skip-external-locking >> my_recovery.ini
echo skip-slave-start >> my_recovery.ini
echo key_buffer_size=16M >> my_recovery.ini
echo max_allowed_packet=1M >> my_recovery.ini
echo table_open_cache=64 >> my_recovery.ini
echo sort_buffer_size=512K >> my_recovery.ini
echo net_buffer_length=8K >> my_recovery.ini
echo read_buffer_size=256K >> my_recovery.ini
echo read_rnd_buffer_size=512K >> my_recovery.ini
echo myisam_sort_buffer_size=8M >> my_recovery.ini
echo log-error=mysql_error.log >> my_recovery.ini
echo server-id=1 >> my_recovery.ini
echo. >> my_recovery.ini
echo # InnoDB Force Recovery >> my_recovery.ini
echo innodb_data_home_dir=C:/xampp/mysql/data >> my_recovery.ini
echo innodb_log_group_home_dir=C:/xampp/mysql/data >> my_recovery.ini
echo innodb_data_file_path=ibdata1:10M:autoextend >> my_recovery.ini
echo innodb_log_file_size=5M >> my_recovery.ini
echo innodb_log_buffer_size=8M >> my_recovery.ini
echo innodb_flush_log_at_trx_commit=1 >> my_recovery.ini
echo innodb_lock_wait_timeout=50 >> my_recovery.ini
echo innodb_force_recovery=1 >> my_recovery.ini

copy my_recovery.ini my.ini
echo ✅ Recovery configuration applied

echo.
echo Now try starting MySQL with:
echo mysqld --console --skip-grant-tables --skip-networking
echo.
pause
