import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { db } from "@/lib/db";
import { PropertyTypeForm } from "../../_components/property-type-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

interface EditPropertyTypePageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

async function getPropertyType(id: string) {
  try {
    const propertyType = await db.propertyType.findUnique({
      where: { id: parseInt(id, 10) },
      include: {
        _count: {
          select: { properties: true },
        },
      },
    });
    return propertyType;
  } catch (error) {
    console.error("Error fetching property type:", error);
    return null;
  }
}

export default async function EditPropertyTypePage({ params }: EditPropertyTypePageProps) {
  const { id, locale } = await params;
  const t = await getTranslations('properties.propertyTypes');

  const propertyType = await getPropertyType(id);

  if (!propertyType) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href={`/${locale}/dashboard/property-types`}>
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('editPropertyType')}</h1>
          <p className="text-muted-foreground">{t('editDescription')}</p>
        </div>
      </div>

      <PropertyTypeForm propertyType={propertyType} isEdit />
    </div>
  );
}