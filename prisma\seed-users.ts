import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

// Define modules directly to avoid import issues
const APPLICATION_MODULES = [
  { id: "tenants", name: "Tenants", description: "Manage tenant information and leases" },
  { id: "properties", name: "Properties", description: "Manage property listings and details" },
  { id: "users", name: "Users", description: "Manage user accounts and profiles" },
  { id: "roles", name: "Roles", description: "Manage roles and permissions" },
] as const;

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Starting user and role seeding...");

  // Create permissions for all modules
  console.log("📋 Creating permissions...");
  const permissions = [];
  
  for (const module of APPLICATION_MODULES) {
    const actions = ["CREATE", "READ", "UPDATE", "DELETE"] as const;
    
    for (const action of actions) {
      const permission = await prisma.permission.upsert({
        where: {
          module_action: {
            module: module.id,
            action: action,
          },
        },
        update: {},
        create: {
          module: module.id,
          action: action,
          description: `${action.toLowerCase()} access for ${module.name}`,
        },
      });
      permissions.push(permission);
    }
  }
  
  console.log(`✅ Created ${permissions.length} permissions`);

  // Create Super Admin role
  console.log("👑 Creating Super Admin role...");
  const superAdminRole = await prisma.role.upsert({
    where: { name: "Super Admin" },
    update: {},
    create: {
      name: "Super Admin",
      description: "Full access to all modules and system administration",
      is_system: true,
    },
  });

  // Assign all permissions to Super Admin role
  for (const permission of permissions) {
    await prisma.rolePermission.upsert({
      where: {
        role_id_permission_id: {
          role_id: superAdminRole.id,
          permission_id: permission.id,
        },
      },
      update: {},
      create: {
        role_id: superAdminRole.id,
        permission_id: permission.id,
      },
    });
  }

  // Create Property Manager role
  console.log("🏢 Creating Property Manager role...");
  const propertyManagerRole = await prisma.role.upsert({
    where: { name: "Property Manager" },
    update: {},
    create: {
      name: "Property Manager",
      description: "Full access to tenants and properties, limited access to users",
      is_system: true,
    },
  });

  // Assign permissions to Property Manager
  const propertyManagerPermissions = permissions.filter(p => 
    (p.module === "tenants" || p.module === "properties") ||
    (p.module === "users" && p.action === "READ")
  );

  for (const permission of propertyManagerPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        role_id_permission_id: {
          role_id: propertyManagerRole.id,
          permission_id: permission.id,
        },
      },
      update: {},
      create: {
        role_id: propertyManagerRole.id,
        permission_id: permission.id,
      },
    });
  }

  // Create Tenant Coordinator role
  console.log("👥 Creating Tenant Coordinator role...");
  const tenantCoordinatorRole = await prisma.role.upsert({
    where: { name: "Tenant Coordinator" },
    update: {},
    create: {
      name: "Tenant Coordinator",
      description: "Can create/edit/view tenants, view-only access to properties",
      is_system: true,
    },
  });

  // Assign permissions to Tenant Coordinator
  const tenantCoordinatorPermissions = permissions.filter(p => 
    (p.module === "tenants") ||
    (p.module === "properties" && p.action === "READ")
  );

  for (const permission of tenantCoordinatorPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        role_id_permission_id: {
          role_id: tenantCoordinatorRole.id,
          permission_id: permission.id,
        },
      },
      update: {},
      create: {
        role_id: tenantCoordinatorRole.id,
        permission_id: permission.id,
      },
    });
  }

  // Create Viewer role
  console.log("👁️ Creating Viewer role...");
  const viewerRole = await prisma.role.upsert({
    where: { name: "Viewer" },
    update: {},
    create: {
      name: "Viewer",
      description: "Read-only access to specified modules",
      is_system: true,
    },
  });

  // Assign read-only permissions to Viewer
  const viewerPermissions = permissions.filter(p => p.action === "READ");

  for (const permission of viewerPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        role_id_permission_id: {
          role_id: viewerRole.id,
          permission_id: permission.id,
        },
      },
      update: {},
      create: {
        role_id: viewerRole.id,
        permission_id: permission.id,
      },
    });
  }

  // Create Super Admin user (Ali Alalawi)
  console.log("👤 Creating Super Admin user...");
  const passwordHash = await bcrypt.hash("123456", 12);
  
  const superAdminUser = await prisma.user.upsert({
    where: { username: "admin" },
    update: {
      email: "<EMAIL>",
      first_name: "Ali",
      last_name: "Alalawi",
      phone: "99474767",
      status: "ACTIVE",
      email_verified: true,
    },
    create: {
      username: "admin",
      email: "<EMAIL>",
      password_hash: passwordHash,
      first_name: "Ali",
      last_name: "Alalawi",
      phone: "99474767",
      status: "ACTIVE",
      email_verified: true,
    },
  });

  // Assign Super Admin role to the user
  await prisma.userRole.upsert({
    where: {
      user_id_role_id: {
        user_id: superAdminUser.id,
        role_id: superAdminRole.id,
      },
    },
    update: {},
    create: {
      user_id: superAdminUser.id,
      role_id: superAdminRole.id,
    },
  });

  console.log("✅ User and role seeding completed!");
  console.log("\n📊 Summary:");
  console.log(`- Created ${permissions.length} permissions`);
  console.log("- Created 4 system roles:");
  console.log("  • Super Admin (full access)");
  console.log("  • Property Manager (tenants + properties + view users)");
  console.log("  • Tenant Coordinator (tenants + view properties)");
  console.log("  • Viewer (read-only access)");
  console.log("- Created Super Admin user:");
  console.log("  • Username: admin");
  console.log("  • Password: 123456");
  console.log("  • Email: <EMAIL>");
  console.log("  • Name: Ali Alalawi");
  console.log("  • Phone: 99474767");
}

main()
  .catch((e) => {
    console.error("❌ Error during seeding:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
