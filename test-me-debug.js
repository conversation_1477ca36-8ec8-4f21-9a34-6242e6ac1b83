const fetch = require('node-fetch');

async function testMe() {
  try {
    console.log('Testing /api/auth/me endpoint...');
    
    // First login to get a token
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: '<EMAIL>',
        password: '123456'
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('Login successful, got token');
    
    // Extract token from Set-Cookie header
    const cookies = loginResponse.headers.get('set-cookie');
    const tokenMatch = cookies.match(/auth-token=([^;]+)/);
    const token = tokenMatch ? tokenMatch[1] : null;
    
    if (!token) {
      console.error('No token found in login response');
      return;
    }
    
    console.log('Token extracted:', token.substring(0, 50) + '...');
    
    // Now test the /me endpoint
    const meResponse = await fetch('http://localhost:3000/api/auth/me', {
      method: 'GET',
      headers: {
        'Cookie': `auth-token=${token}`
      }
    });
    
    console.log('Me endpoint response status:', meResponse.status);
    
    const meData = await meResponse.json();
    console.log('Me endpoint response:', JSON.stringify(meData, null, 2));
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

testMe();