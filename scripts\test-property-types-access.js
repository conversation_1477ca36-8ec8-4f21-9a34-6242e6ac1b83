const fetch = require('node-fetch');

async function testPropertyTypesAccess() {
  console.log('Testing Property Types API access...\n');
  
  // First, login as admin
  console.log('1. Logging in as admin...');
  const loginResponse = await fetch('http://localhost:3002/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: 'admin', password: '123456' })
  });
  
  if (!loginResponse.ok) {
    console.error('Login failed:', await loginResponse.text());
    return;
  }
  
  const loginData = await loginResponse.json();
  console.log('Login successful:', loginData.user.username);
  
  // Get the auth token from cookies
  const cookies = loginResponse.headers.get('set-cookie');
  console.log('\n2. Auth cookies:', cookies ? 'Received' : 'Not received');
  
  // Test property types API
  console.log('\n3. Testing /api/property-types endpoint...');
  const apiResponse = await fetch('http://localhost:3002/api/property-types', {
    headers: { 
      'Cookie': cookies || ''
    }
  });
  
  console.log('API Response Status:', apiResponse.status);
  const responseText = await apiResponse.text();
  
  if (apiResponse.ok) {
    try {
      const data = JSON.parse(responseText);
      console.log('Property Types Data:', data);
    } catch (e) {
      console.log('Response:', responseText);
    }
  } else {
    console.log('Error Response:', responseText);
  }
  
  // Also test the page directly
  console.log('\n4. Testing page access /en/dashboard/property-types...');
  const pageResponse = await fetch('http://localhost:3002/en/dashboard/property-types', {
    headers: { 
      'Cookie': cookies || ''
    }
  });
  
  console.log('Page Response Status:', pageResponse.status);
  if (!pageResponse.ok) {
    console.log('Page Response Headers:', Object.fromEntries(pageResponse.headers.entries()));
  }
}

testPropertyTypesAccess().catch(console.error);