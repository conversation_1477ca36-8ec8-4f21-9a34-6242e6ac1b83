-- Add payment tracking columns to subscription_payments table
-- These columns enable tracking of partial payments

ALTER TABLE `subscription_payments`
ADD COLUMN `amount_due` DECIMAL(10,3) NULL AFTER `amount`,
ADD COLUMN `amount_paid` DECIMAL(10,3) NULL DEFAULT 0 AFTER `amount_due`,
ADD COLUMN `transaction_id` INT NULL AFTER `reference_number`;

-- Update existing records to set amount_due based on current amount
UPDATE `subscription_payments` 
SET `amount_due` = `amount`;

-- Update amount_paid based on status
UPDATE `subscription_payments` 
SET `amount_paid` = `amount_due`
WHERE `status` = 'PAID';

UPDATE `subscription_payments` 
SET `amount_paid` = 0
WHERE `status` IN ('UNPAID', 'OVERDUE');

-- For PARTIALLY_PAID status, we can't determine the exact amount paid
-- so we'll set it to 50% as a default (can be adjusted manually)
UPDATE `subscription_payments` 
SET `amount_paid` = `amount_due` * 0.5
WHERE `status` = 'PARTIALLY_PAID';

-- Make amount_due NOT NULL after populating data
ALTER TABLE `subscription_payments`
MODIFY COLUMN `amount_due` DECIMAL(10,3) NOT NULL;

-- Add foreign key for transaction_id
ALTER TABLE `subscription_payments`
ADD CONSTRAINT `fk_payment_transaction` 
FOREIGN KEY (`transaction_id`) REFERENCES `association_transactions`(`id`) ON DELETE SET NULL;

-- Add index for better query performance
CREATE INDEX `idx_payment_transaction` ON `subscription_payments`(`transaction_id`);