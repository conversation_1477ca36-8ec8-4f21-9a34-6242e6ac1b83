"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { Loader2 } from "lucide-react";

interface AuthGuardProps {
  children: React.ReactNode;
}

export function AuthGuard({ children }: AuthGuardProps) {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Only redirect if we're definitely not loading and definitely no user
    // Add a small delay to avoid race conditions with login
    if (!isLoading && !user && pathname.startsWith("/dashboard")) {
      // Extract locale from pathname to maintain consistency
      const localeMatch = pathname.match(/^\/(en|ar)/);
      const locale = localeMatch ? localeMatch[1] : 'en';

      // Use a timeout to avoid immediate redirect conflicts
      const timeoutId = setTimeout(() => {
        router.push(`/${locale}/login?redirect=${encodeURIComponent(pathname)}`);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [user, isLoading, router, pathname]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  // If no user and on protected route, show loading instead of null
  // This prevents flash of empty content
  if (!user && pathname.startsWith("/dashboard")) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Redirecting to login...</span>
        </div>
      </div>
    );
  }

  // User is authenticated, render children
  return <>{children}</>;
}
