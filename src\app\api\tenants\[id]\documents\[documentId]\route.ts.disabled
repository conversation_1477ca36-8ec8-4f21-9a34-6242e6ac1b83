import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";

import { unlink } from "fs/promises";
import { join } from "path";


// DELETE /api/tenants/[id]/documents/[documentId] - Delete a document
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; documentId: string } }
) {
  try {
    // Verify authentication
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for tenants
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "tenants", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete tenants");
    }
    const decoded = verifyToken(token);
    if (!decoded) {
      return ApiResponseBuilder.unauthorized("Invalid authentication token", request.url);
    }

    const tenantId = parseInt(params.id);
    const documentId = parseInt(params.documentId);
    
    if (isNaN(tenantId) || isNaN(documentId)) {
      return ApiResponseBuilder.error(
        "Invalid ID",
        "INVALID_ID",
        400
      );
    }

    // Check if document exists and belongs to the tenant
    const existingDocument = await db.tenantDocument.findFirst({
      where: { 
        id: documentId,
        tenant_id: tenantId,
      },
    });

    if (!existingDocument) {
      return ApiResponseBuilder.error(
        "Document not found",
        "NOT_FOUND",
        404
      );
    }

    // Delete the actual file from storage
    if (existingDocument.file_path) {
      try {
        const filePath = join(process.cwd(), "public", existingDocument.file_path);
        await unlink(filePath);
      } catch (error) {
        console.error("Error deleting file:", error);
        // Continue with database deletion even if file deletion fails
      }
    }

    // Delete the document record
    await db.tenantDocument.delete({
      where: { id: documentId },
    });

    return ApiResponseBuilder.success({ message: "Document deleted successfully" });
  } catch (error) {
    console.error("Error deleting document:", error);
    return ApiResponseBuilder.error(
      "Failed to delete document",
      "DELETE_ERROR",
      500,
      error
    );
  }
}