import { NextResponse } from "next/server";
import { ZodError } from "zod";
import { serializeDecimal } from "@/lib/decimal";

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  path?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  meta?: {
    page?: number;
    pageSize?: number;
    total?: number;
    totalPages?: number;
  };
}

export class ApiResponseBuilder {
  static success<T>(data: T, meta?: ApiResponse["meta"], status: number = 200): NextResponse<ApiResponse<T>> {
    const response: ApiResponse<T> = {
      success: true,
      data: serializeDecimal(data),
    };
    
    if (meta) {
      response.meta = meta;
    }
    
    return NextResponse.json(response, { status });
  }

  static error(
    message: string,
    code: string = "INTERNAL_ERROR",
    status: number = 500,
    details?: any,
    path?: string
  ): NextResponse<ApiResponse> {
    const response: ApiResponse = {
      success: false,
      error: {
        code,
        message,
        details,
        timestamp: new Date().toISOString(),
        path,
      },
    };
    
    return NextResponse.json(response, { status });
  }

  static validationError(error: ZodError, path?: string): NextResponse<ApiResponse> {
    return this.error(
      "Validation failed",
      "VALIDATION_ERROR",
      400,
      error.errors.map(err => ({
        field: err.path.join("."),
        message: err.message,
        code: err.code,
      })),
      path
    );
  }

  static notFound(resource: string, path?: string): NextResponse<ApiResponse> {
    return this.error(
      `${resource} not found`,
      "NOT_FOUND",
      404,
      undefined,
      path
    );
  }

  static unauthorized(message: string = "Unauthorized", path?: string): NextResponse<ApiResponse> {
    return this.error(
      message,
      "UNAUTHORIZED",
      401,
      undefined,
      path
    );
  }

  static forbidden(message: string = "Forbidden", path?: string): NextResponse<ApiResponse> {
    return this.error(
      message,
      "FORBIDDEN",
      403,
      undefined,
      path
    );
  }

  static badRequest(message: string, details?: any, path?: string): NextResponse<ApiResponse> {
    return this.error(
      message,
      "BAD_REQUEST",
      400,
      details,
      path
    );
  }

  static conflict(message: string, details?: any, path?: string): NextResponse<ApiResponse> {
    return this.error(
      message,
      "CONFLICT",
      409,
      details,
      path
    );
  }

  static tooManyRequests(message: string = "Too many requests", path?: string): NextResponse<ApiResponse> {
    return this.error(
      message,
      "TOO_MANY_REQUESTS",
      429,
      undefined,
      path
    );
  }
}

// Error code constants
export const ERROR_CODES = {
  // General errors
  INTERNAL_ERROR: "INTERNAL_ERROR",
  VALIDATION_ERROR: "VALIDATION_ERROR",
  NOT_FOUND: "NOT_FOUND",
  UNAUTHORIZED: "UNAUTHORIZED",
  FORBIDDEN: "FORBIDDEN",
  BAD_REQUEST: "BAD_REQUEST",
  CONFLICT: "CONFLICT",
  TOO_MANY_REQUESTS: "TOO_MANY_REQUESTS",
  
  // Business logic errors
  DUPLICATE_EMAIL: "DUPLICATE_EMAIL",
  INVALID_CREDENTIALS: "INVALID_CREDENTIALS",
  EXPIRED_TOKEN: "EXPIRED_TOKEN",
  INSUFFICIENT_PERMISSIONS: "INSUFFICIENT_PERMISSIONS",
  RESOURCE_LOCKED: "RESOURCE_LOCKED",
  INVALID_STATUS_TRANSITION: "INVALID_STATUS_TRANSITION",
  QUOTA_EXCEEDED: "QUOTA_EXCEEDED",
  
  // Database errors
  DB_CONNECTION_ERROR: "DB_CONNECTION_ERROR",
  DB_CONSTRAINT_VIOLATION: "DB_CONSTRAINT_VIOLATION",
  DB_TRANSACTION_FAILED: "DB_TRANSACTION_FAILED",
} as const;

export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];