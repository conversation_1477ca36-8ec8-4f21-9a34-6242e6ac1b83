"use client";

import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { useEffect } from "react";
import { Loader2, <PERSON><PERSON><PERSON>t } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useModuleAccess } from "@/hooks/use-permissions";
import Link from "next/link";

interface PermissionGuardProps {
  module: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function PermissionGuard({ module, children, fallback }: PermissionGuardProps) {
  const { hasAccess, isLoading } = useModuleAccess(module);
  const router = useRouter();
  const locale = useLocale();

  // Show loading state while checking permissions
  if (isLoading) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Checking permissions...</span>
        </div>
      </div>
    );
  }

  // If user doesn't have access, show error page
  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
              <ShieldAlert className="h-8 w-8 text-destructive" />
            </div>
            <CardTitle className="text-2xl">Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to access this module.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="mb-6 text-sm text-muted-foreground">
              Please contact your administrator if you believe you should have access to this area.
            </p>
            <div className="flex flex-col gap-2">
              <Button asChild>
                <Link href={`/${locale}/dashboard/default`}>
                  Return to Dashboard
                </Link>
              </Button>
              <Button variant="outline" onClick={() => router.back()}>
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // User has access, render children
  return <>{children}</>;
}