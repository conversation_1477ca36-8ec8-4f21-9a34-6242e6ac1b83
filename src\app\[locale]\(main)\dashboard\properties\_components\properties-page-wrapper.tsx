"use client";

import Link from "next/link";
import { Plus } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PropertiesDataTable } from "./properties-data-table";
import { PermissionGuard } from "@/components/auth/permission-guard";
import { PermissionButton } from "@/components/auth/permission-button";

export function PropertiesPageWrapper() {
  const locale = useLocale();
  const t = useTranslations("properties");
  const tCommon = useTranslations("common");

  return (
    <PermissionGuard module="properties">
      <div className="@container/main flex flex-col gap-4 md:gap-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/${locale}/dashboard`}>
                {tCommon("navigation.dashboard")}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{t("title")}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
              {t("title")}
            </h1>
            <p className="text-muted-foreground">
              {t("description")}
            </p>
          </div>
          <Link href={`/${locale}/dashboard/properties/create`}>
            <PermissionButton
              module="properties"
              action="create"
              hideOnNoPermission
              className="w-full md:w-auto"
            >
              <Plus className="h-4 w-4 ltr:mr-2 rtl:ml-2" />
              {t("addProperty")}
            </PermissionButton>
          </Link>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{t("allProperties")}</CardTitle>
            <CardDescription>
              {t("viewManageProperties")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <PropertiesDataTable />
          </CardContent>
        </Card>
      </div>
    </PermissionGuard>
  );
}