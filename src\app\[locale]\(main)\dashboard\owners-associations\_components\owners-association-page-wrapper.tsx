"use client";

import Link from "next/link";
import { Plus } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { OwnersAssociationDataTable } from "./owners-association-data-table";
import { PermissionGuard } from "@/components/auth/permission-guard";
import { PermissionButton } from "@/components/auth/permission-button";

export function OwnersAssociationPageWrapper() {
  const locale = useLocale();
  const t = useTranslations("ownersAssociations");
  const tCommon = useTranslations("common");

  return (
    <PermissionGuard module="owners-associations">
      <div className="@container/main flex flex-col gap-4 md:gap-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/${locale}/dashboard`}>
                {tCommon("navigation.dashboard")}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{t("title")}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
              {t("title")}
            </h1>
            <p className="text-muted-foreground">
              {t("description")}
            </p>
          </div>
          <Link href={`/${locale}/dashboard/owners-associations/create`}>
            <PermissionButton
              module="owners-associations"
              action="create"
              hideOnNoPermission
              className="w-full md:w-auto"
            >
              <Plus className="h-4 w-4 ltr:mr-2 rtl:ml-2" />
              {t("addAssociation")}
            </PermissionButton>
          </Link>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{t("allAssociations")}</CardTitle>
            <CardDescription>
              {t("viewManageAssociations")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <OwnersAssociationDataTable />
          </CardContent>
        </Card>
      </div>
    </PermissionGuard>
  );
}