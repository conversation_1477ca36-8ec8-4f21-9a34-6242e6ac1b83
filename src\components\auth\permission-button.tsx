"use client";

import React from "react";
import { Button, ButtonProps } from "@/components/ui/button";
import { usePermission } from "@/hooks/use-permissions";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface PermissionButtonProps extends ButtonProps {
  module: string;
  action: "create" | "update" | "delete";
  hideOnNoPermission?: boolean;
  tooltipOnDisabled?: string;
}

export function PermissionButton({
  module,
  action,
  hideOnNoPermission = false,
  tooltipOnDisabled = "You don't have permission to perform this action",
  children,
  disabled,
  ...props
}: PermissionButtonProps) {
  const { hasPermission, isLoading } = usePermission(module, action);

  // Hide button if no permission and hideOnNoPermission is true
  if (!isLoading && !hasPermission && hideOnNoPermission) {
    return null;
  }

  const isDisabled = disabled || !hasPermission || isLoading;

  // If disabled due to permissions, show tooltip
  if (!hasPermission && !hideOnNoPermission) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span tabIndex={0} className="cursor-not-allowed">
              <Button {...props} disabled={isDisabled}>
                {children}
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltipOnDisabled}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <Button {...props} disabled={isDisabled}>
      {children}
    </Button>
  );
}