"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { More<PERSON><PERSON><PERSON><PERSON>, Edit, Trash, Eye } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import { apiClient } from "@/lib/api-client";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  Al<PERSON><PERSON><PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>ial<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ertDialog<PERSON><PERSON>le,
} from "@/components/ui/alert-dialog";
import type { PropertyTypeWithCount } from "@/types/property";
import { useState } from "react";

interface ColumnTranslations {
  id: string;
  nameEn: string;
  nameAr: string;
  descriptionEn: string;
  descriptionAr: string;
  propertiesCount: string;
  createdAt: string;
  actions: string;
  selectAll: string;
  selectRow: string;
  openMenu: string;
  viewDetails: string;
  editPropertyType: string;
  deletePropertyType: string;
}

export function getPropertyTypeColumns(
  t: ColumnTranslations,
  locale: string,
  onRefresh?: () => void
): ColumnDef<PropertyTypeWithCount>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label={t.selectAll}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={t.selectRow}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "actions",
      header: () => <div className="text-center">{t.actions}</div>,
      cell: ({ row }) => {
        const propertyType = row.original;
        const [showDeleteDialog, setShowDeleteDialog] = useState(false);
        const [isDeleting, setIsDeleting] = useState(false);
        const tDelete = useTranslations('properties.propertyTypes.delete');

        const handleDelete = async () => {
          try {
            setIsDeleting(true);
            await apiClient.delete(`/api/property-types/${propertyType.id}`);
            
            toast.success(tDelete('success'));
            setShowDeleteDialog(false);
            onRefresh?.();
          } catch (error) {
            console.error("Delete error:", error);
            toast.error(error instanceof Error ? error.message : tDelete('error'));
          } finally {
            setIsDeleting(false);
          }
        };

        return (
          <div className="text-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  aria-label={t.openMenu}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>{t.actions}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/property-types/${propertyType.id}`}>
                    <Eye className="mr-2 h-4 w-4" />
                    {t.viewDetails}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/property-types/${propertyType.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    {t.editPropertyType}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-destructive"
                  disabled={!!(propertyType._count?.properties && propertyType._count.properties > 0)}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {t.deletePropertyType}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{tDelete('title')}</AlertDialogTitle>
                  <AlertDialogDescription>
                    {tDelete('description', { 
                      name: locale === "ar" ? propertyType.name_ar : propertyType.name_en 
                    })}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>
                    {tDelete('cancelButton')}
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {isDeleting ? tDelete('deleting') : tDelete('confirmButton')}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        );
      },
    },
    {
      accessorKey: "id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.id} className="justify-center" />
      ),
      cell: ({ row }) => (
        <div className="text-center">
          <span className="font-medium">#{row.getValue("id")}</span>
        </div>
      ),
    },
    {
      accessorKey: locale === "ar" ? "name_ar" : "name_en",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={locale === "ar" ? t.nameAr : t.nameEn} />
      ),
      cell: ({ row }) => {
        const nameEn = row.original.name_en;
        const nameAr = row.original.name_ar;
        const displayName = locale === "ar" ? nameAr : nameEn;
        const altName = locale === "ar" ? nameEn : nameAr;
        
        return (
          <div className="flex flex-col text-left rtl:text-right">
            <span className="font-medium">{displayName}</span>
            <span className="text-xs text-muted-foreground">{altName}</span>
          </div>
        );
      },
    },
    {
      accessorKey: locale === "ar" ? "description_ar" : "description_en",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={locale === "ar" ? t.descriptionAr : t.descriptionEn} />
      ),
      cell: ({ row }) => {
        const descriptionEn = row.original.description_en;
        const descriptionAr = row.original.description_ar;
        const displayDescription = locale === "ar" ? descriptionAr : descriptionEn;
        
        return displayDescription ? (
          <span className="text-sm text-muted-foreground line-clamp-2 text-left rtl:text-right">
            {displayDescription}
          </span>
        ) : (
          <span className="text-sm text-muted-foreground text-left rtl:text-right">-</span>
        );
      },
    },
    {
      accessorKey: "_count.properties",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.propertiesCount} className="justify-center" />
      ),
      cell: ({ row }) => {
        const count = row.original._count?.properties || 0;
        return (
          <div className="text-center">
            <Badge variant={count > 0 ? "default" : "secondary"}>
              {count}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: "created_at",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.createdAt} />
      ),
      cell: ({ row }) => {
        const date = row.getValue("created_at") as string;
        return <span>{format(new Date(date), "dd/MM/yyyy")}</span>;
      },
    },
  ];
}