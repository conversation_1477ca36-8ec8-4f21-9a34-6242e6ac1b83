"use client";

import { useState, useEffect, useCallback } from "react";
import { useTranslations, useLocale } from "next-intl";
import { toast } from "sonner";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "@/components/data-table/data-table-pagination";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getOwnersAssociationColumns } from "./owners-association-columns";
import type { OwnersAssociationWithRelations } from "@/types/owners-association";
import { apiClient } from "@/lib/api-client";
import { useRTL } from "@/hooks/use-rtl";
import { cn } from "@/lib/utils";
import { Search, Filter } from "lucide-react";

interface Property {
  id: number;
  name_en: string;
  name_ar: string;
}

export function OwnersAssociationDataTable() {
  const locale = useLocale();
  const { isRTL } = useRTL();
  const t = useTranslations("ownersAssociations.table");
  const tCommon = useTranslations("common");
  
  const [associations, setAssociations] = useState<OwnersAssociationWithRelations[]>([]);
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [globalFilter, setGlobalFilter] = useState("");
  const [propertyFilter, setPropertyFilter] = useState<string>("all");
  
  // Pagination states
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [totalAssociations, setTotalAssociations] = useState(0);

  // Fetch properties for filter
  useEffect(() => {
    const fetchProperties = async () => {
      try {
        const response = await apiClient.properties.list();
        setProperties(response.data || []);
      } catch (error) {
        console.error("Error fetching properties:", error);
      }
    };

    fetchProperties();
  }, []);

  const fetchAssociations = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: (pagination.pageIndex + 1).toString(),
        pageSize: pagination.pageSize.toString(),
        ...(globalFilter && { search: globalFilter }),
        ...(propertyFilter && propertyFilter !== "all" && { property_id: propertyFilter }),
        ...(sorting.length > 0 && {
          sortBy: sorting[0].id,
          sortOrder: sorting[0].desc ? "desc" : "asc",
        }),
      });

      const response = await apiClient.get(`/api/owners-associations?${params}`);
      
      if (response.success) {
        setAssociations(response.data);
        setTotalAssociations(response.meta?.total || 0);
      }
    } catch (error) {
      console.error("Error fetching associations:", error);
      toast.error(t("serverError"));
    } finally {
      setLoading(false);
    }
  }, [pagination, globalFilter, propertyFilter, sorting, t]);

  useEffect(() => {
    fetchAssociations();
  }, [fetchAssociations]);

  const handleRefresh = () => {
    fetchAssociations();
  };

  const handleReset = () => {
    setGlobalFilter("");
    setPropertyFilter("all");
    setPagination({ pageIndex: 0, pageSize: 10 });
    setSorting([]);
  };

  const columns = getOwnersAssociationColumns(locale, t, tCommon, handleRefresh);

  const table = useReactTable({
    data: associations,
    columns,
    pageCount: Math.ceil(totalAssociations / pagination.pageSize),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
  });

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Skeleton className="h-10 w-[300px]" />
          <Skeleton className="h-10 w-[200px]" />
        </div>
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className={cn(
        "flex flex-col gap-4 sm:flex-row sm:items-center",
        isRTL && "sm:flex-row-reverse"
      )}>
        <div className="relative flex-1 max-w-sm">
          <Search className={cn(
            "absolute top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground",
            isRTL ? "right-3" : "left-3"
          )} />
          <Input
            placeholder={t("searchPlaceholder")}
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            className={cn(isRTL ? "pr-9" : "pl-9")}
          />
        </div>
        
        <div className={cn(
          "flex items-center gap-2",
          isRTL && "flex-row-reverse"
        )}>
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Select value={propertyFilter} onValueChange={setPropertyFilter}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder={t("filterByProperty")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("allProperties")}</SelectItem>
              {properties.map((property) => (
                <SelectItem key={property.id} value={property.id.toString()}>
                  {locale === 'ar' ? property.name_ar : property.name_en}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            className={cn("shrink-0", isRTL && "ml-2")}
          >
            {tCommon("reset")}
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border overflow-hidden">
        <div className="w-full overflow-x-auto">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id} className={cn(isRTL && "text-right")}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    className="hover:bg-muted/50"
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className={cn(isRTL && "text-right")}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    {t("noResults")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <DataTablePagination table={table} />
    </div>
  );
}