import { notFound } from "next/navigation";
import { Metadata } from "next";
import { useTranslations, useLocale } from "next-intl";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Edit, ArrowLeft } from "lucide-react";
import * as Icons from "lucide-react";

async function getAmenity(id: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_APP_URL}/api/amenities/${id}`,
      {
        cache: "no-store",
      }
    );

    if (!response.ok) {
      return null;
    }

    const result = await response.json();
    return result.success ? result.data : null;
  } catch (error) {
    console.error("Error fetching amenity:", error);
    return null;
  }
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> {
  const { id } = await params;
  const amenity = await getAmenity(id);
  return {
    title: amenity ? `Amenity - ${amenity.name_en}` : "Amenity Not Found",
    description: amenity
      ? `View details for ${amenity.name_en}`
      : "Amenity not found",
  };
}

export default async function AmenityViewPage({
  params,
}: {
  params: Promise<{ id: string; locale: string }>;
}) {
  const { id } = await params;
  const amenity = await getAmenity(id);

  if (!amenity) {
    notFound();
  }

  const Icon = Icons[amenity.icon as keyof typeof Icons] as any || Icons.Home;

  return (
    <ViewAmenityContent amenity={amenity} Icon={Icon} />
  );
}

function ViewAmenityContent({ amenity, Icon }: { amenity: any; Icon: any }) {
  const t = useTranslations();
  const locale = useLocale();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            asChild
          >
            <Link href={`/${locale}/dashboard/amenities`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("common.back")}
            </Link>
          </Button>
        </div>
        <Button asChild>
          <Link href={`/${locale}/dashboard/amenities/${amenity.id}/edit`}>
            <Edit className="mr-2 h-4 w-4" />
            {t("common.edit")}
          </Link>
        </Button>
      </div>

      <div>
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
          <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-muted">
            <Icon className="h-6 w-6" />
          </div>
          {amenity.name_en}
        </h1>
        <p className="text-muted-foreground">
          {t("amenities.viewDescription")}
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>{t("amenities.basicInformation")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("amenities.nameEn")}
              </label>
              <p className="mt-1">{amenity.name_en}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("amenities.nameAr")}
              </label>
              <p className="mt-1" dir="rtl">{amenity.name_ar}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("amenities.icon")}
              </label>
              <div className="mt-1 flex items-center gap-2">
                <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-muted">
                  <Icon className="h-5 w-5" />
                </div>
                <span>{amenity.icon || "Home"}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t("amenities.usageInformation")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("amenities.associatedProperties")}
              </label>
              <div className="mt-1">
                <Badge variant={amenity._count?.property_amenities > 0 ? "default" : "secondary"}>
                  {amenity._count?.property_amenities || 0} {amenity._count?.property_amenities === 1 ? "property" : "properties"}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("common.createdAt")}
              </label>
              <p className="mt-1">
                {new Date(amenity.created_at).toLocaleDateString()}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("common.updatedAt")}
              </label>
              <p className="mt-1">
                {new Date(amenity.updated_at).toLocaleDateString()}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}