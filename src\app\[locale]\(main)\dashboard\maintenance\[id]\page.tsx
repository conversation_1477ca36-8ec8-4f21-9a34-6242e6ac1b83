import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { 
  ArrowLeft, 
  Edit, 
  AlertTriangle, 
  Calendar, 
  MapPin, 
  User, 
  Phone,
  Banknote,
  Clock,
  CheckCircle,
  FileText
} from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { formatOMR } from "@/lib/format";
import { 
  MaintenanceRequestWithRelations, 
  getPriorityColor, 
  getStatusColor,
  calculateMaintenanceTimeMetrics 
} from "@/types/maintenance";

export const metadata: Metadata = {
  title: "Maintenance Request Details",
  description: "View maintenance request details",
};

async function getMaintenanceRequest(id: string): Promise<MaintenanceRequestWithRelations | null> {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_APP_URL}/api/maintenance/${id}`,
      {
        cache: "no-store",
      }
    );

    if (!response.ok) {
      return null;
    }

    const result = await response.json();
    return result.success ? result.data : null;
  } catch (error) {
    console.error("Error fetching maintenance request:", error);
    return null;
  }
}

export default async function MaintenanceViewPage({
  params,
}: {
  params: Promise<{ id: string; locale: string }>;
}) {
  const { id, locale } = await params;
  const maintenance = await getMaintenanceRequest(id);

  if (!maintenance) {
    notFound();
  }

  const t = await getTranslations({ locale });
  const timeMetrics = calculateMaintenanceTimeMetrics(maintenance);

  const statusIcons = {
    REPORTED: FileText,
    ACKNOWLEDGED: CheckCircle,
    IN_PROGRESS: Clock,
    ON_HOLD: AlertTriangle,
    COMPLETED: CheckCircle,
    CANCELLED: AlertTriangle,
  };

  const StatusIcon = statusIcons[maintenance.status];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" asChild>
            <Link href={`/${locale}/dashboard/maintenance`}>
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {t("maintenance.requestDetails")}
            </h1>
            <p className="text-muted-foreground">
              {t("maintenance.requestNumber")}: {maintenance.request_number}
            </p>
          </div>
        </div>
        {maintenance.status !== "COMPLETED" && maintenance.status !== "CANCELLED" && (
          <Button asChild>
            <Link href={`/${locale}/dashboard/maintenance/${maintenance.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              {t("common.edit")}
            </Link>
          </Button>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          {/* Request Details */}
          <Card>
            <CardHeader>
              <CardTitle>{t("maintenance.requestDetails")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg">{maintenance.title}</h3>
                <p className="text-muted-foreground mt-2">{maintenance.description}</p>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">{t("maintenance.category")}</p>
                  <p className="font-medium">
                    {t(`maintenance.categories.${maintenance.category.toLowerCase()}`)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t("maintenance.priority")}</p>
                  <Badge variant={getPriorityColor(maintenance.priority) as any}>
                    <AlertTriangle className="mr-1 h-3 w-3" />
                    {t(`maintenance.priorities.${maintenance.priority.toLowerCase()}`)}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t("maintenance.status")}</p>
                  <Badge variant={getStatusColor(maintenance.status) as any}>
                    <StatusIcon className="mr-1 h-3 w-3" />
                    {t(`maintenance.statuses.${maintenance.status.toLowerCase()}`)}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t("maintenance.reportedDate")}</p>
                  <p className="font-medium">
                    {new Date(maintenance.reported_date).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                {t("maintenance.locationInformation")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">{t("maintenance.property")}</p>
                  <p className="font-medium">
                    {maintenance.property?.name_en || "—"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t("maintenance.unit")}</p>
                  <p className="font-medium">
                    {maintenance.unit ? `Unit ${maintenance.unit.unit_number}` : "—"}
                  </p>
                </div>
              </div>
              {maintenance.tenant && (
                <div>
                  <p className="text-sm text-muted-foreground">{t("maintenance.tenant")}</p>
                  <p className="font-medium">
                    {maintenance.tenant.first_name} {maintenance.tenant.last_name}
                  </p>
                  <p className="text-sm text-muted-foreground">{maintenance.tenant.email}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Contractor Information */}
          {(maintenance.contractor_name || maintenance.contractor_phone) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  {t("maintenance.contractorInformation")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">{t("maintenance.contractorName")}</p>
                    <p className="font-medium">{maintenance.contractor_name || "—"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t("maintenance.contractorPhone")}</p>
                    <p className="font-medium flex items-center gap-1">
                      <Phone className="h-4 w-4" />
                      {maintenance.contractor_phone || "—"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Status History */}
          {maintenance.status_history && maintenance.status_history.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>{t("maintenance.statusHistory")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {maintenance.status_history.map((history, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-2 h-2 rounded-full bg-primary mt-2" />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <Badge variant={getStatusColor(history.to_status) as any}>
                            {t(`maintenance.statuses.${history.to_status.toLowerCase()}`)}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {new Date(history.created_at).toLocaleString()}
                          </span>
                        </div>
                        {history.changed_by && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {t("maintenance.changedBy")}: User #{history.changed_by}
                          </p>
                        )}
                        {history.notes && (
                          <p className="text-sm mt-1">{history.notes}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Time Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                {t("maintenance.timeMetrics")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">{t("maintenance.responseTime")}</p>
                <p className="font-medium">
                  {timeMetrics.responseTime ? `${timeMetrics.responseTime} hours` : "—"}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t("maintenance.resolutionTime")}</p>
                <p className="font-medium">
                  {timeMetrics.resolutionTime ? `${timeMetrics.resolutionTime} hours` : "—"}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t("maintenance.totalDuration")}</p>
                <p className="font-medium">
                  {timeMetrics.totalDuration ? `${timeMetrics.totalDuration} hours` : "—"}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Cost Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Banknote className="h-5 w-5" />
                {t("maintenance.costInformation")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">{t("maintenance.estimatedCost")}</p>
                <p className="font-medium">
                  {maintenance.estimated_cost ? formatOMR(maintenance.estimated_cost.toString()) : "—"}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t("maintenance.actualCost")}</p>
                <p className="font-medium">
                  {maintenance.actual_cost ? formatOMR(maintenance.actual_cost.toString()) : "—"}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Scheduling */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                {t("maintenance.scheduling")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">{t("maintenance.scheduledDate")}</p>
                <p className="font-medium">
                  {maintenance.scheduled_date 
                    ? new Date(maintenance.scheduled_date).toLocaleDateString()
                    : "—"}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{t("maintenance.completionDate")}</p>
                <p className="font-medium">
                  {maintenance.completed_date 
                    ? new Date(maintenance.completed_date).toLocaleDateString()
                    : "—"}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Internal Notes */}
          {maintenance.internal_notes && (
            <Card>
              <CardHeader>
                <CardTitle>{t("maintenance.internalNotes")}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{maintenance.internal_notes}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}