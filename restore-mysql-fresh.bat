@echo off
echo ========================================
echo Fresh MySQL Data Directory Restore
echo ========================================
echo.

echo WARNING: This will reset MySQL to factory defaults
echo All databases will be lost except what we backup
echo.
set /p confirm="Continue? (y/n): "
if /i not "%confirm%"=="y" goto :end

echo.
echo 1. Stopping MySQL...
taskkill /f /im mysqld.exe 2>nul
timeout /t 3 /nobreak >nul

echo 2. Backing up property_management database...
if exist "C:\xampp\mysql\data\property_management" (
    if not exist "C:\xampp\mysql\property_backup" mkdir "C:\xampp\mysql\property_backup"
    xcopy "C:\xampp\mysql\data\property_management" "C:\xampp\mysql\property_backup\property_management\" /E /I /Y
    echo ✅ Database backed up
) else (
    echo ⚠️  No property_management database found
)

echo 3. Removing current data directory...
cd "C:\xampp\mysql"
if exist "data_old" rmdir /s /q "data_old"
ren "data" "data_old"
echo ✅ Old data directory renamed

echo 4. Restoring fresh data directory...
if exist "backup" (
    xcopy "backup" "data\" /E /I /Y
    echo ✅ Fresh data directory restored from backup
) else (
    echo ❌ No backup directory found
    echo Creating minimal data directory...
    mkdir "data"
    mkdir "data\mysql"
    mkdir "data\performance_schema"
    mkdir "data\test"
)

echo 5. Restoring property_management database...
if exist "property_backup\property_management" (
    xcopy "property_backup\property_management" "data\property_management\" /E /I /Y
    echo ✅ property_management database restored
)

echo.
echo ========================================
echo Fresh restore complete!
echo Try starting MySQL in XAMPP now
echo ========================================

:end
pause
