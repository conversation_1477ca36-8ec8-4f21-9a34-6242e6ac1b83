// Enable authentication middleware to handle protected routes
import { NextRequest, NextResponse } from "next/server";
import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';

import { authMiddleware } from "./middleware/auth-middleware";

// Create the internationalization middleware
const intlMiddleware = createMiddleware(routing);

export function middleware(req: NextRequest) {
  // First, handle authentication for all auth-related routes
  const pathname = req.nextUrl.pathname;
  const isDashboardRoute = pathname.includes("/dashboard") ||
                          pathname.match(/^\/(en|ar)\/dashboard/) ||
                          pathname.startsWith("/dashboard");

  const isLoginRoute = pathname.includes("/login") ||
                      pathname.match(/^\/(en|ar)\/login/) ||
                      pathname.match(/^\/(en|ar)\/auth\/login/) ||
                      pathname === "/login";

  // Run auth middleware for both dashboard and login routes
  if (isDashboardRoute || isLoginRoute) {
    const authResponse = authMiddleware(req);
    if (authResponse) {
      return authResponse;
    }
  }

  // Then handle internationalization
  return intlMiddleware(req);
}

export const config = {
  // Match all pathnames except for
  // - … if they start with `/api`, `/trpc`, `/_next` or `/_vercel`
  // - … the ones containing a dot (e.g. `favicon.ico`)
  matcher: '/((?!api|trpc|_next|_vercel|.*\\..*).*)'
};
