import { z } from "zod";
import type { PropertyType, Property, PropertyStatus, Amenity } from "@/generated/prisma";

// Property Type Schemas
export const propertyTypeSchema = z.object({
  name_en: z.string().min(2, "English name must be at least 2 characters"),
  name_ar: z.string().min(2, "Arabic name must be at least 2 characters"),
  description_en: z.string().optional().nullable(),
  description_ar: z.string().optional().nullable(),
});

export const propertyTypeUpdateSchema = propertyTypeSchema.partial();

export type PropertyTypeInput = z.infer<typeof propertyTypeSchema>;
export type PropertyTypeUpdateInput = z.infer<typeof propertyTypeUpdateSchema>;

// Property Schemas
export const propertySchema = z.object({
  name_en: z.string().min(2, "English name must be at least 2 characters"),
  name_ar: z.string().min(2, "Arabic name must be at least 2 characters"),
  address_en: z.string().min(5, "English address must be at least 5 characters"),
  address_ar: z.string().min(5, "Arabic address must be at least 5 characters"),
  property_type_id: z.number().int().positive("Property type is required"),
  base_rent: z.string().regex(/^\d+(\.\d{1,3})?$/, "Invalid rent format").or(z.number()),
  status: z.enum(["AVAILABLE", "RENTED", "UNDER_MAINTENANCE", "OUT_OF_SERVICE"]),
  total_area: z.string().regex(/^\d+(\.\d{1,2})?$/, "Invalid area format").optional().nullable().or(z.number().optional().nullable()),
  floors_count: z.number().int().min(0).optional().nullable().or(z.literal(0)),
  parking_spaces: z.number().int().min(0).optional().nullable().or(z.literal(0)),
  owner_id: z.number().int().min(0).optional().nullable().or(z.literal(0)),
});

export const propertyUpdateSchema = propertySchema.partial();

export type PropertyInput = z.infer<typeof propertySchema>;
export type PropertyUpdateInput = z.infer<typeof propertyUpdateSchema>;

// Filter types
export interface PropertyTypeFilters {
  search?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface PropertyFilters {
  search?: string;
  property_type_id?: number;
  status?: PropertyStatus;
  min_rent?: number;
  max_rent?: number;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// Extended types with relations
export interface PropertyTypeWithCount extends PropertyType {
  _count?: {
    properties: number;
  };
}

export interface PropertyWithRelations extends Property {
  property_type: PropertyType;
  amenities?: {
    amenity: Amenity;
  }[];
  owner?: {
    id: number;
    name_en: string;
    name_ar: string;
  };
  units?: {
    id: number;
    unit_number: string;
    unit_name_en?: string | null;
    unit_name_ar?: string | null;
    status: string;
    rent_amount: string;
    floor_number?: number | null;
    rooms_count?: number | null;
    bathrooms_count?: number | null;
  }[];
  creator?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
  updater?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
}