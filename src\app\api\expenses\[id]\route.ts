import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { expenseApiSchema } from "@/types/expense";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { ApiResponseBuilder } from "@/lib/api-response";

const prisma = new PrismaClient();

// GET /api/expenses/[id] - Get expense by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for expenses
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "expenses", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view expenses");
    }

    const { id } = await params;
    console.log(`Expense API: GET request received for ID: ${id}`);

    // For development, skip authentication
    if (process.env.NODE_ENV === "development") {
      console.log("Expense API: Development mode - skipping authentication");
    }

    const expenseId = parseInt(id, 10);
    if (isNaN(expenseId)) {
      return NextResponse.json(
        { error: "Invalid expense ID" },
        { status: 400 }
      );
    }

    const expense = await prisma.expense.findUnique({
      where: { id: expenseId },
      include: {
        category: true,
        attachments: true,
        approvals: {
          orderBy: { created_at: "desc" }
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        approver: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });

    if (!expense) {
      return NextResponse.json(
        { error: "Expense not found" },
        { status: 404 }
      );
    }

    console.log(`Expense API: Expense retrieved: ${expense.description}`);
    return NextResponse.json(expense);

  } catch (error) {
    console.error("Expense API: Error fetching expense:", error);
    return NextResponse.json(
      { error: "Failed to fetch expense" },
      { status: 500 }
    );
  }
}

// PUT /api/expenses/[id] - Update expense
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for expenses
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "expenses", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update expenses");
    }

    const { id } = await params;
    console.log(`Expense API: PUT request received for ID: ${id}`);

    // For development, skip authentication
    if (process.env.NODE_ENV === "development") {
      console.log("Expense API: Development mode - skipping authentication");
    }

    const expenseId = parseInt(id, 10);
    if (isNaN(expenseId)) {
      return NextResponse.json(
        { error: "Invalid expense ID" },
        { status: 400 }
      );
    }

    const body = await request.json();
    console.log("Expense API: Request body:", body);

    // Validate the request body
    const validationResult = expenseApiSchema.safeParse(body);
    if (!validationResult.success) {
      console.log("Expense API: Validation failed:", validationResult.error);
      return NextResponse.json(
        { 
          error: "Validation failed", 
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Check if expense exists
    const existingExpense = await prisma.expense.findUnique({
      where: { id: expenseId }
    });

    if (!existingExpense) {
      return NextResponse.json(
        { error: "Expense not found" },
        { status: 404 }
      );
    }

    // Verify category exists
    const category = await prisma.expenseCategory.findUnique({
      where: { id: data.category_id }
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Calculate next due date for recurring expenses
    let nextDueDate = null;
    if (data.is_recurring && data.recurring_frequency) {
      const currentDate = new Date(data.date);
      switch (data.recurring_frequency) {
        case "MONTHLY":
          nextDueDate = new Date(currentDate.setMonth(currentDate.getMonth() + 1));
          break;
        case "QUARTERLY":
          nextDueDate = new Date(currentDate.setMonth(currentDate.getMonth() + 3));
          break;
        case "YEARLY":
          nextDueDate = new Date(currentDate.setFullYear(currentDate.getFullYear() + 1));
          break;
      }
    }

    // Update the expense
    const updatedExpense = await prisma.expense.update({
      where: { id: expenseId },
      data: {
        date: data.date,
        description: data.description,
        amount: data.amount,
        category_id: data.category_id,
        payment_method: data.payment_method,
        paid_by: data.paid_by,
        notes: data.notes || null,
        status: data.status,
        is_recurring: data.is_recurring,
        recurring_frequency: data.recurring_frequency || null,
        next_due_date: nextDueDate,
        updated_by: 1, // TODO: Get from authenticated user
      },
      include: {
        category: true,
        attachments: true,
        approvals: {
          orderBy: { created_at: "desc" }
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        approver: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });

    console.log(`Expense API: Expense updated: ${updatedExpense.id}`);
    return NextResponse.json(updatedExpense);

  } catch (error) {
    console.error("Expense API: Error updating expense:", error);
    return NextResponse.json(
      { error: "Failed to update expense" },
      { status: 500 }
    );
  }
}

// DELETE /api/expenses/[id] - Delete expense
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for expenses
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "expenses", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete expenses");
    }

    const { id } = await params;
    console.log(`Expense API: DELETE request received for ID: ${id}`);

    // For development, skip authentication
    if (process.env.NODE_ENV === "development") {
      console.log("Expense API: Development mode - skipping authentication");
    }

    const expenseId = parseInt(id, 10);
    if (isNaN(expenseId)) {
      return NextResponse.json(
        { error: "Invalid expense ID" },
        { status: 400 }
      );
    }

    // Check if expense exists
    const existingExpense = await prisma.expense.findUnique({
      where: { id: expenseId },
      include: {
        attachments: true
      }
    });

    if (!existingExpense) {
      return NextResponse.json(
        { error: "Expense not found" },
        { status: 404 }
      );
    }

    // TODO: Delete associated files from filesystem
    // for (const attachment of existingExpense.attachments) {
    //   await deleteFile(attachment.file_path);
    // }

    // Delete the expense (cascading deletes will handle attachments and approvals)
    await prisma.expense.delete({
      where: { id: expenseId }
    });

    console.log(`Expense API: Expense deleted: ${expenseId}`);
    return NextResponse.json({ message: "Expense deleted successfully" });

  } catch (error) {
    console.error("Expense API: Error deleting expense:", error);
    return NextResponse.json(
      { error: "Failed to delete expense" },
      { status: 500 }
    );
  }
}
