-- Add reference_number column to association_transactions table
-- This allows tracking payment reference numbers (check numbers, transfer IDs, etc.) in transaction records

ALTER TABLE `association_transactions`
ADD COLUMN `reference_number` VARCHAR(100) NULL AFTER `payment_method`;

-- Add index for searching by reference number
CREATE INDEX `idx_transaction_reference` ON `association_transactions`(`reference_number`);