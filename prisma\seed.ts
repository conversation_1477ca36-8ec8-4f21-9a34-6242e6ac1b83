import { PrismaClient } from '@prisma/client'
import { Decimal } from '@prisma/client/runtime/library'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

// Helper function to generate random dates
function randomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
}

// Helper function to generate random decimal
function randomDecimal(min: number, max: number, precision: number = 3): Decimal {
  const value = Math.random() * (max - min) + min
  return new Decimal(value.toFixed(precision))
}

async function main() {
  console.log('🌱 Starting comprehensive database seeding...')

  // Clear existing data (optional - comment out if you want to keep existing data)
  console.log('🧹 Clearing existing data...')
  try {
    await prisma.maintenanceStatusHistory.deleteMany()
    await prisma.maintenanceAttachment.deleteMany()
    await prisma.maintenanceRequest.deleteMany()
    await prisma.ownerPayoutDetail.deleteMany()
    await prisma.ownerPayout.deleteMany()
    await prisma.paymentAllocation.deleteMany()
    await prisma.payment.deleteMany()
    await prisma.invoiceItem.deleteMany()
    await prisma.invoice.deleteMany()
    await prisma.contractDocument.deleteMany()
    await prisma.contractRenewal.deleteMany()
    await prisma.contractTenant.deleteMany()
    await prisma.contract.deleteMany()
    await prisma.emergencyContact.deleteMany()
    await prisma.tenantDocument.deleteMany()
    await prisma.tenant.deleteMany()
    await prisma.unitAmenity.deleteMany()
    await prisma.unit.deleteMany()
    await prisma.propertyAmenity.deleteMany()
    await prisma.property.deleteMany()
    await prisma.propertyOwner.deleteMany()
    await prisma.amenity.deleteMany()
    await prisma.propertyType.deleteMany()
    await prisma.userRole.deleteMany()
    await prisma.rolePermission.deleteMany()
    await prisma.permission.deleteMany()
    await prisma.role.deleteMany()
    await prisma.user.deleteMany()
    console.log('✅ Existing data cleared')
  } catch (error) {
    console.log('⚠️ Some tables were already empty or had constraints')
  }

  // 1. Create Roles and Permissions
  console.log('👥 Creating roles and permissions...')

  const permissions = [
    { module: 'properties', action: 'CREATE' },
    { module: 'properties', action: 'READ' },
    { module: 'properties', action: 'UPDATE' },
    { module: 'properties', action: 'DELETE' },
    { module: 'tenants', action: 'CREATE' },
    { module: 'tenants', action: 'READ' },
    { module: 'tenants', action: 'UPDATE' },
    { module: 'tenants', action: 'DELETE' },
    { module: 'contracts', action: 'CREATE' },
    { module: 'contracts', action: 'READ' },
    { module: 'contracts', action: 'UPDATE' },
    { module: 'contracts', action: 'DELETE' },
    { module: 'invoices', action: 'CREATE' },
    { module: 'invoices', action: 'READ' },
    { module: 'invoices', action: 'UPDATE' },
    { module: 'invoices', action: 'DELETE' },
    { module: 'payments', action: 'CREATE' },
    { module: 'payments', action: 'READ' },
    { module: 'payments', action: 'UPDATE' },
    { module: 'payments', action: 'DELETE' },
    { module: 'owners-associations', action: 'CREATE' },
    { module: 'owners-associations', action: 'READ' },
    { module: 'owners-associations', action: 'UPDATE' },
    { module: 'owners-associations', action: 'DELETE' },
    { module: 'maintenance', action: 'CREATE' },
    { module: 'maintenance', action: 'READ' },
    { module: 'maintenance', action: 'UPDATE' },
    { module: 'maintenance', action: 'DELETE' },
    { module: 'users', action: 'CREATE' },
    { module: 'users', action: 'READ' },
    { module: 'users', action: 'UPDATE' },
    { module: 'users', action: 'DELETE' },
  ]

  for (const perm of permissions) {
    await prisma.permission.create({
      data: {
        module: perm.module,
        action: perm.action as any,
        description: `${perm.action} access to ${perm.module}`,
      },
    })
  }

  const adminRole = await prisma.role.create({
    data: {
      name: 'Admin',
      description: 'System Administrator with full access',
      is_system: true,
    },
  })

  const managerRole = await prisma.role.create({
    data: {
      name: 'Property Manager',
      description: 'Property Management Staff',
      is_system: false,
    },
  })

  const tenantRole = await prisma.role.create({
    data: {
      name: 'Tenant',
      description: 'Property Tenant with limited access',
      is_system: false,
    },
  })

  // Assign all permissions to admin role
  const allPermissions = await prisma.permission.findMany()
  for (const permission of allPermissions) {
    await prisma.rolePermission.create({
      data: {
        role_id: adminRole.id,
        permission_id: permission.id,
      },
    })
  }

  // 2. Create Users
  console.log('👤 Creating users...')

  const hashedPassword = await bcrypt.hash('admin123', 10)

  const adminUser = await prisma.user.create({
    data: {
      username: 'admin',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      first_name: 'System',
      last_name: 'Administrator',
      phone: '+968 2123 4567',
      status: 'ACTIVE',
      email_verified: true,
    },
  })

  await prisma.userRole.create({
    data: {
      user_id: adminUser.id,
      role_id: adminRole.id,
    },
  })

  const managerUser = await prisma.user.create({
    data: {
      username: 'manager',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      first_name: 'Ahmed',
      last_name: 'Al-Rashid',
      phone: '+968 2234 5678',
      status: 'ACTIVE',
      email_verified: true,
    },
  })

  await prisma.userRole.create({
    data: {
      user_id: managerUser.id,
      role_id: managerRole.id,
    },
  })

  console.log('✅ Users and roles created')

  // 3. Create Property Types
  console.log('🏠 Creating property types...')

  const propertyTypes = [
    {
      name_en: 'Villa',
      name_ar: 'فيلا',
      description_en: 'Standalone villa property',
      description_ar: 'عقار فيلا منفصلة',
    },
    {
      name_en: 'Apartment',
      name_ar: 'شقة',
      description_en: 'Apartment unit in building',
      description_ar: 'وحدة شقة في مبنى',
    },
    {
      name_en: 'Commercial Building',
      name_ar: 'مبنى تجاري',
      description_en: 'Commercial office building',
      description_ar: 'مبنى مكاتب تجاري',
    },
    {
      name_en: 'Townhouse',
      name_ar: 'بيت شعبي',
      description_en: 'Multi-story townhouse',
      description_ar: 'بيت شعبي متعدد الطوابق',
    },
    {
      name_en: 'Studio',
      name_ar: 'استوديو',
      description_en: 'Studio apartment',
      description_ar: 'شقة استوديو',
    },
  ]

  const createdPropertyTypes = []
  for (const type of propertyTypes) {
    const propertyType = await prisma.propertyType.create({
      data: type,
    })
    createdPropertyTypes.push(propertyType)
  }

  // 4. Create Amenities
  console.log('🏊 Creating amenities...')

  const amenities = [
    { name_en: 'Swimming Pool', name_ar: 'مسبح', icon: 'waves' },
    { name_en: 'Gym', name_ar: 'صالة رياضية', icon: 'dumbbell' },
    { name_en: 'Parking', name_ar: 'موقف سيارات', icon: 'car' },
    { name_en: 'Garden', name_ar: 'حديقة', icon: 'trees' },
    { name_en: 'Security', name_ar: 'أمن', icon: 'shield' },
    { name_en: 'Central AC', name_ar: 'تكييف مركزي', icon: 'snowflake' },
    { name_en: 'Elevator', name_ar: 'مصعد', icon: 'move-vertical' },
    { name_en: 'Balcony', name_ar: 'شرفة', icon: 'home' },
    { name_en: 'Storage Room', name_ar: 'غرفة تخزين', icon: 'archive' },
    { name_en: 'Maid Room', name_ar: 'غرفة خادمة', icon: 'user' },
  ]

  const createdAmenities = []
  for (const amenity of amenities) {
    const createdAmenity = await prisma.amenity.create({
      data: amenity,
    })
    createdAmenities.push(createdAmenity)
  }

  // 5. Create Property Owners
  console.log('👨‍💼 Creating property owners...')

  const propertyOwners = [
    {
      name_en: 'Ahmed Al-Rashid',
      name_ar: 'أحمد الراشد',
      email: '<EMAIL>',
      phone: '+968 2123 4567',
      mobile: '+968 9123 4567',
      address_en: 'Al-Khuwair, Muscat, Oman',
      address_ar: 'الخوير، مسقط، عمان',
      tax_id: 'TAX001',
      bank_name: 'Bank Muscat',
      bank_account_number: '**********',
      bank_iban: 'OM81BMAG1234**********56',
      management_fee_percentage: new Decimal('10.00'),
      status: 'ACTIVE' as const,
    },
    {
      name_en: 'Fatima Al-Zahra',
      name_ar: 'فاطمة الزهراء',
      email: '<EMAIL>',
      phone: '+968 2234 5678',
      mobile: '+968 9234 5678',
      address_en: 'Ruwi, Muscat, Oman',
      address_ar: 'روي، مسقط، عمان',
      tax_id: 'TAX002',
      bank_name: 'HSBC Bank Oman',
      bank_account_number: '**********',
      bank_iban: 'OM82HSBC234**********567',
      management_fee_percentage: new Decimal('8.50'),
      status: 'ACTIVE' as const,
    },
    {
      name_en: 'Mohammed Al-Balushi',
      name_ar: 'محمد البلوشي',
      email: '<EMAIL>',
      phone: '+968 2345 6789',
      mobile: '+968 9345 6789',
      address_en: 'Seeb, Muscat, Oman',
      address_ar: 'السيب، مسقط، عمان',
      tax_id: 'TAX003',
      bank_name: 'National Bank of Oman',
      bank_account_number: '**********',
      bank_iban: 'OM83NBOM34**********5678',
      management_fee_percentage: new Decimal('12.00'),
      status: 'ACTIVE' as const,
    },
    {
      name_en: 'Salim Al-Hinai',
      name_ar: 'سالم الهنائي',
      email: '<EMAIL>',
      phone: '+968 2456 7890',
      mobile: '+968 9456 7890',
      address_en: 'Bausher, Muscat, Oman',
      address_ar: 'بوشر، مسقط، عمان',
      tax_id: 'TAX004',
      bank_name: 'Ahli Bank',
      bank_account_number: '**********',
      bank_iban: 'OM84AHLI4**********56789',
      management_fee_percentage: new Decimal('9.00'),
      status: 'ACTIVE' as const,
    },
    {
      name_en: 'Aisha Al-Kindi',
      name_ar: 'عائشة الكندي',
      email: '<EMAIL>',
      phone: '+968 2567 8901',
      mobile: '+968 9567 8901',
      address_en: 'Al-Mawaleh, Muscat, Oman',
      address_ar: 'المعولة، مسقط، عمان',
      tax_id: 'TAX005',
      bank_name: 'Bank Dhofar',
      bank_account_number: '**********',
      bank_iban: '************************',
      management_fee_percentage: new Decimal('11.50'),
      status: 'ACTIVE' as const,
    },
  ]

  const createdOwners = []
  for (const owner of propertyOwners) {
    const propertyOwner = await prisma.propertyOwner.create({
      data: {
        ...owner,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    createdOwners.push(propertyOwner)
  }

  // 6. Create Properties
  console.log('🏢 Creating properties...')

  const properties = [
    {
      name_en: 'Luxury Villa in Al-Khuwair',
      name_ar: 'فيلا فاخرة في الخوير',
      address_en: 'Street 123, Al-Khuwair, Muscat, Oman',
      address_ar: 'شارع 123، الخوير، مسقط، عمان',
      property_type_id: createdPropertyTypes[0].id, // Villa
      owner_id: createdOwners[0].id,
      base_rent: new Decimal('1200.000'),
      status: 'AVAILABLE' as const,
      total_area: new Decimal('500.00'),
      floors_count: 2,
      parking_spaces: 3,
    },
    {
      name_en: 'Modern Apartment Complex',
      name_ar: 'مجمع شقق حديث',
      address_en: 'Building 45, Ruwi, Muscat, Oman',
      address_ar: 'مبنى 45، روي، مسقط، عمان',
      property_type_id: createdPropertyTypes[1].id, // Apartment
      owner_id: createdOwners[1].id,
      base_rent: new Decimal('800.000'),
      status: 'RENTED' as const,
      total_area: new Decimal('1200.00'),
      floors_count: 5,
      parking_spaces: 20,
    },
    {
      name_en: 'Commercial Tower',
      name_ar: 'برج تجاري',
      address_en: 'CBD Area, Muscat, Oman',
      address_ar: 'منطقة الأعمال المركزية، مسقط، عمان',
      property_type_id: createdPropertyTypes[2].id, // Commercial
      owner_id: createdOwners[2].id,
      base_rent: new Decimal('2500.000'),
      status: 'RENTED' as const,
      total_area: new Decimal('2000.00'),
      floors_count: 10,
      parking_spaces: 50,
    },
    {
      name_en: 'Family Townhouse',
      name_ar: 'بيت عائلي',
      address_en: 'Seeb Residential Area, Muscat, Oman',
      address_ar: 'منطقة السيب السكنية، مسقط، عمان',
      property_type_id: createdPropertyTypes[3].id, // Townhouse
      owner_id: createdOwners[3].id,
      base_rent: new Decimal('950.000'),
      status: 'AVAILABLE' as const,
      total_area: new Decimal('350.00'),
      floors_count: 3,
      parking_spaces: 2,
    },
    {
      name_en: 'Studio Apartments',
      name_ar: 'شقق استوديو',
      address_en: 'Al-Mawaleh, Muscat, Oman',
      address_ar: 'المعولة، مسقط، عمان',
      property_type_id: createdPropertyTypes[4].id, // Studio
      owner_id: createdOwners[4].id,
      base_rent: new Decimal('400.000'),
      status: 'RENTED' as const,
      total_area: new Decimal('600.00'),
      floors_count: 4,
      parking_spaces: 15,
    },
  ]

  const createdProperties = []
  for (const property of properties) {
    const createdProperty = await prisma.property.create({
      data: {
        ...property,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    createdProperties.push(createdProperty)
  }

  // Add amenities to properties
  for (let i = 0; i < createdProperties.length; i++) {
    const property = createdProperties[i]
    const amenityCount = Math.floor(Math.random() * 5) + 3 // 3-7 amenities per property
    const selectedAmenities = createdAmenities
      .sort(() => 0.5 - Math.random())
      .slice(0, amenityCount)

    for (const amenity of selectedAmenities) {
      await prisma.propertyAmenity.create({
        data: {
          property_id: property.id,
          amenity_id: amenity.id,
        },
      })
    }
  }

  console.log('🎉 Basic seeding completed successfully!')
  console.log(`✅ Created:`)
  console.log(`   - ${createdPropertyTypes.length} property types`)
  console.log(`   - ${createdAmenities.length} amenities`)
  console.log(`   - ${createdOwners.length} property owners`)
  console.log(`   - ${createdProperties.length} properties`)
  console.log(`   - 2 users with roles`)
  console.log(`   - ${allPermissions.length} permissions`)
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })