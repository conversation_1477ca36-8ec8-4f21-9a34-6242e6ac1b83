import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

// GET /api/maintenance/[id]/attachments - Get attachments for a maintenance request
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for maintenance
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "maintenance", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to read maintenance");
    }

    const { id } = await params;


    const requestId = parseInt(id);
    
    if (isNaN(requestId)) {
      return ApiResponseBuilder.error("Invalid maintenance request ID", "BAD_REQUEST", 400);
    }

    // Check if maintenance request exists
    const maintenanceRequest = await db.maintenanceRequest.findUnique({
      where: { id: requestId },
    });

    if (!maintenanceRequest) {
      return ApiResponseBuilder.error("Maintenance request not found", "NOT_FOUND", 404);
    }

    const attachments = await db.maintenanceAttachment.findMany({
      where: { request_id: requestId },
      include: {
        uploader: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
      orderBy: {
        created_at: "desc",
      },
    });

    return ApiResponseBuilder.success(attachments);
  } catch (error) {
    console.error("Error fetching maintenance attachments:", error);
    return ApiResponseBuilder.error("Failed to fetch maintenance attachments", "INTERNAL_ERROR", 500);
  }
}

// POST /api/maintenance/[id]/attachments - Upload attachment for a maintenance request
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has CREATE permission for maintenance
    const canCreate = hasPermission(userPermissions, "maintenance", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create maintenance");
    }

    const { id } = await params;


    const requestId = parseInt(id);
    
    if (isNaN(requestId)) {
      return ApiResponseBuilder.error("Invalid maintenance request ID", "BAD_REQUEST", 400);
    }

    // Check if maintenance request exists
    const maintenanceRequest = await db.maintenanceRequest.findUnique({
      where: { id: requestId },
    });

    if (!maintenanceRequest) {
      return ApiResponseBuilder.error("Maintenance request not found", "NOT_FOUND", 404);
    }

    // Get form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    
    if (!file) {
      return ApiResponseBuilder.error("No file provided", "BAD_REQUEST", 400);
    }

    // Validate file type and size
    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "application/pdf",
      "text/plain",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];

    if (!allowedTypes.includes(file.type)) {
      return ApiResponseBuilder.error("File type not allowed", "BAD_REQUEST", 400);
    }

    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return ApiResponseBuilder.error("File size exceeds 10MB limit", "BAD_REQUEST", 400);
    }

    // Generate unique filename
    const timestamp = Date.now();
    const extension = file.name.split(".").pop();
    const fileName = `maintenance_${requestId}_${timestamp}.${extension}`;
    const filePath = `/uploads/maintenance/${fileName}`;

    // TODO: Save file to storage (implement file upload logic)
    // For now, we'll just store the file information in the database
    
    const attachment = await db.maintenanceAttachment.create({
      data: {
        request_id: requestId,
        file_name: fileName,
        file_path: filePath,
        file_size: file.size,
        mime_type: file.type,
        uploaded_by: undefined, // TODO: Get from auth
      },
      include: {
        uploader: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    return ApiResponseBuilder.success(attachment);
  } catch (error: any) {
    console.error("Error uploading maintenance attachment:", error);
    return ApiResponseBuilder.error("Failed to upload attachment", "INTERNAL_ERROR", 500);
  }
}