"use client";

import { useState, useEffect } from "react";
import { MemberPaymentsTable } from "./member-payments-table";
import { GenerateSubscriptionPayments } from "./generate-subscription-payments";
import { apiClient } from "@/lib/api-client";
import { cn } from "@/lib/utils";
import { useRTL } from "@/hooks/use-rtl";

interface AssociationPaymentsWrapperProps {
  associationId: number;
}

export function AssociationPaymentsWrapper({ associationId }: AssociationPaymentsWrapperProps) {
  const { isRTL } = useRTL();
  const [subscriptions, setSubscriptions] = useState([]);
  const [members, setMembers] = useState([]);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    // Fetch subscriptions
    apiClient.get(`/api/owners-associations/${associationId}/subscriptions`)
      .then(response => {
        if (response.success) {
          setSubscriptions(response.data.subscriptions || response.data || []);
        }
      })
      .catch(error => console.error("Error fetching subscriptions:", error));

    // Fetch members
    apiClient.get(`/api/owners-associations/${associationId}/members`)
      .then(response => {
        if (response.success) {
          const membersList = response.data?.members || response.data || [];
          setMembers(membersList);
        }
      })
      .catch(error => console.error("Error fetching members:", error));
  }, [associationId]);

  const handleGenerateSuccess = () => {
    // Refresh the payments table
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="space-y-4">
      {subscriptions.length > 0 && members.length > 0 && (
        <div className={cn("flex justify-end", isRTL && "justify-start")}>
          <GenerateSubscriptionPayments
            associationId={associationId}
            subscriptions={subscriptions}
            members={members}
            onSuccess={handleGenerateSuccess}
          />
        </div>
      )}
      <MemberPaymentsTable key={refreshKey} associationId={associationId} />
    </div>
  );
}