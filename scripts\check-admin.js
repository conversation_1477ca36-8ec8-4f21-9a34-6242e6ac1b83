const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkAdmin() {
  try {
    const admin = await prisma.user.findUnique({
      where: { username: 'admin' },
      include: { 
        user_roles: {
          include: {
            role: true
          }
        }
      }
    });
    
    if (!admin) {
      console.log('Admin user not found');
      return;
    }
    
    console.log('Admin user:', {
      id: admin.id,
      username: admin.username,
      email: admin.email,
      status: admin.status,
      roles: admin.user_roles.map(ur => ur.role.name).join(', ')
    });
    
    // Get first role for permissions
    if (admin.user_roles.length > 0) {
      const roleId = admin.user_roles[0].role_id;
      const permissions = await prisma.rolePermission.findMany({
        where: { role_id: roleId },
        include: { permission: true }
      });
      console.log('\nAdmin permissions:', permissions.length);
      permissions.forEach(p => {
        const perms = [];
        if (p.permission.can_view) perms.push('view');
        if (p.permission.can_create) perms.push('create');
        if (p.permission.can_update) perms.push('update');
        if (p.permission.can_delete) perms.push('delete');
        console.log(`- ${p.permission.resource}: ${perms.join(', ')}`);
      });
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdmin();