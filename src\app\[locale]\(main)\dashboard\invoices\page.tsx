import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import Link from "next/link";
import { Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { InvoicesDataTable } from "./_components/invoices-data-table";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "invoices" });

  return {
    title: t("title"),
    description: t("description"),
  };
}

export default async function InvoicesPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "invoices" });
  const tCommon = await getTranslations({ locale, namespace: "common" });

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="space-y-1">
          <h2 className="text-xl font-bold tracking-tight sm:text-2xl">
            {t("title")}
          </h2>
          <p className="text-sm text-muted-foreground sm:text-base">
            {t("description")}
          </p>
        </div>
        <Button asChild className="w-full sm:w-auto">
          <Link href={`/${locale}/dashboard/invoices/new`}>
            <Plus className="mr-2 h-4 w-4" />
            <span className="sm:inline">{t("actions.create")}</span>
          </Link>
        </Button>
      </div>

      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${locale}/dashboard`}>
              {tCommon("navigation.dashboard")}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t("title")}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <Card>
        <CardHeader>
          <CardTitle>{t("allInvoices")}</CardTitle>
          <CardDescription>
            {t("viewManageInvoices")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <InvoicesDataTable />
        </CardContent>
      </Card>
    </div>
  );
}