"use client";

import { useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import type { ExpenseWithDetails } from "@/types/expense";

interface DeleteExpenseDialogProps {
  expense: ExpenseWithDetails;
  children: React.ReactNode;
  onSuccess?: () => void;
  redirectUrl?: string;
}

export function DeleteExpenseDialog({ expense, children, onSuccess, redirectUrl }: DeleteExpenseDialogProps) {
  const t = useTranslations("expenses");
  const tMessages = useTranslations("expenses.messages");
  const tCommon = useTranslations("common");
  const router = useRouter();

  const [isDeleting, setIsDeleting] = useState(false);
  const [open, setOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      setError(null);

      const response = await fetch(`/api/expenses/${expense.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        
        if (response.status === 404) {
          throw new Error(tMessages("expenseNotFound") || "Expense not found");
        }
        
        throw new Error(errorData.error || tMessages("deleteError") || "Failed to delete expense");
      }

      toast.success(tMessages("deleteSuccess"));
      setOpen(false);

      if (redirectUrl) {
        window.location.href = redirectUrl;
      } else if (onSuccess) {
        onSuccess();
      } else {
        router.refresh();
      }

    } catch (error) {
      console.error("Error deleting expense:", error);
      const errorMessage = error instanceof Error ? error.message : tMessages("deleteError") || "Failed to delete expense";
      setError(errorMessage);
      
      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        {children}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            {t("deleteExpense")}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {tMessages("confirmDelete")}
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-4">
          {/* Expense details */}
          <div className="rounded-lg border p-4 space-y-2">
            <div className="flex justify-between">
              <span className="font-medium">{t("fields.description")}:</span>
              <span>{expense.description}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">{t("fields.amount")}:</span>
              <span className="font-mono">{expense.amount.toString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">{t("fields.date")}:</span>
              <span>{new Date(expense.date).toLocaleDateString()}</span>
            </div>
          </div>

          {/* Warning */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {tMessages("deleteWarning") || "This action cannot be undone. This will permanently delete the expense and all associated data."}
            </AlertDescription>
          </Alert>

          {/* Error display */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>
            {tCommon("cancel")}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                {tMessages("deleting") || "Deleting..."}
              </div>
            ) : (
              tCommon("delete")
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
