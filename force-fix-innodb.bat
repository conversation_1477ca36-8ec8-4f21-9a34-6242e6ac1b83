@echo off
echo ========================================
echo FORCE FIX InnoDB - Complete Reset
echo ========================================
echo.

echo 1. Stopping ALL MySQL processes forcefully...
taskkill /f /im mysqld.exe 2>nul
taskkill /f /im mysql.exe 2>nul
timeout /t 5 /nobreak >nul
echo ✅ All MySQL processes stopped

echo.
echo 2. Navigating to MySQL data directory...
cd "C:\xampp\mysql\data"
echo Current directory: %CD%

echo.
echo 3. Backing up important databases...
if exist "property_management" (
    if not exist "..\property_backup" mkdir "..\property_backup"
    xcopy "property_management" "..\property_backup\property_management\" /E /I /Y /Q
    echo ✅ property_management database backed up
) else (
    echo ⚠️  No property_management database found
)

echo.
echo 4. FORCE removing ALL InnoDB files...
del /f /q ibdata1 2>nul && echo ✅ ibdata1 removed
del /f /q ib_logfile0 2>nul && echo ✅ ib_logfile0 removed  
del /f /q ib_logfile1 2>nul && echo ✅ ib_logfile1 removed
del /f /q ib_buffer_pool 2>nul && echo ✅ ib_buffer_pool removed
del /f /q auto.cnf 2>nul && echo ✅ auto.cnf removed
del /f /q mysql-bin.* 2>nul && echo ✅ Binary logs removed
del /f /q *.err 2>nul && echo ✅ Error logs removed

echo.
echo 5. Removing any corrupted master info files...
del /f /q master*.info 2>nul && echo ✅ Master info files removed
del /f /q relay*.info 2>nul && echo ✅ Relay info files removed

echo.
echo 6. Creating minimal MySQL configuration...
cd "..\bin"
echo # Minimal MySQL Configuration > my_minimal.ini
echo [mysqld] >> my_minimal.ini
echo port=3306 >> my_minimal.ini
echo basedir=C:/xampp/mysql >> my_minimal.ini
echo datadir=C:/xampp/mysql/data >> my_minimal.ini
echo skip-grant-tables >> my_minimal.ini
echo skip-networking >> my_minimal.ini
echo skip-slave-start >> my_minimal.ini
echo default-storage-engine=MyISAM >> my_minimal.ini
echo key_buffer_size=16M >> my_minimal.ini
echo max_allowed_packet=1M >> my_minimal.ini
echo table_open_cache=64 >> my_minimal.ini
echo sort_buffer_size=512K >> my_minimal.ini
echo net_buffer_length=8K >> my_minimal.ini
echo read_buffer_size=256K >> my_minimal.ini
echo read_rnd_buffer_size=512K >> my_minimal.ini
echo myisam_sort_buffer_size=8M >> my_minimal.ini
echo log-error=mysql_error.log >> my_minimal.ini

echo.
echo 7. Backing up current my.ini and applying minimal config...
if exist "my.ini" copy my.ini my.ini.backup >nul
copy my_minimal.ini my.ini >nul
echo ✅ Minimal configuration applied (without InnoDB)

echo.
echo ========================================
echo FORCE FIX COMPLETE!
echo.
echo Now try starting MySQL with:
echo mysqld --console --skip-grant-tables --skip-networking
echo.
echo This will start MySQL with MyISAM engine only
echo (no InnoDB) which should work
echo ========================================
pause
