-- AlterTable
ALTER TABLE `association_members` ADD COLUMN `property_id` INTEGER NULL,
    ADD COLUMN `status` ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE',
    MODIFY `ownership_percentage` DECIMAL(5, 2) NULL,
    MODIFY `join_date` DATE NULL;

-- AddForeignKey
ALTER TABLE `association_members` ADD CONSTRAINT `association_members_property_id_fkey` FOREIGN KEY (`property_id`) REFERENCES `properties`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
