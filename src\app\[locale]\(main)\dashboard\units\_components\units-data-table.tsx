"use client";

import * as React from "react";
import { useState, useEffect, useCallback } from "react";
import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { useDataTableInstance } from "@/hooks/use-data-table-instance";
import { DataTable } from "@/components/data-table/data-table";
import { DataTablePagination } from "@/components/data-table/data-table-pagination";
import { UnitsViewOptions } from "./units-view-options";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Loader2, RefreshCw, X } from "lucide-react";
import { apiClient } from "@/lib/api-client";
import { ErrorState } from "@/components/error-state";
import type { UnitWithRelations, UnitFilters } from "@/types/unit";
import type { PropertyWithRelations } from "@/types/property";

import { getUnitColumns } from "./unit-columns";

interface UnitListResponse {
  success: boolean;
  data: UnitWithRelations[];
  meta: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

export function UnitsDataTable() {
  const locale = useLocale();
  const router = useRouter();
  const t = useTranslations('units');
  const tTable = useTranslations('units.table');
  const tCommon = useTranslations('common');
  const tStatus = useTranslations('units.status');

  const [data, setData] = useState<UnitWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [retrying, setRetrying] = useState(false);
  const [properties, setProperties] = useState<PropertyWithRelations[]>([]);
  const [filters, setFilters] = useState<UnitFilters>({
    page: 1,
    pageSize: 10,
    sortBy: "created_at",
    sortOrder: "desc",
  });
  const [searchInput, setSearchInput] = useState("");
  const [totalPages, setTotalPages] = useState(0);
  const [total, setTotal] = useState(0);

  const columnTranslations = {
    id: tTable('id'),
    unitNumber: tTable('unitNumber'),
    unitName: tTable('unitName'),
    property: tTable('property'),
    rooms: tTable('rooms'),
    majalis: tTable('majalis'),
    bathrooms: tTable('bathrooms'),
    area: tTable('area'),
    rentAmount: tTable('rentAmount'),
    status: tTable('status'),
    floorNumber: tTable('floorNumber'),
    amenities: tTable('amenities'),
    createdAt: tTable('createdAt'),
    actions: tTable('actions'),
    selectAll: tTable('selectAll'),
    selectRow: tTable('selectRow'),
    openMenu: tTable('openMenu'),
    viewDetails: tTable('viewDetails'),
    editUnit: tTable('editUnit'),
    deleteUnit: tTable('deleteUnit'),
    viewUnit: tTable('viewUnit'),
    deleteConfirmation: tTable('deleteConfirmation'),
    deleteWarning: tTable('deleteWarning'),
    deleteWarningRented: tTable('deleteWarningRented'),
    cannotDeleteRented: tTable('cannotDeleteRented'),
    unitNotFound: tTable('unitNotFound'),
    deleteError: tTable('deleteError'),
    deleting: tTable('deleting'),
    confirmDelete: tTable('confirmDelete'),
    statusAvailable: tStatus('available'),
    statusRented: tStatus('rented'),
    statusUnderMaintenance: tStatus('underMaintenance'),
    // Column accessor keys for view options
    'unit_number': tTable('unitNumber'),
    'unit_name': tTable('unitName'),
    'rooms_count': tTable('rooms'),
    'majalis_count': tTable('majalis'),
    'bathrooms_count': tTable('bathrooms'),
    'floor_number': tTable('floorNumber'),
    'rent_amount': tTable('rentAmount'),
    'created_at': tTable('createdAt'),
  };

  const commonTranslations = {
    cancel: tCommon('cancel'),
    delete: tCommon('delete'),
  };

  // Fetch properties for filter
  const fetchProperties = async () => {
    try {
      const result = await apiClient.get("/api/properties");
      if (result.success) {
        setProperties(result.data);
      }
    } catch (error) {
      console.error("Error fetching properties:", error);
    }
  };

  // Fetch units
  const fetchUnits = useCallback(async (currentFilters: UnitFilters, isRetry = false) => {
    try {
      if (isRetry) {
        setRetrying(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const params = new URLSearchParams();

      if (currentFilters.search) params.append("search", currentFilters.search);
      if (currentFilters.property_id) params.append("property_id", currentFilters.property_id.toString());
      if (currentFilters.status) params.append("status", currentFilters.status);
      if (currentFilters.min_rent) params.append("min_rent", currentFilters.min_rent.toString());
      if (currentFilters.max_rent) params.append("max_rent", currentFilters.max_rent.toString());
      if (currentFilters.min_rooms) params.append("min_rooms", currentFilters.min_rooms.toString());
      if (currentFilters.max_rooms) params.append("max_rooms", currentFilters.max_rooms.toString());
      if (currentFilters.page) params.append("page", currentFilters.page.toString());
      if (currentFilters.pageSize) params.append("pageSize", currentFilters.pageSize.toString());
      if (currentFilters.sortBy) params.append("sortBy", currentFilters.sortBy);
      if (currentFilters.sortOrder) params.append("sortOrder", currentFilters.sortOrder);

      const result: UnitListResponse = await apiClient.get(`/api/units?${params.toString()}`);
      
      if (result.success) {
        setData(result.data);
        setTotal(result.meta.total);
        setTotalPages(result.meta.totalPages);
        setError(null);
      } else {
        throw new Error(tTable('errorLoading'));
      }
    } catch (error) {
      console.error("Error fetching units:", error);
      const errorObj = error instanceof Error ? error : new Error(tTable('unknownError'));
      setError(errorObj);

      if (!isRetry) {
        toast.error(errorObj.message);
      }
    } finally {
      setLoading(false);
      setRetrying(false);
    }
  }, [tTable]);

  // Create a refresh function that fetches data again
  const handleRefresh = useCallback(() => {
    fetchUnits(filters);
  }, [filters, fetchUnits]);

  const table = useDataTableInstance({
    data,
    columns: getUnitColumns(locale, columnTranslations, commonTranslations, handleRefresh),
    enableRowSelection: true,
    defaultPageSize: filters.pageSize,
    getRowId: (row) => row.id.toString(),
  });

  // Update table pagination when filters change
  React.useEffect(() => {
    if (table && filters.page) {
      table.setPageIndex((filters.page || 1) - 1);
    }
  }, [filters.page, table]);

  React.useEffect(() => {
    if (table && filters.pageSize) {
      table.setPageSize(filters.pageSize);
    }
  }, [filters.pageSize, table]);

  // Initial data fetch
  useEffect(() => {
    fetchProperties();
    fetchUnits(filters);
  }, []);

  // Handle search
  const handleSearch = () => {
    const newFilters = { ...filters, search: searchInput, page: 1 };
    setFilters(newFilters);
    fetchUnits(newFilters);
  };

  // Handle clear search
  const handleClearSearch = () => {
    setSearchInput("");
    const newFilters = { ...filters, search: undefined, page: 1 };
    setFilters(newFilters);
    fetchUnits(newFilters);
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof UnitFilters, value: any) => {
    const newFilters = { ...filters, [key]: value, page: 1 };
    setFilters(newFilters);
    fetchUnits(newFilters);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    fetchUnits(newFilters);
  };

  // Handle page size change
  const handlePageSizeChange = (pageSize: number) => {
    const newFilters = { ...filters, pageSize, page: 1 };
    setFilters(newFilters);
    fetchUnits(newFilters);
  };

  // Handle retry
  const handleRetry = () => {
    fetchUnits(filters, true);
  };

  // Show main error state if initial load failed
  if (error && !data.length && !loading) {
    return (
      <ErrorState
        error={error}
        onRetry={handleRetry}
        className="mx-auto max-w-md"
      />
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex flex-1 items-center space-x-2">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={tTable('searchPlaceholder')}
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                className="pl-8 pr-8"
                disabled={loading || retrying}
              />
              {searchInput && (
                <Button
                  onClick={handleClearSearch}
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1 h-7 w-7 p-0"
                  disabled={loading || retrying}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            <Button
              onClick={handleSearch}
              variant="outline"
              size="sm"
              disabled={loading || retrying}
            >
              {(loading || retrying) ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
            </Button>
            <Button
              onClick={handleRetry}
              variant="outline"
              size="icon"
              disabled={loading || retrying}
            >
              <RefreshCw className={`h-4 w-4 ${retrying ? 'animate-spin' : ''}`} />
            </Button>
          </div>
          
          <UnitsViewOptions table={table} columnTranslations={columnTranslations} />
        </div>

        <div className="flex flex-wrap gap-2">
          <Select
            value={filters.property_id?.toString() || "all"}
            onValueChange={(value) => handleFilterChange("property_id", value === "all" ? undefined : parseInt(value))}
            disabled={loading || retrying}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder={tTable('filterByProperty')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{tTable('allProperties')}</SelectItem>
              {properties.map((property) => (
                <SelectItem key={property.id} value={property.id.toString()}>
                  {locale === "ar" ? property.name_ar : property.name_en}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={filters.status || "all"}
            onValueChange={(value) => handleFilterChange("status", value === "all" ? undefined : value)}
            disabled={loading || retrying}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder={tTable('filterByStatus')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{tTable('allStatuses')}</SelectItem>
              <SelectItem value="AVAILABLE">{tStatus('available')}</SelectItem>
              <SelectItem value="RENTED">{tStatus('rented')}</SelectItem>
              <SelectItem value="UNDER_MAINTENANCE">{tStatus('underMaintenance')}</SelectItem>
            </SelectContent>
          </Select>

        </div>
      </div>

      {/* Data Table */}
      <div className="rounded-md border overflow-hidden">
        <div className="w-full overflow-x-auto [&_[data-slot=table-container]]:overflow-x-visible">
          {loading && !data.length ? (
            <div className="flex h-64 items-center justify-center">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="text-muted-foreground">{tTable('loading')}</span>
              </div>
            </div>
          ) : (
            <DataTable table={table} columns={getUnitColumns(locale, columnTranslations, commonTranslations, handleRefresh)} />
          )}
        </div>
      </div>

      {/* Pagination */}
      <DataTablePagination table={table} />
    </div>
  );
}