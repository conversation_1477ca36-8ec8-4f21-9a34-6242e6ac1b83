const { PrismaClient } = require('../src/generated/prisma');

const prisma = new PrismaClient();

async function debugPermissions() {
  try {
    console.log('=== DEBUGGING SUPERUSER PERMISSIONS ===\n');
    
    // 1. Find the admin user
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        user_roles: {
          include: {
            role: {
              include: {
                role_permissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('1. USER INFORMATION:');
    console.log('   ID:', user.id);
    console.log('   Username:', user.username);
    console.log('   Email:', user.email);
    console.log('   Status:', user.status);
    console.log('');

    console.log('2. USER ROLES:');
    user.user_roles.forEach(ur => {
      console.log(`   - Role: ${ur.role.name} (ID: ${ur.role.id})`);
      console.log(`     Description: ${ur.role.description || 'N/A'}`);
      console.log(`     Is Admin Role: ${ur.role.name.toLowerCase().includes('admin') ? 'YES ✓' : 'NO'}`);
    });
    console.log('');

    console.log('3. PERMISSIONS ASSIGNED TO ROLES:');
    user.user_roles.forEach(ur => {
      console.log(`   Role: ${ur.role.name}`);
      if (ur.role.role_permissions.length === 0) {
        console.log('     ❌ No permissions assigned');
      } else {
        ur.role.role_permissions.forEach(rp => {
          const perms = [];
          if (rp.can_read) perms.push('READ');
          if (rp.can_create) perms.push('CREATE');
          if (rp.can_update) perms.push('UPDATE');
          if (rp.can_delete) perms.push('DELETE');
          console.log(`     - ${rp.permission.module}: ${perms.join(', ')}`);
        });
      }
    });
    console.log('');

    // 4. Check all available permissions
    console.log('4. ALL AVAILABLE PERMISSIONS IN SYSTEM:');
    const allPermissions = await prisma.permission.findMany({
      orderBy: { module: 'asc' }
    });
    
    const modules = [...new Set(allPermissions.map(p => p.module))];
    console.log(`   Total modules: ${modules.length}`);
    modules.forEach(module => {
      console.log(`   - ${module}`);
    });
    console.log('');

    // 5. Test the superuser check logic
    console.log('5. SUPERUSER CHECK LOGIC TEST:');
    const hasAdminRole = user.user_roles.some(ur => {
      const roleName = ur.role.name.toLowerCase();
      return roleName.includes('admin') || 
             roleName.includes('superuser') || 
             roleName === 'administrator' ||
             roleName === 'super admin';
    });
    console.log(`   Has admin/superuser role: ${hasAdminRole ? 'YES ✓' : 'NO ❌'}`);
    
    if (hasAdminRole) {
      console.log('   ✓ User should have FULL ACCESS to all modules');
    } else {
      console.log('   ❌ User will have LIMITED ACCESS based on role permissions');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugPermissions();