const { PrismaClient } = require('./src/generated/prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

async function resetPassword() {
  try {
    const password = 'password123';
    const hashedPassword = await bcrypt.hash(password, 12);
    
    const updatedUser = await prisma.user.update({
      where: { username: 'ali' },
      data: { password_hash: hashedPassword }
    });
    
    console.log('Password reset successfully for user:', updatedUser.username);
    console.log('New password:', password);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetPassword();