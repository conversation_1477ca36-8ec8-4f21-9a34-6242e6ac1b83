import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";

import { writeFile, mkdir } from "fs/promises";
import { join } from "path";
import { DocumentType } from "@/generated/prisma";



import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
// GET /api/tenants/[id]/documents - Get all documents for a tenant
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // No authentication required for GET requests in this context

    const tenantId = parseInt(params.id);
    if (isNaN(tenantId)) {
      return ApiResponseBuilder.error(
        "Invalid tenant ID",
        "INVALID_ID",
        400
      );
    }

    // Check if tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
    });

    if (!tenant) {
      return ApiResponseBuilder.error(
        "Tenant not found",
        "NOT_FOUND",
        404
      );
    }

    // Get all documents for the tenant
    const documents = await db.tenantDocument.findMany({
      where: { tenant_id: tenantId },
      include: {
        uploader: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
      },
      orderBy: {
        created_at: "desc",
      },
    });

    return ApiResponseBuilder.success(documents);
  } catch (error) {
    console.error("Error fetching tenant documents:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch tenant documents",
      "FETCH_ERROR",
      500,
      error
    );
  }
}

// POST /api/tenants/[id]/documents - Upload a new document
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for tenants
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "tenants", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create tenants");
    }

    // Verify authentication
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for tenants
    const canRead = hasPermission(userPermissions, "tenants", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to read tenants");
    }
    const decoded = verifyToken(token);
    if (!decoded) {
      return ApiResponseBuilder.unauthorized("Invalid authentication token", request.url);
    }

    const tenantId = parseInt(params.id);
    if (isNaN(tenantId)) {
      return ApiResponseBuilder.error(
        "Invalid tenant ID",
        "INVALID_ID",
        400
      );
    }

    // Check if tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
    });

    if (!tenant) {
      return ApiResponseBuilder.error(
        "Tenant not found",
        "NOT_FOUND",
        404
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const documentType = formData.get("document_type") as DocumentType;
    const documentNumber = formData.get("document_number") as string | null;
    const issueDate = formData.get("issue_date") as string | null;
    const expiryDate = formData.get("expiry_date") as string | null;

    if (!file) {
      return ApiResponseBuilder.error(
        "No file provided",
        "NO_FILE",
        400
      );
    }

    if (!documentType) {
      return ApiResponseBuilder.error(
        "Document type is required",
        "MISSING_TYPE",
        400
      );
    }

    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      return ApiResponseBuilder.error(
        "File size must be less than 10MB",
        "FILE_TOO_LARGE",
        400
      );
    }

    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), "public", "uploads", "tenant-documents", tenantId.toString());
    await mkdir(uploadDir, { recursive: true });

    // Generate unique filename
    const timestamp = Date.now();
    const fileName = `${timestamp}-${file.name}`;
    const filePath = join(uploadDir, fileName);
    const publicPath = `/uploads/tenant-documents/${tenantId}/${fileName}`;

    // Save the file
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Create document record
    const document = await db.tenantDocument.create({
      data: {
        tenant_id: tenantId,
        document_type: documentType,
        document_number: documentNumber,
        issue_date: issueDate ? new Date(issueDate) : null,
        expiry_date: expiryDate ? new Date(expiryDate) : null,
        file_name: file.name,
        file_path: publicPath,
        file_size: file.size,
        mime_type: file.type,
        uploaded_by: decoded.id,
      },
      include: {
        uploader: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    return ApiResponseBuilder.success(document);
  } catch (error) {
    console.error("Error uploading document:", error);
    return ApiResponseBuilder.error(
      "Failed to upload document",
      "UPLOAD_ERROR",
      500,
      error
    );
  }
}