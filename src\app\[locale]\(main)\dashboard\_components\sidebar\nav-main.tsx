"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTranslations, useLocale } from 'next-intl';

import { ChevronRight } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { type NavGroup, type NavMainItem } from "@/navigation/sidebar/sidebar-items";
import { usePermissions } from "@/hooks/use-permissions";

interface NavMainProps {
  readonly items: readonly NavGroup[];
}

const IsComingSoon = () => {
  const t = useTranslations();
  return (
    <span className="ml-auto rounded-md bg-gray-200 px-2 py-1 text-xs dark:text-gray-800">{t("common.soon")}</span>
  );
};

const NavItemExpanded = ({
  item,
  isActive,
  isSubmenuOpen,
  getTranslatedTitle,
  locale,
}: {
  item: NavMainItem;
  isActive: (url: string, subItems?: NavMainItem["subItems"]) => boolean;
  isSubmenuOpen: (subItems?: NavMainItem["subItems"]) => boolean;
  getTranslatedTitle: (title: string) => string;
  locale: string;
}) => {
  return (
    <Collapsible key={item.title} asChild defaultOpen={isSubmenuOpen(item.subItems)} className="group/collapsible">
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          {item.subItems ? (
            <SidebarMenuButton
              disabled={item.comingSoon}
              isActive={isActive(item.url, item.subItems)}
              tooltip={item.title}
            >
              {item.icon && <item.icon />}
              <span>{getTranslatedTitle(item.title)}</span>
              {item.comingSoon && <IsComingSoon />}
              <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
            </SidebarMenuButton>
          ) : (
            <SidebarMenuButton
              asChild
              aria-disabled={item.comingSoon}
              isActive={isActive(item.url)}
              tooltip={item.title}
            >
              <Link href={`/${locale}${item.url}`} target={item.newTab ? "_blank" : undefined}>
                {item.icon && <item.icon />}
                <span>{getTranslatedTitle(item.title)}</span>
                {item.comingSoon && <IsComingSoon />}
              </Link>
            </SidebarMenuButton>
          )}
        </CollapsibleTrigger>
        {item.subItems && (
          <CollapsibleContent>
            <SidebarMenuSub>
              {item.subItems.map((subItem) => (
                <SidebarMenuSubItem key={subItem.title}>
                  <SidebarMenuSubButton aria-disabled={subItem.comingSoon} isActive={isActive(subItem.url)} asChild>
                    <Link href={`/${locale}${subItem.url}`} target={subItem.newTab ? "_blank" : undefined}>
                      {subItem.icon && <subItem.icon />}
                      <span>{getTranslatedTitle(subItem.title)}</span>
                      {subItem.comingSoon && <IsComingSoon />}
                    </Link>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        )}
      </SidebarMenuItem>
    </Collapsible>
  );
};

const NavItemCollapsed = ({
  item,
  isActive,
  getTranslatedTitle,
  locale,
}: {
  item: NavMainItem;
  isActive: (url: string, subItems?: NavMainItem["subItems"]) => boolean;
  getTranslatedTitle: (title: string) => string;
  locale: string;
}) => {
  return (
    <SidebarMenuItem key={item.title}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuButton
            disabled={item.comingSoon}
            tooltip={item.title}
            isActive={isActive(item.url, item.subItems)}
          >
            {item.icon && <item.icon />}
            <span>{getTranslatedTitle(item.title)}</span>
            <ChevronRight />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-50 space-y-1" side="right" align="start">
          {item.subItems?.map((subItem) => (
            <DropdownMenuItem key={subItem.title} asChild>
              <SidebarMenuSubButton
                asChild
                className="focus-visible:ring-0"
                aria-disabled={subItem.comingSoon}
                isActive={isActive(subItem.url)}
              >
                <Link href={`/${locale}${subItem.url}`} target={subItem.newTab ? "_blank" : undefined}>
                  {subItem.icon && <subItem.icon className="[&>svg]:text-sidebar-foreground" />}
                  <span>{getTranslatedTitle(subItem.title)}</span>
                  {subItem.comingSoon && <IsComingSoon />}
                </Link>
              </SidebarMenuSubButton>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  );
};

export function NavMain({ items }: NavMainProps) {
  const path = usePathname();
  const { state, isMobile } = useSidebar();
  const { hasModuleAccess, isLoading, permissions } = usePermissions();
  const t = useTranslations('navigation');
  const locale = useLocale();

  // Helper function to get translated title for navigation items
  const getTranslatedTitle = (title: string): string => {
    const titleKey = title.toLowerCase().replace(/\s+/g, '');

    // Map specific titles to translation keys
    const titleMap: Record<string, string> = {
      'tenants': t('tenants'),
      'properties': t('properties'),
      'propertytypes': t('propertyTypes'),
      'units': t('units'),
      'amenities': t('amenities'),
      'propertyowners': t('propertyOwners'),
      'ownerpayouts': t('ownerPayouts'),
      'contracts': t('contracts'),
      'maintenance': t('maintenance'),
      'expenses': t('expenses'),
      'invoices': t('invoices'),
      'payments': t('payments'),
      'ownersassociations': t('ownersAssociations'),
      'users': t('users'),
      'roles': t('roles'),
      'settings': t('settings'),
      'dashboard': t('dashboard'),
      'default': t('default'),
      // Group labels
      'dashboards': t('dashboards'),
      'propertymanagement': t('propertyManagement'),
      'usermanagement': t('userManagement'),
      'misc': t('misc'),
    };

    return titleMap[titleKey] || title; // Fallback to original title
  };

  // Helper function to check if user has access to a navigation item
  const hasNavAccess = (url: string): boolean => {
    if (isLoading) return true; // Show items while loading

    // Map URLs to modules - comprehensive list
    if (url.includes('/tenants')) return hasModuleAccess('tenants');
    if (url.includes('/properties') && !url.includes('property-types') && !url.includes('property-owners')) return hasModuleAccess('properties');
    if (url.includes('/property-types')) return hasModuleAccess('property-types');
    if (url.includes('/property-owners')) return hasModuleAccess('property-owners');
    if (url.includes('/units')) return hasModuleAccess('units');
    if (url.includes('/contracts')) return hasModuleAccess('contracts');
    if (url.includes('/invoices')) return hasModuleAccess('invoices');
    if (url.includes('/payments')) return hasModuleAccess('payments');
    if (url.includes('/owners-associations')) return hasModuleAccess('owners-associations');
    if (url.includes('/owner-payouts')) return hasModuleAccess('owner-payouts');
    if (url.includes('/maintenance')) return hasModuleAccess('maintenance');
    if (url.includes('/expenses/categories')) return hasModuleAccess('expense-categories');
    if (url.includes('/expenses') && !url.includes('categories')) return hasModuleAccess('expenses');
    if (url.includes('/users')) return hasModuleAccess('users');
    if (url.includes('/roles')) return hasModuleAccess('roles');
    if (url.includes('/reports')) return hasModuleAccess('reports');
    if (url.includes('/settings')) return hasModuleAccess('settings');
    if (url.includes('/amenities')) return hasModuleAccess('amenities');

    // Allow access to dashboard and other general pages
    return true;
  };

  const isItemActive = (url: string, subItems?: NavMainItem["subItems"]) => {
    const urlWithLocale = `/${locale}${url}`;
    if (subItems?.length) {
      return subItems.some((sub) => path.startsWith(`/${locale}${sub.url}`));
    }
    return path === urlWithLocale;
  };

  const isSubmenuOpen = (subItems?: NavMainItem["subItems"]) => {
    return subItems?.some((sub) => path.startsWith(`/${locale}${sub.url}`)) ?? false;
  };

  return (
    <>
      {items.map((group) => {
        // Filter items based on permissions
        const accessibleItems = group.items.filter(item => hasNavAccess(item.url));

        // Don't render group if no accessible items
        if (accessibleItems.length === 0) return null;

        return (
          <SidebarGroup key={group.id}>
            {group.label && <SidebarGroupLabel>{getTranslatedTitle(group.label)}</SidebarGroupLabel>}
            <SidebarGroupContent className="flex flex-col gap-2">
              <SidebarMenu>
                {accessibleItems.map((item) => {
                if (state === "collapsed" && !isMobile) {
                  // If no subItems, just render the button as a link
                  if (!item.subItems) {
                    return (
                      <SidebarMenuItem key={item.title}>
                        <SidebarMenuButton
                          asChild
                          aria-disabled={item.comingSoon}
                          tooltip={item.title}
                          isActive={isItemActive(item.url)}
                        >
                          <Link href={`/${locale}${item.url}`} target={item.newTab ? "_blank" : undefined}>
                            {item.icon && <item.icon />}
                            <span>{getTranslatedTitle(item.title)}</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    );
                  }
                  // Otherwise, render the dropdown as before
                  return <NavItemCollapsed key={item.title} item={item} isActive={isItemActive} getTranslatedTitle={getTranslatedTitle} locale={locale} />;
                }
                // Expanded view
                return (
                  <NavItemExpanded key={item.title} item={item} isActive={isItemActive} isSubmenuOpen={isSubmenuOpen} getTranslatedTitle={getTranslatedTitle} locale={locale} />
                );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        );
      })}
    </>
  );
}
