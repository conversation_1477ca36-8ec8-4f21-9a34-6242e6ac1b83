"use client";

import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal, Edit, Trash2, Eye, Shield } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";

import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatDateForDisplay } from "@/lib/utils";
import type { RoleWithPermissions } from "@/types/user";
import { useTranslations } from "next-intl";

import { DeleteRoleDialog } from "./delete-role-dialog";

// Helper function to create translated column headers
const createHeader = (translationKey: string) => {
  return ({ column }: any) => {
    const t = useTranslations();
    return <DataTableColumnHeader column={column} title={t(translationKey)} />;
  };
};

export const roleColumns: ColumnDef<RoleWithPermissions & { _count: { user_roles: number } }>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label={(() => {
          const t = useTranslations();
          return t("common.selectAll");
        })()}
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label={(() => {
          const t = useTranslations();
          return t("common.selectRow");
        })()}
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: ({ column }) => {
      const t = useTranslations();
      return <DataTableColumnHeader column={column} title={t("common.id")} className="justify-center" />;
    },
    cell: ({ row }) => <div className="text-center"><span className="font-mono text-sm">{row.original.id}</span></div>,
    enableHiding: false,
  },
  {
    id: "role",
    header: createHeader("navigation.roles"),
    cell: ({ row }) => {
      const role = row.original;
      
      return (
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10">
            <Shield className="h-4 w-4 text-primary" />
          </div>
          <div>
            <div className="font-medium flex items-center gap-2">
              {role.name}
              {role.is_system && (
                <Badge variant="outline" className="text-xs">
                  {(() => {
                    const t = useTranslations();
                    return t("common.system");
                  })()}
                </Badge>
              )}
            </div>
            {role.description && (
              <div className="text-sm text-muted-foreground line-clamp-1">
                {role.description}
              </div>
            )}
          </div>
        </div>
      );
    },
    enableSorting: false,
  },
  {
    id: "permissions",
    header: () => {
      const t = useTranslations();
      return t("roles.permissions");
    },
    cell: ({ row }) => {
      const permissions = row.original.role_permissions;
      
      if (permissions.length === 0) {
        const t = useTranslations();
        return <span className="text-sm text-muted-foreground">{t("roles.noPermissions")}</span>;
      }
      
      // Group permissions by module
      const modulePermissions: Record<string, string[]> = {};
      permissions.forEach(rp => {
        const module = rp.permission.module;
        if (!modulePermissions[module]) {
          modulePermissions[module] = [];
        }
        modulePermissions[module].push(rp.permission.action);
      });
      
      const modules = Object.keys(modulePermissions);
      
      return (
        <div className="flex flex-wrap gap-1">
          {modules.slice(0, 3).map((module) => (
            <Badge key={module} variant="secondary" className="text-xs capitalize">
              {module} ({modulePermissions[module].length})
            </Badge>
          ))}
          {modules.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{modules.length - 3} {(() => {
                const t = useTranslations();
                return t("common.more");
              })()}
            </Badge>
          )}
        </div>
      );
    },
    enableSorting: false,
  },
  {
    id: "user_count",
    header: createHeader("navigation.users"),
    cell: ({ row }) => {
      const count = row.original._count.user_roles;
      return (
        <span className="text-sm">
          {(() => {
            const t = useTranslations();
            return `${count} ${t(count === 1 ? "roles.user" : "navigation.users").toLowerCase()}`;
          })()}
        </span>
      );
    },
  },
  {
    accessorKey: "created_at",
    header: createHeader("common.createdAt"),
    cell: ({ row }) => (
      <span className="text-sm">
        {formatDateForDisplay(row.original.created_at, "table")}
      </span>
    ),
  },
  {
    id: "actions",
    header: () => {
      const t = useTranslations();
      return <div className="text-center">{t("common.actions")}</div>;
    },
    cell: ({ row }) => {
      const role = row.original;
      const params = useParams();
      const locale = params.locale as string;
      
      return (
        <div className="text-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{(() => {
              const t = useTranslations();
              return t("common.actions");
            })()}</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(role.name)}
            >
              {(() => {
                const t = useTranslations();
                return t("roles.copyRoleName");
              })()}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href={`/${locale}/dashboard/roles/${role.id}`}>
                <Eye className="mr-2 h-4 w-4" />
                {(() => {
                  const t = useTranslations();
                  return t("common.viewDetails");
                })()}
              </Link>
            </DropdownMenuItem>
            {!role.is_system && (
              <>
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/roles/${role.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    {(() => {
                      const t = useTranslations();
                      return t("roles.editRole");
                    })()}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DeleteRoleDialog role={role}>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Trash2 className="mr-2 h-4 w-4" />
                    {(() => {
                      const t = useTranslations();
                      return t("roles.deleteRole");
                    })()}
                  </DropdownMenuItem>
                </DeleteRoleDialog>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
        </div>
      );
    },
  },
];
