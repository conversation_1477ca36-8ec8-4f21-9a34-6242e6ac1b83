"use client";

import { useState, useEffect } from "react";
import { useLocale, useTranslations } from "next-intl";
import { Check, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { apiClient } from "@/lib/api-client";
import type { Amenity } from "@/generated/prisma";

interface AmenitySelectorProps {
  selectedIds: number[];
  onChange: (ids: number[]) => void;
  disabled?: boolean;
}

export function AmenitySelector({ selectedIds, onChange, disabled }: AmenitySelectorProps) {
  const locale = useLocale();
  const t = useTranslations("properties.amenities");
  const [amenities, setAmenities] = useState<Amenity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAmenities = async () => {
      try {
        setLoading(true);
        const result = await apiClient.get("/api/amenities");
        
        if (result.success) {
          setAmenities(result.data);
        }
      } catch (error) {
        console.error("Error fetching amenities:", error);
        toast.error("Failed to load amenities");
      } finally {
        setLoading(false);
      }
    };

    fetchAmenities();
  }, []);

  const toggleAmenity = (amenityId: number) => {
    if (disabled) return;

    if (selectedIds.includes(amenityId)) {
      onChange(selectedIds.filter(id => id !== amenityId));
    } else {
      onChange([...selectedIds, amenityId]);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          {t("selectDescription")}
        </p>
        {selectedIds.length > 0 && (
          <Badge variant="secondary">
            {selectedIds.length} {t("selected")}
          </Badge>
        )}
      </div>

      <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4">
        {amenities.map((amenity) => {
          const isSelected = selectedIds.includes(amenity.id);
          const name = locale === "ar" ? amenity.name_ar : amenity.name_en;

          return (
            <Button
              key={amenity.id}
              type="button"
              variant={isSelected ? "default" : "outline"}
              size="sm"
              className={cn(
                "h-auto justify-start px-3 py-2",
                isSelected && "bg-primary text-primary-foreground"
              )}
              onClick={() => toggleAmenity(amenity.id)}
              disabled={disabled}
            >
              <Check 
                className={cn(
                  "mr-2 h-4 w-4",
                  isSelected ? "opacity-100" : "opacity-0"
                )}
              />
              <span className="text-left">{name}</span>
            </Button>
          );
        })}
      </div>
    </div>
  );
}