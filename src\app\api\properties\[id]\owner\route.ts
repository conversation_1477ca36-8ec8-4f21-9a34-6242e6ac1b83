import { NextRequest } from "next/server";
import { verifyToken } from "@/lib/auth";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { z } from "zod";

const updateOwnerSchema = z.object({
  owner_id: z.number().int().positive(),
});

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// PUT /api/properties/[id]/owner - Update property owner
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    console.log("=== PROPERTY OWNER ASSIGNMENT API DEBUG ===");
    const { id: idParam } = await params;
    console.log("Property ID from params:", idParam);
    console.log("Request URL:", request.url);
    
    // Verify authentication
    const token = request.cookies.get("auth-token")?.value;
    if (!token) {
      console.log("No auth token provided");
      return ApiResponseBuilder.unauthorized("No authentication token provided");
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      console.log("Invalid auth token");
      return ApiResponseBuilder.unauthorized("Invalid authentication token");
    }

    const propertyId = parseInt(idParam);
    if (isNaN(propertyId)) {
      console.log("Invalid property ID:", idParam);
      return ApiResponseBuilder.badRequest("Invalid property ID");
    }
    
    console.log("Parsed property ID:", propertyId);

    // Check if property exists
    const property = await db.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      console.log("Property not found with ID:", propertyId);
      return ApiResponseBuilder.notFound("Property");
    }
    
    console.log("Property found:", property);

    // Parse request body
    const body = await request.json();
    console.log("Request body:", body);
    const validation = updateOwnerSchema.safeParse(body);

    if (!validation.success) {
      console.log("Validation failed:", validation.error);
      return ApiResponseBuilder.validationError(validation.error);
    }

    const { owner_id } = validation.data;
    console.log("Owner ID to assign:", owner_id);

    // Verify owner exists
    const owner = await db.propertyOwner.findUnique({
      where: { id: owner_id },
    });

    if (!owner) {
      console.log("Owner not found with ID:", owner_id);
      return ApiResponseBuilder.badRequest("Invalid owner ID", { owner_id });
    }
    
    console.log("Owner found:", owner);

    // Update property owner directly
    console.log("Updating property owner...");
    await db.property.update({
      where: { id: propertyId },
      data: {
        owner_id: owner_id,
        updated_by: decoded.id,
        updated_at: new Date(),
      },
    });
    
    console.log("Transaction completed successfully");

    // Fetch updated property with owner
    const updatedProperty = await db.property.findUnique({
      where: { id: propertyId },
      include: {
        owner: true,
        property_type: true,
      },
    });
    
    console.log("Final updated property:", updatedProperty);
    console.log("=== OWNER ASSIGNMENT API COMPLETED SUCCESSFULLY ===");

    return ApiResponseBuilder.success(updatedProperty);
  } catch (error) {
    console.error("=== ERROR IN OWNER ASSIGNMENT API ===");
    console.error("Error updating property owner:", error);
    return ApiResponseBuilder.error("Failed to update property owner", "INTERNAL_ERROR", 500);
  }
}

// DELETE /api/properties/[id]/owner - Remove property owner
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify authentication
    const token = request.cookies.get("auth-token")?.value;
    if (!token) {
      return ApiResponseBuilder.unauthorized("No authentication token provided");
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return ApiResponseBuilder.unauthorized("Invalid authentication token");
    }

    const { id: idParam } = await params;
    const propertyId = parseInt(idParam);
    if (isNaN(propertyId)) {
      return ApiResponseBuilder.badRequest("Invalid property ID");
    }

    // Remove owner from property
    await db.property.update({
      where: { id: propertyId },
      data: {
        owner_id: null,
        updated_by: decoded.id,
        updated_at: new Date(),
      },
    });

    return ApiResponseBuilder.success({ message: "Property owner removed successfully" });
  } catch (error) {
    console.error("Error removing property owner:", error);
    return ApiResponseBuilder.error("Failed to remove property owner", "INTERNAL_ERROR", 500);
  }
}