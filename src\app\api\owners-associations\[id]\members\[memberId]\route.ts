import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { associationMemberUpdateSchema, calculateTotalOwnership } from "@/types/owners-association";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, checkUserPermission } from "@/lib/auth";
import { Decimal } from "@prisma/client/runtime/library";

// GET /api/owners-associations/[id]/members/[memberId] - Get a single member
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; memberId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await checkUserPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view association members");
    }

    const { id: idParam, memberId: memberIdParam } = await params;
    const associationId = parseInt(idParam);
    const memberId = parseInt(memberIdParam);

    if (isNaN(associationId) || isNaN(memberId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    const member = await db.associationMember.findFirst({
      where: {
        id: memberId,
        association_id: associationId,
      },
      include: {
        association: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
          },
        },
        subscription_payments: {
          orderBy: {
            due_date: "desc",
          },
          take: 10,
          include: {
            subscription: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
              },
            },
          },
        },
        _count: {
          select: {
            subscription_payments: true,
          },
        },
      },
    });

    if (!member) {
      return ApiResponseBuilder.notFound("Member not found");
    }

    // Transform member to handle Decimal serialization
    const transformedMember = {
      ...member,
      ownership_percentage: member.ownership_percentage?.toString() || "0",
      subscription_payments: member.subscription_payments.map(payment => ({
        ...payment,
        amount: payment.amount.toString(),
      })),
    };

    return ApiResponseBuilder.success(transformedMember);
  } catch (error: any) {
    console.error("Error fetching member:", error);
    return ApiResponseBuilder.error("Failed to fetch member", "INTERNAL_ERROR", 500);
  }
}

// PATCH /api/owners-associations/[id]/members/[memberId] - Update a member
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; memberId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for owners-associations
    const canUpdate = await checkUserPermission(decoded.id, "owners-associations", "UPDATE");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update association members");
    }

    const { id: idParam, memberId: memberIdParam } = await params;
    const associationId = parseInt(idParam);
    const memberId = parseInt(memberIdParam);

    if (isNaN(associationId) || isNaN(memberId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    const body = await request.json();
    const validatedData = associationMemberUpdateSchema.parse(body);

    // Check if member exists
    const existingMember = await db.associationMember.findFirst({
      where: {
        id: memberId,
        association_id: associationId,
      },
    });

    if (!existingMember) {
      return ApiResponseBuilder.notFound("Member not found");
    }

    // If unit number is being updated, check for duplicates
    if (validatedData.unit_number && validatedData.unit_number !== existingMember.unit_number) {
      const duplicateUnit = await db.associationMember.findFirst({
        where: {
          association_id: associationId,
          unit_number: validatedData.unit_number,
          id: { not: memberId },
        },
      });

      if (duplicateUnit) {
        return ApiResponseBuilder.error(
          "A member with this unit number already exists in the association",
          "ALREADY_EXISTS",
          400
        );
      }
    }

    // If ownership percentage is being updated, check total ownership
    if (validatedData.ownership_percentage) {
      const otherMembers = await db.associationMember.findMany({
        where: {
          association_id: associationId,
          id: { not: memberId },
        },
      });

      const otherMembersTotal = calculateTotalOwnership(otherMembers);
      const newOwnership = parseFloat(validatedData.ownership_percentage);
      const newTotal = otherMembersTotal + newOwnership;

      if (newTotal > 100.01) { // Allow for small floating point errors
        return ApiResponseBuilder.error(
          `Total ownership would exceed 100%. Current total (excluding this member): ${otherMembersTotal.toFixed(2)}%, trying to set: ${newOwnership}%`,
          "INVALID_OWNERSHIP",
          400
        );
      }
    }

    // Prepare update data
    const updateData: any = {};
    
    if (validatedData.full_name !== undefined) updateData.full_name = validatedData.full_name;
    if (validatedData.unit_number !== undefined) updateData.unit_number = validatedData.unit_number;
    if (validatedData.ownership_percentage !== undefined) {
      updateData.ownership_percentage = new Decimal(validatedData.ownership_percentage);
    }
    if (validatedData.phone !== undefined) updateData.phone = validatedData.phone;
    if (validatedData.email !== undefined) updateData.email = validatedData.email;
    if (validatedData.join_date !== undefined) updateData.join_date = new Date(validatedData.join_date);
    if (validatedData.is_board_member !== undefined) updateData.is_board_member = validatedData.is_board_member;

    const updatedMember = await db.associationMember.update({
      where: { id: memberId },
      data: updateData,
    });

    // Transform member to handle Decimal serialization
    const transformedMember = {
      ...updatedMember,
      ownership_percentage: updatedMember.ownership_percentage?.toString() || "0",
    };

    return ApiResponseBuilder.success(transformedMember);
  } catch (error: any) {
    console.error("Error updating member:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to update member", "INTERNAL_ERROR", 500);
  }
}

// DELETE /api/owners-associations/[id]/members/[memberId] - Delete a member
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; memberId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for owners-associations
    const canDelete = await checkUserPermission(decoded.id, "owners-associations", "DELETE");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete association members");
    }

    const { id: idParam, memberId: memberIdParam } = await params;
    const associationId = parseInt(idParam);
    const memberId = parseInt(memberIdParam);

    if (isNaN(associationId) || isNaN(memberId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    // Check if member exists
    const existingMember = await db.associationMember.findFirst({
      where: {
        id: memberId,
        association_id: associationId,
      },
      include: {
        _count: {
          select: {
            subscription_payments: true,
          },
        },
      },
    });

    if (!existingMember) {
      return ApiResponseBuilder.notFound("Member not found");
    }

    // Check if member has any payments
    if (existingMember._count.subscription_payments > 0) {
      return ApiResponseBuilder.error(
        "Cannot delete member with existing payment records",
        "HAS_DEPENDENCIES",
        400
      );
    }

    // Delete the member
    await db.associationMember.delete({
      where: { id: memberId },
    });

    // Update association member count
    await db.ownersAssociation.update({
      where: { id: associationId },
      data: {
        total_members: {
          decrement: 1,
        },
        updated_by: decoded.id,
      },
    });

    return ApiResponseBuilder.success({ message: "Member deleted successfully" });
  } catch (error: any) {
    console.error("Error deleting member:", error);
    return ApiResponseBuilder.error("Failed to delete member", "INTERNAL_ERROR", 500);
  }
}