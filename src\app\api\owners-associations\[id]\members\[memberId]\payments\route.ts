import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";
import { Decimal } from "@prisma/client/runtime/library";

// GET /api/owners-associations/[id]/members/[memberId]/payments - Get member payment history and summary
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; memberId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await hasPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view member payments");
    }

    const { id: idParam, memberId: memberIdParam } = await params;
    const associationId = parseInt(idParam);
    const memberId = parseInt(memberIdParam);

    if (isNaN(associationId) || isNaN(memberId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    // Check if member exists and belongs to the association
    const member = await db.associationMember.findFirst({
      where: {
        id: memberId,
        association_id: associationId,
      },
    });

    if (!member) {
      return ApiResponseBuilder.notFound("Member not found in this association");
    }

    // Fetch all payments for the member
    const payments = await db.subscriptionPayment.findMany({
      where: {
        member_id: memberId,
        subscription: {
          association_id: associationId,
        },
      },
      include: {
        subscription: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            amount: true,
            frequency: true,
          },
        },
        transaction: {
          select: {
            id: true,
            transaction_date: true,
            amount: true,
            payment_method: true,
            // reference_number: not available in schema
          },
        },
        // installments: not implemented in current schema
      },
      orderBy: {
        due_date: "desc",
      },
    });

    // Calculate summary statistics
    let totalDue = new Decimal(0);
    let totalPaid = new Decimal(0);
    let overdueAmount = new Decimal(0);
    let upcomingAmount = new Decimal(0);
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const paymentsByStatus = {
      paid: 0,
      unpaid: 0,
      overdue: 0,
      partially_paid: 0,
    };

    // Transform payments and calculate totals
    const transformedPayments = payments.map(payment => {
      const amountDue = new Decimal(payment.amount_due?.toString() || payment.amount.toString());
      const amountPaid = new Decimal(payment.amount_paid?.toString() || "0");
      const remainingBalance = amountDue.minus(amountPaid);
      const dueDate = new Date(payment.due_date);

      // Update totals
      totalDue = totalDue.plus(amountDue);
      totalPaid = totalPaid.plus(amountPaid);

      // Count by status
      paymentsByStatus[payment.status.toLowerCase() as keyof typeof paymentsByStatus]++;

      // Calculate overdue and upcoming amounts
      if (payment.status === "OVERDUE" || (payment.status === "UNPAID" && dueDate < today)) {
        overdueAmount = overdueAmount.plus(remainingBalance);
      } else if (payment.status === "UNPAID" && dueDate >= today) {
        upcomingAmount = upcomingAmount.plus(remainingBalance);
      } else if (payment.status === "PARTIALLY_PAID") {
        if (dueDate < today) {
          overdueAmount = overdueAmount.plus(remainingBalance);
        } else {
          upcomingAmount = upcomingAmount.plus(remainingBalance);
        }
      }

      return {
        ...payment,
        amount: payment.amount.toString(),
        amount_due: amountDue.toString(),
        amount_paid: amountPaid.toString(),
        remaining_balance: remainingBalance.toString(),
        payment_percentage: amountDue.isZero() ? 100 : parseFloat(amountPaid.dividedBy(amountDue).times(100).toFixed(2)),
        is_overdue: dueDate < today && payment.status !== "PAID",
        subscription: {
          ...(payment as any).subscription,
          amount: (payment as any).subscription.amount.toString(),
        },
        transaction: (payment as any).transaction ? {
          ...(payment as any).transaction,
          amount: (payment as any).transaction.amount.toString(),
        } : null,
        // installments: not implemented in current schema
        installments: [],
        installments_count: 0,
      };
    });

    // Get upcoming subscriptions that don't have payment records yet
    const activeSubscriptions = await db.associationSubscription.findMany({
      where: {
        association_id: associationId,
        is_active: true,
      },
      select: {
        id: true,
        name_en: true,
        name_ar: true,
        amount: true,
        frequency: true,
      },
    });

    // Calculate payment compliance rate
    const complianceRate = totalDue.isZero() ? 100 : parseFloat(totalPaid.dividedBy(totalDue).times(100).toFixed(2));

    const summary = {
      member: {
        id: member.id,
        full_name: member.full_name,
        unit_number: member.unit_number,
        ownership_percentage: member.ownership_percentage?.toString() || "0",
        join_date: member.join_date,
      },
      payment_summary: {
        total_due: totalDue.toString(),
        total_paid: totalPaid.toString(),
        total_outstanding: totalDue.minus(totalPaid).toString(),
        overdue_amount: overdueAmount.toString(),
        upcoming_amount: upcomingAmount.toString(),
        compliance_rate: complianceRate,
      },
      payment_counts: paymentsByStatus,
      active_subscriptions: activeSubscriptions.map(sub => ({
        ...sub,
        amount: sub.amount.toString(),
      })),
    };

    return ApiResponseBuilder.success({
      summary,
      payments: transformedPayments,
    });
  } catch (error) {
    console.error("Error fetching member payments:", error);
    return ApiResponseBuilder.error("Failed to fetch member payments", "INTERNAL_ERROR", 500);
  }
}