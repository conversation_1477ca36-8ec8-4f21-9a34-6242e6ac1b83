import { NextRequest, NextResponse } from "next/server";
import { getUserFromRequest } from "@/lib/permissions";
import { getAuthUser } from "@/lib/auth";

export async function GET(request: NextRequest) {
  try {
    // Get user from request
    const user = await getUserFromRequest(request);
    
    if (!user || !user.id) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Get auth user with permissions
    const authUser = await getAuthUser(user.id);
    
    if (!authUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Debug information
    const debugInfo = {
      userId: user.id,
      username: authUser.username,
      email: authUser.email,
      permissions: authUser.permissions,
      permissionModules: Object.keys(authUser.permissions),
      totalModules: Object.keys(authUser.permissions).length,
      hasPropertiesAccess: authUser.permissions['properties'],
      hasUsersAccess: authUser.permissions['users'],
      hasRolesAccess: authUser.permissions['roles'],
    };

    return NextResponse.json(debugInfo);
  } catch (error) {
    console.error("Debug permissions error:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}