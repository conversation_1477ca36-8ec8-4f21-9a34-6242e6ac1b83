"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations, useLocale } from "next-intl";
import { Loader2 } from "lucide-react";
import * as Icons from "lucide-react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import {
  AmenityInput,
  amenitySchema,
  AmenityWithRelations,
} from "@/types/amenity";

interface AmenityFormProps {
  initialData?: AmenityWithRelations;
  mode: "create" | "edit";
}

// Common Lucide icons for amenities
const commonIcons = [
  "Home", "Wifi", "Car", "Trees", "Dumbbell", "Waves",
  "Shield", "Camera", "Coffee", "Utensils", "Tv", "Wind",
  "Zap", "Droplets", "Flame", "Phone", "Mail", "MapPin",
  "Clock", "Users", "Building", "Layers", "Package", "Briefcase"
];

export function AmenityForm({ initialData, mode }: AmenityFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const t = useTranslations();
  const locale = useLocale();

  const form = useForm<AmenityInput>({
    resolver: zodResolver(amenitySchema),
    defaultValues: {
      name_en: initialData?.name_en || "",
      name_ar: initialData?.name_ar || "",
      icon: initialData?.icon || "Home",
    },
  });

  const onSubmit = async (data: AmenityInput) => {
    try {
      setIsLoading(true);

      const url = mode === "create" 
        ? "/api/amenities"
        : `/api/amenities/${initialData?.id}`;
      
      const method = mode === "create" ? "POST" : "PUT";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
        credentials: "include", // Include cookies for authentication
      });

      const result = await response.json();

      if (result.success) {
        toast.success(mode === "create" 
          ? t("amenities.createSuccess")
          : t("amenities.updateSuccess")
        );
        
        router.push(`/${locale}/dashboard/amenities`);
        router.refresh();
      } else {
        toast.error(result.message || t("common.unexpectedError"));
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error(t("common.unexpectedError"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {mode === "create" 
            ? t("amenities.createTitle")
            : t("amenities.editTitle")
          }
        </h1>
        <p className="text-muted-foreground">
          {mode === "create"
            ? t("amenities.createDescription")
            : t("amenities.editDescription")
          }
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t("amenities.basicInformation")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name_en"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("amenities.nameEn")} *</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="name_ar"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("amenities.nameAr")} *</FormLabel>
                      <FormControl>
                        <Input {...field} dir="rtl" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="icon"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("amenities.icon")}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value || "Home"}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue>
                            <div className="flex items-center gap-2">
                              {(() => {
                                const IconComponent = Icons[field.value as keyof typeof Icons] as any || Icons.Home;
                                return (
                                  <>
                                    <IconComponent className="h-4 w-4" />
                                    <span>{field.value || "Home"}</span>
                                  </>
                                );
                              })()}
                            </div>
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {commonIcons.map((iconName) => {
                          const IconComponent = Icons[iconName as keyof typeof Icons] as any;
                          return (
                            <SelectItem key={iconName} value={iconName}>
                              <div className="flex items-center gap-2">
                                <IconComponent className="h-4 w-4" />
                                <span>{iconName}</span>
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      {t("amenities.iconDescription")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Submit Buttons */}
          <div className="flex items-center justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {mode === "create" ? t("common.create") : t("common.save")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}