import { NextRequest } from "next/server";

import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { tenantSchema, type TenantFilters } from "@/types/tenant";



import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
// GET /api/tenants - List all tenants with filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for tenants
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "tenants", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view tenants");
    }

    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const filters: TenantFilters = {
      search: searchParams.get("search") || undefined,
      page: parseInt(searchParams.get("page") || "1"),
      pageSize: parseInt(searchParams.get("pageSize") || "10"),
      sortBy: searchParams.get("sortBy") || "created_at",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
    };

    // Build where clause
    const where: any = {};
    
    if (filters.search) {
      where.OR = [
        { first_name: { contains: filters.search } },
        { last_name: { contains: filters.search } },
        { email: { contains: filters.search } },
        { phone: { contains: filters.search } },
        { national_id: { contains: filters.search } },
      ];
    }

    // Calculate pagination
    const skip = ((filters.page || 1) - 1) * (filters.pageSize || 10);
    const take = filters.pageSize || 10;

    // Build order by clause
    const orderBy: any = {};
    orderBy[filters.sortBy || "created_at"] = filters.sortOrder;
    
    // Fetch tenants with relations
    const [tenants, total] = await Promise.all([
      db.tenant.findMany({
        where,
        include: {
          documents: {
            select: {
              id: true,
              document_type: true,
              expiry_date: true,
            },
          },
          emergency_contacts: {
            where: { is_primary: true },
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
          creator: {
            select: {
              id: true,
              username: true,
              first_name: true,
              last_name: true,
            },
          },
          updater: {
            select: {
              id: true,
              username: true,
              first_name: true,
              last_name: true,
            },
          },
        },
        orderBy,
        skip,
        take,
      }),
      db.tenant.count({ where }),
    ]);
    
    const totalPages = Math.ceil(total / take);
    
    return ApiResponseBuilder.success(tenants, {
      page: filters.page || 1,
      pageSize: take,
      total,
      totalPages,
    });
  } catch (error) {
    console.error("Tenants API Error:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch tenants",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// POST /api/tenants - Create a new tenant
export async function POST(request: NextRequest) {
  try {
        // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has CREATE permission for tenants
    const canCreate = hasPermission(userPermissions, "tenants", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create tenants");
    }
    
    const body = await request.json();
    
    // Validate the request body
    const validationResult = tenantSchema.safeParse(body);
    if (!validationResult.success) {
      return ApiResponseBuilder.validationError(validationResult.error);
    }

    const validatedData = validationResult.data;

    // Check if email already exists
    const existingTenant = await db.tenant.findUnique({
      where: { email: validatedData.email },
    });

    if (existingTenant) {
      return ApiResponseBuilder.conflict(
        "A tenant with this email already exists",
        { email: validatedData.email },
        request.url
      );
    }

    // Prepare create data
    const createData: any = {
      ...validatedData,
      created_by: decoded.id,
      updated_by: decoded.id,
    };

    // Convert date strings to Date objects if provided
    if (validatedData.national_id_expiry) {
      createData.national_id_expiry = new Date(validatedData.national_id_expiry);
    }
    if (validatedData.date_of_birth) {
      createData.date_of_birth = new Date(validatedData.date_of_birth);
    }

    // Create the tenant
    const tenant = await db.tenant.create({
      data: createData,
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    return ApiResponseBuilder.success(tenant);
  } catch (error) {
    console.error("Create Tenant Error:", error);
    return ApiResponseBuilder.error(
      "Failed to create tenant",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}