import { z } from "zod";

// Enum values from Prisma schema
export const USER_STATUS_VALUES = ["ACTIVE", "INACTIVE", "SUSPENDED", "PENDING_VERIFICATION"] as const;
export type UserStatus = (typeof USER_STATUS_VALUES)[number];

export const PERMISSION_ACTION_VALUES = ["CREATE", "READ", "UPDATE", "DELETE"] as const;
export type PermissionAction = (typeof PERMISSION_ACTION_VALUES)[number];

// Base user type from database
export interface User {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  first_name: string;
  last_name: string;
  phone: string | null;
  status: UserStatus;
  last_login_at: Date | null;
  email_verified: boolean;
  created_at: Date;
  updated_at: Date;
}

// User with roles and permissions
export interface UserWithRoles extends Omit<User, 'password_hash'> {
  user_roles: {
    id: number;
    role: {
      id: number;
      name: string;
      description: string | null;
      role_permissions: {
        permission: {
          id: number;
          module: string;
          action: PermissionAction;
          description: string | null;
        };
      }[];
    };
  }[];
}

// Role type
export interface Role {
  id: number;
  name: string;
  description: string | null;
  is_system: boolean;
  created_at: Date;
  updated_at: Date;
}

// Role with permissions
export interface RoleWithPermissions extends Role {
  role_permissions: {
    permission: {
      id: number;
      module: string;
      action: PermissionAction;
      description: string | null;
    };
  }[];
}

// Permission type
export interface Permission {
  id: number;
  module: string;
  action: PermissionAction;
  description: string | null;
  created_at: Date;
}

// Module permissions structure
export interface ModulePermissions {
  module: string;
  permissions: {
    create: boolean;
    read: boolean;
    update: boolean;
    delete: boolean;
  };
}

// User filters for API
export interface UserFilters {
  search?: string;
  status?: UserStatus;
  role_id?: number;
  page: number;
  pageSize: number;
  sortBy: string;
  sortOrder: "asc" | "desc";
}

// Role filters for API
export interface RoleFilters {
  search?: string;
  is_system?: boolean;
  page: number;
  pageSize: number;
  sortBy: string;
  sortOrder: "asc" | "desc";
}

// Validation schemas
export const userFormSchema = z.object({
  username: z.string()
    .min(3, "Username must be at least 3 characters")
    .max(50, "Username must be less than 50 characters")
    .regex(/^[a-zA-Z0-9_]+$/, "Username can only contain letters, numbers, and underscores"),
  email: z.string()
    .email("Please enter a valid email address")
    .max(255, "Email must be less than 255 characters"),
  first_name: z.string()
    .min(1, "First name is required")
    .max(100, "First name must be less than 100 characters"),
  last_name: z.string()
    .min(1, "Last name is required")
    .max(100, "Last name must be less than 100 characters"),
  phone: z.string()
    .optional()
    .nullable(),
  status: z.enum(USER_STATUS_VALUES),
  password: z.string()
    .min(6, "Password must be at least 6 characters")
    .optional(), // Optional for updates
  role_ids: z.array(z.number()).optional(),
});

export const roleFormSchema = z.object({
  name: z.string()
    .min(1, "Role name is required")
    .max(100, "Role name must be less than 100 characters"),
  description: z.string()
    .max(500, "Description must be less than 500 characters")
    .optional()
    .nullable(),
  permissions: z.array(z.object({
    module: z.string(),
    actions: z.array(z.enum(PERMISSION_ACTION_VALUES)),
  })),
});

export const loginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

export const changePasswordSchema = z.object({
  current_password: z.string().min(1, "Current password is required"),
  new_password: z.string().min(6, "New password must be at least 6 characters"),
  confirm_password: z.string().min(1, "Please confirm your new password"),
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"],
});

// API schemas (without password for responses)
export const userApiSchema = userFormSchema.omit({ password: true });
export const userCreateApiSchema = userFormSchema.required({ password: true });
export const userUpdateApiSchema = userFormSchema.partial().omit({ password: true });

// Form data types
export type UserFormData = z.infer<typeof userFormSchema>;
export type RoleFormData = z.infer<typeof roleFormSchema>;
export type LoginFormData = z.infer<typeof loginSchema>;
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;

// Application modules for permissions
export const APPLICATION_MODULES = [
  { id: "tenants", name: "Tenants", description: "Manage tenant information and leases" },
  { id: "properties", name: "Properties", description: "Manage property listings and details" },
  { id: "property_types", name: "Property Types", description: "Manage property types and categories" },
  { id: "units", name: "Units", description: "Manage property units and their details" },
  { id: "property_owners", name: "Property Owners", description: "Manage property owner information" },
  { id: "contracts", name: "Contracts", description: "Manage rental contracts and agreements" },
  { id: "invoices", name: "Invoices", description: "Manage invoices and billing" },
  { id: "payments", name: "Payments", description: "Manage payment records and transactions" },
  { id: "owner_payouts", name: "Owner Payouts", description: "Manage payments to property owners" },
  { id: "maintenance", name: "Maintenance", description: "Manage maintenance requests and work orders" },
  { id: "expenses", name: "Expenses", description: "Manage business expenses and costs" },
  { id: "expense_categories", name: "Expense Categories", description: "Manage expense categories and types" },
  { id: "amenities", name: "Amenities", description: "Manage property amenities and features" },
  { id: "users", name: "Users", description: "Manage user accounts and profiles" },
  { id: "roles", name: "Roles", description: "Manage roles and permissions" },
  { id: "reports", name: "Reports", description: "Access and generate various reports" },
  { id: "settings", name: "Settings", description: "Manage application settings and configuration" },
] as const;

export type ApplicationModule = typeof APPLICATION_MODULES[number]["id"];

// Permission checking utilities
export interface UserPermissions {
  [module: string]: {
    create: boolean;
    read: boolean;
    update: boolean;
    delete: boolean;
  };
}

// Authentication context
export interface AuthUser {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  status: UserStatus;
  permissions: UserPermissions;
}

// Session data
export interface SessionData {
  user: AuthUser;
  expires: string;
}
