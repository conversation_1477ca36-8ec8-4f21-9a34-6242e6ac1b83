"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useTranslations, useLocale } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { format } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { paymentSchema, type PaymentInput } from "@/types/payment";
import type { TenantWithRelations } from "@/types/tenant";
import type { InvoiceWithRelations } from "@/types/invoice";

export function PaymentForm() {
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations("payments");
  const tCommon = useTranslations("common");
  
  const [loading, setLoading] = useState(false);
  const [tenants, setTenants] = useState<TenantWithRelations[]>([]);
  const [invoices, setInvoices] = useState<InvoiceWithRelations[]>([]);
  const [selectedInvoices, setSelectedInvoices] = useState<number[]>([]);
  const [loadingTenants, setLoadingTenants] = useState(true);
  const [loadingInvoices, setLoadingInvoices] = useState(false);

  const form = useForm<PaymentInput>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      tenant_id: undefined,
      amount: "",
      payment_date: new Date().toISOString().split('T')[0],
      payment_method: "CASH",
      reference_number: "",
      notes: "",
      allocations: [],
      status: "COMPLETED",
    },
  });

  // Fetch tenants
  useEffect(() => {
    const fetchTenants = async () => {
      try {
        const response = await fetch("/api/tenants");
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setTenants(result.data);
          }
        }
      } catch (error) {
        console.error("Error fetching tenants:", error);
        toast.error("Failed to load tenants");
      } finally {
        setLoadingTenants(false);
      }
    };

    fetchTenants();
  }, []);

  // Fetch unpaid invoices when tenant is selected
  const handleTenantChange = async (tenantId: string) => {
    form.setValue("tenant_id", parseInt(tenantId));
    setSelectedInvoices([]);
    form.setValue("allocations", []);
    
    setLoadingInvoices(true);
    try {
      const response = await fetch(`/api/invoices?tenant_id=${tenantId}&status=PENDING&status=PARTIALLY_PAID&status=OVERDUE`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setInvoices(result.data);
        }
      }
    } catch (error) {
      console.error("Error fetching invoices:", error);
      toast.error("Failed to load invoices");
    } finally {
      setLoadingInvoices(false);
    }
  };

  // Handle invoice selection
  const handleInvoiceToggle = (invoiceId: number) => {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) return;

    const currentAllocations = form.getValues("allocations") || [];
    
    if (selectedInvoices.includes(invoiceId)) {
      // Remove invoice
      setSelectedInvoices(selectedInvoices.filter(id => id !== invoiceId));
      form.setValue("allocations", currentAllocations.filter(a => a.invoice_id !== invoiceId));
    } else {
      // Add invoice
      setSelectedInvoices([...selectedInvoices, invoiceId]);
      const newAllocation = {
        invoice_id: invoiceId,
        allocated_amount: invoice.balance_amount.toString(),
      };
      form.setValue("allocations", [...currentAllocations, newAllocation]);
    }
    
    // Update total amount
    updateTotalAmount();
  };

  // Update total amount based on allocations
  const updateTotalAmount = () => {
    const allocations = form.getValues("allocations") || [];
    const total = allocations.reduce((sum, allocation) => sum + parseFloat(allocation.allocated_amount), 0);
    form.setValue("amount", total.toFixed(3));
  };

  // Handle allocation amount change
  const handleAllocationAmountChange = (invoiceId: number, amount: string) => {
    const allocations = form.getValues("allocations") || [];
    const updatedAllocations = allocations.map(a => 
      a.invoice_id === invoiceId ? { ...a, allocated_amount: amount } : a
    );
    form.setValue("allocations", updatedAllocations);
    updateTotalAmount();
  };

  const onSubmit = async (data: PaymentInput) => {
    try {
      setLoading(true);

      const response = await fetch("/api/payments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error?.message || "Failed to create payment");
      }

      const result = await response.json();
      
      toast.success("Payment created successfully");
      router.push(`/${locale}/dashboard/payments`);
    } catch (error) {
      console.error("Error creating payment:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create payment");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{t("actions.create")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Tenant Selection */}
            <FormField
              control={form.control}
              name="tenant_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("table.tenant")}</FormLabel>
                  <Select
                    onValueChange={handleTenantChange}
                    value={field.value?.toString()}
                    disabled={loadingTenants}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={tCommon("placeholders.select")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {tenants.map((tenant) => (
                        <SelectItem key={tenant.id} value={tenant.id.toString()}>
                          {`${tenant.first_name} ${tenant.last_name}`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Date */}
            <FormField
              control={form.control}
              name="payment_date"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>{t("table.paymentDate")}</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(new Date(field.value), "PPP")
                          ) : (
                            <span>{tCommon("placeholders.pickDate")}</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value ? new Date(field.value) : undefined}
                        onSelect={(date) => field.onChange(date?.toISOString().split('T')[0])}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Method */}
            <FormField
              control={form.control}
              name="payment_method"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("table.method")}</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="CASH">{t("method.cash")}</SelectItem>
                      <SelectItem value="BANK_TRANSFER">{t("method.bankTransfer")}</SelectItem>
                      <SelectItem value="CHECK">{t("method.check")}</SelectItem>
                      <SelectItem value="CARD">{t("method.card")}</SelectItem>
                      <SelectItem value="ONLINE">{t("method.online")}</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Reference Number */}
            <FormField
              control={form.control}
              name="reference_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("table.reference")}</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value ?? ""} />
                  </FormControl>
                  <FormDescription>
                    {tCommon("optional")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Installment Number */}
            <FormField
              control={form.control}
              name="installment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("table.installment")}</FormLabel>
                  <FormControl>
                    <Input 
                      {...field} 
                      type="number"
                      min="1"
                      value={field.value ?? ""} 
                      onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : null)}
                    />
                  </FormControl>
                  <FormDescription>
                    {tCommon("optional")} - {t("installmentDescription")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Invoice Selection */}
            {form.watch("tenant_id") && (
              <div className="space-y-4">
                <FormLabel>{t("selectInvoices")}</FormLabel>
                {loadingInvoices ? (
                  <div className="flex items-center justify-center p-4">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : invoices.length === 0 ? (
                  <p className="text-sm text-muted-foreground">
                    {t("noUnpaidInvoices")}
                  </p>
                ) : (
                  <div className="space-y-2">
                    {invoices.map((invoice) => (
                      <Card
                        key={invoice.id}
                        className={cn(
                          "cursor-pointer transition-colors",
                          selectedInvoices.includes(invoice.id) && "border-primary"
                        )}
                        onClick={() => handleInvoiceToggle(invoice.id)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium">{invoice.invoice_number}</p>
                              <p className="text-sm text-muted-foreground">
                                {invoice.property && (locale === "ar" ? invoice.property.name_ar : invoice.property.name_en)} - 
                                {invoice.unit && invoice.unit.unit_number}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">OMR {invoice.balance_amount.toString()}</p>
                              {selectedInvoices.includes(invoice.id) && (
                                <Input
                                  type="number"
                                  step="0.001"
                                  className="mt-2 w-32"
                                  value={form.getValues("allocations")?.find(a => a.invoice_id === invoice.id)?.allocated_amount || ""}
                                  onChange={(e) => handleAllocationAmountChange(invoice.id, e.target.value)}
                                  onClick={(e) => e.stopPropagation()}
                                />
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Total Amount */}
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("table.amount")} (OMR)</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="number"
                      step="0.001"
                      readOnly={selectedInvoices.length > 0}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("notes")}</FormLabel>
                  <FormControl>
                    <Textarea {...field} value={field.value ?? ""} rows={3} />
                  </FormControl>
                  <FormDescription>
                    {tCommon("optional")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={loading}
          >
            {tCommon("actions.cancel")}
          </Button>
          <Button type="submit" disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {tCommon("actions.create")}
          </Button>
        </div>
      </form>
    </Form>
  );
}