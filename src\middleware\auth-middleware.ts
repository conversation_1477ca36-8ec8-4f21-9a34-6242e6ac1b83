import { NextResponse, type NextRequest } from "next/server";

export function authMiddleware(req: NextRequest) {
  const { pathname } = req.nextUrl;
  const authToken = req.cookies.get("auth-token");
  const isLoggedIn = !!authToken;

  // Extract locale from pathname (e.g., /en/dashboard -> en)
  const localeMatch = pathname.match(/^\/(en|ar)/);
  const locale = localeMatch ? localeMatch[1] : 'en';

  // Check if user is accessing dashboard routes without authentication
  const isDashboardRoute = pathname.includes("/dashboard") || 
                          pathname.match(/^\/(en|ar)\/dashboard/) ||
                          pathname.startsWith("/dashboard");

  if (!isLoggedIn && isDashboardRoute) {
    return NextResponse.redirect(new URL(`/${locale}/login`, req.url));
  }

  // If logged in and trying to access login page, redirect to dashboard
  const isLoginRoute = pathname.includes("/login") ||
                      pathname.match(/^\/(en|ar)\/login/) ||
                      pathname.match(/^\/(en|ar)\/auth\/login/) ||
                      pathname === "/login";

  if (isLoggedIn && isLoginRoute) {
    return NextResponse.redirect(new URL(`/${locale}/dashboard/default`, req.url));
  }

  return NextResponse.next();
}
