"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { MoreHorizontal, Eye, Edit, Trash2, Building } from "lucide-react";
import { PropertyOwnerWithRelations } from "@/types/property-owner";
import Link from "next/link";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { useState } from "react";
import { toast } from "sonner";
import { apiClient } from "@/lib/api-client";
import { useTranslations } from "next-intl";

interface ColumnTranslations {
  id: string;
  name: string;
  contact: string;
  properties: string;
  managementFee: string;
  status: string;
  payouts: string;
  actions: string;
  selectAll: string;
  selectRow: string;
  openMenu: string;
  viewDetails: string;
  editOwner: string;
  deleteOwner: string;
  manageProperties: string;
  statusActive: string;
  statusInactive: string;
}

export function getPropertyOwnerColumns(
  t: ColumnTranslations,
  locale: string,
  onRefresh?: () => void
): ColumnDef<PropertyOwnerWithRelations>[] {
  return [
    {
      accessorKey: "id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.id} className="justify-center" />
      ),
      cell: ({ row }) => (
        <div className="text-center">
          <span className="font-medium">#{row.getValue("id")}</span>
        </div>
      ),
    },
    {
      id: "actions",
      header: () => <div className="text-center">{t.actions}</div>,
      cell: ({ row }) => {
        const owner = row.original;
        const [showDeleteDialog, setShowDeleteDialog] = useState(false);
        const [isDeleting, setIsDeleting] = useState(false);
        const tDelete = useTranslations('propertyOwners.delete');
        const tMessages = useTranslations('propertyOwners.messages');

        const handleDelete = async () => {
          try {
            setIsDeleting(true);
            const result = await apiClient.delete(`/api/property-owners/${owner.id}`);

            if (!result.success) {
              throw new Error(result.error?.message || tMessages('deleteError'));
            }

            toast.success(tDelete('success'));
            setShowDeleteDialog(false);
            onRefresh?.();
          } catch (error) {
            console.error("Delete error:", error);
            toast.error(error instanceof Error ? error.message : tMessages('deleteError'));
          } finally {
            setIsDeleting(false);
          }
        };

        return (
          <div className="text-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  aria-label={t.openMenu}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>{t.actions}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/property-owners/${owner.id}`}>
                    <Eye className="mr-2 h-4 w-4" />
                    {t.viewDetails}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/property-owners/${owner.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    {t.editOwner}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-destructive"
                  disabled={false}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  {t.deleteOwner}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{tDelete('title')}</AlertDialogTitle>
                  <AlertDialogDescription>
                    {tDelete('description', { name: locale === "ar" ? owner.name_ar : owner.name_en })}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>{tDelete('cancel')}</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {isDeleting ? tDelete('deleting') : tDelete('confirm')}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        );
      },
    },
    {
      accessorKey: locale === "ar" ? "name_ar" : "name_en",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.name} />
      ),
      cell: ({ row }) => {
        const owner = row.original;
        const nameEn = owner.name_en;
        const nameAr = owner.name_ar;
        const displayName = locale === "ar" ? nameAr : nameEn;
        const altName = locale === "ar" ? nameEn : nameAr;
        
        return (
          <div className={`flex flex-col ${locale === "ar" ? "text-right" : "text-left"}`}>
            <span className="font-medium">{displayName}</span>
            <span className="text-xs text-muted-foreground">{altName}</span>
            {owner.email && (
              <span className="text-xs text-muted-foreground">{owner.email}</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "phone",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.contact} />
      ),
      cell: ({ row }) => {
        const owner = row.original;
        return (
          <div className="text-sm">
            {owner.phone && <div>{owner.phone}</div>}
            {owner.mobile && <div className="text-muted-foreground">{owner.mobile}</div>}
          </div>
        );
      },
    },
    {
      id: "properties",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.properties} />
      ),
      cell: ({ row }) => {
        const propertyCount = row.original.primary_properties?.length || 0;
        
        return (
          <div className="flex items-center justify-center gap-2">
            <Building className="h-4 w-4 text-muted-foreground" />
            <span>{propertyCount}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "management_fee_percentage",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.managementFee} />
      ),
      cell: ({ row }) => {
        const fee = row.original.management_fee_percentage;
        return (
          <div className="text-center">
            {fee ? `${fee}%` : "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.status} />
      ),
      cell: ({ row }) => {
        const status = row.original.status;
        
        return (
          <div className="text-center">
            <Badge variant={status === "ACTIVE" ? "default" : "secondary"}>
              {status === "ACTIVE" ? t.statusActive : t.statusInactive}
            </Badge>
          </div>
        );
      },
    },
    {
      id: "payouts",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.payouts} />
      ),
      cell: ({ row }) => {
        const count = row.original.payouts?.length || 0;
        return (
          <div className="text-center">
            <span className="text-muted-foreground">{count}</span>
          </div>
        );
      },
    },
  ];
}