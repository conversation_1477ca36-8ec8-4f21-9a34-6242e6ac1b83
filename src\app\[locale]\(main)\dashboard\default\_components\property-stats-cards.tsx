"use client";

import { Building2, <PERSON>, DollarSign, <PERSON>ch, TrendingUp, Home, AlertCircle, CheckCircle } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

interface StatsData {
  totalProperties: number;
  occupiedUnits: number;
  totalUnits: number;
  activeContracts: number;
  totalRevenue: number;
  pendingMaintenance: number;
  overdueInvoices: number;
  monthlyGrowth: number;
}

export function PropertyStatsCards() {
  const t = useTranslations();
  const [stats, setStats] = useState<StatsData>({
    totalProperties: 0,
    occupiedUnits: 0,
    totalUnits: 0,
    activeContracts: 0,
    totalRevenue: 0,
    pendingMaintenance: 0,
    overdueInvoices: 0,
    monthlyGrowth: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchStats() {
      try {
        // Fetch optimized dashboard stats from single endpoint
        const response = await fetch("/api/dashboard/quick-stats", {
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error('Failed to fetch dashboard stats');
        }

        const result = await response.json();

        if (result.success) {
          setStats({
            totalProperties: result.data.totalProperties,
            occupiedUnits: result.data.occupiedUnits,
            totalUnits: result.data.totalUnits,
            activeContracts: result.data.activeContracts,
            totalRevenue: result.data.totalRevenue,
            pendingMaintenance: result.data.pendingMaintenance,
            overdueInvoices: result.data.overdueInvoices,
            monthlyGrowth: result.data.monthlyGrowth,
          });
        }
      } catch (error) {
        console.error("Error fetching stats:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchStats();
  }, []);

  const occupancyRate = stats.totalUnits > 0 
    ? Math.round((stats.occupiedUnits / stats.totalUnits) * 100) 
    : 0;

  const cards = [
    {
      title: t("dashboard.totalProperties"),
      value: stats.totalProperties,
      icon: Building2,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: t("dashboard.occupancyRate"),
      value: `${occupancyRate}%`,
      subtitle: `${stats.occupiedUnits}/${stats.totalUnits} ${t("dashboard.units")}`,
      icon: Home,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: t("dashboard.monthlyRevenue"),
      value: `OMR ${stats.totalRevenue.toLocaleString()}`,
      subtitle: `+${stats.monthlyGrowth}% ${t("dashboard.fromLastMonth")}`,
      icon: DollarSign,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      trend: true,
    },
    {
      title: t("dashboard.activeContracts"),
      value: stats.activeContracts,
      icon: Users,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
    {
      title: t("dashboard.pendingMaintenance"),
      value: stats.pendingMaintenance,
      icon: Wrench,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
      alert: stats.pendingMaintenance > 5,
    },
    {
      title: t("dashboard.overdueInvoices"),
      value: stats.overdueInvoices,
      icon: AlertCircle,
      color: "text-red-600",
      bgColor: "bg-red-50",
      alert: stats.overdueInvoices > 0,
    },
  ];

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-24 bg-muted animate-pulse rounded" />
              <div className="h-8 w-8 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-7 w-16 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {cards.map((card, index) => {
        const Icon = card.icon;
        return (
          <Card key={index} className={card.alert ? "border-red-200" : ""}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${card.bgColor}`}>
                <Icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              {card.subtitle && (
                <p className={`text-xs ${card.trend ? "text-green-600" : "text-muted-foreground"} mt-1`}>
                  {card.trend && <TrendingUp className="inline h-3 w-3 mr-1" />}
                  {card.subtitle}
                </p>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}