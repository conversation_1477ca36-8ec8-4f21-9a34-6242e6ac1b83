import bcrypt from 'bcryptjs';
import { PrismaClient } from './src/generated/prisma';

const prisma = new PrismaClient();

async function testAuth() {
  try {
    console.log('🔍 Searching for admin user...');
    
    // Find the admin user
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: 'admin' },
          { email: '<EMAIL>' }
        ]
      }
    });

    if (!user) {
      console.log('❌ Admin user not found!');
      
      // Create admin user
      console.log('📝 Creating admin user...');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const newUser = await prisma.user.create({
        data: {
          username: 'admin',
          email: '<EMAIL>',
          password_hash: hashedPassword,
          first_name: 'System',
          last_name: 'Administrator',
          status: 'ACTIVE'
        }
      });
      
      console.log('✅ Admin user created:', newUser.email);
      return;
    }

    console.log('✅ User found:', {
      id: user.id,
      username: user.username,
      email: user.email,
      status: user.status
    });

    // Test password
    const testPassword = 'admin123';
    const isValid = await bcrypt.compare(testPassword, user.password_hash);
    
    console.log(`\n🔐 Password test for "${testPassword}":`, isValid ? '✅ VALID' : '❌ INVALID');
    
    // If invalid, update the password
    if (!isValid) {
      const newHash = await bcrypt.hash(testPassword, 10);
      console.log('\n📝 Updating password...');
      
      await prisma.user.update({
        where: { id: user.id },
        data: { password_hash: newHash }
      });
      console.log('✅ Password updated successfully!');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAuth();