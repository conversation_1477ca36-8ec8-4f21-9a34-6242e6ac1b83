-- AlterTable
ALTER TABLE `association_transactions` ADD COLUMN `associationSubscriptionId` INTEGER NULL,
    ADD COLUMN `member_id` INTEGER NULL,
    ADD COLUMN `reference_number` VARCHAR(100) NULL;

-- AlterTable
ALTER TABLE `payments` ADD COLUMN `installment` INTEGER NULL;

-- AlterTable
ALTER TABLE `subscription_payments` ADD COLUMN `amount_due` DECIMAL(10, 3) NULL,
    ADD COLUMN `amount_paid` DECIMAL(10, 3) NULL DEFAULT 0,
    ADD COLUMN `installment` INTEGER NULL,
    ADD COLUMN `transaction_id` INTEGER NULL;

-- CreateTable
CREATE TABLE `_TransactionPayments` (
    `A` INTEGER NOT NULL,
    `B` INTEGER NOT NULL,

    UNIQUE INDEX `_TransactionPayments_AB_unique`(`A`, `B`),
    INDEX `_TransactionPayments_B_index`(`B`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `association_transactions_member_id_idx` ON `association_transactions`(`member_id`);

-- CreateIndex
CREATE INDEX `subscription_payments_transaction_id_idx` ON `subscription_payments`(`transaction_id`);

-- AddForeignKey
ALTER TABLE `subscription_payments` ADD CONSTRAINT `subscription_payments_transaction_id_fkey` FOREIGN KEY (`transaction_id`) REFERENCES `association_transactions`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `association_transactions` ADD CONSTRAINT `association_transactions_member_id_fkey` FOREIGN KEY (`member_id`) REFERENCES `association_members`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `association_transactions` ADD CONSTRAINT `association_transactions_associationSubscriptionId_fkey` FOREIGN KEY (`associationSubscriptionId`) REFERENCES `association_subscriptions`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_TransactionPayments` ADD CONSTRAINT `_TransactionPayments_A_fkey` FOREIGN KEY (`A`) REFERENCES `association_transactions`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_TransactionPayments` ADD CONSTRAINT `_TransactionPayments_B_fkey` FOREIGN KEY (`B`) REFERENCES `subscription_payments`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
