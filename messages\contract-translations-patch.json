{"contracts": {"title": "Contracts", "description": "Manage property rental contracts", "addContract": "Add Contract", "newContract": "New Contract", "newContractDescription": "Create a new rental contract", "editContract": "Edit Contract", "viewContract": "View Contract", "table": {"contractNumber": "Contract Number", "property": "Property", "unit": "Unit", "tenants": "Tenants", "period": "Period", "monthlyRent": "Monthly Rent", "status": "Status", "createdAt": "Created At", "actions": "Actions", "selectAll": "Select all", "selectRow": "Select row", "openMenu": "Open menu", "viewDetails": "View details", "editContract": "Edit contract", "terminateContract": "Terminate contract", "renewContract": "Renew contract", "deleteContract": "Delete contract", "deleteConfirmation": "Are you sure you want to delete contract", "deleteWarning": "This action cannot be undone. All related documents will also be deleted.", "deleteError": "Failed to delete contract", "deleting": "Deleting...", "confirmDelete": "Delete", "to": "to", "expiringSoon": "Expiring Soon", "searchPlaceholder": "Search contracts...", "filterByStatus": "Filter by status", "allStatuses": "All statuses", "noResults": "No contracts found.", "serverError": "Failed to fetch contracts", "statusBadges": {"DRAFT": "Draft", "ACTIVE": "Active", "EXPIRED": "Expired", "TERMINATED": "Terminated", "RENEWED": "Renewed"}}, "form": {"contractDetails": "Contract Details", "contractDetailsDescription": "Basic information about the contract", "contractNumber": "Contract Number", "contractNumberDescription": "Leave empty to auto-generate", "status": "Status", "property": "Property", "unit": "Unit", "startDate": "Start Date", "endDate": "End Date", "financialDetails": "Financial Details", "financialDetailsDescription": "Rent and payment information", "monthlyRent": "Monthly Rent", "paymentDueDay": "Payment Due Day", "paymentDueDayDescription": "Day of the month (1-31)", "securityDeposit": "Security Deposit", "insuranceAmount": "Insurance Amount", "insuranceDueDate": "Insurance Due Date", "additionalInfo": "Additional Information", "notes": "Notes", "placeholders": {"contractNumber": "e.g., CNT-2024-001", "status": "Select status", "property": "Select property", "unit": "Select unit", "notes": "Enter any additional notes..."}, "statuses": {"DRAFT": "Draft", "ACTIVE": "Active", "EXPIRED": "Expired", "TERMINATED": "Terminated", "RENEWED": "Renewed"}, "buttons": {"cancel": "Cancel", "create": "Create Contract", "update": "Update Contract", "creating": "Creating...", "updating": "Updating..."}, "validation": {"contractNumberRequired": "Contract number is required", "propertyRequired": "Property is required", "unitRequired": "Unit is required", "startDateRequired": "Start date is required", "endDateRequired": "End date is required", "monthlyRentRequired": "Monthly rent is required", "invalidAmount": "Invalid amount format"}}, "tenants": {"title": "Contract Tenants", "addTenant": "Add Tenant", "noTenants": "No tenants assigned to this contract", "primaryTenant": "Primary", "selectTenant": "Select Tenant", "removeTenant": "Re<PERSON><PERSON>", "makeMainTenant": "Make Primary", "removeConfirmation": "Are you sure you want to remove this tenant from the contract?", "cannotRemoveLastTenant": "Cannot remove the last tenant from an active contract"}, "documents": {"title": "Contract Documents", "uploadDocument": "Upload Document", "noDocuments": "No documents uploaded", "documentType": "Document Type", "documentName": "Document Name", "fileName": "File Name", "uploadedBy": "Uploaded By", "uploadedAt": "Uploaded At", "fileSize": "File Size", "download": "Download", "delete": "Delete", "types": {"CONTRACT": "Contract", "ADDENDUM": "Addendum", "TERMINATION_LETTER": "Termination Letter", "RENEWAL_AGREEMENT": "Renewal Agreement", "PAYMENT_RECEIPT": "Payment Receipt", "OTHER": "Other"}, "validation": {"fileRequired": "Please select a file", "documentTypeRequired": "Document type is required", "documentNameRequired": "Document name is required", "fileSizeLimit": "File size must be less than 10MB"}}, "terminate": {"title": "Terminate Contract", "terminationDate": "Termination Date", "reason": "Reason for Termination", "confirmTermination": "Confirm Termination", "terminateButton": "Terminate Contract", "cancelButton": "Cancel", "success": "Contract terminated successfully", "error": "Failed to terminate contract"}, "renew": {"title": "Renew Contract", "newStartDate": "New Start Date", "newEndDate": "New End Date", "newMonthlyRent": "New Monthly Rent", "keepExisting": "Keep existing", "renewalNotes": "Renewal Notes", "confirmRenewal": "Confirm <PERSON>wal", "renewButton": "Renew Contract", "cancelButton": "Cancel", "success": "Contract renewed successfully", "error": "Failed to renew contract"}}}