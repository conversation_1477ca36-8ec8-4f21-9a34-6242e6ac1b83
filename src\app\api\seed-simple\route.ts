import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { Decimal } from "@prisma/client/runtime/library";

export async function POST(request: NextRequest) {
  try {
    console.log('🌱 Starting simple database seeding...');
    
    // Test database connection first
    console.log('Testing database connection...');
    await db.$queryRaw`SELECT 1`;
    console.log('✅ Database connection successful');

    // Check what tables exist
    console.log('Checking existing tables...');
    const tables = await db.$queryRaw`SHOW TABLES`;
    console.log('Available tables:', tables);

    // Try to create a simple property owner first
    console.log('Creating property owner...');
    try {
      const owner = await db.propertyOwner.create({
        data: {
          name_en: 'Test Owner',
          name_ar: 'مالك تجريبي',
          email: '<EMAIL>',
          phone: '+968 1234 5678',
          mobile: '+968 9876 5432',
          address_en: 'Test Address',
          address_ar: 'عنوان تجريبي',
          status: 'ACTIVE',
          created_by: 1,
          updated_by: 1,
        },
      });
      console.log('✅ Property owner created:', owner);
    } catch (ownerError) {
      console.error('❌ Failed to create property owner:', ownerError);
      throw ownerError;
    }

    // Try to create a property type
    console.log('Creating property type...');
    try {
      const propertyType = await db.propertyType.create({
        data: {
          name_en: 'Test Villa',
          name_ar: 'فيلا تجريبية',
          description_en: 'Test villa property',
          description_ar: 'عقار فيلا تجريبي',
        },
      });
      console.log('✅ Property type created:', propertyType);
    } catch (typeError) {
      console.error('❌ Failed to create property type:', typeError);
      throw typeError;
    }

    console.log('🎉 Simple seeding completed successfully!');

    return ApiResponseBuilder.success({
      message: 'Simple seeding completed successfully',
      data: {
        propertyOwners: 1,
        propertyTypes: 1,
      },
    });

  } catch (error) {
    console.error('❌ Error during simple seeding:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      name: error instanceof Error ? error.name : 'Unknown error type'
    });
    
    return ApiResponseBuilder.error(
      `Failed to seed database: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'SEED_ERROR',
      500,
      process.env.NODE_ENV === 'development' ? {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined
      } : undefined
    );
  }
}