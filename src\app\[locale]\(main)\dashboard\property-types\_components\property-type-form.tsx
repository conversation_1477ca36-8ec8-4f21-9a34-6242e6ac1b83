"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslations, useLocale } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { apiClient } from "@/lib/api-client";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { propertyTypeSchema, type PropertyTypeInput, type PropertyTypeWithCount } from "@/types/property";

interface PropertyTypeFormProps {
  propertyType?: PropertyTypeWithCount;
  isEdit?: boolean;
}

export function PropertyTypeForm({ propertyType, isEdit = false }: PropertyTypeFormProps) {
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations("properties.propertyTypes.form");
  const tButtons = useTranslations("properties.propertyTypes.form.buttons");
  const tValidation = useTranslations("properties.propertyTypes.form.validation");
  const tMessages = useTranslations("properties.propertyTypes.messages");
  
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<PropertyTypeInput>({
    resolver: zodResolver(propertyTypeSchema),
    defaultValues: {
      name_en: propertyType?.name_en || "",
      name_ar: propertyType?.name_ar || "",
      description_en: propertyType?.description_en || "",
      description_ar: propertyType?.description_ar || "",
    },
  });

  const onSubmit = async (data: PropertyTypeInput) => {
    try {
      setIsSubmitting(true);

      const url = isEdit
        ? `/api/property-types/${propertyType?.id}`
        : "/api/property-types";
      
      const result = isEdit
        ? await apiClient.put(url, data)
        : await apiClient.post(url, data);

      toast.success(
        isEdit
          ? tMessages('updateSuccess')
          : tMessages('createSuccess')
      );

      router.push(`/${locale}/dashboard/property-types`);
      router.refresh();
    } catch (error) {
      console.error("Submit error:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : isEdit ? tMessages('updateError') : tMessages('createError')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>
              {isEdit ? t("nameEn") + " / " + t("nameAr") : t("nameEn") + " / " + t("nameAr")}
            </CardTitle>
            <CardDescription>
              {isEdit ? t("updateNamesDescription") : t("enterNamesDescription")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="name_en"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("nameEn")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder={t("nameEnPlaceholder")}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="name_ar"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("nameAr")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder={t("nameArPlaceholder")}
                        disabled={isSubmitting}
                        dir="rtl"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>
              {t("descriptionEn") + " / " + t("descriptionAr")}
            </CardTitle>
            <CardDescription>
              {t("optionalDescriptions")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="description_en"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("descriptionEn")}</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      value={field.value || ""}
                      placeholder={t("descriptionEnPlaceholder")}
                      disabled={isSubmitting}
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description_ar"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("descriptionAr")}</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      value={field.value || ""}
                      placeholder={t("descriptionArPlaceholder")}
                      disabled={isSubmitting}
                      rows={3}
                      dir="rtl"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <div className="flex items-center justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            {tButtons("cancel")}
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEdit ? tButtons("updating") : tButtons("creating")}
              </>
            ) : (
              <>{isEdit ? tButtons("update") : tButtons("create")}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}