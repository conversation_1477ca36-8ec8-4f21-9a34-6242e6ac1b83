"use client";

import { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useTranslations, useLocale } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { apiClient } from "@/lib/api-client";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { propertySchema, type PropertyInput, type PropertyWithRelations, type PropertyTypeWithCount } from "@/types/property";
import { AmenitySelector } from "./amenity-selector";
import { OwnerSelect } from "./owner-select";

interface PropertyFormProps {
  property?: PropertyWithRelations;
  propertyTypes?: PropertyTypeWithCount[];
  amenities?: any[];
  owners?: any[];
  isEdit?: boolean;
}

export function PropertyForm({ property, propertyTypes: propPropertyTypes, amenities: propAmenities, owners: propOwners, isEdit = false }: PropertyFormProps) {
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale();
  const t = useTranslations("properties.form");
  const tButtons = useTranslations("properties.form.buttons");
  const tValidation = useTranslations("properties.form.validation");
  const tStatus = useTranslations("properties.status");
  const tMessages = useTranslations("properties.messages");
  const tSections = useTranslations("properties.form.sections");
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [propertyTypes, setPropertyTypes] = useState<PropertyTypeWithCount[]>(propPropertyTypes || []);
  const [loadingTypes, setLoadingTypes] = useState(!propPropertyTypes);
  const [owners, setOwners] = useState<any[]>(propOwners || []);
  const [loadingOwners, setLoadingOwners] = useState(!propOwners);
  const [selectedAmenities, setSelectedAmenities] = useState<number[]>(
    property?.amenities?.map(pa => pa.amenity.id) || []
  );
  const [hasUnits, setHasUnits] = useState(false);

  const form = useForm<PropertyInput>({
    resolver: zodResolver(propertySchema),
    defaultValues: {
      name_en: property?.name_en || "",
      name_ar: property?.name_ar || "",
      address_en: property?.address_en || "",
      address_ar: property?.address_ar || "",
      property_type_id: property?.property_type_id || 0,
      base_rent: property?.base_rent?.toString() || "",
      status: property?.status || "AVAILABLE",
      total_area: property?.total_area?.toString() || "",
      floors_count: property?.floors_count || 0,
      parking_spaces: property?.parking_spaces || 0,
      owner_id: property?.owner?.id || 0,
    },
  });

  // Fetch property types
  useEffect(() => {
    if (propPropertyTypes) return; // Skip if already provided
    
    const fetchPropertyTypes = async () => {
      try {
        setLoadingTypes(true);
        const result = await apiClient.get("/api/property-types");
        
        if (result.success) {
          setPropertyTypes(result.data);
        }
      } catch (error) {
        console.error("Error fetching property types:", error);
        toast.error("Failed to load property types");
      } finally {
        setLoadingTypes(false);
      }
    };

    fetchPropertyTypes();
  }, [propPropertyTypes]);

  // Fetch owners
  useEffect(() => {
    if (propOwners) return; // Skip if already provided
    
    const fetchOwners = async () => {
      try {
        setLoadingOwners(true);
        const result = await apiClient.get("/api/property-owners");
        
        if (result.success) {
          setOwners(result.data);
        }
      } catch (error) {
        console.error("Error fetching property owners:", error);
        toast.error("Failed to load property owners");
      } finally {
        setLoadingOwners(false);
      }
    };

    fetchOwners();
  }, [propOwners]);

  const onSubmit = async (data: PropertyInput) => {
    try {
      setIsSubmitting(true);

      const url = isEdit
        ? `/api/properties/${property?.id}`
        : "/api/properties";
      
      console.log("=== PROPERTY FORM SUBMISSION DEBUG ===");
      console.log("Form data received:", data);
      console.log("Is edit mode:", isEdit);
      console.log("API URL:", url);
      console.log("Property data to submit:", data);
      
      const result = isEdit
        ? await apiClient.put(url, data)
        : await apiClient.post(url, data);
        
      console.log("Property creation/update result:", result);
      console.log("Property ID for subsequent operations:", isEdit ? property?.id : result.data.id);

      // Update amenities for the property
      const propertyId = isEdit ? property?.id : result.data.id;
      if (propertyId) {
        try {
          console.log("Updating amenities:", { propertyId, selectedAmenities });
          const amenityResult = await apiClient.put(`/api/properties/${propertyId}/amenities`, { amenity_ids: selectedAmenities });
          console.log("Amenity update result:", amenityResult);
        } catch (amenityError) {
          console.error("Failed to update amenities:", amenityError);
          // Continue even if amenity update fails
        }
      }

      toast.success(
        isEdit
          ? tMessages('updateSuccess')
          : tMessages('createSuccess')
      );

      // Get the locale from the current pathname
      const pathSegments = pathname.split('/');
      const currentLocale = pathSegments[1] || locale;
      
      console.log("Navigation debug:", {
        pathname,
        pathSegments,
        currentLocale,
        targetPath: `/${currentLocale}/dashboard/properties`
      });
      
      // Navigate to properties list
      router.push(`/${currentLocale}/dashboard/properties`);
      router.refresh();
    } catch (error) {
      console.error("Submit error:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : isEdit ? tMessages('updateError') : tMessages('createError')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{tSections('basicInformation')}</CardTitle>
            <CardDescription>
              {tSections('basicInformationDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="name_en"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("nameEn")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder={t("nameEnPlaceholder")}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="name_ar"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("nameAr")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder={t("nameArPlaceholder")}
                        disabled={isSubmitting}
                        dir="rtl"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="property_type_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("propertyType")}</FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      defaultValue={field.value?.toString()}
                      disabled={isSubmitting || loadingTypes}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("selectPropertyType")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {propertyTypes.map((type) => (
                          <SelectItem key={type.id} value={type.id.toString()}>
                            {locale === "ar" ? type.name_ar : type.name_en}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("status")}</FormLabel>
                    <Select 
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("selectStatus")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="AVAILABLE">{tStatus("available")}</SelectItem>
                        <SelectItem value="RENTED">{tStatus("rented")}</SelectItem>
                        <SelectItem value="UNDER_MAINTENANCE">{tStatus("underMaintenance")}</SelectItem>
                        <SelectItem value="OUT_OF_SERVICE">{tStatus("outOfService")}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="owner_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("owner")}</FormLabel>
                  <FormControl>
                    <OwnerSelect
                      value={field.value ?? undefined}
                      onChange={field.onChange}
                      owners={owners}
                      loading={loadingOwners}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                  <FormDescription>
                    {t("ownerDescription")}
                  </FormDescription>
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{tSections('location')}</CardTitle>
            <CardDescription>
              {tSections('locationDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="address_en"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("addressEn")}</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder={t("addressEnPlaceholder")}
                      disabled={isSubmitting}
                      rows={2}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="address_ar"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("addressAr")}</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder={t("addressArPlaceholder")}
                      disabled={isSubmitting}
                      rows={2}
                      dir="rtl"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{tSections('financialDetails')}</CardTitle>
            <CardDescription>
              {tSections('financialDetailsDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="base_rent"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("baseRent")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="text"
                        placeholder={t("baseRentPlaceholder")}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      Format: 0.000 (3 decimal places)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="total_area"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("totalArea")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        value={field.value ?? ""}
                        type="text"
                        placeholder={t("totalAreaPlaceholder")}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional field in square meters
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="floors_count"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("floorsCount")}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder={t("floorsCountPlaceholder")}
                        disabled={isSubmitting}
                        value={field.value === 0 || field.value === null ? "" : field.value}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="parking_spaces"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("parkingSpaces")}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder={t("parkingSpacesPlaceholder")}
                        disabled={isSubmitting}
                        value={field.value === 0 || field.value === null ? "" : field.value}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{tSections('amenities')}</CardTitle>
            <CardDescription>
              {tSections('amenitiesDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AmenitySelector
              selectedIds={selectedAmenities}
              onChange={setSelectedAmenities}
              disabled={isSubmitting}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{tSections('units')}</CardTitle>
            <CardDescription>
              {tSections('unitsDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="has-units"
                checked={hasUnits}
                onCheckedChange={(checked) => setHasUnits(checked === true)}
                disabled={isSubmitting}
              />
              <label
                htmlFor="has-units"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {t("hasUnits")}
              </label>
            </div>
            <FormDescription className="mt-2">
              {t("hasUnitsDescription")}
            </FormDescription>
          </CardContent>
        </Card>

        <div className="flex items-center justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            {tButtons("cancel")}
          </Button>
          <Button type="submit" disabled={isSubmitting || loadingTypes}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEdit ? tButtons("updating") : tButtons("creating")}
              </>
            ) : (
              <>{isEdit ? tButtons("update") : tButtons("create")}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}