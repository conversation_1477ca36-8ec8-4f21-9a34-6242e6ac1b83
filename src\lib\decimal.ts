import { Decimal } from "@prisma/client/runtime/library";

export function serializeDecimal(value: any): any {
  if (value === null || value === undefined) {
    return value;
  }

  // Check for Decimal type by looking at its properties
  if (value && typeof value === 'object' && 'd' in value && 'e' in value && 's' in value) {
    // This is likely a Decimal object
    return parseFloat(value.toString());
  }

  if (value instanceof Decimal) {
    return value.toNumber();
  }

  if (value instanceof Date) {
    return value.toISOString();
  }

  if (Array.isArray(value)) {
    return value.map(serializeDecimal);
  }

  if (typeof value === "object" && value !== null) {
    const serialized: any = {};
    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        serialized[key] = serializeDecimal(value[key]);
      }
    }
    return serialized;
  }

  return value;
}