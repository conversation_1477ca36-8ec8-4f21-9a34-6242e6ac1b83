"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { format, addMonths, addDays } from "date-fns";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon, FileText, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { apiClient } from "@/lib/api-client";
import { useRTL } from "@/hooks/use-rtl";

interface GenerateSubscriptionPaymentsProps {
  associationId: number;
  subscriptions: Array<{
    id: number;
    name_en: string;
    name_ar: string;
    amount: string;
    frequency: string;
    is_active: boolean;
  }>;
  members: Array<{
    id: number;
    full_name: string;
    unit_number: string;
  }>;
  onSuccess: () => void;
}

export function GenerateSubscriptionPayments({
  associationId,
  subscriptions,
  members,
  onSuccess,
}: GenerateSubscriptionPaymentsProps) {
  const t = useTranslations("ownersAssociations.payments");
  const { isRTL } = useRTL();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState<string>("");
  const [selectedMonth, setSelectedMonth] = useState<Date>(new Date());
  const [selectedMembers, setSelectedMembers] = useState<string>("all");

  const activeSubscriptions = subscriptions.filter(s => s.is_active);

  const handleGenerate = async () => {
    if (!selectedSubscription) {
      toast.error(t("selectSubscriptionError"));
      return;
    }

    try {
      setLoading(true);
      
      const subscription = subscriptions.find(s => s.id.toString() === selectedSubscription);
      if (!subscription) return;

      // Calculate due date based on frequency
      let dueDate = new Date(selectedMonth);
      dueDate.setDate(5); // Default to 5th of the month
      
      // Generate payments for selected members
      const membersToProcess = selectedMembers === "all" 
        ? members 
        : members.filter(m => m.id.toString() === selectedMembers);

      let successCount = 0;
      let errorCount = 0;

      for (const member of membersToProcess) {
        try {
          await apiClient.post(
            `/api/owners-associations/${associationId}/subscription-payments`,
            {
              subscription_id: parseInt(selectedSubscription),
              member_id: member.id,
              amount_due: subscription.amount, // Keep as string
              amount_paid: "0", // String format
              due_date: format(dueDate, "yyyy-MM-dd"),
              status: "UNPAID",
              payment_method: null,
              payment_date: format(dueDate, "yyyy-MM-dd"), // Use due_date as payment_date for now
              notes: null,
              reference_number: null,
            }
          );
          successCount++;
        } catch (error: any) {
          console.error(`Error creating payment for member ${member.id}:`, error);
          errorCount++;
        }
      }

      if (successCount > 0) {
        toast.success(t("generateSuccess", { count: successCount }));
        onSuccess();
        setOpen(false);
      }
      
      if (errorCount > 0) {
        toast.error(t("generatePartialError", { count: errorCount }));
      }
    } catch (error) {
      console.error("Error generating payments:", error);
      toast.error(t("generateError"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="sm" variant="outline">
          <FileText className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
          {t("generatePayments")}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("generatePaymentsDialog.title")}</DialogTitle>
          <DialogDescription>
            {t("generatePaymentsDialog.description")}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {/* Subscription Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              {t("subscription")}
            </label>
            <Select value={selectedSubscription} onValueChange={setSelectedSubscription}>
              <SelectTrigger>
                <SelectValue placeholder={t("selectSubscription")} />
              </SelectTrigger>
              <SelectContent>
                {activeSubscriptions.map((subscription) => (
                  <SelectItem key={subscription.id} value={subscription.id.toString()}>
                    <div className="flex flex-col">
                      <span>{isRTL ? subscription.name_ar : subscription.name_en}</span>
                      <span className="text-xs text-muted-foreground">
                        {t("amount")}: {subscription.amount} - {t(`frequency.${subscription.frequency.toLowerCase()}`)}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Month Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              {t("billingMonth")}
            </label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !selectedMonth && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                  {format(selectedMonth, "MMMM yyyy")}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={selectedMonth}
                  onSelect={(date) => date && setSelectedMonth(date)}
                  disabled={(date) => date < new Date("2024-01-01")}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Member Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              {t("members")}
            </label>
            <Select value={selectedMembers} onValueChange={setSelectedMembers}>
              <SelectTrigger>
                <SelectValue placeholder={t("selectMembers")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("allMembers")}</SelectItem>
                {members.map((member) => (
                  <SelectItem key={member.id} value={member.id.toString()}>
                    {member.full_name} ({t("unit")} {member.unit_number})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)} disabled={loading}>
            {t("cancel")}
          </Button>
          <Button onClick={handleGenerate} disabled={loading || !selectedSubscription}>
            {loading && <Loader2 className={cn("h-4 w-4 animate-spin", isRTL ? "ml-2" : "mr-2")} />}
            {t("generate")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}