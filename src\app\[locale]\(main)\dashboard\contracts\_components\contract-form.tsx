"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useTranslations, useLocale } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { Loader2, CalendarIcon, X } from "lucide-react";
import { format } from "date-fns";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { contractSchema, type ContractInput, type ContractWithRelations } from "@/types/contract";
import type { Property, Unit, Tenant } from "@/generated/prisma";

interface ContractFormProps {
  contract?: ContractWithRelations;
  isEdit?: boolean;
}

export function ContractForm({ contract, isEdit = false }: ContractFormProps) {
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations("contracts.form");
  const tButtons = useTranslations("contracts.form.buttons");
  const tValidation = useTranslations("contracts.form.validation");
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [properties, setProperties] = useState<Property[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loadingProperties, setLoadingProperties] = useState(true);
  const [loadingUnits, setLoadingUnits] = useState(false);
  const [loadingTenants, setLoadingTenants] = useState(true);

  const form = useForm<ContractInput>({
    resolver: zodResolver(contractSchema),
    defaultValues: {
      contract_number: contract?.contract_number || "",
      property_id: contract?.property_id || 0,
      unit_id: contract?.unit_id || 0,
      tenant_ids: contract?.tenants?.map(ct => ct.tenant_id) || [],
      start_date: contract?.start_date ? format(new Date(contract.start_date), "yyyy-MM-dd") : "",
      end_date: contract?.end_date ? format(new Date(contract.end_date), "yyyy-MM-dd") : "",
      monthly_rent: contract?.monthly_rent?.toString() || "",
      payment_due_day: contract?.payment_due_day || 1,
      security_deposit: contract?.security_deposit?.toString() || "",
      insurance_amount: contract?.insurance_amount?.toString() || "",
      insurance_due_date: contract?.insurance_due_date ? format(new Date(contract.insurance_due_date), "yyyy-MM-dd") : "",
      status: contract?.status || "DRAFT",
      notes: contract?.notes || "",
    },
  });

  // Fetch properties and tenants on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [propertiesRes, tenantsRes] = await Promise.all([
          fetch("/api/properties", { credentials: 'include' }),
          fetch("/api/tenants", { credentials: 'include' })
        ]);
        
        const [propertiesResult, tenantsResult] = await Promise.all([
          propertiesRes.json(),
          tenantsRes.json()
        ]);
        
        if (propertiesResult.success) {
          setProperties(propertiesResult.data);
        }
        if (tenantsResult.success) {
          setTenants(tenantsResult.data);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error(t("messages.loadDataError"));
      } finally {
        setLoadingProperties(false);
        setLoadingTenants(false);
      }
    };
    fetchData();
  }, []);

  // Fetch units when property is selected
  useEffect(() => {
    const propertyId = form.watch("property_id");
    if (propertyId && propertyId > 0) {
      const fetchUnits = async () => {
        try {
          setLoadingUnits(true);
          const response = await fetch(`/api/units?property_id=${propertyId}`, { credentials: 'include' });
          const result = await response.json();
          if (result.success) {
            setUnits(result.data);
          }
        } catch (error) {
          console.error("Error fetching units:", error);
          toast.error(t("messages.loadUnitsError"));
        } finally {
          setLoadingUnits(false);
        }
      };
      fetchUnits();
    } else {
      setUnits([]);
    }
  }, [form.watch("property_id")]);

  const onSubmit = async (data: ContractInput) => {
    try {
      setIsSubmitting(true);

      const url = isEdit
        ? `/api/contracts/${contract?.id}`
        : "/api/contracts";
      
      const method = isEdit ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error?.message || t("messages.saveError"));
      }

      toast.success(
        isEdit
          ? t("messages.updateSuccess")
          : t("messages.createSuccess")
      );

      router.push(`/${locale}/dashboard/contracts`);
      router.refresh();
    } catch (error) {
      console.error("Submit error:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : t("messages.genericError")
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{t("contractDetails")}</CardTitle>
            <CardDescription>
              {t("contractDetailsDescription")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="contract_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("contractNumber")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder={t("placeholders.contractNumber")}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      {t("contractNumberDescription")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("status")}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isSubmitting}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("placeholders.status")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="DRAFT">{t("statuses.DRAFT")}</SelectItem>
                        <SelectItem value="ACTIVE">{t("statuses.ACTIVE")}</SelectItem>
                        <SelectItem value="EXPIRED">{t("statuses.EXPIRED")}</SelectItem>
                        <SelectItem value="TERMINATED">{t("statuses.TERMINATED")}</SelectItem>
                        <SelectItem value="RENEWED">{t("statuses.RENEWED")}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="property_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("property")}</FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(parseInt(value))} 
                      defaultValue={field.value?.toString()}
                      disabled={isSubmitting || loadingProperties}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("placeholders.property")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {properties.map((property) => (
                          <SelectItem key={property.id} value={property.id.toString()}>
                            {locale === "ar" ? property.name_ar : property.name_en}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="unit_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("unit")}</FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(parseInt(value))} 
                      defaultValue={field.value?.toString()}
                      disabled={isSubmitting || loadingUnits || !form.watch("property_id")}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("placeholders.unit")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {units.map((unit) => (
                          <SelectItem key={unit.id} value={unit.id.toString()}>
                            {unit.unit_number}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="tenant_ids"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("tenants")}</FormLabel>
                  <Select 
                    onValueChange={(value) => {
                      const currentIds = field.value || [];
                      const tenantId = parseInt(value);
                      if (!currentIds.includes(tenantId)) {
                        field.onChange([...currentIds, tenantId]);
                      }
                    }}
                    disabled={isSubmitting || loadingTenants}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t("placeholders.tenants")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {tenants.map((tenant) => (
                        <SelectItem key={tenant.id} value={tenant.id.toString()}>
                          {tenant.first_name} {tenant.last_name} - {tenant.phone}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {field.value && field.value.length > 0 && (
                      <div className="mt-2 space-y-2">
                        {field.value.map((tenantId, index) => {
                          const tenant = tenants.find(t => t.id === tenantId);
                          return tenant ? (
                            <div key={tenantId} className="flex items-center justify-between rounded-md border p-2">
                              <span className="text-sm">
                                {tenant.first_name} {tenant.last_name}
                                {index === 0 && <Badge className="ml-2" variant="secondary">{t("primaryTenant")}</Badge>}
                              </span>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  field.onChange(field.value.filter(id => id !== tenantId));
                                }}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : null;
                        })}
                      </div>
                    )}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("startDate")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="date"
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("endDate")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="date"
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t("financialDetails")}</CardTitle>
            <CardDescription>
              {t("financialDetailsDescription")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="monthly_rent"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("monthlyRent")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="text"
                        placeholder="0.000"
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>OMR</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="payment_due_day"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("paymentDueDay")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        min="1"
                        max="31"
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>{t("paymentDueDayDescription")}</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="security_deposit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("securityDeposit")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="text"
                        placeholder="0.000"
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>OMR</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="insurance_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("insuranceAmount")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        value={field.value ?? ""}
                        type="text"
                        placeholder="0.000"
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>OMR (optional)</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="insurance_due_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("insuranceDueDate")}</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      value={field.value ?? ""}
                      type="date"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t("additionalInfo")}</CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("notes")}</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      value={field.value ?? ""}
                      placeholder={t("placeholders.notes")}
                      className="min-h-[100px]"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <div className="flex items-center justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            {tButtons("cancel")}
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEdit ? tButtons("updating") : tButtons("creating")}
              </>
            ) : (
              <>{isEdit ? tButtons("update") : tButtons("create")}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}