import { notFound } from "next/navigation";
import Link from "next/link";
import { cookies, headers } from "next/headers";
import { ArrowLeft, Edit, Shield, Calendar, Users } from "lucide-react";
import { getTranslations } from "next-intl/server";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatDateForDisplay } from "@/lib/utils";
import type { RoleWithPermissions } from "@/types/user";
import { APPLICATION_MODULES } from "@/types/user";

interface RoleDetailPageProps {
  params: Promise<{
    locale: string;
    id: string;
  }>;
}

async function getRole(id: number): Promise<(RoleWithPermissions & { _count: { user_roles: number } }) | null> {
  try {
    // Get dynamic host for multi-port development
    const headersList = await headers();
    const host = headersList.get('host') || 'localhost:3001';
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}`;
    
    // Get cookies for server-side authentication
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');

    const response = await fetch(`${baseUrl}/api/roles/${id}`, {
      cache: 'no-store',
      headers: {
        'Cookie': authToken ? `auth-token=${authToken.value}` : '',
      },
    });

    if (!response.ok) {
      return null;
    }

    return response.json();
  } catch (error) {
    console.error('Error fetching role:', error);
    return null;
  }
}

export default async function RoleDetailPage({ params }: RoleDetailPageProps) {
  const { locale, id } = await params;
  const roleId = parseInt(id);
  const t = await getTranslations('roles');
  const tModules = await getTranslations('roles.modules');

  if (isNaN(roleId)) {
    notFound();
  }

  const role = await getRole(roleId);

  if (!role) {
    notFound();
  }

  // Group permissions by module
  const permissionsByModule: Record<string, string[]> = {};
  role.role_permissions.forEach(rp => {
    const module = rp.permission.module;
    if (!permissionsByModule[module]) {
      permissionsByModule[module] = [];
    }
    permissionsByModule[module].push(rp.permission.action);
  });

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/${locale}/dashboard/roles`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t('backToRoles')}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Shield className="h-8 w-8 text-primary" />
              {role.name}
            </h1>
            <p className="text-muted-foreground">{t('roleDetailsAndPermissions')}</p>
          </div>
        </div>
        {!role.is_system && (
          <Button asChild>
            <Link href={`/${locale}/dashboard/roles/${role.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              {t('editRole')}
            </Link>
          </Button>
        )}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Role Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('roleInformation')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div>
                <div className="text-sm font-medium">{t('name')}:</div>
                <div className="flex items-center gap-2">
                  <span className="text-lg font-semibold">{role.name}</span>
                  {role.is_system && (
                    <Badge variant="outline" className="text-xs">
                      {t('systemRole')}
                    </Badge>
                  )}
                </div>
              </div>
              
              {role.description && (
                <div>
                  <div className="text-sm font-medium">{t('description')}:</div>
                  <div className="text-sm text-muted-foreground">
                    {role.description}
                  </div>
                </div>
              )}

              <div>
                <div className="text-sm font-medium flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span>{t('assignedUsers')}:</span>
                </div>
                <div className="text-sm text-muted-foreground">
                  {role._count.user_roles} {role._count.user_roles === 1 ? t('user') : t('users')}
                </div>
              </div>

              <div>
                <div className="text-sm font-medium flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>{t('created')}:</span>
                </div>
                <div className="text-sm text-muted-foreground">
                  {formatDateForDisplay(role.created_at, "long")}
                </div>
              </div>

              <div>
                <div className="text-sm font-medium flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>{t('lastUpdated')}:</span>
                </div>
                <div className="text-sm text-muted-foreground">
                  {formatDateForDisplay(role.updated_at, "long")}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Permissions Overview */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="text-lg">{t('permissions')}</CardTitle>
            <CardDescription>
              {t('moduleBasedPermissions')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {Object.keys(permissionsByModule).length > 0 ? (
              <div className="space-y-6">
                {APPLICATION_MODULES.map((module) => {
                  const modulePermissions = permissionsByModule[module.id] || [];
                  const hasFullAccess = modulePermissions.length === 4 && 
                    ['CREATE', 'READ', 'UPDATE', 'DELETE'].every(action => modulePermissions.includes(action));
                  
                  if (modulePermissions.length === 0) {
                    return (
                      <div key={module.id} className="flex items-center justify-between p-4 border rounded-lg bg-muted/50">
                        <div className="flex items-center space-x-3">
                          <Shield className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <h4 className="font-medium text-muted-foreground">{tModules(module.id)}</h4>
                            <p className="text-sm text-muted-foreground">{tModules(`${module.id}Description`)}</p>
                          </div>
                        </div>
                        <Badge variant="secondary">{t('noAccess')}</Badge>
                      </div>
                    );
                  }

                  return (
                    <div key={module.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Shield className="h-5 w-5 text-primary" />
                          <div>
                            <h4 className="font-medium">{tModules(module.id)}</h4>
                            <p className="text-sm text-muted-foreground">{tModules(`${module.id}Description`)}</p>
                          </div>
                        </div>
                        <Badge variant={hasFullAccess ? "default" : "secondary"}>
                          {hasFullAccess ? t('fullAccess') : t('partialAccess')}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2 sm:grid-cols-4">
                        {[
                          { action: "CREATE", label: t('create') },
                          { action: "READ", label: t('view') },
                          { action: "UPDATE", label: t('edit') },
                          { action: "DELETE", label: t('delete') },
                        ].map((perm) => (
                          <div key={perm.action} className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${
                              modulePermissions.includes(perm.action) 
                                ? 'bg-green-500' 
                                : 'bg-gray-300'
                            }`} />
                            <span className={`text-sm ${
                              modulePermissions.includes(perm.action) 
                                ? 'text-foreground' 
                                : 'text-muted-foreground'
                            }`}>
                              {perm.label}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">{t('noPermissionsAssigned')}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
