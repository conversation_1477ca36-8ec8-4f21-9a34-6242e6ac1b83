import { notFound } from "next/navigation";
import { Metadata } from "next";
import { AmenityForm } from "../../_components/amenity-form";

async function getAmenity(id: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_APP_URL}/api/amenities/${id}`,
      {
        cache: "no-store",
      }
    );

    if (!response.ok) {
      return null;
    }

    const result = await response.json();
    return result.success ? result.data : null;
  } catch (error) {
    console.error("Error fetching amenity:", error);
    return null;
  }
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> {
  const { id } = await params;
  const amenity = await getAmenity(id);
  return {
    title: amenity ? `Edit - ${amenity.name_en}` : "Edit Amenity",
    description: amenity
      ? `Edit amenity ${amenity.name_en}`
      : "Edit amenity",
  };
}

export default async function EditAmenityPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const amenity = await getAmenity(id);

  if (!amenity) {
    notFound();
  }

  return <AmenityForm mode="edit" initialData={amenity} />;
}