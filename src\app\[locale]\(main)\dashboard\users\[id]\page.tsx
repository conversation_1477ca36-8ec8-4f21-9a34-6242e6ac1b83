import { notFound } from "next/navigation";
import Link from "next/link";
import { cookies, headers } from "next/headers";
import { ArrowLeft, Edit, Mail, Phone, User, Shield, Calendar, Key } from "lucide-react";
import { getTranslations } from "next-intl/server";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { getInitials, formatDateForDisplay } from "@/lib/utils";
import type { UserWithRoles } from "@/types/user";

interface UserDetailPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

async function getUser(id: number): Promise<UserWithRoles | null> {
  try {
    // Get dynamic host for multi-port development
    const headersList = await headers();
    const host = headersList.get('host') || 'localhost:3001';
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}`;
    
    // Get cookies for server-side authentication
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');

    const response = await fetch(`${baseUrl}/api/users/${id}`, {
      cache: 'no-store',
      headers: {
        'Cookie': authToken ? `auth-token=${authToken.value}` : '',
      },
    });

    if (!response.ok) {
      return null;
    }

    return response.json();
  } catch (error) {
    console.error('Error fetching user:', error);
    return null;
  }
}

export default async function UserDetailPage({ params }: UserDetailPageProps) {
  const { id, locale } = await params;
  const userId = parseInt(id);
  const t = await getTranslations('users');
  const tCommon = await getTranslations('common');

  if (isNaN(userId)) {
    notFound();
  }

  const user = await getUser(userId);

  if (!user) {
    notFound();
  }

  const fullName = `${user.first_name} ${user.last_name}`;
  const statusVariant = 
    user.status === "ACTIVE" ? "default" :
    user.status === "INACTIVE" ? "secondary" :
    user.status === "SUSPENDED" ? "destructive" :
    "outline";

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/${locale}/dashboard/users`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t('backToUsers')}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{fullName}</h1>
            <p className="text-muted-foreground">{t('userDetails')}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href={`/${locale}/dashboard/users/${user.id}/change-password`}>
              <Key className="mr-2 h-4 w-4" />
              {t('changePassword')}
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/${locale}/dashboard/users/${user.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              {t('editUser')}
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('basicInformation')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-4">
              <Avatar className="h-12 w-12">
                <AvatarFallback className="text-lg">
                  {getInitials(fullName)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="text-lg font-semibold">{fullName}</h3>
                <Badge variant={statusVariant}>
                  {t(`statuses.${user.status}`)}
                </Badge>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">@{user.username}</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{user.email}</span>
              </div>
              {user.phone && (
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{user.phone}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Role Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('rolesAndPermissions')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {user.user_roles.length > 0 ? (
              <div className="space-y-3">
                {user.user_roles.map((userRole) => (
                  <div key={userRole.id} className="flex items-start space-x-3">
                    <Shield className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div>
                      <div className="font-medium text-sm">{userRole.role.name}</div>
                      {userRole.role.description && (
                        <div className="text-xs text-muted-foreground">
                          {userRole.role.description}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">
                {t('noRoles')}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('accountSettings')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div>
                <div className="text-sm font-medium">{t('emailVerified')}:</div>
                <Badge variant={user.email_verified ? "default" : "secondary"} className="text-xs">
                  {user.email_verified ? t('verified') : t('notVerified')}
                </Badge>
              </div>
              <div>
                <div className="text-sm font-medium flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>{t('lastLogin')}:</span>
                </div>
                <div className="text-sm text-muted-foreground">
                  {user.last_login_at 
                    ? new Date(user.last_login_at).toLocaleDateString(locale === 'ar' ? 'ar-OM' : 'en-OM', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })
                    : t('never')
                  }
                </div>
              </div>
              <div>
                <div className="text-sm font-medium flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>{tCommon('createdAt')}:</span>
                </div>
                <div className="text-sm text-muted-foreground">
                  {new Date(user.created_at).toLocaleDateString(locale === 'ar' ? 'ar-OM' : 'en-OM', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              </div>
              <div>
                <div className="text-sm font-medium flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>{tCommon('updatedAt')}:</span>
                </div>
                <div className="text-sm text-muted-foreground">
                  {new Date(user.updated_at).toLocaleDateString(locale === 'ar' ? 'ar-OM' : 'en-OM', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
