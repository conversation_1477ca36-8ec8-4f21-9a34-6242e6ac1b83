import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import Link from "next/link";
import { Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PropertyTypesDataTable } from "./_components/property-types-data-table";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "properties" });

  return {
    title: t("propertyTypes.title"),
    description: t("propertyTypes.description"),
  };
}

export default async function PropertyTypesPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "properties" });
  const tCommon = await getTranslations({ locale, namespace: "common" });

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${locale}/dashboard`}>
              {tCommon("navigation.dashboard")}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t("propertyTypes.title")}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
            {t("propertyTypes.title")}
          </h1>
          <p className="text-muted-foreground">
            {t("propertyTypes.description")}
          </p>
        </div>
        <Button asChild size="default" className="w-full md:w-auto">
          <Link href={`/${locale}/dashboard/property-types/create`}>
            <Plus className="h-4 w-4 ltr:mr-2 rtl:ml-2" />
            {t("propertyTypes.addPropertyType")}
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("propertyTypes.allPropertyTypes")}</CardTitle>
          <CardDescription>
            {t("propertyTypes.viewManage")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PropertyTypesDataTable />
        </CardContent>
      </Card>
    </div>
  );
}