const { PrismaClient } = require('./src/generated/prisma');
const prisma = new PrismaClient();

async function testPaymentFixes() {
  console.log('🧪 TESTING PAYMENT FUNCTIONALITY FIXES\n');
  console.log('=' .repeat(60));

  let testsPassed = 0;
  let testsFailed = 0;

  try {
    // Test 1: Check if subscription_payments table works with both schemas
    console.log('\n1️⃣ Testing subscription_payments table compatibility...');
    
    try {
      const paymentCount = await prisma.subscriptionPayment.count();
      console.log('✅ Basic query works - Payment count:', paymentCount);
      testsPassed++;
    } catch (error) {
      console.log('❌ Basic query failed:', error.message);
      testsFailed++;
    }

    // Test 2: Try to query with enhanced schema fields
    console.log('\n2️⃣ Testing enhanced schema fields...');
    
    try {
      // This will work if the migration was applied
      const enhancedQuery = await prisma.$queryRaw`
        SELECT id, amount, amount_due, amount_paid, remaining_balance 
        FROM subscription_payments 
        LIMIT 1;
      `;
      console.log('✅ Enhanced schema fields available');
      console.log('📊 Sample data:', enhancedQuery[0] || 'No records found');
      testsPassed++;
    } catch (error) {
      console.log('⚠️ Enhanced schema not available (expected if migration not run)');
      console.log('📝 Error:', error.message);
      // This is expected if migration hasn't been run, so don't count as failure
    }

    // Test 3: Check if subscription_payment_installments table exists
    console.log('\n3️⃣ Testing subscription_payment_installments table...');
    
    try {
      const installmentCount = await prisma.$queryRaw`
        SELECT COUNT(*) as count FROM subscription_payment_installments;
      `;
      console.log('✅ Installments table exists - Count:', installmentCount[0]?.count || 0);
      testsPassed++;
    } catch (error) {
      console.log('⚠️ Installments table does not exist (expected if migration not run)');
      console.log('📝 Error:', error.message);
      // This is expected if migration hasn't been run, so don't count as failure
    }

    // Test 4: Test API schema compatibility
    console.log('\n4️⃣ Testing API schema compatibility...');
    
    try {
      // Test the payment query with include relations
      const paymentsWithRelations = await prisma.subscriptionPayment.findMany({
        take: 1,
        include: {
          subscription: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
              amount: true,
              frequency: true,
            },
          },
          member: {
            select: {
              id: true,
              full_name: true,
              unit_number: true,
              email: true,
              phone: true,
            },
          },
          creator: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
            },
          },
        },
      });
      
      console.log('✅ API relations query works');
      console.log('📊 Sample payment with relations:', paymentsWithRelations[0] ? 'Found' : 'No records');
      testsPassed++;
    } catch (error) {
      console.log('❌ API relations query failed:', error.message);
      testsFailed++;
    }

    // Test 5: Test transformed payment data
    console.log('\n5️⃣ Testing payment data transformation...');
    
    try {
      const payments = await prisma.subscriptionPayment.findMany({ take: 1 });
      
      if (payments.length > 0) {
        const payment = payments[0];
        const hasEnhancedSchema = 'amount_due' in payment;
        
        // Simulate the transformation logic from the API
        const transformedPayment = {
          ...payment,
          amount: payment.amount.toString(),
          amount_due: hasEnhancedSchema ? payment.amount_due?.toString() || payment.amount.toString() : payment.amount.toString(),
          amount_paid: hasEnhancedSchema ? payment.amount_paid?.toString() || '0' : (payment.status === 'PAID' ? payment.amount.toString() : '0'),
          enhanced_schema: hasEnhancedSchema,
        };
        
        console.log('✅ Payment transformation works');
        console.log('📊 Enhanced schema available:', hasEnhancedSchema);
        console.log('📊 Transformed payment structure:', {
          id: transformedPayment.id,
          amount: transformedPayment.amount,
          amount_due: transformedPayment.amount_due,
          amount_paid: transformedPayment.amount_paid,
          status: transformedPayment.status,
          enhanced_schema: transformedPayment.enhanced_schema,
        });
        testsPassed++;
      } else {
        console.log('⚠️ No payment records to test transformation');
      }
    } catch (error) {
      console.log('❌ Payment transformation failed:', error.message);
      testsFailed++;
    }

    // Test 6: Check association transactions table
    console.log('\n6️⃣ Testing association_transactions compatibility...');
    
    try {
      const transactionCount = await prisma.associationTransaction.count();
      console.log('✅ Association transactions query works - Count:', transactionCount);
      testsPassed++;
    } catch (error) {
      console.log('❌ Association transactions query failed:', error.message);
      testsFailed++;
    }

    // Test 7: Check view if it exists
    console.log('\n7️⃣ Testing member payment summary view...');
    
    try {
      const viewData = await prisma.$queryRaw`
        SELECT * FROM member_subscription_payment_summary LIMIT 1;
      `;
      console.log('✅ Payment summary view works');
      console.log('📊 Sample view data:', viewData[0] || 'No records found');
      testsPassed++;
    } catch (error) {
      console.log('⚠️ Payment summary view not available (expected if migration not run)');
      console.log('📝 Error:', error.message);
    }

    // Final results
    console.log('\n' + '='.repeat(60));
    console.log('🏁 TEST RESULTS SUMMARY');
    console.log('='.repeat(60));
    console.log(`✅ Tests Passed: ${testsPassed}`);
    console.log(`❌ Tests Failed: ${testsFailed}`);
    console.log(`📊 Success Rate: ${Math.round((testsPassed / (testsPassed + testsFailed)) * 100)}%`);
    
    if (testsFailed === 0) {
      console.log('\n🎉 ALL CORE TESTS PASSED! Payment functionality should work correctly.');
    } else if (testsFailed <= 2) {
      console.log('\n⚠️ SOME TESTS FAILED: Core functionality works but some features may be limited.');
      console.log('💡 Consider running the database migration script for full functionality.');
    } else {
      console.log('\n🚨 MULTIPLE TEST FAILURES: There may be significant issues that need attention.');
    }

    console.log('\n📋 NEXT STEPS:');
    console.log('1. If enhanced schema tests failed, run: mysql -u root -p property_management_system < database_payment_fix.sql');
    console.log('2. If basic tests failed, check database connection and table structure');
    console.log('3. Test the payment UI after running any fixes');
    
  } catch (error) {
    console.error('💥 CRITICAL ERROR during testing:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the tests
testPaymentFixes().catch(console.error);