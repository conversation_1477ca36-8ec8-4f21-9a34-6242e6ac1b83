import { NextRequest } from "next/server";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";

// GET /api/properties/[id]/units - Get all units for a property
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "properties", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view properties");
    }

    const { id } = await params;
    const propertyId = parseInt(id);
    if (isNaN(propertyId)) {
      return ApiResponseBuilder.error(
        "Invalid property ID",
        "INVALID_ID",
        400
      );
    }

    // Check if property exists
    const property = await db.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      return ApiResponseBuilder.error(
        "Property not found",
        "NOT_FOUND",
        404
      );
    }

    // Get all units for the property
    const units = await db.unit.findMany({
      where: { property_id: propertyId },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
      },
      orderBy: [
        { floor_number: "asc" },
        { unit_number: "asc" },
      ],
    });

    // Calculate summary statistics
    const summary = {
      total: units.length,
      available: units.filter(u => u.status === "AVAILABLE").length,
      rented: units.filter(u => u.status === "RENTED").length,
      underMaintenance: units.filter(u => u.status === "UNDER_MAINTENANCE").length,
    };

    return ApiResponseBuilder.success({
      units,
      summary,
    });
  } catch (error) {
    console.error("Error fetching property units:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch property units",
      "FETCH_ERROR",
      500,
      error
    );
  }
}