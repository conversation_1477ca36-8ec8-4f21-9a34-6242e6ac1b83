@echo off
echo ========================================
echo Simple MySQL Start - Bypass All Issues
echo ========================================
echo.

echo 1. Stopping MySQL...
taskkill /f /im mysqld.exe 2>nul
timeout /t 3 /nobreak >nul

echo.
echo 2. Starting MySQL with maximum compatibility...
cd "C:\xampp\mysql\bin"

echo Starting MySQL with all problematic features disabled...
mysqld --console ^
  --skip-innodb ^
  --skip-grant-tables ^
  --skip-networking ^
  --skip-slave-start ^
  --default-storage-engine=MyISAM ^
  --port=3306 ^
  --basedir=C:/xampp/mysql ^
  --datadir=C:/xampp/mysql/data ^
  --log-error=mysql_simple.log

pause
