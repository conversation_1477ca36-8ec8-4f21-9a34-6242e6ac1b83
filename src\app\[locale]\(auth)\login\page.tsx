import { setRequestLocale } from 'next-intl/server';
import { useTranslations } from 'next-intl';
import { routing } from '@/i18n/routing';
import Link from 'next/link';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ProfessionalLoginForm } from '@/components/auth/professional-login-form';
import { LanguageSwitcher } from '@/components/auth/language-switcher';
import { CompanyLogo } from '@/components/auth/company-logo';
import { CompanyName } from '@/components/auth/company-name';
import { APP_CONFIG } from '@/config/app-config';

interface LoginPageProps {
  params: Promise<{ locale: string }>;
}

// Generate static params for all supported locales
export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

export default async function LoginPage({ params }: LoginPageProps) {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <div className={`min-h-screen bg-gradient-to-br from-background to-muted/20 ${locale === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Language Switcher */}
      <div className="absolute top-4 ltr:right-4 rtl:left-4 z-10">
        <LanguageSwitcher currentLocale={locale} />
      </div>

      {/* Main Content */}
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="w-full max-w-md space-y-8">
          {/* Company Logo and Branding */}
          <div className="text-center space-y-6">
            <CompanyLogo size="xl" className="justify-center" showCompanyName={false} />
            <CompanyName locale={locale} />
            <div className="space-y-2">
              <LoginPageContent locale={locale} />
            </div>
          </div>

          {/* Login Card */}
          <Card className="shadow-lg border-0 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60">
            <CardHeader className="space-y-1 text-center">
              <CardTitle className="text-2xl font-semibold tracking-tight">
                <LoginTitle />
              </CardTitle>
              <CardDescription>
                <LoginDescription />
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProfessionalLoginForm locale={locale} />
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center space-y-4">
            <div className="text-sm text-muted-foreground">
              <LoginFooter />
            </div>
            <div className="text-xs text-muted-foreground">
              {APP_CONFIG.copyright}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Server components for translations
function LoginPageContent({ locale }: { locale: string }) {
  const t = useTranslations('auth');

  return (
    <>
      <h1 className="text-4xl font-bold tracking-tight text-foreground">
        {t('welcome')}
      </h1>
      <p className="text-lg text-muted-foreground">
        {t('loginToContinue')}
      </p>
    </>
  );
}

function LoginTitle() {
  const t = useTranslations('auth');
  return t('loginToAccount');
}

function LoginDescription() {
  const t = useTranslations('auth');
  return t('enterCredentials');
}

function LoginFooter() {
  const t = useTranslations('auth');

  return null; // Remove register link
}
