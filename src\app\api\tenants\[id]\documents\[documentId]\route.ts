import { NextRequest } from "next/server";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { unlink } from "fs/promises";
import path from "path";

interface RouteParams {
  params: Promise<{
    id: string;
    documentId: string;
  }>;
}

// DELETE /api/tenants/:id/documents/:documentId - Delete a document
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "tenants", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update tenants");
    }

    const { id, documentId: documentIdParam } = await params;
    const tenantId = parseInt(id);
    const documentId = parseInt(documentIdParam);
    
    if (isNaN(tenantId) || isNaN(documentId)) {
      return ApiResponseBuilder.badRequest("Invalid ID");
    }

    // Check if document exists
    const document = await db.tenantDocument.findFirst({
      where: { 
        id: documentId,
        tenant_id: tenantId 
      },
    });

    if (!document) {
      return ApiResponseBuilder.notFound("Document");
    }

    // Delete the file from disk
    try {
      const filePath = path.join(process.cwd(), "public", document.file_path);
      await unlink(filePath);
    } catch (error) {
      console.error("Error deleting file from disk:", error);
      // Continue with database deletion even if file deletion fails
    }

    // Delete the document record from database
    await db.tenantDocument.delete({
      where: { id: documentId },
    });

    return ApiResponseBuilder.success({ message: "Document deleted successfully" });
  } catch (error) {
    console.error("Delete Tenant Document Error:", error);
    return ApiResponseBuilder.error(
      "Failed to delete document",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}