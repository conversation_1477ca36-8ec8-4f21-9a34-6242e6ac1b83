"use client";

import { useAuth } from "@/contexts/auth-context";
import { usePermissions } from "@/hooks/use-permissions";
import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";

export default function DebugAuthPage() {
  const authContext = useAuth();
  const permissions = usePermissions();
  const [apiTest, setApiTest] = useState<any>(null);
  const pathname = usePathname();
  const locale = pathname.split('/')[1];

  useEffect(() => {
    // Test direct API call
    fetch('/api/auth/me', { credentials: 'include' })
      .then(res => res.json())
      .then(data => setApiTest(data))
      .catch(err => setApiTest({ error: err.message }));
  }, []);

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Authentication Debug Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* AuthContext */}
        <div className="p-4 border rounded">
          <h2 className="text-lg font-semibold mb-2">AuthContext</h2>
          <div className="space-y-1">
            <p><strong>Loading:</strong> {authContext.isLoading ? 'Yes' : 'No'}</p>
            <p><strong>User:</strong> {authContext.user ? authContext.user.username : 'None'}</p>
            <p><strong>User ID:</strong> {authContext.user?.id || 'None'}</p>
            <p><strong>Permissions Count:</strong> {authContext.user?.permissions ? Object.keys(authContext.user.permissions).length : 0}</p>
            <p><strong>Properties Read:</strong> {authContext.user?.permissions?.properties?.read ? 'Yes' : 'No'}</p>
          </div>
        </div>

        {/* usePermissions Hook */}
        <div className="p-4 border rounded">
          <h2 className="text-lg font-semibold mb-2">usePermissions Hook</h2>
          <div className="space-y-1">
            <p><strong>Loading:</strong> {permissions.isLoading ? 'Yes' : 'No'}</p>
            <p><strong>User:</strong> {permissions.user ? permissions.user.username : 'None'}</p>
            <p><strong>User ID:</strong> {permissions.user?.id || 'None'}</p>
            <p><strong>Error:</strong> {permissions.error || 'None'}</p>
            <p><strong>Permissions Count:</strong> {Object.keys(permissions.permissions).length}</p>
            <p><strong>Properties Access:</strong> {permissions.hasModuleAccess('properties') ? 'Yes' : 'No'}</p>
          </div>
        </div>

        {/* Direct API Test */}
        <div className="p-4 border rounded">
          <h2 className="text-lg font-semibold mb-2">Direct API Test</h2>
          <div className="space-y-1">
            {apiTest ? (
              <>
                {apiTest.error ? (
                  <p className="text-red-600"><strong>Error:</strong> {apiTest.error}</p>
                ) : (
                  <>
                    <p><strong>User:</strong> {apiTest.user?.username || 'None'}</p>
                    <p><strong>User ID:</strong> {apiTest.user?.id || 'None'}</p>
                    <p><strong>Permissions Count:</strong> {apiTest.user?.permissions ? Object.keys(apiTest.user.permissions).length : 0}</p>
                    <p><strong>Properties Read:</strong> {apiTest.user?.permissions?.properties?.read ? 'Yes' : 'No'}</p>
                  </>
                )}
              </>
            ) : (
              <p>Loading...</p>
            )}
          </div>
        </div>

        {/* Cookie Info */}
        <div className="p-4 border rounded">
          <h2 className="text-lg font-semibold mb-2">Cookie Info</h2>
          <div className="space-y-1">
            <p><strong>All Cookies:</strong></p>
            <p className="text-sm font-mono bg-gray-100 p-2 rounded">{document.cookie || 'None'}</p>
            <p><strong>Has auth-token:</strong> {document.cookie.includes('auth-token') ? 'Yes' : 'No'}</p>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <button 
          onClick={() => window.location.href = `/${locale}/dashboard/properties`}
          className="px-4 py-2 bg-blue-500 text-white rounded mr-2"
        >
          Navigate to Properties
        </button>
        <button 
          onClick={() => authContext.refetch()}
          className="px-4 py-2 bg-green-500 text-white rounded mr-2"
        >
          Refetch Auth
        </button>
        <button 
          onClick={() => permissions.refetch()}
          className="px-4 py-2 bg-purple-500 text-white rounded"
        >
          Refetch Permissions
        </button>
      </div>
    </div>
  );
}