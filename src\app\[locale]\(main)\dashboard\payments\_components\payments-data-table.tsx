"use client";

import * as React from "react";
import { useState, useEffect, useCallback } from "react";
import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { useDataTableInstance } from "@/hooks/use-data-table-instance";
import { DataTable } from "@/components/data-table/data-table";
import { DataTablePagination } from "@/components/data-table/data-table-pagination";
import { DataTableViewOptions } from "@/components/data-table/data-table-view-options";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Loader2, RefreshCw } from "lucide-react";
import { ErrorState } from "@/components/error-state";
import type { PaymentWithRelations } from "@/types/payment";

import { getPaymentColumns } from "./payment-columns";

interface PaymentListResponse {
  success: boolean;
  data: PaymentWithRelations[];
  meta: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

export function PaymentsDataTable() {
  const locale = useLocale();
  const router = useRouter();
  const t = useTranslations('payments');
  const tTable = useTranslations('payments.table');
  const tCommon = useTranslations('common');
  const tStatus = useTranslations('payments.status');
  const tMethod = useTranslations('payments.method');

  const [data, setData] = useState<PaymentWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [retrying, setRetrying] = useState(false);
  const [filters, setFilters] = useState({
    page: 1,
    pageSize: 10,
    sortBy: "created_at",
    sortOrder: "desc" as "asc" | "desc",
    search: "",
    status: undefined as string | undefined,
    payment_method: undefined as string | undefined,
  });
  const [searchInput, setSearchInput] = useState("");
  const [totalPages, setTotalPages] = useState(0);
  const [total, setTotal] = useState(0);

  const columnTranslations = {
    id: tTable('id'),
    paymentNumber: tTable('paymentNumber'),
    tenant: tTable('tenant'),
    property: tTable('property'),
    unit: tTable('unit'),
    paymentDate: tTable('paymentDate'),
    amount: tTable('amount'),
    method: tTable('method'),
    status: tTable('status'),
    reference: tTable('reference'),
    actions: tTable('actions'),
    selectAll: tTable('selectAll'),
    selectRow: tTable('selectRow'),
    openMenu: tTable('openMenu'),
    viewDetails: tTable('viewDetails'),
    editPayment: tTable('editPayment'),
    deletePayment: tTable('deletePayment'),
    generateReceipt: tTable('generateReceipt'),
    statusPending: tStatus('pending'),
    statusCompleted: tStatus('completed'),
    statusFailed: tStatus('failed'),
    statusRefunded: tStatus('refunded'),
    methodCash: tMethod('cash'),
    methodBankTransfer: tMethod('bankTransfer'),
    methodCheck: tMethod('check'),
    methodCard: tMethod('card'),
    methodOnline: tMethod('online'),
  };

  const table = useDataTableInstance({
    data,
    columns: getPaymentColumns(columnTranslations, locale, router.refresh),
    enableRowSelection: true,
    defaultPageSize: filters.pageSize,
    getRowId: (row) => row.id.toString(),
  });

  // Update table pagination when filters change
  React.useEffect(() => {
    if (table && filters.page) {
      table.setPageIndex((filters.page || 1) - 1);
    }
  }, [filters.page, table]);

  React.useEffect(() => {
    if (table && filters.pageSize) {
      table.setPageSize(filters.pageSize);
    }
  }, [filters.pageSize, table]);

  // Fetch payments
  const fetchPayments = async (currentFilters: typeof filters, isRetry = false) => {
    try {
      if (isRetry) {
        setRetrying(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const params = new URLSearchParams();

      if (currentFilters.search) params.append("search", currentFilters.search);
      if (currentFilters.status) params.append("status", currentFilters.status);
      if (currentFilters.payment_method) params.append("payment_method", currentFilters.payment_method);
      if (currentFilters.page) params.append("page", currentFilters.page.toString());
      if (currentFilters.pageSize) params.append("pageSize", currentFilters.pageSize.toString());
      if (currentFilters.sortBy) params.append("sortBy", currentFilters.sortBy);
      if (currentFilters.sortOrder) params.append("sortOrder", currentFilters.sortOrder);

      const response = await fetch(`/api/payments?${params.toString()}`);

      if (!response.ok) {
        let errorMessage = tTable('errorLoading');

        if (response.status === 404) {
          errorMessage = tTable('notFound');
        } else if (response.status === 500) {
          errorMessage = tTable('serverError');
        }

        throw new Error(errorMessage);
      }

      const result: PaymentListResponse = await response.json();
      
      if (result.success) {
        setData(result.data);
        setTotal(result.meta.total);
        setTotalPages(result.meta.totalPages);
        setError(null);
      } else {
        throw new Error(tTable('errorLoading'));
      }
    } catch (error) {
      console.error("Error fetching payments:", error);
      const errorObj = error instanceof Error ? error : new Error(tTable('unknownError'));
      setError(errorObj);

      if (!isRetry) {
        toast.error(errorObj.message);
      }
    } finally {
      setLoading(false);
      setRetrying(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchPayments(filters);
  }, []);

  // Handle search
  const handleSearch = () => {
    const newFilters = { ...filters, search: searchInput, page: 1 };
    setFilters(newFilters);
    fetchPayments(newFilters);
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof typeof filters, value: any) => {
    const newFilters = { ...filters, [key]: value, page: 1 };
    setFilters(newFilters);
    fetchPayments(newFilters);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    fetchPayments(newFilters);
  };

  // Handle page size change
  const handlePageSizeChange = (pageSize: number) => {
    const newFilters = { ...filters, pageSize, page: 1 };
    setFilters(newFilters);
    fetchPayments(newFilters);
  };

  // Handle retry
  const handleRetry = () => {
    fetchPayments(filters, true);
  };

  // Show main error state if initial load failed
  if (error && !data.length && !loading) {
    return (
      <ErrorState
        error={error}
        onRetry={handleRetry}
        className="mx-auto max-w-md"
      />
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex flex-1 items-center space-x-2">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={tTable('searchPlaceholder')}
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                className="pl-8"
                disabled={loading || retrying}
              />
            </div>
            <Button
              onClick={handleSearch}
              variant="outline"
              size="sm"
              disabled={loading || retrying}
            >
              {(loading || retrying) ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
            </Button>
            <Button
              onClick={handleRetry}
              variant="outline"
              size="icon"
              disabled={loading || retrying}
            >
              <RefreshCw className={`h-4 w-4 ${retrying ? 'animate-spin' : ''}`} />
            </Button>
          </div>
          
          <DataTableViewOptions table={table} />
        </div>

        <div className="flex flex-wrap gap-2">
          <Select
            value={filters.payment_method || "all"}
            onValueChange={(value) => handleFilterChange("payment_method", value === "all" ? undefined : value)}
            disabled={loading || retrying}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder={tTable('filterByMethod')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{tTable('allMethods')}</SelectItem>
              <SelectItem value="CASH">{tMethod('cash')}</SelectItem>
              <SelectItem value="BANK_TRANSFER">{tMethod('bankTransfer')}</SelectItem>
              <SelectItem value="CHECK">{tMethod('check')}</SelectItem>
              <SelectItem value="CARD">{tMethod('card')}</SelectItem>
              <SelectItem value="ONLINE">{tMethod('online')}</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.status || "all"}
            onValueChange={(value) => handleFilterChange("status", value === "all" ? undefined : value)}
            disabled={loading || retrying}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder={tTable('filterByStatus')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{tTable('allStatuses')}</SelectItem>
              <SelectItem value="PENDING">{tStatus('pending')}</SelectItem>
              <SelectItem value="COMPLETED">{tStatus('completed')}</SelectItem>
              <SelectItem value="FAILED">{tStatus('failed')}</SelectItem>
              <SelectItem value="REFUNDED">{tStatus('refunded')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Data Table */}
      <div className="rounded-md border overflow-hidden">
        <div className="w-full overflow-x-auto [&_[data-slot=table-container]]:overflow-x-visible">
        {loading && !data.length ? (
          <div className="flex h-64 items-center justify-center">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="text-muted-foreground">{tTable('loading')}</span>
            </div>
          </div>
        ) : (
          <DataTable table={table} columns={getPaymentColumns(columnTranslations, locale, router.refresh)} />
        )}
        </div>
      </div>

      {/* Pagination */}
      <DataTablePagination table={table} />
    </div>
  );
}