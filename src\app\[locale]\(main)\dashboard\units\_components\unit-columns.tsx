"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import { UnitActions } from "./unit-actions";
import type { UnitWithRelations } from "@/types/unit";

interface ColumnTranslations {
  id: string;
  unitNumber: string;
  unitName: string;
  property: string;
  rooms: string;
  majalis: string;
  bathrooms: string;
  area: string;
  rentAmount: string;
  status: string;
  floorNumber: string;
  amenities: string;
  createdAt: string;
  actions: string;
  selectAll: string;
  selectRow: string;
  openMenu: string;
  viewDetails: string;
  editUnit: string;
  deleteUnit: string;
  viewUnit: string;
  deleteConfirmation: string;
  deleteWarning: string;
  deleteWarningRented: string;
  cannotDeleteRented: string;
  unitNotFound: string;
  deleteError: string;
  deleting: string;
  confirmDelete: string;
  statusAvailable: string;
  statusRented: string;
  statusUnderMaintenance: string;
}

interface CommonTranslations {
  cancel: string;
  delete: string;
}

export function getUnitColumns(
  locale: string,
  t: ColumnTranslations,
  tCommon: CommonTranslations,
  onRefresh: () => void
): ColumnDef<UnitWithRelations>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label={t.selectAll}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={t.selectRow}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "actions",
      header: () => <div className="text-center">{t.actions}</div>,
      cell: ({ row }) => (
        <div className="text-center">
          <UnitActions 
            unit={{
              ...row.original,
              rent_amount: row.original.rent_amount.toString(),
              area: row.original.area?.toString() || null
            } as any} 
            locale={locale}
            onRefresh={onRefresh}
            translations={{
              openMenu: t.openMenu,
              viewUnit: t.viewUnit,
              editUnit: t.editUnit,
              deleteUnit: t.deleteUnit,
            }}
          />
        </div>
      ),
    },
    {
      accessorKey: "id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.id} className="justify-center" />
      ),
      cell: ({ row }) => (
        <div className="w-[80px] text-center">{row.getValue("id")}</div>
      ),
      enableSorting: false,
    },
    {
      accessorKey: "unit_number",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.unitNumber} />
      ),
      cell: ({ row }) => {
        const unitNumber = row.getValue("unit_number") as string;
        return <span className="font-medium">{unitNumber}</span>;
      },
    },
    {
      id: "unit_name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.unitName} />
      ),
      cell: ({ row }) => {
        const unit = row.original;
        const name = locale === "ar" ? unit.unit_name_ar : unit.unit_name_en;
        return (
          <div className={locale === "ar" ? "text-right" : "text-left"}>
            {name || "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "property",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.property} />
      ),
      cell: ({ row }) => {
        const property = row.original.property;
        const propertyName = locale === "ar" ? property.name_ar : property.name_en;
        const propertyType = locale === "ar" 
          ? property.property_type.name_ar 
          : property.property_type.name_en;
        
        return (
          <div>
            <span className="font-medium">{propertyName}</span>
            <span className="block text-xs text-muted-foreground">
              {propertyType}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "rooms_count",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.rooms} />
      ),
      cell: ({ row }) => (
        <div className="text-center">{row.getValue("rooms_count") || "-"}</div>
      ),
    },
    {
      accessorKey: "majalis_count",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.majalis} />
      ),
      cell: ({ row }) => (
        <div className="text-center">{row.getValue("majalis_count") || "-"}</div>
      ),
    },
    {
      accessorKey: "bathrooms_count",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.bathrooms} />
      ),
      cell: ({ row }) => (
        <div className="text-center">{row.getValue("bathrooms_count") || "-"}</div>
      ),
    },
    {
      accessorKey: "floor_number",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.floorNumber} />
      ),
      cell: ({ row }) => {
        const floor = row.getValue("floor_number") as number | null;
        return (
          <div className="text-center">
            {floor !== null ? floor : "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "area",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.area} />
      ),
      cell: ({ row }) => {
        const area = row.getValue("area") as string | null;
        return (
          <div className="text-center">
            {area ? `${parseFloat(area).toFixed(2)} m²` : "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "rent_amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.rentAmount} />
      ),
      cell: ({ row }) => {
        const amount = row.getValue("rent_amount") as string;
        return (
          <div className="text-center font-medium">
            {parseFloat(amount).toFixed(3)} OMR
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.status} />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        const statusText = 
          status === "AVAILABLE" ? t.statusAvailable :
          status === "RENTED" ? t.statusRented :
          status === "UNDER_MAINTENANCE" ? t.statusUnderMaintenance :
          status;

        const variant = 
          status === "AVAILABLE" ? "success" :
          status === "RENTED" ? "default" :
          status === "UNDER_MAINTENANCE" ? "warning" :
          "default";

        return (
          <div className="flex justify-center">
            <Badge variant={variant as any}>
              {statusText}
            </Badge>
          </div>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "amenities",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.amenities} />
      ),
      cell: ({ row }) => {
        const amenities = row.original.amenities || [];
        
        if (amenities.length === 0) {
          return <div className="text-muted-foreground">-</div>;
        }

        const displayCount = 2;
        const visibleAmenities = amenities.slice(0, displayCount);
        const remainingCount = amenities.length - displayCount;

        return (
          <div className="flex flex-wrap gap-1 max-w-[200px]">
            {visibleAmenities.map((amenity: any) => (
              <Badge key={amenity.id} variant="outline" className="text-xs">
                {locale === "ar" ? amenity.name_ar : amenity.name_en}
              </Badge>
            ))}
            {remainingCount > 0 && (
              <Badge variant="outline" className="text-xs">
                +{remainingCount}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "created_at",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.createdAt} />
      ),
      cell: ({ row }) => {
        const date = row.getValue("created_at") as string;
        return <span>{format(new Date(date), "dd/MM/yyyy")}</span>;
      },
    },
  ];
}