import { notFound } from "next/navigation";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { getTranslations } from 'next-intl/server';

import { Button } from "@/components/ui/button";
import { ErrorBoundary } from "@/components/error-boundary";
import { TenantForm } from "../../_components/tenant-form";
import type { TenantWithRelations } from "@/types/tenant";

interface EditTenantPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

async function getTenant(id: string): Promise<TenantWithRelations | null> {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002'}/api/tenants/${id}`, {
      cache: 'no-store',
    });

    if (!response.ok) {
      return null;
    }

    return response.json();
  } catch (error) {
    console.error("Error fetching tenant:", error);
    return null;
  }
}

export default async function EditTenantPage({ params }: EditTenantPageProps) {
  const { id, locale } = await params;
  const tenant = await getTenant(id);
  const t = await getTranslations({ locale, namespace: 'tenants' });



  if (!tenant) {
    notFound();
  }

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild>
          <Link href={`/${locale}/dashboard/tenants`}>
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">{t('backToTenants')}</span>
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t('editTenant')}: {tenant.first_name} {tenant.last_name}
          </h1>
          <p className="text-muted-foreground">
            {t('editDescription')}
          </p>
        </div>
      </div>

      <ErrorBoundary>
        <TenantForm tenant={tenant} isEdit />
      </ErrorBoundary>
    </div>
  );
}
