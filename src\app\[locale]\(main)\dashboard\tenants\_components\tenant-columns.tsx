"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { MoreHorizontal, Edit, Trash, Eye, FileText, Users } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import type { TenantWithRelations } from "@/types/tenant";

export function getTenantColumns(
  locale: string,
  t: (key: string) => string,
  tCommon: (key: string) => string,
  onRefresh: () => void
): ColumnDef<TenantWithRelations>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label={t("selectAll")}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={t("selectRow")}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "first_name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("name")} />
      ),
      cell: ({ row }) => {
        const tenant = row.original;
        const fullName = `${tenant.first_name} ${tenant.last_name}`;
        return <span className="font-medium">{fullName}</span>;
      },
    },
    {
      accessorKey: "email",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("email")} />
      ),
      cell: ({ row }) => {
        const email = row.getValue("email") as string;
        return <span className="text-sm">{email}</span>;
      },
    },
    {
      accessorKey: "phone",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("phone")} />
      ),
      cell: ({ row }) => {
        const phone = row.getValue("phone") as string | null;
        return phone ? (
          <span>{phone}</span>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      accessorKey: "national_id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("nationalId")} />
      ),
      cell: ({ row }) => {
        const nationalId = row.getValue("national_id") as string | null;
        const expiryDate = row.original.national_id_expiry;
        
        if (!nationalId) {
          return <span className="text-muted-foreground">-</span>;
        }
        
        const isExpired = expiryDate && new Date(expiryDate) < new Date();
        
        return (
          <div className="space-y-1">
            <span className={isExpired ? "text-destructive" : ""}>{nationalId}</span>
            {expiryDate && (
              <span className={`block text-xs ${isExpired ? "text-destructive" : "text-muted-foreground"}`}>
                {t("expires")}: {format(new Date(expiryDate), "dd/MM/yyyy")}
              </span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "nationality",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("nationality")} />
      ),
      cell: ({ row }) => {
        const nationality = row.getValue("nationality") as string | null;
        return nationality ? (
          <span>{nationality}</span>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      id: "documents",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("documents")} />
      ),
      cell: ({ row }) => {
        const tenant = row.original;
        const docCount = tenant.documents?.length || 0;
        
        return (
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{docCount}</span>
          </div>
        );
      },
    },
    {
      id: "emergencyContact",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("emergencyContact")} />
      ),
      cell: ({ row }) => {
        const tenant = row.original;
        const primaryContact = tenant.emergency_contacts?.find(c => c.is_primary);
        
        if (!primaryContact) {
          return <span className="text-muted-foreground text-sm">-</span>;
        }
        
        return (
          <div className="space-y-1">
            <span className="text-sm">{primaryContact.name}</span>
            <span className="text-xs text-muted-foreground">{primaryContact.phone}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "created_at",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("createdAt")} />
      ),
      cell: ({ row }) => {
        const date = row.getValue("created_at") as string;
        return <span>{format(new Date(date), "dd/MM/yyyy")}</span>;
      },
    },
    {
      id: "actions",
      header: () => <div className="text-center">{t("actions")}</div>,
      cell: ({ row }) => {
        const tenant = row.original;
        const [showDeleteDialog, setShowDeleteDialog] = useState(false);
        const [isDeleting, setIsDeleting] = useState(false);

        const handleDelete = async () => {
          try {
            setIsDeleting(true);
            const response = await fetch(`/api/tenants/${tenant.id}`, {
              method: "DELETE",
            });

            if (!response.ok) {
              const result = await response.json();
              throw new Error(result.error?.message || t("deleteError"));
            }

            toast.success(t("deleteSuccess"));
            setShowDeleteDialog(false);
            onRefresh();
          } catch (error) {
            console.error("Delete error:", error);
            toast.error(
              error instanceof Error ? error.message : t("deleteError")
            );
          } finally {
            setIsDeleting(false);
          }
        };

        return (
          <div className="text-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">{t("openMenu")}</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>{t("actions")}</DropdownMenuLabel>
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/tenants/${tenant.id}`}>
                    <Eye className="mr-2 h-4 w-4" />
                    {t("viewDetails")}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/tenants/${tenant.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    {t("editTenant")}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-destructive"
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {t("deleteTenant")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{t("deleteTenant")}</AlertDialogTitle>
                  <AlertDialogDescription>
                    {t("deleteConfirmation")} <strong>{tenant.first_name} {tenant.last_name}</strong>?
                    <br />
                    <br />
                    {t("deleteWarning")}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>
                    {tCommon("cancel")}
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {isDeleting ? t("deleting") : t("confirmDelete")}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        );
      },
    },
  ];
}