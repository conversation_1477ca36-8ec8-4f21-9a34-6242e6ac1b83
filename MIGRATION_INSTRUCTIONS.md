# Payment Tracking Migration Instructions

## Overview
This migration adds comprehensive payment tracking functionality to the Owners Association system, including:
- Member linkage to transactions
- Partial payment support
- Payment installment tracking
- Enhanced financial reporting

## Prerequisites
- MySQL database access with root privileges
- Node.js installed
- Application stopped during migration

## Migration Steps

### Option 1: Automated Migration (Recommended)
Run the migration script that handles everything automatically:

```bash
node apply-payment-migration.js
```

This script will:
1. Create a database backup
2. Apply the migration
3. Regenerate Prisma client
4. Restore the member relation in the API

### Option 2: Manual Migration
If you prefer to run the steps manually:

1. **Backup your database first:**
   ```bash
   mysqldump -u root -p property_management > backup_before_migration.sql
   ```

2. **Apply the migration:**
   ```bash
   mysql -u root -p property_management < prisma/migrations/full_payment_tracking_migration.sql
   ```

3. **Regenerate Prisma client:**
   ```bash
   npx prisma generate
   ```

4. **Uncomment the member relation in the API:**
   - Open `src/app/api/owners-associations/[id]/transactions/route.ts`
   - Find the commented `member` relation (around line 106)
   - Uncomment the entire member block

## Verification
After migration, verify that:
1. The application starts without errors
2. Transactions page loads successfully
3. Member names appear in the transactions table
4. You can create new transactions linked to members

## Rollback Instructions
If you need to rollback the migration:

```bash
mysql -u root -p property_management < backup_before_migration.sql
```

Then revert the API changes by commenting out the member relation again.

## New Features Available
After successful migration:
- **Member Payment Tracking**: See which member made each transaction
- **Partial Payments**: Record partial subscription payments
- **Payment History**: View complete payment history for each member
- **Enhanced Reporting**: Better financial insights with member-level data

## Troubleshooting
- **Error: Column 'member_id' doesn't exist**: The migration hasn't been applied yet
- **Error: Cannot read property 'full_name'**: The member relation needs to be uncommented
- **Prisma errors**: Run `npx prisma generate` again

## Support
If you encounter issues:
1. Check the error logs
2. Verify the migration was applied: `SHOW COLUMNS FROM association_transactions;`
3. Ensure Prisma client is regenerated
4. Restore from backup if needed