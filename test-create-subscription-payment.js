const { PrismaClient } = require('./src/generated/prisma');
const prisma = new PrismaClient();

async function createTestData() {
  try {
    // First, check if we have an association with a subscription
    const association = await prisma.ownersAssociation.findFirst({
      include: {
        subscriptions: true,
        members: true
      }
    });

    if (!association) {
      console.log('No associations found');
      return;
    }

    console.log('Found association:', association.name_en);
    console.log('Subscriptions:', association.subscriptions.length);
    console.log('Members:', association.members.length);

    if (association.subscriptions.length === 0 || association.members.length === 0) {
      console.log('Association needs subscriptions and members');
      return;
    }

    // Try to create a subscription payment
    const subscription = association.subscriptions[0];
    const member = association.members[0];

    console.log('\nTrying to create subscription payment...');
    console.log('Subscription:', subscription.name_en);
    console.log('Member:', member.full_name);

    try {
      const payment = await prisma.subscriptionPayment.create({
        data: {
          subscription_id: subscription.id,
          member_id: member.id,
          amount: subscription.amount,
          payment_date: null,
          due_date: new Date(),
          status: 'UNPAID',
          payment_method: 'CASH',
          created_by: 1
        }
      });
      
      console.log('\nPayment created successfully:', payment);
    } catch (createError) {
      console.error('\nError creating payment:', createError.message);
      
      // Try to describe the table structure
      const result = await prisma.$queryRaw`DESCRIBE subscription_payments`;
      console.log('\nTable structure:');
      result.forEach(col => {
        console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(required)'}`);
      });
    }

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createTestData();