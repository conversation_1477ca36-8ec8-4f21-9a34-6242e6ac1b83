"use client";

import Link from "next/link";
import { <PERSON>ge<PERSON><PERSON><PERSON>, Bell, CreditCard, LogOut } from "lucide-react";
import { useTranslations, useLocale } from "next-intl";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
} from "@/components/ui/dropdown-menu";
import { getInitials } from "@/lib/utils";
import { useAuth } from "@/contexts/auth-context";

export function AuthUserSwitcher() {
  const { user, logout } = useAuth();
  const t = useTranslations("userProfile");
  const locale = useLocale();

  if (!user) {
    return null;
  }

  const displayName = `${user.first_name} ${user.last_name}`;

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="relative flex size-9 shrink-0 overflow-hidden rounded-lg cursor-pointer bg-primary text-primary-foreground border-2 border-primary hover:opacity-80 transition-opacity">
          <span className="flex size-full items-center justify-center rounded-lg bg-primary text-primary-foreground font-bold text-sm">
            {getInitials(displayName)}
          </span>
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="min-w-56 space-y-1 rounded-lg" side="bottom" align="end" sideOffset={4}>
        <div className="flex w-full items-center justify-between gap-2 px-1 py-1.5">
          <div className="relative flex size-9 shrink-0 overflow-hidden rounded-lg bg-muted">
            <span className="flex size-full items-center justify-center rounded-lg font-semibold text-sm">
              {getInitials(displayName)}
            </span>
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">{displayName}</span>
            <span className="truncate text-xs text-muted-foreground">{user.email}</span>
          </div>
        </div>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href={`/${locale}/dashboard/account`}>
              <BadgeCheck />
              {t("account")}
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <CreditCard />
            {t("billing")}
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Bell />
            {t("notifications")}
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout}>
          <LogOut />
          {t("logout")}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
