import { z } from "zod";
import type { Unit, UnitStatus, Property, Amenity } from "@/generated/prisma";

// Unit Schemas
export const unitSchema = z.object({
  property_id: z.number().int().positive("Property is required"),
  unit_number: z.string().min(1, "Unit number is required").max(50),
  unit_name_en: z.string().optional().nullable(),
  unit_name_ar: z.string().optional().nullable(),
  floor_number: z.number().int().min(0).optional().nullable(),
  rooms_count: z.number().int().min(0).optional().nullable(),
  majalis_count: z.number().int().min(0).optional().nullable(),
  bathrooms_count: z.number().int().min(0).optional().nullable(),
  area: z.string().regex(/^\d+(\.\d{1,2})?$/, "Invalid area format").optional().nullable().or(z.number().optional().nullable()),
  rent_amount: z.string().regex(/^\d+(\.\d{1,3})?$/, "Invalid rent format").or(z.number()),
  status: z.enum(["AVAILABLE", "RENTED", "UNDER_MAINTENANCE"]),
  description_en: z.string().optional().nullable(),
  description_ar: z.string().optional().nullable(),
  amenity_ids: z.array(z.number()).optional(),
});

export const unitUpdateSchema = unitSchema.partial().omit({ property_id: true }).extend({
  amenity_ids: z.array(z.number()).optional(),
});

export type UnitInput = z.infer<typeof unitSchema>;
export type UnitUpdateInput = z.infer<typeof unitUpdateSchema>;

// Filter types
export interface UnitFilters {
  search?: string;
  property_id?: number;
  status?: UnitStatus;
  min_rent?: number;
  max_rent?: number;
  min_rooms?: number;
  max_rooms?: number;
  floor_number?: number;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// Extended types with relations
export interface UnitWithRelations extends Unit {
  property: Property & {
    property_type: {
      id: number;
      name_en: string;
      name_ar: string;
    };
  };
  amenities?: Amenity[];
  creator?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
  updater?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
  _count?: {
    contracts: number;
    invoices: number;
    payments: number;
    maintenance_requests: number;
  };
}