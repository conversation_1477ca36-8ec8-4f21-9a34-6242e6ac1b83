import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { db } from "@/lib/db";
import { UnitForm } from "../../_components/unit-form";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

interface EditUnitPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

async function getUnit(id: string) {
  try {
    const unit = await db.unit.findUnique({
      where: { id: parseInt(id, 10) },
      include: {
        property: true,
        amenities: {
          include: {
            amenity: true,
          },
        },
      },
    });
    
    if (unit) {
      // Convert Decimal fields to strings for client component
      return {
        ...unit,
        area: unit.area?.toString() || null,
        rent_amount: unit.rent_amount.toString(),
        // Transform property Decimal fields
        property: {
          ...unit.property,
          base_rent: unit.property.base_rent.toString(),
          total_area: unit.property.total_area?.toString() || null,
        },
        // Ensure amenities are serializable
        amenities: unit.amenities.map(ua => ({
          ...ua,
          amenity: {
            ...ua.amenity,
          }
        })),
      };
    }
    
    return unit;
  } catch (error) {
    console.error("Error fetching unit:", error);
    return null;
  }
}

async function getProperties() {
  try {
    const properties = await db.property.findMany({
      orderBy: { name_en: "asc" },
    });
    
    return properties.map(property => ({
      ...property,
      base_rent: property.base_rent.toString(),
      total_area: property.total_area?.toString() || null,
    }));
  } catch (error) {
    console.error("Error fetching properties:", error);
    return [];
  }
}

async function getAmenities() {
  try {
    const amenities = await db.amenity.findMany({
      orderBy: { name_en: "asc" },
    });
    return amenities;
  } catch (error) {
    console.error("Error fetching amenities:", error);
    return [];
  }
}

export default async function EditUnitPage({ params }: EditUnitPageProps) {
  const { id, locale } = await params;
  const t = await getTranslations('units');

  const [unit, properties, amenities] = await Promise.all([
    getUnit(id),
    getProperties(),
    getAmenities(),
  ]);

  if (!unit) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href={`/${locale}/dashboard/units`}>
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('editUnit')}</h1>
          <p className="text-muted-foreground">{t('editDescription')}</p>
        </div>
      </div>

      <UnitForm 
        unit={unit as any} 
        properties={properties as any}
        amenities={amenities}
        isEdit 
      />
    </div>
  );
}