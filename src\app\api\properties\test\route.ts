import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";

export async function GET(request: NextRequest) {
  try {
    // Simple test - just count properties
    const count = await db.property.count();
    
    // Try to fetch first property
    const firstProperty = await db.property.findFirst({
      include: {
        property_type: true,
      }
    });
    
    return ApiResponseBuilder.success({
      count,
      firstProperty,
      message: "Test successful"
    });
  } catch (error: any) {
    console.error("Test API Error:", error);
    return ApiResponseBuilder.error(
      error.message || "Database error",
      "DATABASE_ERROR",
      500,
      {
        errorDetails: error.toString(),
        stack: error.stack
      }
    );
  }
}