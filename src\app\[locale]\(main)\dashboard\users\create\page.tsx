import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { getTranslations } from "next-intl/server";

import { Button } from "@/components/ui/button";
import { UserForm } from "../_components/user-form";

export default async function CreateUserPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations("users");
  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${locale}/dashboard/users`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("backToUsers")}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("createUser")}</h1>
          <p className="text-muted-foreground">{t("createDescription")}</p>
        </div>
      </div>

      <UserForm mode="create" />
    </div>
  );
}
