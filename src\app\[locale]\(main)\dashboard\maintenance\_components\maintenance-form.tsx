"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations, useLocale } from "next-intl";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import {
  MaintenanceRequestInput,
  maintenanceRequestSchema,
  MaintenanceRequestWithRelations,
} from "@/types/maintenance";

interface MaintenanceFormProps {
  initialData?: MaintenanceRequestWithRelations;
  mode: "create" | "edit";
}

interface Property {
  id: number;
  name_en: string;
  name_ar: string;
  units: Array<{
    id: number;
    unit_number: string;
    status: string;
  }>;
}

interface Tenant {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
}

export function MaintenanceForm({ initialData, mode }: MaintenanceFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [properties, setProperties] = useState<Property[]>([]);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations();

  const form = useForm<MaintenanceRequestInput>({
    resolver: zodResolver(maintenanceRequestSchema),
    defaultValues: {
      property_id: initialData?.property_id || 0,
      unit_id: initialData?.unit_id,
      tenant_id: initialData?.tenant_id,
      title: initialData?.title || "",
      description: initialData?.description || "",
      priority: initialData?.priority || "MEDIUM",
      category: initialData?.category || "OTHER",
      scheduled_date: initialData?.scheduled_date ? new Date(initialData.scheduled_date).toISOString().split('T')[0] : "",
      estimated_cost: initialData?.estimated_cost ? Number(initialData.estimated_cost) : undefined,
      contractor_name: initialData?.contractor_name || "",
      contractor_phone: initialData?.contractor_phone || "",
      internal_notes: initialData?.internal_notes || "",
    },
  });

  // Fetch properties and tenants on mount
  useEffect(() => {
    fetchProperties();
    fetchTenants();
  }, []);

  const fetchProperties = async () => {
    try {
      const response = await fetch("/api/properties");
      const result = await response.json();
      
      if (result.success) {
        setProperties(result.data);
      }
    } catch (error) {
      console.error("Error fetching properties:", error);
    }
  };

  const fetchTenants = async () => {
    try {
      const response = await fetch("/api/tenants");
      const result = await response.json();
      
      if (result.success) {
        setTenants(result.data);
      }
    } catch (error) {
      console.error("Error fetching tenants:", error);
    }
  };

  const handlePropertyChange = (propertyId: string) => {
    const property = properties.find(p => p.id === parseInt(propertyId));
    setSelectedProperty(property || null);
    form.setValue("unit_id", undefined); // Reset unit when property changes
  };

  const onSubmit = async (data: MaintenanceRequestInput) => {
    try {
      setIsLoading(true);

      const url = mode === "create" 
        ? "/api/maintenance"
        : `/api/maintenance/${initialData?.id}`;
      
      const method = mode === "create" ? "POST" : "PUT";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(mode === "create" 
          ? t("maintenance.messages.createSuccess")
          : t("maintenance.messages.updateSuccess")
        );
        
        router.push(`/${locale}/dashboard/maintenance`);
        router.refresh();
      } else {
        toast.error(result.message || t("common.unexpectedError"));
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error(t("common.unexpectedError"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {mode === "create" 
            ? t("maintenance.createTitle")
            : t("maintenance.editTitle")
          }
        </h1>
        <p className="text-muted-foreground">
          {mode === "create"
            ? t("maintenance.createDescription")
            : t("maintenance.editDescription")
          }
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t("maintenance.basicInformation")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("maintenance.title")} *</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("maintenance.description")} *</FormLabel>
                    <FormControl>
                      <Textarea {...field} rows={4} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("maintenance.priority")} *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="LOW">{t("maintenance.priorities.low")}</SelectItem>
                          <SelectItem value="MEDIUM">{t("maintenance.priorities.medium")}</SelectItem>
                          <SelectItem value="HIGH">{t("maintenance.priorities.high")}</SelectItem>
                          <SelectItem value="EMERGENCY">{t("maintenance.priorities.emergency")}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("maintenance.category")} *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ELECTRICAL">{t("maintenance.categories.electrical")}</SelectItem>
                          <SelectItem value="PLUMBING">{t("maintenance.categories.plumbing")}</SelectItem>
                          <SelectItem value="HVAC">{t("maintenance.categories.hvac")}</SelectItem>
                          <SelectItem value="STRUCTURAL">{t("maintenance.categories.structural")}</SelectItem>
                          <SelectItem value="APPLIANCES">{t("maintenance.categories.appliances")}</SelectItem>
                          <SelectItem value="PAINTING">{t("maintenance.categories.painting")}</SelectItem>
                          <SelectItem value="CLEANING">{t("maintenance.categories.cleaning")}</SelectItem>
                          <SelectItem value="LANDSCAPING">{t("maintenance.categories.landscaping")}</SelectItem>
                          <SelectItem value="SECURITY">{t("maintenance.categories.security")}</SelectItem>
                          <SelectItem value="OTHER">{t("maintenance.categories.other")}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Location Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t("maintenance.locationInformation")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="property_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("maintenance.property")} *</FormLabel>
                      <Select 
                        onValueChange={(value) => {
                          field.onChange(parseInt(value));
                          handlePropertyChange(value);
                        }} 
                        value={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t("maintenance.selectProperty")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {properties.map((property) => (
                            <SelectItem key={property.id} value={property.id.toString()}>
                              {property.name_en}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="unit_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("maintenance.unit")}</FormLabel>
                      <Select 
                        onValueChange={(value) => field.onChange(value === "none" ? null : value ? parseInt(value) : undefined)} 
                        value={field.value?.toString() || "none"}
                        disabled={!selectedProperty}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t("maintenance.selectUnit")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">{t("maintenance.form.noUnit")}</SelectItem>
                          {selectedProperty?.units.map((unit) => (
                            <SelectItem key={unit.id} value={unit.id.toString()}>
                              Unit {unit.unit_number}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="tenant_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("maintenance.tenant")}</FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(value === "none" ? null : value ? parseInt(value) : undefined)} 
                      value={field.value?.toString() || "none"}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("maintenance.selectTenant")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">{t("maintenance.form.noTenant")}</SelectItem>
                        {tenants.map((tenant) => (
                          <SelectItem key={tenant.id} value={tenant.id.toString()}>
                            {tenant.first_name} {tenant.last_name} - {tenant.email}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Scheduling and Cost */}
          <Card>
            <CardHeader>
              <CardTitle>{t("maintenance.schedulingAndCost")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="scheduled_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("maintenance.scheduledDate")}</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="estimated_cost"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("maintenance.estimatedCost")}</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.001"
                          min="0"
                          {...field}
                          value={field.value || ""}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormDescription>
                        {t("maintenance.estimatedCostDescription")}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="contractor_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("maintenance.contractorName")}</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contractor_phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("maintenance.contractorPhone")}</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="internal_notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("maintenance.internalNotes")}</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        value={field.value || ""}
                        rows={3}
                        placeholder={t("maintenance.internalNotesPlaceholder")}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Submit Buttons */}
          <div className="flex items-center justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {mode === "create" ? t("common.create") : t("common.save")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}