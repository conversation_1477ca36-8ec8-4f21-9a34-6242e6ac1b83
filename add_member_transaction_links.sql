-- Add member and payment links to association transactions
ALTER TABLE `association_transactions`
ADD COLUMN `member_id` INT NULL AFTER `attachment_name`,
ADD COLUMN `subscription_payment_id` INT NULL AFTER `member_id`,
ADD COLUMN `payment_installment_id` INT NULL AFTER `subscription_payment_id`;

-- Add foreign key constraints
ALTER TABLE `association_transactions`
ADD CONSTRAINT `fk_transaction_member` 
FOREIGN KEY (`member_id`) REFERENCES `association_members`(`id`) ON DELETE SET NULL,
ADD CONSTRAINT `fk_transaction_subscription_payment` 
FOREIGN KEY (`subscription_payment_id`) REFERENCES `subscription_payments`(`id`) ON DELETE SET NULL,
ADD CONSTRAINT `fk_transaction_payment_installment` 
FOREIGN KEY (`payment_installment_id`) REFERENCES `subscription_payment_installments`(`id`) ON DELETE SET NULL;

-- Add indexes for better query performance
CREATE INDEX `idx_transaction_member` ON `association_transactions`(`member_id`);
CREATE INDEX `idx_transaction_subscription_payment` ON `association_transactions`(`subscription_payment_id`);
CREATE INDEX `idx_transaction_payment_installment` ON `association_transactions`(`payment_installment_id`);

-- Update existing transactions to link with members based on payment records
UPDATE association_transactions t
INNER JOIN subscription_payments sp ON t.id = sp.transaction_id
SET t.member_id = sp.member_id,
    t.subscription_payment_id = sp.id
WHERE t.type = 'INCOME' AND t.category = 'subscription';

-- Update existing transactions from installments
UPDATE association_transactions t
INNER JOIN subscription_payment_installments spi ON t.id = spi.transaction_id
INNER JOIN subscription_payments sp ON spi.subscription_payment_id = sp.id
SET t.member_id = sp.member_id,
    t.payment_installment_id = spi.id
WHERE t.type = 'INCOME' AND t.category = 'subscription';