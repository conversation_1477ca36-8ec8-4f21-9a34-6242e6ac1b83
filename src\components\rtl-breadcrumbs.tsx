'use client';

import { useTranslations } from 'next-intl';
import { ArrowRight, ArrowLeft } from 'lucide-react';
import { useRTL } from '@/hooks/use-rtl';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface RTLBreadcrumbsProps {
  items: BreadcrumbItem[];
}

/**
 * RTL-aware breadcrumbs component following next-intl best practices
 * Demonstrates proper icon mirroring and layout direction handling
 */
export function RTLBreadcrumbs({ items }: RTLBreadcrumbsProps) {
  const t = useTranslations('breadcrumbs');
  const { isRTL, direction } = useRTL();

  return (
    <nav aria-label={t('navigation')} className="flex items-center space-x-2 rtl:space-x-reverse">
      {items.map((item, index) => (
        <div key={index} className="flex items-center">
          {item.href ? (
            <a
              href={item.href}
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              {item.label}
            </a>
          ) : (
            <span className="text-sm font-medium text-foreground">
              {item.label}
            </span>
          )}
          
          {index < items.length - 1 && (
            <div 
              className="mx-2 text-muted-foreground"
              style={{ marginInlineStart: 8, marginInlineEnd: 8 }}
            >
              {/* Use appropriate arrow based on text direction */}
              {direction === 'ltr' ? (
                <ArrowRight className="h-4 w-4" />
              ) : (
                <ArrowLeft className="h-4 w-4" />
              )}
            </div>
          )}
        </div>
      ))}
    </nav>
  );
}
