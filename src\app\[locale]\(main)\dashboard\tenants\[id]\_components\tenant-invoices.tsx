"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import { format } from "date-fns";
import { FileText, Eye, Download } from "lucide-react";
import { toast } from "sonner";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/localization";

interface TenantInvoicesProps {
  tenantId: string;
}

export function TenantInvoices({ tenantId }: TenantInvoicesProps) {
  const locale = useLocale();
  const t = useTranslations("tenants");
  const tInvoices = useTranslations("invoices");
  const tCommon = useTranslations("common");
  
  const [invoices, setInvoices] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchInvoices();
  }, [tenantId]);

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/invoices?tenantId=${tenantId}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch invoices");
      }

      const result = await response.json();
      if (result.success) {
        setInvoices(result.data);
      }
    } catch (error) {
      console.error("Error fetching invoices:", error);
      toast.error(t("invoices.fetchError"));
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    // Convert uppercase snake_case to lowercase camelCase for translation keys
    const statusKey = status.toLowerCase().replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    
    const variant = 
      status === "PAID" ? "default" :
      status === "PARTIALLY_PAID" ? "secondary" :
      status === "OVERDUE" ? "destructive" :
      status === "CANCELLED" ? "secondary" :
      "outline";
    
    return (
      <Badge variant={variant}>
        {tInvoices(`status.${statusKey}`)}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("invoices.title")}</CardTitle>
          <CardDescription>{t("invoices.description")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("invoices.title")}</CardTitle>
        <CardDescription>{t("invoices.description")}</CardDescription>
      </CardHeader>
      <CardContent>
        {invoices.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">{t("invoices.noInvoices")}</p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{tInvoices("table.invoiceNumber")}</TableHead>
                  <TableHead>{tInvoices("table.property")}</TableHead>
                  <TableHead>{tInvoices("table.issueDate")}</TableHead>
                  <TableHead>{tInvoices("table.dueDate")}</TableHead>
                  <TableHead>{tInvoices("table.amount")}</TableHead>
                  <TableHead>{tInvoices("table.paidAmount")}</TableHead>
                  <TableHead>{tInvoices("table.status")}</TableHead>
                  <TableHead>{tCommon("actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-medium">
                      {invoice.invoice_number}
                    </TableCell>
                    <TableCell>
                      {invoice.contract?.property?.name || invoice.contract?.unit?.name}
                    </TableCell>
                    <TableCell>
                      {invoice.invoice_date ? format(new Date(invoice.invoice_date), "dd/MM/yyyy") : tCommon("notAvailable")}
                    </TableCell>
                    <TableCell>
                      {invoice.due_date ? format(new Date(invoice.due_date), "dd/MM/yyyy") : tCommon("notAvailable")}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(invoice.total_amount)}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(invoice.paid_amount || 0)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(invoice.status)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/${locale}/dashboard/invoices/${invoice.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // Download invoice PDF
                            window.open(`/api/invoices/${invoice.id}/pdf`, '_blank');
                          }}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}