const { PrismaClient } = require('./src/generated/prisma');
const prisma = new PrismaClient();

async function checkPaymentColumns() {
  try {
    // Try to query with enhanced schema fields
    console.log('Checking if enhanced payment columns exist...\n');
    
    try {
      // Try to select amount_due and amount_paid columns
      const testQuery = await prisma.$queryRaw`
        SELECT 
          id, 
          amount,
          amount_due,
          amount_paid,
          status
        FROM subscription_payments 
        LIMIT 1
      `;
      
      console.log('✅ Enhanced columns (amount_due, amount_paid) exist!');
      console.log('Sample row:', testQuery[0]);
    } catch (error) {
      console.log('❌ Enhanced columns do not exist');
      console.log('Error:', error.message);
      
      // Check what columns actually exist
      const columns = await prisma.$queryRaw`
        SHOW COLUMNS FROM subscription_payments
      `;
      
      console.log('\nActual columns in subscription_payments table:');
      columns.forEach(col => {
        console.log(`- ${col.Field} (${col.Type})`);
      });
    }
    
    // Check a specific payment
    const payment = await prisma.subscriptionPayment.findFirst({
      where: { id: 1 }
    });
    
    if (payment) {
      console.log('\nPayment record #1:');
      console.log('- amount:', payment.amount?.toString());
      console.log('- amount_due:', payment.amount_due?.toString() || 'undefined');
      console.log('- amount_paid:', payment.amount_paid?.toString() || 'undefined');
      console.log('- status:', payment.status);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkPaymentColumns();