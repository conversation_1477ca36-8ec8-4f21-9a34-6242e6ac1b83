@echo off
REM Windows-compatible auto-commit hook
REM This script runs after certain Claude Code operations

REM Check if we're in a git repository
git rev-parse --git-dir >nul 2>&1
if %errorlevel% neq 0 (
    echo Not a git repository, skipping auto-commit
    exit /b 0
)

REM Check if there are changes to commit
git status --porcelain >nul 2>&1
if %errorlevel% neq 0 (
    echo No changes to commit
    exit /b 0
)

REM Get the count of modified files
for /f %%i in ('git status --porcelain ^| find /c /v ""') do set changes=%%i

if %changes% equ 0 (
    echo No changes to commit
    exit /b 0
)

REM Add all changes
git add -A

REM Create commit message
set "commit_msg=Auto-commit: %changes% files modified"

REM Commit changes
git commit -m "%commit_msg%" >nul 2>&1

if %errorlevel% equ 0 (
    echo Successfully auto-committed %changes% files
) else (
    echo Failed to auto-commit changes
)

exit /b 0