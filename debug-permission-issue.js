// Simple test to debug permission loading
const { PrismaClient } = require('./src/generated/prisma/client');
const prisma = new PrismaClient();

async function debugPermissions() {
  try {
    console.log('=== DEBUG: User permissions in database ===');
    
    // Get user with full permission chain
    const user = await prisma.user.findFirst({
      where: { username: 'ali' },
      include: {
        user_roles: {
          include: {
            role: {
              include: {
                role_permissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!user) {
      console.log('User not found');
      return;
    }

    console.log(`User: ${user.username} (ID: ${user.id})`);
    console.log(`Status: ${user.status}`);
    console.log('');

    // Check role assignment
    console.log('=== ROLES ===');
    for (const userRole of user.user_roles) {
      console.log(`Role: ${userRole.role.name} (ID: ${userRole.role.id})`);
    }
    console.log('');

    // Check individual permissions
    console.log('=== PERMISSIONS ===');
    const allPermissions = user.user_roles.flatMap(ur => 
      ur.role.role_permissions.map(rp => ({
        module: rp.permission.module,
        action: rp.permission.action,
        roleName: ur.role.name
      }))
    );

    const groupedPermissions = {};
    allPermissions.forEach(p => {
      if (!groupedPermissions[p.module]) {
        groupedPermissions[p.module] = {
          create: false,
          read: false,
          update: false,
          delete: false
        };
      }
      groupedPermissions[p.module][p.action.toLowerCase()] = true;
    });

    console.log('Computed permissions object:');
    console.log(JSON.stringify(groupedPermissions, null, 2));

    // Test specific properties access
    console.log('');
    console.log('=== PROPERTIES MODULE ACCESS ===');
    const propertiesPerms = groupedPermissions['properties'];
    if (propertiesPerms) {
      console.log('Properties permissions found:');
      console.log(`- Read: ${propertiesPerms.read}`);
      console.log(`- Create: ${propertiesPerms.create}`);
      console.log(`- Update: ${propertiesPerms.update}`);
      console.log(`- Delete: ${propertiesPerms.delete}`);
      
      const hasAnyAccess = propertiesPerms.create || propertiesPerms.read || propertiesPerms.update || propertiesPerms.delete;
      console.log(`- Has module access: ${hasAnyAccess}`);
    } else {
      console.log('No properties permissions found!');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugPermissions();