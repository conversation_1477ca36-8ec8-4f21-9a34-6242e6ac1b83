/**
 * Authentication utilities for the property management application
 * Handles password hashing, JWT tokens, and user authentication
 */

import bcrypt from "bcryptjs";
import jwt, { type SignOptions } from "jsonwebtoken";
import { db } from "@/lib/db";
import type { AuthUser, UserPermissions } from "@/types/user";
import { generateTokenEdge } from "@/lib/auth-edge";
import { getUserPermissions as getPermissionsFromDB } from "@/lib/permissions";

// Environment variables
const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key-change-in-production";
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d";

/**
 * Hash a password using bcrypt
 * @param password - Plain text password
 * @returns Hashed password
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

/**
 * Verify a password against its hash
 * @param password - Plain text password
 * @param hash - Hashed password
 * @returns Boolean indicating if password is correct
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

/**
 * Generate a JWT token for a user (Node.js runtime)
 * @param user - User object
 * @returns JWT token
 */
export function generateToken(user: AuthUser): string {
  const payload = {
    id: user.id,
    username: user.username,
    email: user.email,
  };

  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN } as any);
}

/**
 * Generate a JWT token for a user (Edge Runtime compatible)
 * @param user - User object
 * @returns JWT token
 */
export async function generateTokenAsync(user: AuthUser): Promise<string> {
  const payload = {
    id: user.id,
    username: user.username,
    email: user.email,
  };

  return await generateTokenEdge(payload);
}

/**
 * Verify and decode a JWT token
 * @param token - JWT token
 * @returns Decoded token payload or null if invalid
 */
export function verifyToken(token: string): any {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded;
  } catch (error) {
    return null;
  }
}

/**
 * Get user permissions from database
 * @param userId - User ID
 * @returns User permissions object
 */
export async function getUserPermissions(userId: number): Promise<UserPermissions> {
  // First check if user is admin/superuser
  const userWithRoles = await db.user.findUnique({
    where: { id: userId },
    include: {
      user_roles: {
        include: {
          role: true,
        },
      },
    },
  });

  if (!userWithRoles) {
    return {};
  }

  // Check if user has admin role
  const isAdmin = userWithRoles.user_roles.some(ur => {
    const roleName = ur.role.name.toLowerCase();
    return roleName.includes('admin') || 
           roleName.includes('superuser') || 
           roleName === 'administrator' ||
           roleName === 'super admin';
  });


  if (isAdmin) {
    // Return full permissions for all modules
    const allModules = [
      'dashboard', 'properties', 'property-types', 'property-owners', 'units',
      'tenants', 'contracts', 'invoices', 'payments', 'owner-payouts',
      'maintenance', 'expenses', 'expense-categories', 'amenities', 'users', 'roles',
      'reports', 'settings', 'owners-associations'
    ];

    const permissions: UserPermissions = {};
    for (const module of allModules) {
      permissions[module] = {
        create: true,
        read: true,
        update: true,
        delete: true,
      };
    }
    return permissions;
  }

  // For non-admin users, get permissions from roles
  const fullUserWithRoles = await db.user.findUnique({
    where: { id: userId },
    include: {
      user_roles: {
        include: {
          role: {
            include: {
              role_permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      },
    },
  });

  const permissions: UserPermissions = {};

  // Collect all permissions from all roles
  if (fullUserWithRoles) {
    for (const userRole of fullUserWithRoles.user_roles) {
      for (const rolePermission of userRole.role.role_permissions) {
        const { module, action } = rolePermission.permission;
        
        if (!permissions[module]) {
          permissions[module] = {
            create: false,
            read: false,
            update: false,
            delete: false,
          };
        }

        // Map permission actions to boolean flags
        switch (action) {
          case "CREATE":
            permissions[module].create = true;
            break;
          case "READ":
            permissions[module].read = true;
            break;
          case "UPDATE":
            permissions[module].update = true;
            break;
          case "DELETE":
            permissions[module].delete = true;
            break;
        }
      }
    }
  }

  return permissions;
}

/**
 * Get authenticated user with permissions
 * @param userId - User ID
 * @returns AuthUser object or null if not found
 */
export async function getAuthUser(userId: number): Promise<AuthUser | null> {
  const user = await db.user.findUnique({
    where: { id: userId, status: "ACTIVE" },
    select: {
      id: true,
      username: true,
      email: true,
      first_name: true,
      last_name: true,
      status: true,
    },
  });

  if (!user) {
    return null;
  }

  const permissionsArray = await getPermissionsFromDB(userId);
  
  // Transform permissions array to the expected object format
  const permissions: UserPermissions = {};
  for (const permission of permissionsArray) {
    permissions[permission.module] = {
      create: permission.actions.includes('CREATE'),
      read: permission.actions.includes('READ'),
      update: permission.actions.includes('UPDATE'),
      delete: permission.actions.includes('DELETE'),
    };
  }

  return {
    ...user,
    permissions,
  };
}

/**
 * Authenticate user with username/email and password
 * @param identifier - Username or email
 * @param password - Plain text password
 * @returns AuthUser object or null if authentication fails
 */
export async function authenticateUser(
  identifier: string,
  password: string
): Promise<AuthUser | null> {
  // Find user by username or email
  const user = await db.user.findFirst({
    where: {
      OR: [
        { username: identifier },
        { email: identifier },
      ],
      status: "ACTIVE",
    },
  });

  if (!user) {
    return null;
  }

  // Verify password
  const isValidPassword = await verifyPassword(password, user.password_hash);
  if (!isValidPassword) {
    return null;
  }

  // Update last login
  await db.user.update({
    where: { id: user.id },
    data: { last_login_at: new Date() },
  });

  // Get user with permissions
  return getAuthUser(user.id);
}

/**
 * Check if user has specific permission
 * @param permissions - User permissions object
 * @param module - Module name
 * @param action - Permission action
 * @returns Boolean indicating if user has permission
 */
export function hasPermission(
  permissions: UserPermissions,
  module: string,
  action: "create" | "read" | "update" | "delete"
): boolean {
  return permissions[module]?.[action] || false;
}

/**
 * Check if user has any permission for a module
 * @param permissions - User permissions object
 * @param module - Module name
 * @returns Boolean indicating if user has any permission for the module
 */
export function hasModuleAccess(
  permissions: UserPermissions,
  module: string
): boolean {
  const modulePerms = permissions[module];
  if (!modulePerms) return false;
  
  return modulePerms.create || modulePerms.read || modulePerms.update || modulePerms.delete;
}

/**
 * Helper function to check permissions from array format (for API routes)
 * @param userId - User ID
 * @param module - Module name  
 * @param action - Permission action
 * @returns Boolean indicating if user has permission
 */
export async function checkUserPermission(
  userId: number,
  module: string,
  action: "CREATE" | "READ" | "UPDATE" | "DELETE"
): Promise<boolean> {
  const userPermissions = await getPermissionsFromDB(userId);
  const modulePermission = userPermissions.find(p => p.module === module);
  return modulePermission ? modulePermission.actions.includes(action) : false;
}

/**
 * Generate a secure random password
 * @param length - Password length (default: 12)
 * @returns Random password
 */
export function generateRandomPassword(length: number = 12): string {
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
  let password = "";
  
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  
  return password;
}

/**
 * Validate password strength
 * @param password - Password to validate
 * @returns Object with validation result and message
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  message: string;
} {
  if (password.length < 6) {
    return { isValid: false, message: "Password must be at least 6 characters long" };
  }

  if (password.length > 128) {
    return { isValid: false, message: "Password must be less than 128 characters long" };
  }

  // Check for at least one letter and one number
  const hasLetter = /[a-zA-Z]/.test(password);
  const hasNumber = /\d/.test(password);

  if (!hasLetter || !hasNumber) {
    return { 
      isValid: false, 
      message: "Password must contain at least one letter and one number" 
    };
  }

  return { isValid: true, message: "Password is valid" };
}
