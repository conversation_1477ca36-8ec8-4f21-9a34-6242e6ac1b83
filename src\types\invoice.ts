import { z } from "zod";
import type {
  Invoice,
  InvoiceStatus,
  InvoiceItem,
  Contract,
  Tenant,
  Property,
  Unit,
  User,
  Payment,
  PaymentAllocation
} from "@/generated/prisma";

// Invoice Schema
export const invoiceSchema = z.object({
  invoice_number: z.string().optional(), // Auto-generated if not provided
  contract_id: z.number().optional().nullable(),
  tenant_id: z.number().min(1, "Tenant is required"),
  property_id: z.number().min(1, "Property is required"),
  unit_id: z.number().min(1, "Unit is required"),
  invoice_date: z.string().min(1, "Invoice date is required"),
  due_date: z.string().min(1, "Due date is required"),
  status: z.nativeEnum({
    DRAFT: "DRAFT",
    PENDING: "PENDING",
    PARTIALLY_PAID: "PARTIALLY_PAID",
    PAID: "PAID",
    OVERDUE: "OVERDUE",
    CANCELLED: "CANCELLED"
  } as const).default("PENDING"),
  notes: z.string().optional().nullable(),
  items: z.array(z.object({
    description_en: z.string().min(1, "Description in English is required"),
    description_ar: z.string().min(1, "Description in Arabic is required"),
    quantity: z.number().min(1, "Quantity must be at least 1"),
    unit_price: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format"),
  }))
});

// Invoice Item Schema
export const invoiceItemSchema = z.object({
  description_en: z.string().min(1, "Description in English is required"),
  description_ar: z.string().min(1, "Description in Arabic is required"),
  quantity: z.number().min(1, "Quantity must be at least 1"),
  unit_price: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format"),
});

// Invoice Filter Schema
export const invoiceFilterSchema = z.object({
  contract_id: z.number().optional(),
  tenant_id: z.number().optional(),
  property_id: z.number().optional(),
  unit_id: z.number().optional(),
  status: z.nativeEnum({
    DRAFT: "DRAFT",
    PENDING: "PENDING",
    PARTIALLY_PAID: "PARTIALLY_PAID",
    PAID: "PAID",
    OVERDUE: "OVERDUE",
    CANCELLED: "CANCELLED"
  } as const).optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  due_date_from: z.string().optional(),
  due_date_to: z.string().optional(),
  search: z.string().optional(),
});

// Types for API requests
export type InvoiceInput = z.infer<typeof invoiceSchema>;
export type InvoiceItemInput = z.infer<typeof invoiceItemSchema>;
export type InvoiceFilter = z.infer<typeof invoiceFilterSchema>;

// Extended types with relations
export interface InvoiceWithRelations extends Invoice {
  contract?: Contract | null;
  tenant: Tenant;
  property: Property;
  unit: Unit;
  creator?: User | null;
  updater?: User | null;
  items: InvoiceItem[];
  payments: Payment[];
  allocations: PaymentAllocation[];
}

export interface InvoiceItemWithInvoice extends InvoiceItem {
  invoice: Invoice;
}

// Helper function to generate invoice number
export function generateInvoiceNumber(year: number, month: number, sequence: number): string {
  const yearStr = year.toString();
  const monthStr = month.toString().padStart(2, '0');
  const sequenceStr = sequence.toString().padStart(4, '0');
  return `INV-${yearStr}-${monthStr}-${sequenceStr}`;
}

// Helper function to calculate late fee
export function calculateLateFee(originalAmount: number | string, daysOverdue: number, lateFeePercentage: number = 5): number {
  const amount = typeof originalAmount === 'string' ? parseFloat(originalAmount) : originalAmount;
  if (daysOverdue <= 0) return 0;
  
  // Calculate late fee as percentage of original amount
  const lateFee = (amount * lateFeePercentage) / 100;
  
  // Round to 3 decimal places for OMR
  return Math.round(lateFee * 1000) / 1000;
}

// Helper function to check if invoice is overdue
export function isInvoiceOverdue(invoice: Invoice): boolean {
  if (invoice.status === 'PAID' || invoice.status === 'CANCELLED') return false;
  
  const now = new Date();
  const dueDate = new Date(invoice.due_date);
  return now > dueDate;
}

// Helper function to calculate days overdue
export function getDaysOverdue(dueDate: Date | string): number {
  const now = new Date();
  const due = new Date(dueDate);
  
  if (now <= due) return 0;
  
  const diffTime = Math.abs(now.getTime() - due.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
}

// formatCurrency function moved to @/lib/localization.ts for consistency
// Use: import { formatCurrency } from "@/lib/utils";

// Helper function to calculate invoice totals
export function calculateInvoiceTotals(items: InvoiceItemInput[]): {
  subtotal: number;
  total: number;
} {
  const subtotal = items.reduce((sum, item) => {
    const amount = parseFloat(item.unit_price) * item.quantity;
    return sum + amount;
  }, 0);
  
  return {
    subtotal: Math.round(subtotal * 1000) / 1000,
    total: Math.round(subtotal * 1000) / 1000
  };
}

// Helper function to get invoice status color
export function getInvoiceStatusColor(status: InvoiceStatus): string {
  const colors = {
    DRAFT: 'secondary',
    PENDING: 'warning',
    PARTIALLY_PAID: 'info',
    PAID: 'success',
    OVERDUE: 'destructive',
    CANCELLED: 'muted'
  };
  return colors[status] || 'secondary';
}