"use client";

import { User, Phone, Mail, MapPin, Calendar, CreditCard } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";
import { useTranslations } from "next-intl";

interface PropertyOwnerTabProps {
  property: any;
  locale: string;
}

export function PropertyOwnerTab({ property, locale }: PropertyOwnerTabProps) {
  const t = useTranslations("properties.tabs.owner");
  const owner = property.owner;

  if (!owner) {
    return (
      <Card>
        <CardHeader className={locale === "ar" ? "text-right" : ""}>
          <CardTitle className={`flex items-center gap-2 ${locale === "ar" ? "flex-row-reverse justify-start" : ""}`}>
            <User className="h-5 w-5" />
            {t("title")}
          </CardTitle>
          <CardDescription>
            {t("description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <User className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-semibold">{t("noOwner")}</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              {t("noOwnerDescription")}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const ownerName = locale === "ar" ? owner.name_ar : owner.name_en;

  return (
    <Card>
      <CardHeader className={locale === "ar" ? "text-right" : ""}>
        <CardTitle className={`flex items-center gap-2 ${locale === "ar" ? "flex-row-reverse justify-start" : ""}`}>
          <User className="h-5 w-5" />
          {t("title")}
        </CardTitle>
        <CardDescription>
          {t("description")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6" dir={locale === "ar" ? "rtl" : "ltr"}>
        {/* Owner Basic Information */}
        <div className={`grid gap-6 md:grid-cols-2 ${locale === "ar" ? "text-right" : ""}`}>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">{t("ownerDetails")}</h4>
              <div className="space-y-3">
                <div className={`flex items-center gap-2 ${locale === "ar" ? "flex-row-reverse" : ""}`}>
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{ownerName}</p>
                    <p className="text-sm text-muted-foreground">{t("ownerName")}</p>
                  </div>
                </div>
                
                {(owner.phone || owner.mobile) && (
                  <div className={`flex items-center gap-2 ${locale === "ar" ? "flex-row-reverse" : ""}`}>
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{owner.phone || owner.mobile}</p>
                      <p className="text-sm text-muted-foreground">{t("phoneNumber")}</p>
                    </div>
                  </div>
                )}
                
                {owner.email && (
                  <div className={`flex items-center gap-2 ${locale === "ar" ? "flex-row-reverse" : ""}`}>
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{owner.email}</p>
                      <p className="text-sm text-muted-foreground">{t("emailAddress")}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">{t("financialInformation")}</h4>
              <div className="space-y-3">
                {owner.tax_id && (
                  <div>
                    <p className="text-sm text-muted-foreground">{t("taxId")}</p>
                    <p className="font-medium">{owner.tax_id}</p>
                  </div>
                )}
              </div>
            </div>
            
            {owner.bank_account_number && (
              <div>
                <h5 className="text-sm font-medium text-muted-foreground mb-2">{t("bankingInformation")}</h5>
                <div className="space-y-2">
                  <div className={`flex items-center gap-2 ${locale === "ar" ? "flex-row-reverse" : ""}`}>
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{owner.bank_account_number}</p>
                      <p className="text-sm text-muted-foreground">{t("accountNumber")}</p>
                    </div>
                  </div>
                  {owner.bank_name && (
                    <div className={locale === "ar" ? "mr-6" : "ml-6"}>
                      <p className="text-sm">{owner.bank_name}</p>
                      <p className="text-xs text-muted-foreground">{t("bankName")}</p>
                    </div>
                  )}
                  {owner.bank_iban && (
                    <div className={locale === "ar" ? "mr-6" : "ml-6"}>
                      <p className="text-sm">{owner.bank_iban}</p>
                      <p className="text-xs text-muted-foreground">{t("iban")}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* Financial Summary */}
        <div className={locale === "ar" ? "text-right" : ""}>
          <h4 className="text-sm font-medium text-muted-foreground mb-4">{t("financialSummary")}</h4>
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardContent className={`pt-4 ${locale === "ar" ? "text-right" : ""}`}>
                <div className="text-2xl font-bold text-green-600">
                  {parseFloat(property.base_rent).toFixed(3)} OMR
                </div>
                <p className="text-sm text-muted-foreground">{t("monthlyBaseRent")}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className={`pt-4 ${locale === "ar" ? "text-right" : ""}`}>
                <div className="text-2xl font-bold">0.000 OMR</div>
                <p className="text-sm text-muted-foreground">{t("outstandingBalance")}</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className={`pt-4 ${locale === "ar" ? "text-right" : ""}`}>
                <div className="text-2xl font-bold">0.000 OMR</div>
                <p className="text-sm text-muted-foreground">{t("totalPaid")}</p>
              </CardContent>
            </Card>
          </div>
        </div>

      </CardContent>
    </Card>
  );
}