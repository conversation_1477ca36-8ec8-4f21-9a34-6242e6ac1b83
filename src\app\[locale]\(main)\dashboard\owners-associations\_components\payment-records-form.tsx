"use client";

import { useState, useEffect } from "react";
import { useTranslations, useLocale } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import { toast } from "sonner";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  CalendarIcon,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  CreditCard,
  DollarSign,
  User,
  Building,
  Receipt,
  ArrowLeft,
  CheckCircle,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useRTL } from "@/hooks/use-rtl";
import { apiClient } from "@/lib/api-client";
import { formatCurrency } from "@/lib/utils";
import type { SubscriptionPaymentWithRelations } from "@/types/owners-association";

// Payment record schema
const paymentRecordSchema = z.object({
  amount: z.string().min(1, "Amount is required"),
  payment_date: z.date(),
  payment_method: z.enum(["CASH", "BANK_TRANSFER", "CHECK", "CREDIT_CARD"]),
  reference_number: z.string().optional(),
  notes: z.string().optional(),
});

interface PaymentRecord {
  id: number;
  payment_id: number;
  amount: number;
  payment_date: Date;
  payment_method: string;
  reference_number?: string;
  notes?: string;
  created_by: number;
  created_at: Date;
  updated_at: Date;
}

type PaymentRecordFormData = z.infer<typeof paymentRecordSchema>;

interface PaymentRecordsFormProps {
  payment: SubscriptionPaymentWithRelations;
  associationId: number;
  onBack: () => void;
  onSuccess?: () => void;
}

export function PaymentRecordsForm({
  payment,
  associationId,
  onBack,
  onSuccess,
}: PaymentRecordsFormProps) {
  const locale = useLocale();
  const { isRTL } = useRTL();
  const t = useTranslations("ownersAssociations");
  const tCommon = useTranslations("common");
  const dateLocale = locale === 'ar' ? ar : enUS;

  const [records, setRecords] = useState<PaymentRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingRecord, setEditingRecord] = useState<PaymentRecord | null>(null);
  const [deletingRecord, setDeletingRecord] = useState<PaymentRecord | null>(null);
  const [showForm, setShowForm] = useState(false);

  const form = useForm<PaymentRecordFormData>({
    resolver: zodResolver(paymentRecordSchema),
    defaultValues: {
      amount: "",
      payment_date: new Date(),
      payment_method: "BANK_TRANSFER",
      reference_number: "",
      notes: "",
    },
  });

  useEffect(() => {
    fetchPaymentRecords();
  }, []);

  const fetchPaymentRecords = async () => {
    try {
      setLoading(true);
      const url = `/api/owners-associations/${associationId}/subscription-payments/${payment.id}/records`;
      
      const response = await apiClient.get(url);

      if (response.success) {
        setRecords(response.data.records || []);
      }
    } catch (error) {
      console.error("Error fetching payment records:", error);
      toast.error(t("paymentRecords.fetchError"));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (data: PaymentRecordFormData) => {
    try {
      setLoading(true);
      const endpoint = editingRecord
        ? `/api/owners-associations/${associationId}/subscription-payments/${payment.id}/records/${editingRecord.id}`
        : `/api/owners-associations/${associationId}/subscription-payments/${payment.id}/records`;

      const method = editingRecord ? "PUT" : "POST";

      const response = await apiClient[method.toLowerCase() as 'post' | 'put'](endpoint, {
        amount: parseFloat(data.amount),
        payment_date: data.payment_date.toISOString(),
        payment_method: data.payment_method,
        reference_number: data.reference_number || undefined,
        notes: data.notes || undefined,
      });

      if (response.success) {
        toast.success(editingRecord ? t("paymentRecords.updateSuccess") : t("paymentRecords.createSuccess"));
        fetchPaymentRecords();
        cancelEdit();
        onSuccess?.();
      }
    } catch (error: any) {
      console.error("Error saving payment record:", error);
      toast.error(error.response?.data?.error || t("paymentRecords.saveError"));
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (record: PaymentRecord) => {
    setEditingRecord(record);
    form.reset({
      amount: record.amount.toString(),
      payment_date: new Date(record.payment_date),
      payment_method: record.payment_method as any,
      reference_number: record.reference_number || "",
      notes: record.notes || "",
    });
    setShowForm(true);
  };

  const handleDelete = async () => {
    if (!deletingRecord) return;

    try {
      setLoading(true);
      const endpoint = `/api/owners-associations/${associationId}/subscription-payments/${payment.id}/records/${deletingRecord.id}`;
      
      await apiClient.delete(endpoint);
      
      toast.success(t("paymentRecords.deleteSuccess"));
      fetchPaymentRecords();
      onSuccess?.();
    } catch (error) {
      console.error("Error deleting payment record:", error);
      toast.error(t("paymentRecords.deleteError"));
    } finally {
      setLoading(false);
      setDeletingRecord(null);
    }
  };

  const cancelEdit = () => {
    setEditingRecord(null);
    form.reset({
      amount: "",
      payment_date: new Date(),
      payment_method: "BANK_TRANSFER",
      reference_number: "",
      notes: "",
    });
    setShowForm(false);
  };

  const amountDue = parseFloat(payment.amount_due?.toString() || payment.amount.toString());
  const totalPaid = records.reduce((sum, record) => sum + parseFloat(record.amount.toString()), 0);
  const remainingAmount = amountDue - totalPaid;
  const progressPercentage = amountDue > 0 ? Math.min((totalPaid / amountDue) * 100, 100) : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={cn("flex items-center justify-between", isRTL && "flex-row-reverse")}>
        <div className={cn("flex items-center gap-3", isRTL && "flex-row-reverse")}>
          <Button variant="ghost" size="sm" onClick={onBack} className={cn("gap-2", isRTL && "flex-row-reverse")}>
            <ArrowLeft className="h-4 w-4" />
            {tCommon("back")}
          </Button>
          <div>
            <h1 className={cn("text-2xl font-semibold flex items-center gap-2", isRTL && "flex-row-reverse")}>
              <Receipt className="h-6 w-6" />
              {t("paymentRecords.title")}
            </h1>
            <p className="text-muted-foreground">{t("paymentRecords.description")}</p>
          </div>
        </div>
        <Button 
          onClick={() => setShowForm(true)} 
          className={cn("gap-2", isRTL && "flex-row-reverse")}
        >
          <Plus className="h-4 w-4" />
          {t("paymentRecords.addRecord")}
        </Button>
      </div>

      {/* Payment Summary Card */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">{t("paymentRecords.paymentSummary")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-3">
            <div className={cn("space-y-2", isRTL && "text-right")}>
              <div className="text-sm text-muted-foreground flex items-center gap-2">
                <User className="h-4 w-4" />
                {t("paymentRecords.member")}
              </div>
              <div className="font-medium">{payment.member.full_name}</div>
              <div className="text-sm text-muted-foreground">
                {t("paymentRecords.unit")}: {payment.member.unit_number || 'N/A'}
              </div>
            </div>
            
            <div className={cn("space-y-2", isRTL && "text-right")}>
              <div className="text-sm text-muted-foreground flex items-center gap-2">
                <Building className="h-4 w-4" />
                {t("paymentRecords.subscription")}
              </div>
              <div className="font-medium">{payment.subscription.name_en}</div>
              <div className="text-sm text-muted-foreground">
                {t("paymentRecords.dueDate")}: {format(new Date(payment.due_date), "MMM dd, yyyy", { locale: dateLocale })}
              </div>
            </div>

            <div className={cn("space-y-2", isRTL && "text-right")}>
              <div className="text-sm text-muted-foreground flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                {t("paymentRecords.paymentProgress")}
              </div>
              <div className="space-y-2">
                <div className={cn("flex justify-between text-sm", isRTL && "flex-row-reverse")}>
                  <span>{t("paymentRecords.paid")}</span>
                  <span className="font-medium">{formatCurrency(totalPaid)}</span>
                </div>
                <Progress value={progressPercentage} className="h-2" />
                <div className={cn("flex justify-between text-sm", isRTL && "flex-row-reverse")}>
                  <span>{t("paymentRecords.remaining")}</span>
                  <span className="font-medium">{formatCurrency(Math.max(0, remainingAmount))}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Records List */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-medium">{t("paymentRecords.recordsList")}</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          ) : records.length === 0 ? (
            <div className="text-center py-8 space-y-3">
              <CreditCard className="h-12 w-12 text-muted-foreground mx-auto" />
              <p className="text-muted-foreground">{t("paymentRecords.noRecords")}</p>
              <Button
                size="sm"
                onClick={() => setShowForm(true)}
                className={cn("gap-2", isRTL && "flex-row-reverse")}
              >
                <Plus className="h-4 w-4" />
                {t("paymentRecords.addFirstRecord")}
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className={cn(isRTL && "text-right")}>{t("paymentRecords.date")}</TableHead>
                    <TableHead className={cn(isRTL && "text-right")}>{t("paymentRecords.amount")}</TableHead>
                    <TableHead className={cn(isRTL && "text-right")}>{t("paymentRecords.method")}</TableHead>
                    <TableHead className={cn(isRTL && "text-right")}>{t("paymentRecords.reference")}</TableHead>
                    <TableHead className={cn(isRTL && "text-right")}>{t("paymentRecords.notes")}</TableHead>
                    <TableHead className={cn("w-[100px]", isRTL && "text-right")}>{tCommon("actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {records.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell className={cn(isRTL && "text-right")}>
                        {format(new Date(record.payment_date), "MMM dd, yyyy", { locale: dateLocale })}
                      </TableCell>
                      <TableCell className={cn("font-medium", isRTL && "text-right")}>
                        {formatCurrency(parseFloat(record.amount.toString()))}
                      </TableCell>
                      <TableCell className={cn(isRTL && "text-right")}>
                        <Badge variant="outline" className="text-xs">
                          {t(`paymentRecords.paymentMethod.${record.payment_method.toLowerCase()}`)}
                        </Badge>
                      </TableCell>
                      <TableCell className={cn(isRTL && "text-right")}>
                        {record.reference_number || "-"}
                      </TableCell>
                      <TableCell className={cn("max-w-[200px] truncate", isRTL && "text-right")}>
                        {record.notes || "-"}
                      </TableCell>
                      <TableCell>
                        <div className={cn("flex items-center gap-1", isRTL && "flex-row-reverse")}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(record)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDeletingRecord(record)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Form */}
      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-medium">
              {editingRecord ? t("paymentRecords.editRecord") : t("paymentRecords.addRecord")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("paymentRecords.amount")}</FormLabel>
                        <FormControl>
                          <Input {...field} type="number" step="0.001" placeholder="0.000" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="payment_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("paymentRecords.paymentDate")}</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground",
                                  isRTL && "text-right"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP", { locale: dateLocale })
                                ) : (
                                  <span>{t("paymentRecords.selectDate")}</span>
                                )}
                                <CalendarIcon className={cn("ml-auto h-4 w-4 opacity-50", isRTL && "ml-0 mr-auto")} />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="payment_method"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("paymentRecords.method")}</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t("paymentRecords.selectMethod")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="CASH">{t("paymentRecords.paymentMethod.cash")}</SelectItem>
                            <SelectItem value="BANK_TRANSFER">{t("paymentRecords.paymentMethod.bank_transfer")}</SelectItem>
                            <SelectItem value="CHECK">{t("paymentRecords.paymentMethod.check")}</SelectItem>
                            <SelectItem value="CREDIT_CARD">{t("paymentRecords.paymentMethod.credit_card")}</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="reference_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("paymentRecords.referenceNumber")}</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder={t("paymentRecords.referenceNumberPlaceholder")} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("paymentRecords.notes")}</FormLabel>
                      <FormControl>
                        <Textarea {...field} placeholder={t("paymentRecords.notesPlaceholder")} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className={cn("flex gap-3", isRTL && "flex-row-reverse")}>
                  <Button type="submit" disabled={loading} className={cn("gap-2", isRTL && "flex-row-reverse")}>
                    <Save className="h-4 w-4" />
                    {editingRecord ? t("paymentRecords.updateRecord") : t("paymentRecords.saveRecord")}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={cancelEdit}
                    className={cn("gap-2", isRTL && "flex-row-reverse")}
                  >
                    <X className="h-4 w-4" />
                    {tCommon("cancel")}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingRecord} onOpenChange={() => setDeletingRecord(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("paymentRecords.deleteDialog.title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("paymentRecords.deleteDialog.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{tCommon("cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              {tCommon("delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}