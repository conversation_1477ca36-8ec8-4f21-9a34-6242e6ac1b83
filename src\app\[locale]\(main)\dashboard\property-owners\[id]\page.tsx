import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { db } from "@/lib/db";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit, Building, DollarSign } from "lucide-react";
import Link from "next/link";
import { formatOMR } from "@/lib/format";

interface ViewPropertyOwnerPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

async function getPropertyOwner(id: string) {
  try {
    const owner = await db.propertyOwner.findUnique({
      where: { id: parseInt(id, 10) },
      include: {
        primary_properties: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            address_en: true,
            address_ar: true,
            base_rent: true,
            status: true,
          },
        },
        payouts: {
          orderBy: {
            payout_date: "desc",
          },
          take: 5,
          select: {
            id: true,
            payout_number: true,
            payout_date: true,
            period_start: true,
            period_end: true,
            total_rent_collected: true,
            management_fee: true,
            net_amount: true,
            status: true,
          },
        },
        _count: {
          select: {
            payouts: true,
          },
        },
      },
    });
    
    if (owner) {
      // Convert Decimal fields to strings for client component
      return {
        ...owner,
        management_fee_percentage: owner.management_fee_percentage?.toString() || null,
        primary_properties: owner.primary_properties.map(property => ({
          ...property,
          base_rent: property.base_rent.toString()
        })),
        payouts: owner.payouts.map(payout => ({
          ...payout,
          total_rent_collected: payout.total_rent_collected.toString(),
          management_fee: payout.management_fee?.toString() || null,
          net_amount: payout.net_amount.toString()
        }))
      };
    }
    
    return owner;
  } catch (error) {
    console.error("Error fetching property owner:", error);
    return null;
  }
}

export default async function ViewPropertyOwnerPage({ params }: ViewPropertyOwnerPageProps) {
  const { id, locale } = await params;
  const t = await getTranslations('propertyOwners');
  const tCommon = await getTranslations('common');

  const owner = await getPropertyOwner(id);

  if (!owner) {
    notFound();
  }

  const ownerName = locale === "ar" ? owner.name_ar : owner.name_en;
  const ownerAddress = locale === "ar" ? owner.address_ar : owner.address_en;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href={`/${locale}/dashboard/property-owners`}>
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{ownerName}</h1>
            <p className="text-muted-foreground">{t('viewDescription')}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href={`/${locale}/dashboard/property-owners/${id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              {tCommon('edit')}
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>{t('basicInformation')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <div className="text-sm text-muted-foreground">{t('nameEn')}</div>
              <div className="font-medium">{owner.name_en}</div>
            </div>
            <div className="grid gap-2">
              <div className="text-sm text-muted-foreground">{t('nameAr')}</div>
              <div className="font-medium" dir="rtl">{owner.name_ar}</div>
            </div>
            <div className="grid gap-2">
              <div className="text-sm text-muted-foreground">{tCommon('status')}</div>
              <div>
                <Badge variant={owner.status === "ACTIVE" ? "default" : "secondary"}>
                  {t(`status.${owner.status.toLowerCase()}`)}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>{t('contactInformation')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {owner.email && (
              <div className="grid gap-2">
                <div className="text-sm text-muted-foreground">{t('email')}</div>
                <div className="font-medium">{owner.email}</div>
              </div>
            )}
            {owner.phone && (
              <div className="grid gap-2">
                <div className="text-sm text-muted-foreground">{t('phone')}</div>
                <div className="font-medium">{owner.phone}</div>
              </div>
            )}
            {owner.mobile && (
              <div className="grid gap-2">
                <div className="text-sm text-muted-foreground">{t('mobile')}</div>
                <div className="font-medium">{owner.mobile}</div>
              </div>
            )}
            {ownerAddress && (
              <div className="grid gap-2">
                <div className="text-sm text-muted-foreground">{tCommon('address')}</div>
                <div className="font-medium" dir={locale === "ar" ? "rtl" : "ltr"}>{ownerAddress}</div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Business Information */}
        <Card>
          <CardHeader>
            <CardTitle>{t('businessInformation')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {owner.tax_id && (
              <div className="grid gap-2">
                <div className="text-sm text-muted-foreground">{t('taxId')}</div>
                <div className="font-medium">{owner.tax_id}</div>
              </div>
            )}
            {owner.management_fee_percentage && (
              <div className="grid gap-2">
                <div className="text-sm text-muted-foreground">{t('managementFeePercentage')}</div>
                <div className="font-medium">{owner.management_fee_percentage}%</div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Banking Information */}
        <Card>
          <CardHeader>
            <CardTitle>{t('bankingInformation')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {owner.bank_name && (
              <div className="grid gap-2">
                <div className="text-sm text-muted-foreground">{t('bankName')}</div>
                <div className="font-medium">{owner.bank_name}</div>
              </div>
            )}
            {owner.bank_account_number && (
              <div className="grid gap-2">
                <div className="text-sm text-muted-foreground">{t('bankAccountNumber')}</div>
                <div className="font-medium">{owner.bank_account_number}</div>
              </div>
            )}
            {owner.bank_iban && (
              <div className="grid gap-2">
                <div className="text-sm text-muted-foreground">{t('bankIban')}</div>
                <div className="font-medium">{owner.bank_iban}</div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Properties */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>{t('properties')}</CardTitle>
            <CardDescription>
              {t('activeProperties')}
            </CardDescription>
          </div>
          <Button asChild variant="outline">
            <Link href={`/${locale}/dashboard/property-owners/${id}/properties`}>
              <Building className="mr-2 h-4 w-4" />
              {t('manageProperties')}
            </Link>
          </Button>
        </CardHeader>
        <CardContent>
          {owner.primary_properties.length === 0 ? (
            <p className="text-sm text-muted-foreground">{t('noProperties')}</p>
          ) : (
            <div className="space-y-4">
              {owner.primary_properties.map((property) => {
                const propertyName = locale === "ar" ? property.name_ar : property.name_en;
                const propertyAddress = locale === "ar" ? property.address_ar : property.address_en;
                
                return (
                  <div key={property.id} className="flex items-center justify-between border-b pb-4 last:border-0">
                    <div className="space-y-1">
                      <Link 
                        href={`/${locale}/dashboard/properties/${property.id}`}
                        className="font-medium hover:underline"
                      >
                        {propertyName}
                      </Link>
                      <p className="text-sm text-muted-foreground">{propertyAddress}</p>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatOMR(property.base_rent.toString())}</div>
                      <Badge variant={property.status === "AVAILABLE" ? "default" : "secondary"}>
                        {property.status}
                      </Badge>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Payouts */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>{t('recentPayouts')}</CardTitle>
            <CardDescription>
              {t('last5Payouts')}
            </CardDescription>
          </div>
          <Button asChild variant="outline">
            <Link href={`/${locale}/dashboard/owner-payouts?owner=${id}`}>
              <DollarSign className="mr-2 h-4 w-4" />
              {t('viewAllPayouts')}
            </Link>
          </Button>
        </CardHeader>
        <CardContent>
          {owner.payouts.length === 0 ? (
            <p className="text-sm text-muted-foreground">{t('noPayouts')}</p>
          ) : (
            <div className="space-y-4">
              {owner.payouts.map((payout) => (
                <div key={payout.id} className="flex items-center justify-between border-b pb-4 last:border-0">
                  <div className="space-y-1">
                    <div className="font-medium">{payout.payout_number}</div>
                    <p className="text-sm text-muted-foreground">
                      {new Date(payout.payout_date).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{formatOMR(payout.net_amount.toString())}</div>
                    <Badge variant={payout.status === "PAID" ? "default" : "secondary"}>
                      {payout.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}