import { NextRequest } from "next/server";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { z } from "zod";

const emergencyContactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  relationship: z.string().min(1, "Relationship is required"),
  phone: z.string().min(1, "Phone is required"),
  email: z.string().email().optional().or(z.literal("")),
  is_primary: z.boolean().default(false),
});

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/tenants/:id/emergency-contacts - Get all emergency contacts for a tenant
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "tenants", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view tenants");
    }

    const { id } = await params;
    const tenantId = parseInt(id);
    
    if (isNaN(tenantId)) {
      return ApiResponseBuilder.badRequest("Invalid tenant ID");
    }

    const emergencyContacts = await db.emergencyContact.findMany({
      where: { tenant_id: tenantId },
      orderBy: [
        { is_primary: "desc" },
        { created_at: "desc" },
      ],
    });

    return ApiResponseBuilder.success(emergencyContacts);
  } catch (error) {
    console.error("Get Emergency Contacts Error:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch emergency contacts",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// POST /api/tenants/:id/emergency-contacts - Create a new emergency contact
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "tenants", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update tenants");
    }

    const { id } = await params;
    const tenantId = parseInt(id);
    
    if (isNaN(tenantId)) {
      return ApiResponseBuilder.badRequest("Invalid tenant ID");
    }

    // Check if tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
    });

    if (!tenant) {
      return ApiResponseBuilder.notFound("Tenant");
    }

    const body = await request.json();
    const validationResult = emergencyContactSchema.safeParse(body);
    
    if (!validationResult.success) {
      return ApiResponseBuilder.validationError(validationResult.error);
    }

    const data = validationResult.data;

    // If this is set as primary, unset other primary contacts
    if (data.is_primary) {
      await db.emergencyContact.updateMany({
        where: { tenant_id: tenantId },
        data: { is_primary: false },
      });
    }

    const emergencyContact = await db.emergencyContact.create({
      data: {
        tenant_id: tenantId,
        name: data.name,
        relationship: data.relationship,
        phone: data.phone,
        email: data.email || null,
        is_primary: data.is_primary,
        created_by: decoded.id,
        updated_by: decoded.id,
      },
    });

    return ApiResponseBuilder.success(emergencyContact);
  } catch (error) {
    console.error("Create Emergency Contact Error:", error);
    return ApiResponseBuilder.error(
      "Failed to create emergency contact",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}