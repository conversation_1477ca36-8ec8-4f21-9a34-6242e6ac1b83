import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

const predefinedCategories = [
  { name_en: "Office Supplies", name_ar: "مستلزمات المكتب", sort_order: 1 },
  { name_en: "Fuel/Petrol", name_ar: "وقود/بنزين", sort_order: 2 },
  { name_en: "Maintenance Tools", name_ar: "أدوات الصيانة", sort_order: 3 },
  { name_en: "Utilities", name_ar: "المرافق", sort_order: 4 },
  { name_en: "Marketing/Advertising", name_ar: "التسويق/الإعلان", sort_order: 5 },
  { name_en: "Salaries", name_ar: "الرواتب", sort_order: 6 },
  { name_en: "Travel", name_ar: "السفر", sort_order: 7 },
  { name_en: "Insurance", name_ar: "التأمين", sort_order: 8 },
  { name_en: "Legal/Professional Services", name_ar: "الخدمات القانونية/المهنية", sort_order: 9 },
  { name_en: "Other", name_ar: "أخرى", sort_order: 10 }
];

async function initializeExpenseCategories() {
  console.log("Initializing expense categories...");

  try {
    for (const category of predefinedCategories) {
      const existing = await prisma.expenseCategory.findFirst({
        where: { name_en: category.name_en }
      });

      if (!existing) {
        await prisma.expenseCategory.create({
          data: {
            ...category,
            is_active: true,
            created_by: 1,
            updated_by: 1
          }
        });
        console.log(`Created category: ${category.name_en}`);
      } else {
        console.log(`Category already exists: ${category.name_en}`);
      }
    }

    console.log("Expense categories initialization completed!");
  } catch (error) {
    console.error("Error initializing expense categories:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the initialization
if (require.main === module) {
  initializeExpenseCategories();
}

export { initializeExpenseCategories };
