@echo off
echo ========================================
echo NUCLEAR MYSQL FIX - Complete Reset
echo ========================================
echo.

echo WARNING: This will completely reset MySQL
echo All data will be lost except what we backup
echo.
set /p confirm="Type YES to continue: "
if not "%confirm%"=="YES" goto :end

echo.
echo 1. Killing ALL MySQL processes...
taskkill /f /im mysqld.exe 2>nul
taskkill /f /im mysql.exe 2>nul
wmic process where "name='mysqld.exe'" delete 2>nul
timeout /t 5 /nobreak >nul
echo ✅ All MySQL processes terminated

echo.
echo 2. Backing up property_management database...
if exist "C:\xampp\mysql\data\property_management" (
    if not exist "C:\xampp\mysql\BACKUP" mkdir "C:\xampp\mysql\BACKUP"
    xcopy "C:\xampp\mysql\data\property_management" "C:\xampp\mysql\BACKUP\property_management\" /E /I /Y /Q
    echo ✅ Database backed up to BACKUP folder
)

echo.
echo 3. Completely removing data directory...
cd "C:\xampp\mysql"
if exist "data" (
    attrib -r -h -s data\*.* /s /d
    rmdir /s /q "data"
    echo ✅ Data directory completely removed
)

echo.
echo 4. Creating fresh data directory structure...
mkdir "data"
mkdir "data\mysql"
mkdir "data\performance_schema"
mkdir "data\test"
echo ✅ Fresh data directory created

echo.
echo 5. Creating MySQL system tables manually...
cd "data\mysql"
echo. > db.frm
echo. > user.frm
echo. > host.frm
cd ".."

echo.
echo 6. Creating DISABLE InnoDB configuration...
cd "..\bin"
echo # MySQL Configuration - InnoDB DISABLED > my_no_innodb.ini
echo [mysqld] >> my_no_innodb.ini
echo port=3306 >> my_no_innodb.ini
echo basedir=C:/xampp/mysql >> my_no_innodb.ini
echo datadir=C:/xampp/mysql/data >> my_no_innodb.ini
echo skip-grant-tables >> my_no_innodb.ini
echo skip-networking >> my_no_innodb.ini
echo skip-slave-start >> my_no_innodb.ini
echo skip-innodb >> my_no_innodb.ini
echo default-storage-engine=MyISAM >> my_no_innodb.ini
echo default-table-type=MyISAM >> my_no_innodb.ini
echo key_buffer_size=16M >> my_no_innodb.ini
echo max_allowed_packet=1M >> my_no_innodb.ini
echo table_open_cache=64 >> my_no_innodb.ini
echo sort_buffer_size=512K >> my_no_innodb.ini
echo net_buffer_length=8K >> my_no_innodb.ini
echo read_buffer_size=256K >> my_no_innodb.ini
echo read_rnd_buffer_size=512K >> my_no_innodb.ini
echo myisam_sort_buffer_size=8M >> my_no_innodb.ini
echo log-error=mysql_error.log >> my_no_innodb.ini
echo sql_mode="" >> my_no_innodb.ini

echo.
echo 7. Applying NO-InnoDB configuration...
copy my_no_innodb.ini my.ini
echo ✅ InnoDB completely disabled

echo.
echo 8. Initializing MySQL with install script...
mysql_install_db --datadir=C:\xampp\mysql\data --basedir=C:\xampp\mysql --force

echo.
echo ========================================
echo NUCLEAR FIX COMPLETE!
echo.
echo Now try starting MySQL with:
echo mysqld --console --skip-innodb --skip-grant-tables
echo.
echo This should start MySQL without any InnoDB
echo ========================================

:end
pause
