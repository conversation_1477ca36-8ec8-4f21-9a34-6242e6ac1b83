"use client";

import { useAuth } from "@/contexts/auth-context";
import { useEffect } from "react";

export default function TestAuthPage() {
  const { user, isLoading } = useAuth();

  useEffect(() => {
    console.log("TestAuth: Component mounted");
    console.log("TestAuth: isLoading:", isLoading);
    console.log("TestAuth: user:", user);
  }, [user, isLoading]);

  if (isLoading) {
    return <div>Loading authentication...</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Authentication Test Page</h1>
      
      {user ? (
        <div className="bg-green-100 p-4 rounded">
          <h2 className="text-lg font-semibold text-green-800">✅ User Authenticated</h2>
          <div className="mt-2">
            <p><strong>ID:</strong> {user.id}</p>
            <p><strong>Username:</strong> {user.username}</p>
            <p><strong>Email:</strong> {user.email}</p>
            <p><strong>Name:</strong> {user.first_name} {user.last_name}</p>
            <p><strong>Status:</strong> {user.status}</p>
          </div>
          
          <div className="mt-4">
            <a 
              href="/dashboard/default" 
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Go to Dashboard
            </a>
          </div>
        </div>
      ) : (
        <div className="bg-red-100 p-4 rounded">
          <h2 className="text-lg font-semibold text-red-800">❌ Not Authenticated</h2>
          <p className="mt-2">User is not logged in.</p>
          
          <div className="mt-4">
            <a 
              href="/login" 
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Go to Login
            </a>
          </div>
        </div>
      )}
    </div>
  );
}
