/**
 * Edge Runtime compatible authentication utilities
 * Uses jose library instead of jsonwebtoken for Edge Runtime compatibility
 */

import { SignJWT, jwtVerify } from "jose";

// Environment variables
const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key-change-in-production";
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d";

// Convert secret to Uint8Array for jose
const secret = new TextEncoder().encode(JWT_SECRET);

/**
 * Generate a JWT token for a user (Edge Runtime compatible)
 * @param payload - Token payload
 * @returns JWT token
 */
export async function generateTokenEdge(payload: any): Promise<string> {
  const token = await new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime(JWT_EXPIRES_IN)
    .sign(secret);
  
  return token;
}

/**
 * Verify and decode a JWT token (Edge Runtime compatible)
 * @param token - JWT token
 * @returns Decoded token payload or null if invalid
 */
export async function verifyTokenEdge(token: string): Promise<any> {
  try {
    const { payload } = await jwtVerify(token, secret);
    return payload;
  } catch (error) {
    return null;
  }
}
