const { PrismaClient } = require('./src/generated/prisma/client');
const prisma = new PrismaClient();

async function testAuthEndpoint() {
  try {
    // Login to get a token
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'ali',
        password: 'password123'
      }),
    });

    console.log('Login response status:', loginResponse.status);
    
    if (!loginResponse.ok) {
      const errorData = await loginResponse.text();
      console.log('Login error:', errorData);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('Login successful');

    // Get the cookie from the response
    const setCookieHeader = loginResponse.headers.get('Set-Cookie');
    console.log('Set-Cookie header:', setCookieHeader);

    // Extract auth-token value
    let authToken = null;
    if (setCookieHeader) {
      const tokenMatch = setCookieHeader.match(/auth-token=([^;]+)/);
      if (tokenMatch) {
        authToken = tokenMatch[1];
        console.log('Auth token extracted:', authToken.substring(0, 20) + '...');
      }
    }

    if (!authToken) {
      console.log('No auth token found in response');
      return;
    }

    // Test /api/auth/me endpoint
    const meResponse = await fetch('http://localhost:3000/api/auth/me', {
      method: 'GET',
      headers: {
        'Cookie': `auth-token=${authToken}`,
      },
    });

    console.log('Me response status:', meResponse.status);
    
    if (meResponse.ok) {
      const meData = await meResponse.json();
      console.log('Me response user:', meData.user.username);
      console.log('Me response permissions:');
      console.log(JSON.stringify(meData.user.permissions, null, 2));
    } else {
      const errorData = await meResponse.text();
      console.log('Me error:', errorData);
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAuthEndpoint();