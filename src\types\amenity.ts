import { z } from "zod";
import type { Amenity, PropertyAmenity, Property } from "@/generated/prisma";

// Amenity Types
export interface AmenityWithRelations extends Amenity {
  property_amenities?: PropertyAmenityWithRelations[];
  _count?: {
    property_amenities: number;
  };
}

export interface PropertyAmenityWithRelations extends PropertyAmenity {
  amenity?: Amenity;
  property?: Property;
}

// Amenity Schemas
export const amenitySchema = z.object({
  name_en: z.string().min(1, "Name in English is required").max(100),
  name_ar: z.string().min(1, "Name in Arabic is required").max(100),
  icon: z.string().max(50).optional().nullable(),
});

export const amenityUpdateSchema = amenitySchema.partial();

// Amenity Filter Schema
export const amenityFilterSchema = z.object({
  search: z.string().optional(),
  page: z.number().int().positive().default(1),
  pageSize: z.number().int().positive().max(100).default(10),
  sortBy: z.string().default("name_en"),
  sortOrder: z.enum(["asc", "desc"]).default("asc"),
});

// Type exports
export type AmenityInput = z.infer<typeof amenitySchema>;
export type AmenityUpdateInput = z.infer<typeof amenityUpdateSchema>;
export type AmenityFilters = z.infer<typeof amenityFilterSchema>;

// Helper function to get icon component name
export function getIconName(icon: string | null | undefined): string {
  return icon || "Home"; // Default to Home icon if not specified
}