const jwt = require('jsonwebtoken');
const fetch = require('node-fetch');

async function testMembersAPI() {
  try {
    // First find the admin user to get the correct ID
    const { PrismaClient } = require('./src/generated/prisma');
    const prisma = new PrismaClient();
    
    const adminUser = await prisma.user.findFirst({
      where: { username: 'admin' }
    });
    
    if (!adminUser) {
      console.log('Admin user not found!');
      return;
    }
    
    // Create a test token with correct admin ID
    const payload = {
      id: adminUser.id,
      username: adminUser.username,
      email: adminUser.email
    };
    
    await prisma.$disconnect();

    const token = jwt.sign(payload, process.env.JWT_SECRET || 'your-secret-key-change-in-production', { expiresIn: '7d' });

    // Test fetching members
    const response = await fetch('http://localhost:3000/api/owners-associations/1/members', {
      headers: {
        'Cookie': `auth-token=${token}`,
        'Accept': 'application/json',
      }
    });
    
    console.log('Status:', response.status);
    console.log('Status Text:', response.statusText);
    
    const data = await response.json();
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (data.success && data.data) {
      const members = data.data.members || data.data;
      console.log('\nMembers found:', members.length);
      if (Array.isArray(members)) {
        members.forEach(member => {
          console.log(`- ${member.full_name} (Unit: ${member.unit_number})`);
        });
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

testMembersAPI();