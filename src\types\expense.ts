import { z } from "zod";

// Enums matching Prisma schema
export const PAYMENT_METHOD_VALUES = [
  "CASH",
  "BANK_TRANSFER", 
  "CREDIT_CARD",
  "DEBIT_CARD",
  "CHECK",
  "OTHER"
] as const;

export const EXPENSE_STATUS_VALUES = [
  "PENDING",
  "APPROVED", 
  "REJECTED"
] as const;

export const RECURRING_FREQUENCY_VALUES = [
  "MONTHLY",
  "QUARTERLY",
  "YEARLY"
] as const;

export const APPROVAL_ACTION_VALUES = [
  "APPROVED",
  "REJECTED",
  "REQUESTED_CHANGES"
] as const;

export type PaymentMethod = typeof PAYMENT_METHOD_VALUES[number];
export type ExpenseStatus = typeof EXPENSE_STATUS_VALUES[number];
export type RecurringFrequency = typeof RECURRING_FREQUENCY_VALUES[number];
export type ApprovalAction = typeof APPROVAL_ACTION_VALUES[number];

// Base types from database
export interface ExpenseCategory {
  id: number;
  name_en: string;
  name_ar: string;
  description: string | null;
  is_active: boolean;
  sort_order: number;
  created_by: number | null;
  updated_by: number | null;
  created_at: Date;
  updated_at: Date;
}

export interface ExpenseAttachment {
  id: number;
  expense_id: number;
  filename: string;
  original_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  uploaded_at: Date;
}

export interface ExpenseApproval {
  id: number;
  expense_id: number;
  user_id: number;
  action: ApprovalAction;
  comments: string | null;
  created_at: Date;
}

export interface Expense {
  id: number;
  date: Date;
  description: string;
  amount: number;
  category_id: number;
  payment_method: PaymentMethod;
  paid_by: string;
  notes: string | null;
  status: ExpenseStatus;
  is_recurring: boolean;
  recurring_frequency: RecurringFrequency | null;
  next_due_date: Date | null;
  approved_by: number | null;
  approved_at: Date | null;
  created_by: number | null;
  updated_by: number | null;
  created_at: Date;
  updated_at: Date;
}

// Extended types with relations
export interface ExpenseWithDetails extends Expense {
  category: ExpenseCategory;
  attachments: ExpenseAttachment[];
  approvals: ExpenseApproval[];
  creator?: {
    id: number;
    first_name: string;
    last_name: string;
  } | null;
  updater?: {
    id: number;
    first_name: string;
    last_name: string;
  } | null;
  approver?: {
    id: number;
    first_name: string;
    last_name: string;
  } | null;
}

// Form validation schemas
export const expenseCategoryFormSchema = z.object({
  name_en: z.string().min(1, "English name is required").max(100, "English name must be less than 100 characters"),
  name_ar: z.string().min(1, "Arabic name is required").max(100, "Arabic name must be less than 100 characters"),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
  sort_order: z.number().min(0, "Sort order must be non-negative").default(0),
});

export const expenseFormInputSchema = z.object({
  date: z.string().min(1, "Date is required"),
  description: z.string().min(1, "Description is required").max(255, "Description must be less than 255 characters"),
  amount: z.string().min(1, "Amount is required"),
  category_id: z.string().refine((val) => val !== "", "Category is required"),
  payment_method: z.enum(PAYMENT_METHOD_VALUES, {
    required_error: "Payment method is required",
    invalid_type_error: "Please select a valid payment method",
  }),
  paid_by: z.string().min(1, "Paid by is required").max(100, "Paid by must be less than 100 characters"),
  notes: z.string().optional(),
  status: z.enum(EXPENSE_STATUS_VALUES, {
    required_error: "Status is required",
    invalid_type_error: "Please select a valid status",
  }).default("PENDING"),
  is_recurring: z.boolean().default(false),
  recurring_frequency: z.enum(RECURRING_FREQUENCY_VALUES).optional(),
});

// Form schema for creating/editing expenses (client-side) with transformations
export const expenseFormSchema = expenseFormInputSchema.extend({
  date: z.string().min(1, "Date is required").transform((val) => new Date(val)),
  amount: z.string().min(1, "Amount is required").transform((val) => parseFloat(val)),
  category_id: z.string().refine((val) => val !== "", "Category is required").transform((val) => parseInt(val, 10)),
}).refine((data) => {
  if (data.is_recurring && !data.recurring_frequency) {
    return false;
  }
  return true;
}, {
  message: "Recurring frequency is required when expense is recurring",
  path: ["recurring_frequency"],
});

// API schema for validating data received from client
export const expenseApiSchema = z.object({
  date: z.string().min(1, "Date is required").transform((val) => new Date(val)),
  description: z.string().min(1, "Description is required").max(255, "Description must be less than 255 characters"),
  amount: z.number().min(0, "Amount must be a positive number"),
  category_id: z.number().min(1, "Category is required"),
  payment_method: z.enum(PAYMENT_METHOD_VALUES, {
    required_error: "Payment method is required",
    invalid_type_error: "Please select a valid payment method",
  }),
  paid_by: z.string().min(1, "Paid by is required").max(100, "Paid by must be less than 100 characters"),
  notes: z.string().optional(),
  status: z.enum(EXPENSE_STATUS_VALUES, {
    required_error: "Status is required",
    invalid_type_error: "Please select a valid status",
  }),
  is_recurring: z.boolean().default(false),
  recurring_frequency: z.enum(RECURRING_FREQUENCY_VALUES).optional(),
}).refine((data) => {
  if (data.is_recurring && !data.recurring_frequency) {
    return false;
  }
  return true;
}, {
  message: "Recurring frequency is required when expense is recurring",
  path: ["recurring_frequency"],
});

// Filter and search schemas
export const expenseFilterSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  sortBy: z.string().default("created_at"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
  search: z.string().optional(),
  category_id: z.number().optional(),
  status: z.enum(EXPENSE_STATUS_VALUES).optional(),
  payment_method: z.enum(PAYMENT_METHOD_VALUES).optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  amount_min: z.number().optional(),
  amount_max: z.number().optional(),
  paid_by: z.string().optional(),
});

// Report schemas
export const expenseReportSchema = z.object({
  type: z.enum(["summary", "detailed", "trend", "approval"]),
  date_from: z.string().min(1, "Start date is required"),
  date_to: z.string().min(1, "End date is required"),
  category_ids: z.array(z.number()).optional(),
  status: z.enum(EXPENSE_STATUS_VALUES).optional(),
  payment_method: z.enum(PAYMENT_METHOD_VALUES).optional(),
  paid_by: z.string().optional(),
  format: z.enum(["pdf", "excel", "csv"]).default("pdf"),
});

// Type exports for forms
export type ExpenseCategoryFormData = z.infer<typeof expenseCategoryFormSchema>;
export type ExpenseFormData = z.infer<typeof expenseFormSchema>;
export type ExpenseFormInputData = z.infer<typeof expenseFormInputSchema>;
export type ExpenseApiData = z.infer<typeof expenseApiSchema>;
export type ExpenseFilterData = z.infer<typeof expenseFilterSchema>;
export type ExpenseReportData = z.infer<typeof expenseReportSchema>;
