const fetch = require('node-fetch');

async function testAuthEndpoint() {
  try {
    // First, login to get auth token
    console.log('1. Logging in as admin...');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });

    if (!loginResponse.ok) {
      console.error('Login failed:', await loginResponse.text());
      return;
    }

    const loginData = await loginResponse.json();
    console.log('Login successful');
    
    // Extract auth token from cookies
    const setCookieHeader = loginResponse.headers.get('set-cookie');
    const authToken = setCookieHeader?.match(/auth-token=([^;]+)/)?.[1];
    
    if (!authToken) {
      console.error('No auth token found in response');
      return;
    }

    console.log('\n2. Testing /api/auth/me endpoint...');
    const meResponse = await fetch('http://localhost:3001/api/auth/me', {
      headers: {
        'Cookie': `auth-token=${authToken}`
      }
    });

    if (!meResponse.ok) {
      console.error('Auth/me failed:', await meResponse.text());
      return;
    }

    const meData = await meResponse.json();
    console.log('\nUser data:');
    console.log('- ID:', meData.user.id);
    console.log('- Username:', meData.user.username);
    console.log('- Email:', meData.user.email);
    
    console.log('\nPermissions:');
    const permissions = meData.user.permissions;
    const modules = Object.keys(permissions);
    console.log('- Total modules with permissions:', modules.length);
    console.log('- Modules:', modules.join(', '));
    
    console.log('\nSample module permissions:');
    if (permissions.properties) {
      console.log('- Properties:', permissions.properties);
    }
    if (permissions.users) {
      console.log('- Users:', permissions.users);
    }
    if (permissions.roles) {
      console.log('- Roles:', permissions.roles);
    }

    console.log('\n3. Testing debug endpoint...');
    const debugResponse = await fetch('http://localhost:3001/api/debug/permissions', {
      headers: {
        'Cookie': `auth-token=${authToken}`
      }
    });

    if (!debugResponse.ok) {
      console.error('Debug endpoint failed:', await debugResponse.text());
      return;
    }

    const debugData = await debugResponse.json();
    console.log('\nDebug data:');
    console.log('- Total modules:', debugData.totalModules);
    console.log('- Permission modules:', debugData.permissionModules);

  } catch (error) {
    console.error('Error:', error);
  }
}

testAuthEndpoint();