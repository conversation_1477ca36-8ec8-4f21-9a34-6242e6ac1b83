import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { contractSchema, contractFilterSchema, generateContractNumber } from "@/types/contract";
import { ApiResponseBuilder } from "@/lib/api-response";

import { serializeDecimal } from "@/lib/decimal";



import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for contracts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "contracts", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view contracts");
    }

    const searchParams = request.nextUrl.searchParams;
    
    // Parse filters
    const filters = contractFilterSchema.parse({
      property_id: searchParams.get("property_id") ? parseInt(searchParams.get("property_id")!) : undefined,
      unit_id: searchParams.get("unit_id") ? parseInt(searchParams.get("unit_id")!) : undefined,
      tenant_id: searchParams.get("tenantId") ? parseInt(searchParams.get("tenantId")!) : undefined,
      status: searchParams.get("status") || undefined,
      start_date_from: searchParams.get("start_date_from") || undefined,
      start_date_to: searchParams.get("start_date_to") || undefined,
      end_date_from: searchParams.get("end_date_from") || undefined,
      end_date_to: searchParams.get("end_date_to") || undefined,
      search: searchParams.get("search") || undefined,
    });

    // Build where clause
    const where: any = {};

    if (filters.property_id) {
      where.property_id = filters.property_id;
    }

    if (filters.unit_id) {
      where.unit_id = filters.unit_id;
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.tenant_id) {
      where.tenants = {
        some: {
          tenant_id: filters.tenant_id
        }
      };
    }

    if (filters.start_date_from || filters.start_date_to) {
      where.start_date = {};
      if (filters.start_date_from) {
        where.start_date.gte = new Date(filters.start_date_from);
      }
      if (filters.start_date_to) {
        where.start_date.lte = new Date(filters.start_date_to);
      }
    }

    if (filters.end_date_from || filters.end_date_to) {
      where.end_date = {};
      if (filters.end_date_from) {
        where.end_date.gte = new Date(filters.end_date_from);
      }
      if (filters.end_date_to) {
        where.end_date.lte = new Date(filters.end_date_to);
      }
    }

    if (filters.search) {
      where.OR = [
        { contract_number: { contains: filters.search } },
        { notes: { contains: filters.search } },
      ];
    }

    // Pagination
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const skip = (page - 1) * pageSize;

    // Sorting
    const sortBy = searchParams.get("sortBy") || "created_at";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    // Execute query with pagination
    const [contracts, total] = await Promise.all([
      db.contract.findMany({
        where,
        include: {
          property: true,
          unit: true,
          tenants: {
            include: {
              tenant: true,
            },
          },
          documents: true,
          creator: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
          updater: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
        },
        skip,
        take: pageSize,
        orderBy: { [sortBy]: sortOrder },
      }),
      db.contract.count({ where }),
    ]);

    // Transform contracts to ensure all Decimal values are serialized
    const transformedContracts = contracts.map(contract => ({
      ...serializeDecimal(contract),
      property: contract.property ? serializeDecimal(contract.property) : null,
      unit: contract.unit ? serializeDecimal(contract.unit) : null,
    }));

    return ApiResponseBuilder.success(transformedContracts, {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error) {
    console.error("Error fetching contracts:", error);
    return ApiResponseBuilder.error("Failed to fetch contracts", "INTERNAL_ERROR", 500);
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for contracts
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "contracts", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create contracts");
    }

    const body = await request.json();
    const validatedData = contractSchema.parse(body);

    // Check if property exists
    // Property check is optional since property_id can be null
    if (validatedData.property_id) {
      const property = await db.property.findUnique({
        where: { id: validatedData.property_id },
      });

      if (!property) {
        return ApiResponseBuilder.error("Property not found", "NOT_FOUND", 404);
      }
    }

    // Check if unit exists
    let unit = null;
    if (validatedData.unit_id) {
      unit = await db.unit.findUnique({
        where: { id: validatedData.unit_id },
      });

      if (!unit) {
        return ApiResponseBuilder.error("Unit not found", "NOT_FOUND", 404);
      }

      // If both property and unit are provided, verify they match
      if (validatedData.property_id && unit.property_id !== validatedData.property_id) {
        return ApiResponseBuilder.error("Unit doesn't belong to the selected property", "BAD_REQUEST", 400);
      }
    }

    // Check for overlapping active contracts for the same unit
    const overlappingContract = await db.contract.findFirst({
      where: {
        unit_id: validatedData.unit_id,
        status: "ACTIVE",
        OR: [
          {
            AND: [
              { start_date: { lte: new Date(validatedData.end_date) } },
              { end_date: { gte: new Date(validatedData.start_date) } },
            ],
          },
        ],
      },
    });

    if (overlappingContract) {
      return ApiResponseBuilder.error("There is already an active contract for this unit in the specified period", "BAD_REQUEST", 400);
    }

    // Extract tenant_ids from validated data
    const { tenant_ids, ...contractData } = validatedData;

    // Generate contract number if not provided
    const contractNumber = contractData.contract_number || 
      generateContractNumber(
        contractData.property_id || 0, 
        unit?.unit_number || "PROP", 
        new Date().getFullYear()
      );

    // Create contract with tenants
    const contract = await db.contract.create({
      data: {
        ...contractData,
        contract_number: contractNumber,
        start_date: new Date(contractData.start_date),
        end_date: new Date(contractData.end_date),
        insurance_due_date: contractData.insurance_due_date ? new Date(contractData.insurance_due_date) : null,
        created_by: decoded.id,
        updated_by: decoded.id,
        tenants: {
          create: tenant_ids.map((tenant_id, index) => ({
            tenant_id,
            is_primary: index === 0, // First tenant is primary
          })),
        },
      },
      include: {
        property: true,
        unit: true,
        tenants: {
          include: {
            tenant: true,
          },
        },
        documents: true,
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    // Update unit status if contract is active
    if (contractData.status === "ACTIVE" && contractData.unit_id) {
      await db.unit.update({
        where: { id: contractData.unit_id },
        data: { status: "RENTED" },
      });
    }

    // Transform contract to ensure all Decimal values are serialized
    const transformedContract = {
      ...serializeDecimal(contract),
      property: contract.property ? serializeDecimal(contract.property) : null,
      unit: contract.unit ? serializeDecimal(contract.unit) : null,
    };

    return ApiResponseBuilder.success(transformedContract);
  } catch (error) {
    console.error("Error creating contract:", error);
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return ApiResponseBuilder.error("Contract number already exists", "BAD_REQUEST", 400);
    }
    if (error && typeof error === 'object' && 'name' in error && error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error as any);
    }
    return ApiResponseBuilder.error("Failed to create contract", "INTERNAL_ERROR", 500);
  }
}