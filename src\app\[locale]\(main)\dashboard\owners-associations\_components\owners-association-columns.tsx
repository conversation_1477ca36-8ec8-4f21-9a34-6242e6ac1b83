"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { MoreHorizontal, Eye, Edit, Trash2, Users, FileText, Calculator } from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import type { OwnersAssociationWithRelations } from "@/types/owners-association";

export function getOwnersAssociationColumns(
  locale: string,
  t: (key: string) => string,
  tCommon: (key: string) => string,
  onRefresh: () => void
): ColumnDef<OwnersAssociationWithRelations>[] {
  const isRTL = locale === 'ar';
  const dateLocale = locale === 'ar' ? ar : enUS;

  return [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("name")} />
      ),
      cell: ({ row }) => {
        const association = row.original;
        const name = locale === 'ar' ? association.name_ar : association.name_en;
        const propertyName = locale === 'ar' 
          ? association.property?.name_ar 
          : association.property?.name_en;

        return (
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="font-medium">{name}</div>
            <div className="text-sm text-muted-foreground flex items-center gap-1">
              {propertyName}
            </div>
          </div>
        );
      },
      enableSorting: true,
      sortingFn: (rowA, rowB) => {
        const nameA = locale === 'ar' ? rowA.original.name_ar : rowA.original.name_en;
        const nameB = locale === 'ar' ? rowB.original.name_ar : rowB.original.name_en;
        return nameA.localeCompare(nameB);
      },
    },
    {
      accessorKey: "president_name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("president")} />
      ),
      cell: ({ row }) => {
        return (
          <div className={cn("font-medium", isRTL && "text-right")}>
            {row.original.president_name}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "establishment_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("establishmentDate")} />
      ),
      cell: ({ row }) => {
        const date = new Date(row.original.establishment_date);
        return (
          <div className={cn("text-sm", isRTL && "text-right")}>
            {format(date, "dd/MM/yyyy", { locale: dateLocale })}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "members_count",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("membersCount")} className="justify-center" />
      ),
      cell: ({ row }) => {
        const count = row.original._count?.members || 0;
        return (
          <div className="flex items-center gap-2 justify-center">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{count}</span>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "subscriptions_count",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("subscriptionsCount")} className="justify-center" />
      ),
      cell: ({ row }) => {
        const count = row.original._count?.subscriptions || 0;
        return (
          <div className="flex items-center gap-2 justify-center">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{count}</span>
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "contact_info",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("contactInfo")} />
      ),
      cell: ({ row }) => {
        const { contact_email, contact_phone } = row.original;
        
        if (!contact_email && !contact_phone) {
          return (
            <div className={cn("text-sm text-muted-foreground", isRTL && "text-right")}>
              {t("noContact")}
            </div>
          );
        }

        return (
          <div className={cn("space-y-1 text-sm", isRTL && "text-right")}>
            {contact_email && (
              <div className="truncate max-w-[120px]" title={contact_email}>
                {contact_email}
              </div>
            )}
            {contact_phone && (
              <div className="text-muted-foreground">{contact_phone}</div>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("status")} className="justify-center" />
      ),
      cell: ({ row }) => {
        return (
          <div className="flex justify-center">
            <Badge variant="outline">
              {t("statusActive")}
            </Badge>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      id: "actions",
      header: () => <div className="text-center">{tCommon("actions")}</div>,
      cell: ({ row }) => {
        const association = row.original;

        return (
          <div className="flex justify-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">{tCommon("openMenu")}</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align={isRTL ? "start" : "end"}>
                <DropdownMenuLabel>{tCommon("actions")}</DropdownMenuLabel>
                
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/owners-associations/${association.id}`}>
                    <Eye className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                    {tCommon("view")}
                  </Link>
                </DropdownMenuItem>
                
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/owners-associations/${association.id}/edit`}>
                    <Edit className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                    {tCommon("edit")}
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuSeparator />
                
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/owners-associations/${association.id}?tab=members`}>
                    <Users className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                    {t("manageMembers")}
                  </Link>
                </DropdownMenuItem>
                
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/owners-associations/${association.id}?tab=subscriptions`}>
                    <FileText className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                    {t("manageSubscriptions")}
                  </Link>
                </DropdownMenuItem>
                
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/owners-associations/${association.id}?tab=transactions`}>
                    <Calculator className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                    {t("manageTransactions")}
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuSeparator />
                
                <DropdownMenuItem 
                  className="text-destructive focus:text-destructive"
                  onClick={() => {
                    // TODO: Implement delete functionality
                    console.log("Delete association:", association.id);
                  }}
                >
                  <Trash2 className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                  {tCommon("delete")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];
}