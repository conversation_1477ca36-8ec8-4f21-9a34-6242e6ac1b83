const fs = require('fs');
const path = require('path');

// Map of API folders to their permission module names
const apiModules = {
  'property-owners': 'property-owners',
  'units': 'units',
  'tenants': 'tenants',
  'contracts': 'contracts',
  'invoices': 'invoices',
  'payments': 'payments',
  'owner-payouts': 'owner-payouts',
  'maintenance': 'maintenance',
  'expenses': 'expenses',
  'expense-categories': 'expense-categories',
};

function updateApiFile(filePath, moduleName) {
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  // Check if already has permission imports
  if (content.includes('getUserFromRequest') && content.includes('hasPermission')) {
    console.log(`✓ ${path.basename(filePath)} already has permissions`);
    return;
  }
  
  // Add import if not present
  if (!content.includes('getUserFromRequest')) {
    // Find the last import line
    const importMatch = content.match(/import[\s\S]*?from\s+["'][^"']+["'];/g);
    if (importMatch) {
      const lastImport = importMatch[importMatch.length - 1];
      const lastImportIndex = content.lastIndexOf(lastImport);
      const insertPosition = lastImportIndex + lastImport.length;
      
      const newImport = '\nimport { getUserFromRequest, hasPermission } from "@/lib/permissions";';
      content = content.slice(0, insertPosition) + newImport + content.slice(insertPosition);
    }
  }
  
  // Update GET method
  if (content.includes('export async function GET')) {
    const getMethodRegex = /export async function GET\([^)]*\)[^{]*{\s*try\s*{/;
    const permissionCheck = `
    // Check authentication and permissions
    const user = await getUserFromRequest(request);
    if (!user || !user.userId) {
      return ApiResponseBuilder.unauthorized();
    }

    // Check if user has READ permission for ${moduleName}
    const canRead = await hasPermission(user.userId, "${moduleName}", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view ${moduleName.replace('-', ' ')}");
    }
`;
    
    content = content.replace(getMethodRegex, (match) => {
      return match + permissionCheck;
    });
  }
  
  // Update POST method
  if (content.includes('export async function POST')) {
    // Look for existing auth code to replace
    const authPatterns = [
      /\/\/ Verify authentication[\s\S]*?\/\/ TODO:.*?\n/,
      /const token = request\.cookies\.get\("auth-token"\)[\s\S]*?}\s*\n/,
    ];
    
    let replaced = false;
    for (const pattern of authPatterns) {
      if (pattern.test(content)) {
        content = content.replace(pattern, `    // Check authentication and permissions
    const user = await getUserFromRequest(request);
    if (!user || !user.userId) {
      return ApiResponseBuilder.unauthorized();
    }

    // Check if user has CREATE permission for ${moduleName}
    const canCreate = await hasPermission(user.userId, "${moduleName}", "CREATE");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create ${moduleName.replace('-', ' ')}");
    }
`);
        replaced = true;
        break;
      }
    }
    
    if (!replaced) {
      const postMethodRegex = /export async function POST\([^)]*\)[^{]*{\s*try\s*{/;
      const permissionCheck = `
    // Check authentication and permissions
    const user = await getUserFromRequest(request);
    if (!user || !user.userId) {
      return ApiResponseBuilder.unauthorized();
    }

    // Check if user has CREATE permission for ${moduleName}
    const canCreate = await hasPermission(user.userId, "${moduleName}", "CREATE");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create ${moduleName.replace('-', ' ')}");
    }
`;
      
      content = content.replace(postMethodRegex, (match) => {
        return match + permissionCheck;
      });
    }
  }
  
  // Update PUT method
  if (content.includes('export async function PUT')) {
    const authPatterns = [
      /\/\/ Verify authentication[\s\S]*?\/\/ TODO:.*?\n/,
      /const token = request\.cookies\.get\("auth-token"\)[\s\S]*?}\s*\n/,
    ];
    
    let replaced = false;
    for (const pattern of authPatterns) {
      if (pattern.test(content)) {
        content = content.replace(pattern, `    // Check authentication and permissions
    const user = await getUserFromRequest(request);
    if (!user || !user.userId) {
      return ApiResponseBuilder.unauthorized();
    }

    // Check if user has UPDATE permission for ${moduleName}
    const canUpdate = await hasPermission(user.userId, "${moduleName}", "UPDATE");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update ${moduleName.replace('-', ' ')}");
    }
`);
        replaced = true;
        break;
      }
    }
    
    if (!replaced) {
      const putMethodRegex = /export async function PUT\([^)]*\)[^{]*{\s*try\s*{/;
      const permissionCheck = `
    // Check authentication and permissions
    const user = await getUserFromRequest(request);
    if (!user || !user.userId) {
      return ApiResponseBuilder.unauthorized();
    }

    // Check if user has UPDATE permission for ${moduleName}
    const canUpdate = await hasPermission(user.userId, "${moduleName}", "UPDATE");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update ${moduleName.replace('-', ' ')}");
    }
`;
      
      content = content.replace(putMethodRegex, (match) => {
        return match + permissionCheck;
      });
    }
  }
  
  // Update DELETE method
  if (content.includes('export async function DELETE')) {
    const authPatterns = [
      /\/\/ Verify authentication[\s\S]*?\/\/ TODO:.*?\n/,
      /const token = request\.cookies\.get\("auth-token"\)[\s\S]*?}\s*\n/,
    ];
    
    let replaced = false;
    for (const pattern of authPatterns) {
      if (pattern.test(content)) {
        content = content.replace(pattern, `    // Check authentication and permissions
    const user = await getUserFromRequest(request);
    if (!user || !user.userId) {
      return ApiResponseBuilder.unauthorized();
    }

    // Check if user has DELETE permission for ${moduleName}
    const canDelete = await hasPermission(user.userId, "${moduleName}", "DELETE");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete ${moduleName.replace('-', ' ')}");
    }
`);
        replaced = true;
        break;
      }
    }
    
    if (!replaced) {
      const deleteMethodRegex = /export async function DELETE\([^)]*\)[^{]*{\s*try\s*{/;
      const permissionCheck = `
    // Check authentication and permissions
    const user = await getUserFromRequest(request);
    if (!user || !user.userId) {
      return ApiResponseBuilder.unauthorized();
    }

    // Check if user has DELETE permission for ${moduleName}
    const canDelete = await hasPermission(user.userId, "${moduleName}", "DELETE");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete ${moduleName.replace('-', ' ')}");
    }
`;
      
      content = content.replace(deleteMethodRegex, (match) => {
        return match + permissionCheck;
      });
    }
  }
  
  // Save if modified
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Updated ${path.basename(filePath)}`);
  }
}

// Process each module
for (const [folder, module] of Object.entries(apiModules)) {
  console.log(`\nProcessing ${module}...`);
  
  const modulePath = path.join(__dirname, '..', 'src', 'app', 'api', folder);
  
  // Check main route.ts
  const mainRoute = path.join(modulePath, 'route.ts');
  if (fs.existsSync(mainRoute)) {
    updateApiFile(mainRoute, module);
  }
  
  // Check [id]/route.ts
  const idRoute = path.join(modulePath, '[id]', 'route.ts');
  if (fs.existsSync(idRoute)) {
    updateApiFile(idRoute, module);
  }
  
  // Check other subfolders
  if (fs.existsSync(modulePath)) {
    const items = fs.readdirSync(modulePath);
    for (const item of items) {
      const itemPath = path.join(modulePath, item);
      if (fs.statSync(itemPath).isDirectory() && item !== '[id]') {
        const subRoute = path.join(itemPath, 'route.ts');
        if (fs.existsSync(subRoute)) {
          updateApiFile(subRoute, module);
        }
      }
    }
  }
}

console.log('\n✅ Permission updates complete!');