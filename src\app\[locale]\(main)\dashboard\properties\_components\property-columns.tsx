"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { MoreH<PERSON>zontal, Edit, Trash, Eye, ChevronDown, ChevronRight, Building2 } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import { apiClient } from "@/lib/api-client";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialog<PERSON>ontent,
  AlertDialogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>ial<PERSON><PERSON><PERSON><PERSON>,
  AlertDialog<PERSON>itle,
} from "@/components/ui/alert-dialog";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import type { PropertyWithRelations } from "@/types/property";
import { useState } from "react";

interface ColumnTranslations {
  id: string;
  name: string;
  address: string;
  propertyType: string;
  baseRent: string;
  status: string;
  owner: string;
  totalArea: string;
  floorsCount: string;
  parkingSpaces: string;
  amenities: string;
  amenity: string;
  units?: string;
  createdAt: string;
  actions: string;
  selectAll: string;
  selectRow: string;
  openMenu: string;
  viewDetails: string;
  editProperty: string;
  deleteProperty: string;
  statusAvailable: string;
  statusRented: string;
  statusUnderMaintenance: string;
  statusOutOfService: string;
}

function formatCurrency(amount: number | string): string {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  return new Intl.NumberFormat('en-OM', {
    minimumFractionDigits: 3,
    maximumFractionDigits: 3,
  }).format(numAmount);
}

function getStatusVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
  switch (status) {
    case "AVAILABLE":
      return "default";
    case "RENTED":
      return "secondary";
    case "UNDER_MAINTENANCE":
      return "outline";
    case "OUT_OF_SERVICE":
      return "destructive";
    default:
      return "default";
  }
}

export function getPropertyColumns(
  t: ColumnTranslations,
  locale: string,
  onRefresh?: () => void
): ColumnDef<PropertyWithRelations>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label={t.selectAll}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={t.selectRow}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "expand",
      header: () => null,
      cell: ({ row }) => {
        const property = row.original;
        const unitCount = property.units?.length || 0;
        
        if (unitCount === 0) {
          return null;
        }
        
        return (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => row.toggleExpanded()}
          >
            {row.getIsExpanded() ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
          </Button>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "actions",
      header: () => <div className="text-center">{t.actions}</div>,
      cell: ({ row }) => {
        const property = row.original;
        const [showDeleteDialog, setShowDeleteDialog] = useState(false);
        const [isDeleting, setIsDeleting] = useState(false);
        const tDelete = useTranslations('properties.delete');
        const tMessages = useTranslations('properties.messages');

        const handleDelete = async () => {
          try {
            setIsDeleting(true);
            const result = await apiClient.delete(`/api/properties/${property.id}`);

            if (!result.success) {
              throw new Error(result.error?.message || tMessages('deleteError'));
            }

            toast.success(tDelete('success'));
            setShowDeleteDialog(false);
            onRefresh?.();
          } catch (error) {
            console.error("Delete error:", error);
            toast.error(error instanceof Error ? error.message : tMessages('deleteError'));
          } finally {
            setIsDeleting(false);
          }
        };

        return (
          <div className="text-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  aria-label={t.openMenu}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>{t.actions}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/properties/${property.id}`}>
                    <Eye className="mr-2 h-4 w-4" />
                    {t.viewDetails}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/properties/${property.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    {t.editProperty}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-destructive"
                  disabled={property.status === "RENTED"}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {t.deleteProperty}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{tDelete('title')}</AlertDialogTitle>
                  <AlertDialogDescription>
                    {tDelete('description', { name: locale === "ar" ? property.name_ar : property.name_en })}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>{tDelete('cancel')}</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {isDeleting ? tDelete('deleting') : tDelete('confirm')}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        );
      },
    },
    {
      accessorKey: "id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.id} className="justify-center" />
      ),
      cell: ({ row }) => (
        <div className="text-center">
          <span className="font-medium">#{row.getValue("id")}</span>
        </div>
      ),
    },
    {
      accessorKey: locale === "ar" ? "name_ar" : "name_en",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.name} />
      ),
      cell: ({ row }) => {
        const nameEn = row.original.name_en;
        const nameAr = row.original.name_ar;
        const displayName = locale === "ar" ? nameAr : nameEn;
        const altName = locale === "ar" ? nameEn : nameAr;
        
        return (
          <div className={`flex flex-col ${locale === "ar" ? "text-right" : "text-left"}`}>
            <span className="font-medium">{displayName}</span>
            <span className="text-xs text-muted-foreground">{altName}</span>
          </div>
        );
      },
    },
    {
      id: "propertyType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.propertyType} />
      ),
      cell: ({ row }) => {
        const propertyType = row.original.property_type;
        const typeName = locale === "ar" ? propertyType.name_ar : propertyType.name_en;
        
        return (
          <div className="text-center">
            <Badge variant="outline">
              {typeName}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: "base_rent",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.baseRent} />
      ),
      cell: ({ row }) => {
        const rent = row.getValue("base_rent") as string;
        return (
          <div className="text-center">
            <span className="font-medium">
              {formatCurrency(rent)} OMR
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.status} />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        let statusText = "";
        
        switch (status) {
          case "AVAILABLE":
            statusText = t.statusAvailable;
            break;
          case "RENTED":
            statusText = t.statusRented;
            break;
          case "UNDER_MAINTENANCE":
            statusText = t.statusUnderMaintenance;
            break;
          case "OUT_OF_SERVICE":
            statusText = t.statusOutOfService;
            break;
        }
        
        return (
          <div className="text-center">
            <Badge variant={getStatusVariant(status)}>
              {statusText}
            </Badge>
          </div>
        );
      },
    },
    {
      id: "owner",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.owner || "Owner"} />
      ),
      cell: ({ row }) => {
        const owner = row.original.owner;
        
        if (!owner) {
          return <span className="text-muted-foreground">-</span>;
        }
        
        const ownerName = locale === "ar" ? owner.name_ar : owner.name_en;
        
        return <span>{ownerName}</span>;
      },
    },
    {
      accessorKey: locale === "ar" ? "address_ar" : "address_en",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.address} />
      ),
      cell: ({ row }) => {
        const addressEn = row.original.address_en;
        const addressAr = row.original.address_ar;
        const displayAddress = locale === "ar" ? addressAr : addressEn;
        
        return (
          <span className="text-sm text-muted-foreground line-clamp-1">
            {displayAddress}
          </span>
        );
      },
    },
    {
      accessorKey: "total_area",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.totalArea} />
      ),
      cell: ({ row }) => {
        const area = row.getValue("total_area") as string | null;
        return area ? (
          <span>{parseFloat(area).toFixed(2)} m²</span>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      id: "amenities",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.amenities} />
      ),
      cell: ({ row }) => {
        const property = row.original;
        const amenityCount = property.amenities?.length || 0;
        
        if (amenityCount === 0) {
          return <span className="text-muted-foreground">-</span>;
        }
        
        const amenityText = amenityCount === 1 
          ? t.amenity || "amenity" 
          : t.amenities || "amenities";
        
        return (
          <Badge variant="secondary">
            {amenityCount} {amenityText}
          </Badge>
        );
      },
    },
    {
      id: "units",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.units || "Units"} />
      ),
      cell: ({ row }) => {
        const property = row.original;
        const unitCount = property.units?.length || 0;
        
        if (unitCount === 0) {
          return <span className="text-muted-foreground">-</span>;
        }
        
        return (
          <Badge variant="outline" className="gap-1">
            <Building2 className="h-3 w-3" />
            {unitCount} {unitCount === 1 ? "unit" : "units"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "created_at",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.createdAt} />
      ),
      cell: ({ row }) => {
        const date = row.getValue("created_at") as string;
        return <span>{format(new Date(date), "dd/MM/yyyy")}</span>;
      },
    },
  ];
}