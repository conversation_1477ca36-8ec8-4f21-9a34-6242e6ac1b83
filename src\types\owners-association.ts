import { z } from "zod";
import type {
  OwnersAssociation,
  AssociationMember,
  AssociationSubscription,
  SubscriptionPayment,
  AssociationTransaction,
  SubscriptionFrequency,
  SubscriptionPaymentStatus,
  TransactionType,
  PaymentMethod,
  Property,
  User,
} from "@/generated/prisma";

// ===========================================
// OWNERS ASSOCIATION SCHEMAS
// ===========================================

export const ownersAssociationSchema = z.object({
  name_en: z.string().min(2, "English name must be at least 2 characters"),
  name_ar: z.string().min(2, "Arabic name must be at least 2 characters"),
  property_id: z.number().int().min(1, "Property is required"),
  establishment_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  president_name: z.string().min(2, "President name is required"),
  management_term_duration: z.number().int().min(1, "Term duration must be at least 1 month"),
  contact_email: z.string().email("Invalid email format").optional().nullable(),
  contact_phone: z.string().regex(/^\+?\d{8,20}$/, "Invalid phone number").optional().nullable(),
});

export const ownersAssociationUpdateSchema = ownersAssociationSchema.partial();

// ===========================================
// ASSOCIATION MEMBER SCHEMAS
// ===========================================

export const associationMemberSchema = z.object({
  association_id: z.number().int().positive(),
  full_name: z.string().min(2, "Full name is required"),
  unit_number: z.string().min(1, "Unit number is required"),
  ownership_percentage: z.string().regex(/^\d{1,3}(\.\d{1,2})?$/, "Invalid percentage format"),
  phone: z.string().regex(/^\+?\d{8,20}$/, "Invalid phone number").optional().nullable(),
  email: z.string().email("Invalid email format").optional().nullable(),
  join_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  is_board_member: z.boolean().default(false),
});

export const associationMemberUpdateSchema = associationMemberSchema.partial().omit({ association_id: true });

// ===========================================
// SUBSCRIPTION SCHEMAS
// ===========================================

export const associationSubscriptionSchema = z.object({
  association_id: z.number().int().positive(),
  name_en: z.string().min(2, "English name is required"),
  name_ar: z.string().min(2, "Arabic name is required"),
  amount: z.string().regex(/^\d+(\.\d{1,3})?$/, "Invalid amount format"),
  frequency: z.enum(["MONTHLY", "QUARTERLY", "YEARLY"]),
  is_active: z.boolean().default(true),
});

export const associationSubscriptionUpdateSchema = associationSubscriptionSchema.partial().omit({ association_id: true });

// ===========================================
// SUBSCRIPTION PAYMENT SCHEMAS
// ===========================================

export const subscriptionPaymentSchema = z.object({
  subscription_id: z.number().int().positive(),
  member_id: z.number().int().positive(),
  payment_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format").optional().nullable(),
  due_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  amount_due: z.string().regex(/^\d+(\.\d{1,3})?$/, "Invalid amount format"),
  amount_paid: z.string().regex(/^\d+(\.\d{1,3})?$/, "Invalid amount format").default("0"),
  status: z.enum(["PAID", "UNPAID", "OVERDUE", "PARTIALLY_PAID"]),
  payment_method: z.enum(["CASH", "BANK_TRANSFER", "CREDIT_CARD", "DEBIT_CARD", "CHECK", "OTHER"]).optional().nullable(),
  reference_number: z.string().optional().nullable(),
  transaction_id: z.number().int().positive().optional().nullable(),
  notes: z.string().optional().nullable(),
});

export const recordPaymentSchema = z.object({
  subscription_payment_id: z.number().int().positive(),
  amount: z.string().regex(/^\d+(\.\d{1,3})?$/, "Invalid amount format"),
  payment_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  payment_method: z.enum(["CASH", "BANK_TRANSFER", "CREDIT_CARD", "DEBIT_CARD", "CHECK", "OTHER"]),
  reference_number: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  create_transaction: z.boolean().default(true), // Whether to create an income transaction
});

export const paymentInstallmentSchema = z.object({
  subscription_payment_id: z.number().int().positive(),
  transaction_id: z.number().int().positive(),
  amount: z.string().regex(/^\d+(\.\d{1,3})?$/, "Invalid amount format"),
  payment_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  payment_method: z.enum(["CASH", "BANK_TRANSFER", "CREDIT_CARD", "DEBIT_CARD", "CHECK", "OTHER"]),
  reference_number: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
});

// ===========================================
// TRANSACTION SCHEMAS
// ===========================================

export const associationTransactionSchema = z.object({
  association_id: z.number().int().positive(),
  transaction_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  type: z.enum(["EXPENSE", "INCOME"]),
  category: z.string().min(1, "Category is required"),
  description: z.string().min(5, "Description must be at least 5 characters"),
  amount: z.string().regex(/^\d+(\.\d{1,3})?$/, "Invalid amount format"),
  payment_method: z.enum(["CASH", "BANK_TRANSFER", "CREDIT_CARD", "DEBIT_CARD", "CHECK", "OTHER"]),
  notes: z.string().optional().nullable(),
  attachment_url: z.string().optional().nullable(),
  attachment_name: z.string().optional().nullable(),
});

export const associationTransactionUpdateSchema = associationTransactionSchema.partial().omit({ association_id: true });

// ===========================================
// FILTER SCHEMAS
// ===========================================

export const ownersAssociationFilterSchema = z.object({
  search: z.string().optional(),
  property_id: z.number().optional(),
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  sortBy: z.string().default("created_at"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const memberFilterSchema = z.object({
  search: z.string().optional(),
  is_board_member: z.boolean().optional(),
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  sortBy: z.string().default("full_name"),
  sortOrder: z.enum(["asc", "desc"]).default("asc"),
});

export const transactionFilterSchema = z.object({
  search: z.string().optional(),
  type: z.enum(["EXPENSE", "INCOME"]).optional(),
  category: z.string().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  sortBy: z.string().default("transaction_date"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const paymentFilterSchema = z.object({
  status: z.enum(["PAID", "UNPAID", "OVERDUE", "PARTIALLY_PAID"]).optional(),
  member_id: z.number().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  sortBy: z.string().default("due_date"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// ===========================================
// TYPE EXPORTS
// ===========================================

export type OwnersAssociationInput = z.infer<typeof ownersAssociationSchema>;
export type OwnersAssociationUpdateInput = z.infer<typeof ownersAssociationUpdateSchema>;
export type AssociationMemberInput = z.infer<typeof associationMemberSchema>;
export type AssociationMemberUpdateInput = z.infer<typeof associationMemberUpdateSchema>;
export type AssociationSubscriptionInput = z.infer<typeof associationSubscriptionSchema>;
export type AssociationSubscriptionUpdateInput = z.infer<typeof associationSubscriptionUpdateSchema>;
export type SubscriptionPaymentInput = z.infer<typeof subscriptionPaymentSchema>;
export type RecordPaymentInput = z.infer<typeof recordPaymentSchema>;
export type PaymentInstallmentInput = z.infer<typeof paymentInstallmentSchema>;
export type AssociationTransactionInput = z.infer<typeof associationTransactionSchema>;
export type AssociationTransactionUpdateInput = z.infer<typeof associationTransactionUpdateSchema>;

export type OwnersAssociationFilter = z.infer<typeof ownersAssociationFilterSchema>;
export type MemberFilter = z.infer<typeof memberFilterSchema>;
export type TransactionFilter = z.infer<typeof transactionFilterSchema>;
export type PaymentFilter = z.infer<typeof paymentFilterSchema>;

// ===========================================
// EXTENDED TYPES WITH RELATIONS
// ===========================================

export interface OwnersAssociationWithRelations extends OwnersAssociation {
  property: Property;
  members: AssociationMember[];
  subscriptions: AssociationSubscription[];
  creator?: User | null;
  updater?: User | null;
  _count?: {
    members: number;
    subscriptions: number;
    transactions: number;
  };
}

export interface AssociationMemberWithRelations extends AssociationMember {
  association: OwnersAssociation;
  subscription_payments: SubscriptionPayment[];
  _count?: {
    subscription_payments: number;
  };
}

export interface AssociationSubscriptionWithRelations extends AssociationSubscription {
  association: OwnersAssociation;
  payments: SubscriptionPayment[];
  _count?: {
    payments: number;
  };
}

export interface SubscriptionPaymentWithRelations extends SubscriptionPayment {
  subscription: AssociationSubscription;
  member: AssociationMember;
  transaction?: AssociationTransaction | null;
  creator?: User | null;
}

// Note: SubscriptionPaymentInstallment table doesn't exist in current schema
// If needed, add the table to schema.prisma and regenerate types

export interface AssociationTransactionWithRelations extends AssociationTransaction {
  association: OwnersAssociation;
  member?: AssociationMember | null;
  creator?: User | null;
  subscription_payments?: SubscriptionPayment[];
}

// ===========================================
// HELPER FUNCTIONS
// ===========================================

export function calculateOverdueStatus(dueDate: Date): boolean {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const due = new Date(dueDate);
  due.setHours(0, 0, 0, 0);
  return due < today;
}

export function calculateTotalOwnership(members: AssociationMember[]): number {
  return members.reduce((total, member) => {
    const percentage = typeof member.ownership_percentage === "string"
      ? parseFloat(member.ownership_percentage)
      : typeof member.ownership_percentage === "object" && member.ownership_percentage !== null
      ? parseFloat(member.ownership_percentage.toString())
      : Number(member.ownership_percentage);
    return total + percentage;
  }, 0);
}

export function validateTotalOwnership(members: AssociationMember[]): boolean {
  const total = calculateTotalOwnership(members);
  return Math.abs(total - 100) < 0.01; // Allow for small floating point errors
}

// formatCurrency function moved to @/lib/localization.ts for consistency
// Use: import { formatCurrency } from "@/lib/utils";

// ===========================================
// TRANSACTION CATEGORIES
// ===========================================

export const EXPENSE_CATEGORIES = [
  { value: "maintenance", label_en: "Maintenance", label_ar: "الصيانة" },
  { value: "cleaning", label_en: "Cleaning", label_ar: "التنظيف" },
  { value: "security", label_en: "Security", label_ar: "الأمن" },
  { value: "utilities", label_en: "Utilities", label_ar: "المرافق" },
  { value: "supplies", label_en: "Supplies", label_ar: "اللوازم" },
  { value: "fuel", label_en: "Fuel", label_ar: "الوقود" },
  { value: "insurance", label_en: "Insurance", label_ar: "التأمين" },
  { value: "legal", label_en: "Legal", label_ar: "قانوني" },
  { value: "other", label_en: "Other", label_ar: "أخرى" },
] as const;

export const INCOME_CATEGORIES = [
  { value: "subscription", label_en: "Subscription", label_ar: "الاشتراك" },
  { value: "ad_revenue", label_en: "Advertisement Revenue", label_ar: "إيرادات الإعلانات" },
  { value: "donations", label_en: "Donations", label_ar: "التبرعات" },
  { value: "rental", label_en: "Rental Income", label_ar: "دخل الإيجار" },
  { value: "fines", label_en: "Fines", label_ar: "الغرامات" },
  { value: "other", label_en: "Other", label_ar: "أخرى" },
] as const;

export type ExpenseCategory = typeof EXPENSE_CATEGORIES[number]["value"];
export type IncomeCategory = typeof INCOME_CATEGORIES[number]["value"];