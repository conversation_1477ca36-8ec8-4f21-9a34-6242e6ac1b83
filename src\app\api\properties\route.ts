import { NextRequest } from "next/server";
import { z } from "zod";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { propertySchema, type PropertyFilters } from "@/types/property";
import { Decimal } from "@prisma/client/runtime/library";

// GET /api/properties - List all properties with filtering
export async function GET(request: NextRequest) {
  console.log("Properties API - GET request received");
  
  try {
    // Get token from cookie
    const token = request.cookies.get("auth-token")?.value;
    if (!token) {
      return ApiResponseBuilder.unauthorized("No authentication token provided");
    }
    
    // Verify token
    const decoded = verifyToken(token);
    if (!decoded) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions and check if user has READ permission for properties
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "properties", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view properties");
    }
    const { searchParams } = new URL(request.url);
    console.log("Search params:", Object.fromEntries(searchParams.entries()));
    
    // Parse query parameters
    const filters: PropertyFilters = {
      search: searchParams.get("search") || undefined,
      property_type_id: searchParams.get("property_type_id") ? parseInt(searchParams.get("property_type_id")!) : undefined,
      status: searchParams.get("status") as any || undefined,
      min_rent: searchParams.get("min_rent") ? parseFloat(searchParams.get("min_rent")!) : undefined,
      max_rent: searchParams.get("max_rent") ? parseFloat(searchParams.get("max_rent")!) : undefined,
      page: parseInt(searchParams.get("page") || "1"),
      pageSize: parseInt(searchParams.get("pageSize") || "10"),
      sortBy: searchParams.get("sortBy") || "created_at",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
    };

    // Build where clause
    const where: any = {};
    
    if (filters.search) {
      where.OR = [
        { name_en: { contains: filters.search } },
        { name_ar: { contains: filters.search } },
        { address_en: { contains: filters.search } },
        { address_ar: { contains: filters.search } },
      ];
    }
    
    if (filters.property_type_id) {
      where.property_type_id = filters.property_type_id;
    }
    
    if (filters.status) {
      where.status = filters.status;
    }
    
    if (filters.min_rent !== undefined || filters.max_rent !== undefined) {
      where.base_rent = {};
      if (filters.min_rent !== undefined) {
        where.base_rent.gte = filters.min_rent;
      }
      if (filters.max_rent !== undefined) {
        where.base_rent.lte = filters.max_rent;
      }
    }

    // Calculate pagination
    const skip = ((filters.page || 1) - 1) * (filters.pageSize || 10);
    const take = filters.pageSize || 10;

    // Build order by clause
    const orderBy: any = {};
    if (filters.sortBy === "property_type") {
      orderBy.property_type = { name_en: filters.sortOrder };
    } else {
      orderBy[filters.sortBy || "created_at"] = filters.sortOrder;
    }
    
    console.log("Fetching properties with where clause:", JSON.stringify(where, null, 2));
    console.log("Order by:", orderBy);
    console.log("Skip:", skip, "Take:", take);
    
    // Fetch properties with relations
    const [properties, total] = await Promise.all([
      db.property.findMany({
        where,
        include: {
          property_type: true,
          amenities: {
            include: {
              amenity: true,
            },
          },
          owner: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
            },
          },
          units: {
            select: {
              id: true,
              unit_number: true,
              unit_name_en: true,
              unit_name_ar: true,
              status: true,
              rent_amount: true,
              floor_number: true,
              rooms_count: true,
              bathrooms_count: true,
            },
          },
          creator: true,
          updater: true,
        },
        orderBy,
        skip,
        take,
      }),
      db.property.count({ where }),
    ]);
    
    const totalPages = Math.ceil(total / take);
    
    console.log("Properties fetched successfully:", {
      count: properties.length,
      total,
      totalPages
    });
    
    // Transform properties to ensure all Decimal values are serialized
    const transformedProperties = properties.map(property => ({
      ...property,
      base_rent: property.base_rent.toString(),
      total_area: property.total_area?.toString() || null,
      units: property.units.map(unit => ({
        ...unit,
        rent_amount: unit.rent_amount.toString()
      })),
      amenities: property.amenities.map(pa => pa.amenity)
    }));
    
    return ApiResponseBuilder.success(transformedProperties, {
      page: filters.page || 1,
      pageSize: take,
      total,
      totalPages,
    });
  } catch (error) {
    console.error("Properties API Error:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch properties",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined
    );
  }
}

// POST /api/properties - Create a new property
export async function POST(request: NextRequest) {
  console.log("\n=== PROPERTY CREATION API STARTED ===");
  try {
    // Get token from cookie
    const token = request.cookies.get("auth-token")?.value;
    if (!token) {
      console.log("❌ No auth token provided");
      return ApiResponseBuilder.unauthorized("No authentication token provided");
    }
    
    // Verify token
    const decoded = verifyToken(token);
    if (!decoded) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }
    
    console.log("✅ Authentication successful, user ID:", decoded.id);

    // Get user permissions and check if user has CREATE permission for properties
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "properties", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create properties");
    }
    
    const body = await request.json();
    console.log("Property creation request body:", body);
    
    // Validate the request body
    const validationResult = propertySchema.safeParse(body);
    if (!validationResult.success) {
      console.log("Validation failed:", validationResult.error);
      return ApiResponseBuilder.validationError(validationResult.error);
    }

    const validatedData = validationResult.data;
    console.log("Validated data:", validatedData);

    // Check if property type exists
    const propertyType = await db.propertyType.findUnique({
      where: { id: validatedData.property_type_id },
    });

    if (!propertyType) {
      return ApiResponseBuilder.badRequest(
        "Invalid property type",
        { property_type_id: validatedData.property_type_id },
        request.url
      );
    }

    // Convert base_rent to Decimal
    const baseRent = new Decimal(validatedData.base_rent);
    const totalArea = validatedData.total_area ? new Decimal(validatedData.total_area) : null;

    // Verify owner exists if provided
    let ownerId = null;
    if (validatedData.owner_id && validatedData.owner_id > 0) {
      const owner = await db.propertyOwner.findUnique({
        where: { id: validatedData.owner_id },
      });
      
      if (!owner) {
        console.log("Owner not found with ID:", validatedData.owner_id);
        return ApiResponseBuilder.badRequest(
          "Invalid owner ID",
          { owner_id: validatedData.owner_id },
          request.url
        );
      }
      ownerId = validatedData.owner_id;
      console.log("Owner validated:", owner);
    }

    // Create the property with direct owner assignment
    console.log("Creating property with direct owner assignment...");
    const property = await db.property.create({
      data: {
        name_en: validatedData.name_en,
        name_ar: validatedData.name_ar,
        address_en: validatedData.address_en,
        address_ar: validatedData.address_ar,
        property_type_id: validatedData.property_type_id,
        owner_id: ownerId, // Direct owner assignment
        base_rent: baseRent,
        status: validatedData.status,
        total_area: totalArea,
        floors_count: validatedData.floors_count === 0 ? null : validatedData.floors_count,
        parking_spaces: validatedData.parking_spaces === 0 ? null : validatedData.parking_spaces,
        created_by: decoded.id,
        updated_by: decoded.id,
      },
      include: {
        property_type: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
          },
        },
        owner: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
          },
        },
        creator: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    console.log("Property created successfully with direct owner:", property);
    
    // Transform property to ensure all Decimal values are serialized
    const transformedProperty = {
      ...property,
      base_rent: property.base_rent.toString(),
      total_area: property.total_area?.toString() || null
    };
    
    return ApiResponseBuilder.success(transformedProperty);
  } catch (error) {
    console.error("Create Property Error:", error);
    return ApiResponseBuilder.error(
      "Failed to create property",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}
