"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  MoreHorizontal, 
  Eye, 
  CheckCircle, 
  XCircle, 
  DollarSign,
  FileText,
  Ban
} from "lucide-react";
import { OwnerPayoutWithRelations } from "@/types/owner-payout";
import { formatOMR } from "@/lib/format";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { ApprovePayoutDialog } from "./approve-payout-dialog";
import { PayPayoutDialog } from "./pay-payout-dialog";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";

interface ColumnTranslations {
  payoutNumber: string;
  owner: string;
  payoutDate: string;
  period: string;
  rentCollected: string;
  managementFee: string;
  netAmount: string;
  status: string;
  actions: string;
  approve: string;
  reject: string;
  markAsPaid: string;
  view: string;
  statusPending: string;
  statusApproved: string;
  statusPaid: string;
  statusCancelled: string;
}

export function getOwnerPayoutColumns(
  t: ColumnTranslations,
  locale: string,
  onRefresh?: () => void
): ColumnDef<OwnerPayoutWithRelations>[] {
  return [
    {
      accessorKey: "payout_number",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.payoutNumber} />
      ),
      cell: ({ row }) => {
        return (
          <Link 
            href={`/${locale}/dashboard/owner-payouts/${row.original.id}`}
            className="font-medium text-primary hover:underline"
          >
            {row.original.payout_number}
          </Link>
        );
      },
    },
    {
      accessorKey: locale === "ar" ? "owner.name_ar" : "owner.name_en",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.owner} />
      ),
      cell: ({ row }) => {
        const owner = row.original.owner;
        if (!owner) return "—";
        
        const name = locale === "ar" ? owner.name_ar : owner.name_en;
        
        return (
          <div>
            <div className="font-medium">{name}</div>
            {owner.email && (
              <div className="text-sm text-muted-foreground">{owner.email}</div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "payout_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.payoutDate} />
      ),
      cell: ({ row }) => {
        return new Date(row.original.payout_date).toLocaleDateString();
      },
    },
    {
      accessorKey: "period",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.period} />
      ),
      cell: ({ row }) => {
        const start = new Date(row.original.period_start).toLocaleDateString();
        const end = new Date(row.original.period_end).toLocaleDateString();
        return (
          <div className="text-sm">
            {start} - {end}
          </div>
        );
      },
    },
    {
      accessorKey: "total_rent_collected",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.rentCollected} />
      ),
      cell: ({ row }) => {
        return formatOMR(row.original.total_rent_collected.toString());
      },
    },
    {
      accessorKey: "management_fee",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.managementFee} />
      ),
      cell: ({ row }) => {
        return (
          <span className="text-muted-foreground">
            -{formatOMR(row.original.management_fee.toString())}
          </span>
        );
      },
    },
    {
      accessorKey: "net_amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.netAmount} />
      ),
      cell: ({ row }) => {
        return (
          <span className="font-medium">
            {formatOMR(row.original.net_amount.toString())}
          </span>
        );
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.status} />
      ),
      cell: ({ row }) => {
        const status = row.original.status;
        
        const statusConfig = {
          PENDING: { variant: "secondary" as const, icon: FileText },
          APPROVED: { variant: "default" as const, icon: CheckCircle },
          PAID: { variant: "default" as const, icon: DollarSign },
          CANCELLED: { variant: "destructive" as const, icon: Ban },
        };
        
        const config = statusConfig[status];
        const Icon = config.icon;
        const statusLabels = {
          PENDING: t.statusPending,
          APPROVED: t.statusApproved,
          PAID: t.statusPaid,
          CANCELLED: t.statusCancelled,
        };
        
        return (
          <Badge variant={config.variant} className="gap-1">
            <Icon className="h-3 w-3" />
            {statusLabels[status]}
          </Badge>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className="text-center">{t.actions}</div>,
      cell: ({ row }) => {
        const payout = row.original;
        const [showApproveDialog, setShowApproveDialog] = useState(false);
        const [showPayDialog, setShowPayDialog] = useState(false);

        return (
          <div className="text-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>{t.actions}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/owner-payouts/${payout.id}`}>
                    <Eye className="mr-2 h-4 w-4" />
                    {t.view}
                  </Link>
                </DropdownMenuItem>
              
              {payout.status === "PENDING" && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setShowApproveDialog(true)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    {t.approve}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    className="text-destructive"
                    onClick={() => setShowApproveDialog(true)}
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    {t.reject}
                  </DropdownMenuItem>
                </>
              )}
              
              {payout.status === "APPROVED" && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setShowPayDialog(true)}>
                    <DollarSign className="mr-2 h-4 w-4" />
                    {t.markAsPaid}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

            <ApprovePayoutDialog
              payout={payout}
              open={showApproveDialog}
              onOpenChange={setShowApproveDialog}
            />
            
            <PayPayoutDialog
              payout={payout}
              open={showPayDialog}
              onOpenChange={setShowPayDialog}
            />
          </div>
        );
      },
    },
  ];
}