"use client";

import { useState, useEffect, useCallback } from "react";
import { useTranslations, useLocale } from "next-intl";
import { toast } from "sonner";
import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Users, 
  Crown,
  Phone,
  Mail,
  Building
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useRTL } from "@/hooks/use-rtl";
import { apiClient } from "@/lib/api-client";
import type { AssociationMemberWithRelations } from "@/types/owners-association";
import { AddMemberForm } from "./add-member-form";
import { EditMemberForm } from "./edit-member-form";
import { useRouter } from "next/navigation";

interface AssociationMembersTableProps {
  associationId: number;
}

export function AssociationMembersTable({ associationId }: AssociationMembersTableProps) {
  const locale = useLocale();
  const { isRTL } = useRTL();
  const router = useRouter();
  const t = useTranslations("ownersAssociations.members");
  const tCommon = useTranslations("common");
  const dateLocale = locale === 'ar' ? ar : enUS;
  
  const [members, setMembers] = useState<AssociationMemberWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingMember, setEditingMember] = useState<AssociationMemberWithRelations | null>(null);

  const fetchMembers = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        ...(searchTerm && { search: searchTerm }),
      });

      const response = await apiClient.get(
        `/api/owners-associations/${associationId}/members?${params}`
      );
      
      if (response.success) {
        setMembers(response.data.members || response.data);
      }
    } catch (error) {
      console.error("Error fetching members:", error);
      toast.error(t("serverError"));
    } finally {
      setLoading(false);
    }
  }, [associationId, searchTerm, t]);

  useEffect(() => {
    fetchMembers();
  }, [fetchMembers]);

  const handleDeleteMember = async (memberId: number) => {
    try {
      await apiClient.delete(`/api/owners-associations/${associationId}/members/${memberId}`);
      toast.success(t("deleteSuccess"));
      fetchMembers();
    } catch (error) {
      console.error("Error deleting member:", error);
      toast.error(t("deleteError"));
    }
  };

  const columns: ColumnDef<AssociationMemberWithRelations>[] = [
    {
      accessorKey: "full_name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("fullName")} />
      ),
      cell: ({ row }) => {
        const member = row.original;
        return (
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className={cn("font-medium flex items-center gap-2", isRTL && "flex-row-reverse")}>
              {member.is_board_member && (
                <Crown className="h-4 w-4 text-yellow-500" />
              )}
              {member.full_name}
            </div>
            <div className="text-sm text-muted-foreground">
              {t("unitNumber")}: {member.unit_number}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "ownership_percentage",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("ownershipPercentage")} />
      ),
      cell: ({ row }) => {
        const percentage = row.original.ownership_percentage 
          ? parseFloat(row.original.ownership_percentage.toString())
          : 0;
        return (
          <div className={cn("font-medium", isRTL && "text-right")}>
            {percentage.toFixed(2)}%
          </div>
        );
      },
    },
    {
      accessorKey: "contact_info",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("contactInfo")} />
      ),
      cell: ({ row }) => {
        const { phone, email } = row.original;
        
        return (
          <div className={cn("space-y-1 text-sm", isRTL && "text-right")}>
            {phone && (
              <div className={cn("flex items-center gap-1", isRTL && "flex-row-reverse")}>
                <Phone className="h-3 w-3 text-muted-foreground" />
                {phone}
              </div>
            )}
            {email && (
              <div className={cn("flex items-center gap-1 truncate max-w-[150px]", isRTL && "flex-row-reverse")}>
                <Mail className="h-3 w-3 text-muted-foreground" />
                <span title={email}>{email}</span>
              </div>
            )}
            {!phone && !email && (
              <span className="text-muted-foreground">{t("noContact")}</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "join_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("joinDate")} />
      ),
      cell: ({ row }) => {
        if (!row.original.join_date) {
          return <div className={cn("text-sm", isRTL && "text-right")}>-</div>;
        }
        const date = new Date(row.original.join_date);
        return (
          <div className={cn("text-sm", isRTL && "text-right")}>
            {format(date, "dd/MM/yyyy", { locale: dateLocale })}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("status")} />
      ),
      cell: ({ row }) => {
        const member = row.original;
        
        return (
          <div className="space-y-1">
            <Badge variant={member.is_board_member ? "default" : "outline"}>
              {member.is_board_member ? t("statusBoardMember") : t("statusMember")}
            </Badge>
            {member.is_board_member && (
              <Badge variant="outline" className="text-xs">
                {t("boardMember")}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className={cn("text-right", isRTL && "text-left")}>{tCommon("actions")}</div>,
      cell: ({ row }) => {
        const member = row.original;

        return (
          <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditingMember(member)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeleteMember(member.id)}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: members,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  // Calculate total ownership percentage
  const totalOwnership = members.reduce((total, member) => {
    const percentage = member.ownership_percentage 
      ? parseFloat(member.ownership_percentage.toString())
      : 0;
    return total + percentage;
  }, 0);

  const ownershipValid = Math.abs(totalOwnership - 100) < 0.01;

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-96" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[400px] w-full" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className={cn("flex items-center justify-between", isRTL && "flex-row-reverse")}>
          <div>
            <CardTitle className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
              <Users className="h-5 w-5" />
              {t("title")}
            </CardTitle>
            <CardDescription>
              {t("description")}
            </CardDescription>
          </div>
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                {t("addMember")}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-lg">
              <DialogHeader>
                <DialogTitle>{t("addMemberDialog.title")}</DialogTitle>
                <DialogDescription>
                  {t("addMemberDialog.description")}
                </DialogDescription>
              </DialogHeader>
              <AddMemberForm 
                associationId={associationId}
                onSuccess={() => {
                  setShowAddDialog(false);
                  fetchMembers();
                }}
                onCancel={() => setShowAddDialog(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary Stats */}
        <div className="grid gap-4 md:grid-cols-3">
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="text-sm font-medium">{t("totalMembers")}</div>
            <div className="text-2xl font-bold">{members.length}</div>
          </div>
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="text-sm font-medium">{t("totalOwnership")}</div>
            <div className={cn("text-2xl font-bold", ownershipValid ? "text-green-600" : "text-red-600")}>
              {totalOwnership.toFixed(2)}%
            </div>
          </div>
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="text-sm font-medium">{t("boardMembers")}</div>
            <div className="text-2xl font-bold">
              {members.filter(m => m.is_board_member).length}
            </div>
          </div>
        </div>

        {!ownershipValid && (
          <div className="rounded-md bg-yellow-50 p-4 border border-yellow-200">
            <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
              <Building className="h-4 w-4 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">
                {t("ownershipWarning")}
              </span>
            </div>
          </div>
        )}

        {/* Search */}
        <div className="relative max-w-sm">
          <Search className={cn(
            "absolute top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground",
            isRTL ? "right-3" : "left-3"
          )} />
          <Input
            placeholder={t("searchMembers")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={cn(isRTL ? "pr-9" : "pl-9")}
          />
        </div>

        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id} className={cn(isRTL && "text-right")}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow 
                    key={row.id} 
                    className="hover:bg-muted/50 cursor-pointer"
                    onClick={(e) => {
                      // Don't navigate if clicking on action buttons
                      const target = e.target as HTMLElement;
                      if (target.closest('button')) return;
                      
                      router.push(`/${locale}/dashboard/owners-associations/${associationId}/members/${row.original.id}`);
                    }}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className={cn(isRTL && "text-right")}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    {t("noMembers")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* Edit Member Dialog */}
      <Dialog open={!!editingMember} onOpenChange={(open) => !open && setEditingMember(null)}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>{t("editMemberDialog.title")}</DialogTitle>
            <DialogDescription>
              {t("editMemberDialog.description")}
            </DialogDescription>
          </DialogHeader>
          {editingMember && (
            <EditMemberForm 
              associationId={associationId}
              member={editingMember}
              onSuccess={() => {
                setEditingMember(null);
                fetchMembers();
              }}
              onCancel={() => setEditingMember(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
}