import { NextRequest } from "next/server";

import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { unitSchema, type UnitFilters } from "@/types/unit";
import { Decimal } from "@prisma/client/runtime/library";



import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
// GET /api/units - List all units with filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for units
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "units", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view units");
    }

    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const filters: UnitFilters = {
      search: searchParams.get("search") || undefined,
      property_id: searchParams.get("property_id") ? parseInt(searchParams.get("property_id")!) : undefined,
      status: searchParams.get("status") as any || undefined,
      min_rent: searchParams.get("min_rent") ? parseFloat(searchParams.get("min_rent")!) : undefined,
      max_rent: searchParams.get("max_rent") ? parseFloat(searchParams.get("max_rent")!) : undefined,
      min_rooms: searchParams.get("min_rooms") ? parseInt(searchParams.get("min_rooms")!) : undefined,
      max_rooms: searchParams.get("max_rooms") ? parseInt(searchParams.get("max_rooms")!) : undefined,
      floor_number: searchParams.get("floor_number") ? parseInt(searchParams.get("floor_number")!) : undefined,
      page: parseInt(searchParams.get("page") || "1"),
      pageSize: parseInt(searchParams.get("pageSize") || "10"),
      sortBy: searchParams.get("sortBy") || "created_at",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
    };

    // Build where clause
    const where: any = {};
    
    if (filters.search) {
      where.OR = [
        { unit_number: { contains: filters.search } },
        { unit_name_en: { contains: filters.search } },
        { unit_name_ar: { contains: filters.search } },
        { description_en: { contains: filters.search } },
        { description_ar: { contains: filters.search } },
      ];
    }
    
    if (filters.property_id) {
      where.property_id = filters.property_id;
    }
    
    if (filters.status) {
      where.status = filters.status;
    }
    
    if (filters.min_rent !== undefined || filters.max_rent !== undefined) {
      where.rent_amount = {};
      if (filters.min_rent !== undefined) {
        where.rent_amount.gte = filters.min_rent;
      }
      if (filters.max_rent !== undefined) {
        where.rent_amount.lte = filters.max_rent;
      }
    }
    
    if (filters.min_rooms !== undefined || filters.max_rooms !== undefined) {
      where.rooms_count = {};
      if (filters.min_rooms !== undefined) {
        where.rooms_count.gte = filters.min_rooms;
      }
      if (filters.max_rooms !== undefined) {
        where.rooms_count.lte = filters.max_rooms;
      }
    }
    
    if (filters.floor_number !== undefined) {
      where.floor_number = filters.floor_number;
    }

    // Calculate pagination
    const skip = ((filters.page || 1) - 1) * (filters.pageSize || 10);
    const take = filters.pageSize || 10;

    // Build order by clause
    const orderBy: any = {};
    if (filters.sortBy === "property") {
      orderBy.property = { name_en: filters.sortOrder };
    } else {
      orderBy[filters.sortBy || "created_at"] = filters.sortOrder;
    }
    
    // Fetch units with relations
    const [units, total] = await Promise.all([
      db.unit.findMany({
        where,
        include: {
          property: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
              property_type: {
                select: {
                  id: true,
                  name_en: true,
                  name_ar: true,
                },
              },
            },
          },
          amenities: {
            include: {
              amenity: true,
            },
          },
          creator: {
            select: {
              id: true,
              username: true,
              first_name: true,
              last_name: true,
            },
          },
          updater: {
            select: {
              id: true,
              username: true,
              first_name: true,
              last_name: true,
            },
          },
        },
        orderBy,
        skip,
        take,
      }),
      db.unit.count({ where }),
    ]);
    
    const totalPages = Math.ceil(total / take);
    
    // Transform the units data
    const transformedUnits = units.map(unit => ({
      ...unit,
      rent_amount: unit.rent_amount.toString(),
      area: unit.area?.toString() || null,
      amenities: unit.amenities.map(ua => ua.amenity),
    }));
    
    return ApiResponseBuilder.success(transformedUnits, {
      page: filters.page || 1,
      pageSize: take,
      total,
      totalPages,
    });
  } catch (error) {
    console.error("Units API Error:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch units",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// POST /api/units - Create a new unit
export async function POST(request: NextRequest) {
  try {
        // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has CREATE permission for units
    const canCreate = hasPermission(userPermissions, "units", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create units");
    }
    
    const body = await request.json();
    
    // Validate the request body
    const validationResult = unitSchema.safeParse(body);
    if (!validationResult.success) {
      return ApiResponseBuilder.validationError(validationResult.error);
    }

    const validatedData = validationResult.data;

    // Check if property exists
    const property = await db.property.findUnique({
      where: { id: validatedData.property_id },
    });

    if (!property) {
      return ApiResponseBuilder.badRequest(
        "Invalid property",
        { property_id: validatedData.property_id },
        request.url
      );
    }

    // Check if unit number is unique for this property
    const existingUnit = await db.unit.findUnique({
      where: {
        property_id_unit_number: {
          property_id: validatedData.property_id,
          unit_number: validatedData.unit_number,
        },
      },
    });

    if (existingUnit) {
      return ApiResponseBuilder.conflict(
        "Unit number already exists for this property",
        { unit_number: validatedData.unit_number },
        request.url
      );
    }

    // Prepare create data
    const { amenity_ids, ...unitData } = validatedData;
    const createData: any = {
      ...unitData,
      created_by: decoded.id,
      updated_by: decoded.id,
    };

    // Convert numeric fields to Decimal
    createData.rent_amount = new Decimal(validatedData.rent_amount);
    if (validatedData.area) {
      createData.area = new Decimal(validatedData.area);
    }
    
    // Handle nullable integer fields - convert 0 to null
    if (validatedData.floor_number === 0) {
      createData.floor_number = null;
    }
    if (validatedData.rooms_count === 0) {
      createData.rooms_count = null;
    }
    if (validatedData.majalis_count === 0) {
      createData.majalis_count = null;
    }
    if (validatedData.bathrooms_count === 0) {
      createData.bathrooms_count = null;
    }

    // Add amenities if provided
    if (amenity_ids && amenity_ids.length > 0) {
      createData.amenities = {
        create: amenity_ids.map(amenity_id => ({
          amenity_id,
        })),
      };
    }

    // Create the unit
    const unit = await db.unit.create({
      data: createData,
      include: {
        property: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            property_type: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
              },
            },
          },
        },
        amenities: {
          include: {
            amenity: true,
          },
        },
        creator: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    // Transform the response
    const transformedUnit = {
      ...unit,
      rent_amount: unit.rent_amount.toString(),
      area: unit.area?.toString() || null,
      amenities: unit.amenities.map(ua => ua.amenity),
    };

    return ApiResponseBuilder.success(transformedUnit);
  } catch (error) {
    console.error("Create Unit Error:", error);
    return ApiResponseBuilder.error(
      "Failed to create unit",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}