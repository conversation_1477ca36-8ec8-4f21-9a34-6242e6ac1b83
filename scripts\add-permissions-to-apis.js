const fs = require('fs');
const path = require('path');

// Map of API folders to their permission module names
const apiModules = {
  'property-types': 'property-types',
  'property-owners': 'property-owners',
  'units': 'units',
  'tenants': 'tenants',
  'contracts': 'contracts',
  'invoices': 'invoices',
  'payments': 'payments',
  'owner-payouts': 'owner-payouts',
  'maintenance': 'maintenance',
  'expenses': 'expenses',
  'expense-categories': 'expense-categories',
};

// List all API files that need permission checks
const apiPath = path.join(__dirname, '..', 'src', 'app', 'api');

function findApiFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('auth') && !item.startsWith('debug')) {
      findApiFiles(fullPath, files);
    } else if (item === 'route.ts' || item === '[id]/route.ts') {
      files.push(fullPath);
    }
  }
  
  return files;
}

const apiFiles = findApiFiles(apiPath);

console.log('Found API files:');
apiFiles.forEach(file => {
  const relativePath = path.relative(apiPath, file);
  console.log(`- ${relativePath}`);
});

// For each API file, check if it needs permission imports
apiFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  const relativePath = path.relative(apiPath, file);
  
  // Extract module name from path
  const pathParts = relativePath.split(path.sep);
  const moduleName = pathParts[0];
  
  if (!apiModules[moduleName]) {
    console.log(`\nSkipping ${relativePath} - no module mapping`);
    return;
  }
  
  const permissionModule = apiModules[moduleName];
  
  // Check if permissions are already imported
  if (!content.includes('getUserFromRequest') && !content.includes('hasPermission')) {
    console.log(`\n${relativePath} needs permission imports`);
    console.log(`Module: ${permissionModule}`);
    console.log(`Add: import { getUserFromRequest, hasPermission } from "@/lib/permissions";`);
    
    // Check which methods need permissions
    if (content.includes('export async function GET')) {
      console.log(`- GET method needs READ permission check`);
    }
    if (content.includes('export async function POST')) {
      console.log(`- POST method needs CREATE permission check`);
    }
    if (content.includes('export async function PUT')) {
      console.log(`- PUT method needs UPDATE permission check`);
    }
    if (content.includes('export async function DELETE')) {
      console.log(`- DELETE method needs DELETE permission check`);
    }
  } else {
    console.log(`\n✓ ${relativePath} already has permission imports`);
  }
});