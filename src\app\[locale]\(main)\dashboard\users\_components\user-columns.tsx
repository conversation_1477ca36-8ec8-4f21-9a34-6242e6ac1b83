"use client";

import { ColumnDef } from "@tanstack/react-table";
import { MoreH<PERSON><PERSON><PERSON>, Edit, Trash2, Eye, Key } from "lucide-react";
import Link from "next/link";

import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { getInitials, formatDateForDisplay } from "@/lib/utils";
import type { UserWithRoles } from "@/types/user";
import { useTranslations } from "next-intl";

import { DeleteUserDialog } from "./delete-user-dialog";

// Helper function to create translated column headers
const createHeader = (translationKey: string) => {
  return ({ column }: any) => {
    const t = useTranslations();
    return <DataTableColumnHeader column={column} title={t(translationKey)} />;
  };
};

export const userColumns: ColumnDef<UserWithRoles>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: ({ column }) => {
      const t = useTranslations();
      return <DataTableColumnHeader column={column} title={t("common.id")} className="justify-center" />;
    },
    cell: ({ row }) => <div className="text-center"><span className="font-mono text-sm">{row.original.id}</span></div>,
    enableHiding: false,
  },
  {
    id: "user",
    header: createHeader("users.user"),
    cell: ({ row }) => {
      const user = row.original;
      const fullName = `${user.first_name} ${user.last_name}`;
      
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="text-xs">
              {getInitials(fullName)}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{fullName}</div>
            <div className="text-sm text-muted-foreground">@{user.username}</div>
          </div>
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: "email",
    header: createHeader("common.email"),
    cell: ({ row }) => (
      <span className="text-sm">{row.original.email}</span>
    ),
  },
  {
    accessorKey: "phone",
    header: createHeader("common.phone"),
    cell: ({ row }) => (
      <span className="text-sm">{row.original.phone || "—"}</span>
    ),
  },
  {
    id: "roles",
    header: () => {
      const t = useTranslations();
      return t("navigation.roles");
    },
    cell: ({ row }) => {
      const roles = row.original.user_roles;
      
      if (roles.length === 0) {
        const t = useTranslations();
        return <span className="text-sm text-muted-foreground">{t("users.noRoles")}</span>;
      }
      
      const t = useTranslations();
      return (
        <div className="flex flex-wrap gap-1">
          {roles.slice(0, 2).map((userRole) => (
            <Badge key={userRole.id} variant="secondary" className="text-xs">
              {userRole.role.name}
            </Badge>
          ))}
          {roles.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{roles.length - 2} {t("common.more")}
            </Badge>
          )}
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: "status",
    header: createHeader("common.status"),
    cell: ({ row }) => {
      const status = row.original.status;
      const variant = 
        status === "ACTIVE" ? "default" :
        status === "INACTIVE" ? "secondary" :
        status === "SUSPENDED" ? "destructive" :
        "outline";
      
      return (
        <Badge variant={variant} className="capitalize">
          {status.toLowerCase().replace("_", " ")}
        </Badge>
      );
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: "last_login_at",
    header: createHeader("users.lastLogin"),
    cell: ({ row }) => {
      const lastLogin = row.original.last_login_at;
      const t = useTranslations();
      return (
        <span className="text-sm">
          {lastLogin ? formatDateForDisplay(lastLogin, "table") : t("users.never")}
        </span>
      );
    },
  },
  {
    accessorKey: "created_at",
    header: createHeader("common.createdAt"),
    cell: ({ row }) => (
      <span className="text-sm">
        {formatDateForDisplay(row.original.created_at, "table")}
      </span>
    ),
  },
  {
    id: "actions",
    header: () => {
      const t = useTranslations();
      return <div className="text-center">{t("common.actions")}</div>;
    },
    cell: ({ row }) => {
      const user = row.original;
      const t = useTranslations();
      
      return (
        <div className="text-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t("common.actions")}</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(user.email)}
            >
              {t("users.copyEmail")}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href={`/dashboard/users/${user.id}`}>
                <Eye className="mr-2 h-4 w-4" />
                {t("common.viewDetails")}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={`/dashboard/users/${user.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                {t("users.editUser")}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={`/dashboard/users/${user.id}/change-password`}>
                <Key className="mr-2 h-4 w-4" />
                {t("users.changePassword")}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DeleteUserDialog user={user}>
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                <Trash2 className="mr-2 h-4 w-4" />
                {t("users.deleteUser")}
              </DropdownMenuItem>
            </DeleteUserDialog>
          </DropdownMenuContent>
        </DropdownMenu>
        </div>
      );
    },
  },
];
