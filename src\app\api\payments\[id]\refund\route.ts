import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ApiResponseBuilder } from "@/lib/api-response";
import { z } from "zod";


const refundSchema = z.object({
  reason: z.string().min(1, "Refund reason is required"),
  refund_amount: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format").optional(), // If not provided, refund full amount
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for payments
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "payments", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create payments");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const paymentId = parseInt(id);
    if (isNaN(paymentId)) {
      return ApiResponseBuilder.error("Invalid payment ID", "BAD_REQUEST", 400);
    }

    const body = await request.json();
    const validatedData = refundSchema.parse(body);

    // Check if payment exists
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        allocations: {
          include: {
            invoice: true,
          },
        },
      },
    });

    if (!payment) {
      return ApiResponseBuilder.error("Payment not found", "NOT_FOUND", 404);
    }

    // Check if payment can be refunded
    if (payment.status !== 'COMPLETED') {
      return ApiResponseBuilder.error("Only completed payments can be refunded", "BAD_REQUEST", 400);
    }

    const refundAmount = validatedData.refund_amount 
      ? parseFloat(validatedData.refund_amount) 
      : parseFloat(payment.amount.toString());

    if (refundAmount > parseFloat(payment.amount.toString())) {
      return ApiResponseBuilder.error("Refund amount cannot exceed payment amount", "BAD_REQUEST", 400);
    }

    // Process refund in a transaction
    const refundedPayment = await prisma.$transaction(async (tx) => {
      // Update payment status
      const updatedPayment = await tx.payment.update({
        where: { id: paymentId },
        data: {
          status: 'REFUNDED',
          notes: `${payment.notes ? payment.notes + "\n\n" : ""}Refund Reason: ${validatedData.reason}${validatedData.refund_amount ? `\nRefund Amount: ${refundAmount.toFixed(3)}` : " (Full Refund)"}`,
        },
      });

      // Revert invoice payments
      for (const allocation of payment.allocations) {
        const invoice = allocation.invoice;
        
        // Calculate proportional refund for this allocation
        const allocationProportion = parseFloat(allocation.allocated_amount.toString()) / parseFloat(payment.amount.toString());
        const refundForAllocation = refundAmount * allocationProportion;
        
        const newPaidAmount = Math.max(0, parseFloat(invoice.paid_amount.toString()) - refundForAllocation);
        const newBalance = parseFloat(invoice.total_amount.toString()) - newPaidAmount;
        
        let newStatus = invoice.status;
        if (newPaidAmount <= 0) {
          newStatus = new Date(invoice.due_date) < new Date() ? 'OVERDUE' : 'PENDING';
        } else if (newBalance > 0) {
          newStatus = 'PARTIALLY_PAID';
        }

        await tx.invoice.update({
          where: { id: invoice.id },
          data: {
            paid_amount: newPaidAmount,
            balance_amount: newBalance,
            status: newStatus,
            updated_by: decoded.id,
          },
        });
      }

      return updatedPayment;
    });

    // Fetch the updated payment with relations
    const result = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        invoice: true,
        tenant: true,
        allocations: {
          include: {
            invoice: true,
          },
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    return ApiResponseBuilder.success({
      message: "Payment refunded successfully",
      payment: result,
    });
  } catch (error) {
    console.error("Error refunding payment:", error);
    return ApiResponseBuilder.error("Failed to refund payment", "INTERNAL_ERROR", 500);
  }
}