import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ApiResponseBuilder } from "@/lib/api-response";
import { readFile } from "fs/promises";
import { join } from "path";



import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; documentId: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for contracts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "contracts", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to read contracts");
    }

    // User is already authenticated via token above

    const { id, documentId: documentIdParam } = await params;

    const contractId = parseInt(id);
    const documentId = parseInt(documentIdParam);

    if (isNaN(contractId) || isNaN(documentId)) {
      return ApiResponseBuilder.error("Invalid contract or document ID", "INVALID_ID", 400);
    }

    // Check if document exists and belongs to the contract
    const document = await prisma.contractDocument.findFirst({
      where: {
        id: documentId,
        contract_id: contractId,
      },
    });

    if (!document) {
      return ApiResponseBuilder.error("Document not found", "NOT_FOUND", 404);
    }

    // Read file from disk
    try {
      const filePath = join(process.cwd(), "public", document.file_path);
      const fileBuffer = await readFile(filePath);

      // Return file as response
      return new NextResponse(fileBuffer, {
        headers: {
          "Content-Type": document.mime_type,
          "Content-Disposition": `attachment; filename="${document.file_name}"`,
          "Content-Length": document.file_size.toString(),
        },
      });
    } catch (error) {
      console.error("Error reading file:", error);
      return ApiResponseBuilder.error("File not found", "NOT_FOUND", 404);
    }
  } catch (error) {
    console.error("Error downloading contract document:", error);
    return ApiResponseBuilder.error("Failed to download contract document", "INTERNAL_ERROR", 500);
  }
}