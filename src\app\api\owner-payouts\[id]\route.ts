import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ownerPayoutApprovalSchema, ownerPayoutPaymentSchema } from "@/types/owner-payout";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

// GET /api/owner-payouts/[id] - Get a single owner payout
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owner-payouts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "owner-payouts", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view owner payouts");
    }

    const { id: idParam } = await params;


    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.error("Invalid payout ID", "BAD_REQUEST", 400);
    }

    const payout = await db.ownerPayout.findUnique({
      where: { id },
      include: {
        owner: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            email: true,
            phone: true,
            mobile: true,
            bank_name: true,
            bank_account_number: true,
            bank_iban: true,
          },
        },
        approver: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        payer: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        payout_details: {
          include: {
            property: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
                address_en: true,
                address_ar: true,
              },
            },
          },
          orderBy: {
            property: {
              name_en: "asc",
            },
          },
        },
      },
    });

    if (!payout) {
      return ApiResponseBuilder.error("Owner payout not found", "NOT_FOUND", 404);
    }

    // Transform payout to handle Decimal serialization
    const transformedPayout = {
      ...payout,
      total_rent_collected: payout.total_rent_collected.toString(),
      management_fee: payout.management_fee.toString(),
      other_deductions: payout.other_deductions.toString(),
      net_amount: payout.net_amount.toString(),
      payout_details: payout.payout_details.map(detail => ({
        ...detail,
        rent_collected: detail.rent_collected.toString(),
        management_fee: detail.management_fee.toString(),
        net_amount: detail.net_amount.toString(),
      })),
    };

    return ApiResponseBuilder.success(transformedPayout);
  } catch (error) {
    console.error("Error fetching owner payout:", error);
    return ApiResponseBuilder.error("Failed to fetch owner payout", "INTERNAL_ERROR", 500);
  }
}

// DELETE /api/owner-payouts/[id] - Cancel an owner payout
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has DELETE permission for owner-payouts
    const canDelete = hasPermission(userPermissions, "owner-payouts", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete owner payouts");
    }

    const { id: idParam } = await params;


    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.error("Invalid payout ID", "BAD_REQUEST", 400);
    }

    const payout = await db.ownerPayout.findUnique({
      where: { id },
    });

    if (!payout) {
      return ApiResponseBuilder.error("Owner payout not found", "NOT_FOUND", 404);
    }

    if (payout.status === "PAID") {
      return ApiResponseBuilder.error("Cannot cancel a paid payout", "BAD_REQUEST", 400);
    }

    if (payout.status === "CANCELLED") {
      return ApiResponseBuilder.error("Payout is already cancelled", "BAD_REQUEST", 400);
    }

    const cancelledPayout = await db.ownerPayout.update({
      where: { id },
      data: {
        status: "CANCELLED",
      },
      include: {
        owner: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            email: true,
          },
        },
      },
    });

    // Transform payout to handle Decimal serialization
    const transformedPayout = {
      ...cancelledPayout,
      total_rent_collected: cancelledPayout.total_rent_collected.toString(),
      management_fee: cancelledPayout.management_fee.toString(),
      other_deductions: cancelledPayout.other_deductions.toString(),
      net_amount: cancelledPayout.net_amount.toString(),
    };

    return ApiResponseBuilder.success(transformedPayout);
  } catch (error) {
    console.error("Error cancelling owner payout:", error);
    return ApiResponseBuilder.error("Failed to cancel owner payout", "INTERNAL_ERROR", 500);
  }
}