import { PrismaClient } from '../src/generated/prisma'
import { Decimal } from '../src/generated/prisma/runtime/library'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

// Helper function to generate random dates
function randomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
}

// Helper function to generate random decimal
function randomDecimal(min: number, max: number, precision: number = 3): Decimal {
  const value = Math.random() * (max - min) + min
  return new Decimal(value.toFixed(precision))
}

async function main() {
  console.log('🌱 Starting comprehensive database seeding...')

  // Clear existing data (optional - comment out if you want to keep existing data)
  console.log('🧹 Clearing existing data...')
  await prisma.maintenanceStatusHistory.deleteMany()
  await prisma.maintenanceAttachment.deleteMany()
  await prisma.maintenanceRequest.deleteMany()
  await prisma.ownerPayoutDetail.deleteMany()
  await prisma.ownerPayout.deleteMany()
  await prisma.paymentAllocation.deleteMany()
  await prisma.payment.deleteMany()
  await prisma.invoiceItem.deleteMany()
  await prisma.invoice.deleteMany()
  await prisma.contractDocument.deleteMany()
  await prisma.contractRenewal.deleteMany()
  await prisma.contractTenant.deleteMany()
  await prisma.contract.deleteMany()
  await prisma.emergencyContact.deleteMany()
  await prisma.tenantDocument.deleteMany()
  await prisma.tenant.deleteMany()
  await prisma.unitAmenity.deleteMany()
  await prisma.unit.deleteMany()
  await prisma.propertyAmenity.deleteMany()
  await prisma.property.deleteMany()
  await prisma.propertyOwner.deleteMany()
  await prisma.amenity.deleteMany()
  await prisma.propertyType.deleteMany()
  await prisma.userRole.deleteMany()
  await prisma.rolePermission.deleteMany()
  await prisma.permission.deleteMany()
  await prisma.role.deleteMany()
  await prisma.user.deleteMany()

  // 1. Create Roles and Permissions
  console.log('👥 Creating roles and permissions...')
  
  // Create permissions for ALL modules used in the system
  const modules = [
    'properties',
    'property-types', 
    'property-owners',
    'units',
    'tenants',
    'contracts',
    'invoices',
    'payments',
    'owner-payouts',
    'maintenance',
    'expenses',
    'expense-categories',
    'amenities',
    'users',
    'roles',
    'settings',
    'dashboard',
    'reports'
  ];

  const actions = ['CREATE', 'READ', 'UPDATE', 'DELETE'];
  const permissions = [];

  // Generate all permissions for all modules
  for (const module of modules) {
    for (const action of actions) {
      permissions.push({ module, action });
    }
  }

  for (const perm of permissions) {
    await prisma.permission.create({
      data: {
        module: perm.module,
        action: perm.action as any,
        description: `${perm.action} access to ${perm.module}`,
      },
    })
  }

  const adminRole = await prisma.role.create({
    data: {
      name: 'Admin',
      description: 'System Administrator with full access',
      is_system: true,
    },
  })

  const managerRole = await prisma.role.create({
    data: {
      name: 'Property Manager',
      description: 'Property Management Staff',
      is_system: false,
    },
  })

  const tenantRole = await prisma.role.create({
    data: {
      name: 'Tenant',
      description: 'Property Tenant with limited access',
      is_system: false,
    },
  })

  // Assign all permissions to admin role
  const allPermissions = await prisma.permission.findMany()
  for (const permission of allPermissions) {
    await prisma.rolePermission.create({
      data: {
        role_id: adminRole.id,
        permission_id: permission.id,
      },
    })
  }

  // 2. Create Users
  console.log('👤 Creating users...')
  
  const hashedPassword = await bcrypt.hash('admin123', 10)
  
  const adminUser = await prisma.user.create({
    data: {
      username: 'admin',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      first_name: 'System',
      last_name: 'Administrator',
      phone: '+968 2123 4567',
      status: 'ACTIVE',
      email_verified: true,
    },
  })

  await prisma.userRole.create({
    data: {
      user_id: adminUser.id,
      role_id: adminRole.id,
    },
  })

  const managerUser = await prisma.user.create({
    data: {
      username: 'manager',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      first_name: 'Ahmed',
      last_name: 'Al-Rashid',
      phone: '+968 2234 5678',
      status: 'ACTIVE',
      email_verified: true,
    },
  })

  await prisma.userRole.create({
    data: {
      user_id: managerUser.id,
      role_id: managerRole.id,
    },
  })

  // 3. Create Property Types
  console.log('🏠 Creating property types...')
  
  const propertyTypes = [
    {
      name_en: 'Villa',
      name_ar: 'فيلا',
      description_en: 'Standalone villa property',
      description_ar: 'عقار فيلا منفصلة',
    },
    {
      name_en: 'Apartment',
      name_ar: 'شقة',
      description_en: 'Apartment unit in building',
      description_ar: 'وحدة شقة في مبنى',
    },
    {
      name_en: 'Commercial Building',
      name_ar: 'مبنى تجاري',
      description_en: 'Commercial office building',
      description_ar: 'مبنى مكاتب تجاري',
    },
    {
      name_en: 'Townhouse',
      name_ar: 'بيت شعبي',
      description_en: 'Multi-story townhouse',
      description_ar: 'بيت شعبي متعدد الطوابق',
    },
    {
      name_en: 'Studio',
      name_ar: 'استوديو',
      description_en: 'Studio apartment',
      description_ar: 'شقة استوديو',
    },
  ]

  const createdPropertyTypes = []
  for (const type of propertyTypes) {
    const propertyType = await prisma.propertyType.create({
      data: type,
    })
    createdPropertyTypes.push(propertyType)
  }

  // 4. Create Amenities
  console.log('🏊 Creating amenities...')
  
  const amenities = [
    { name_en: 'Swimming Pool', name_ar: 'مسبح', icon: 'waves' },
    { name_en: 'Gym', name_ar: 'صالة رياضية', icon: 'dumbbell' },
    { name_en: 'Parking', name_ar: 'موقف سيارات', icon: 'car' },
    { name_en: 'Garden', name_ar: 'حديقة', icon: 'trees' },
    { name_en: 'Security', name_ar: 'أمن', icon: 'shield' },
    { name_en: 'Central AC', name_ar: 'تكييف مركزي', icon: 'snowflake' },
    { name_en: 'Elevator', name_ar: 'مصعد', icon: 'move-vertical' },
    { name_en: 'Balcony', name_ar: 'شرفة', icon: 'home' },
    { name_en: 'Storage Room', name_ar: 'غرفة تخزين', icon: 'archive' },
    { name_en: 'Maid Room', name_ar: 'غرفة خادمة', icon: 'user' },
  ]

  const createdAmenities = []
  for (const amenity of amenities) {
    const createdAmenity = await prisma.amenity.create({
      data: amenity,
    })
    createdAmenities.push(createdAmenity)
  }

  // 5. Create Property Owners
  console.log('👨‍💼 Creating property owners...')

  const propertyOwners = [
    {
      name_en: 'Ahmed Al-Rashid',
      name_ar: 'أحمد الراشد',
      email: '<EMAIL>',
      phone: '+968 2123 4567',
      mobile: '+968 9123 4567',
      address_en: 'Al-Khuwair, Muscat, Oman',
      address_ar: 'الخوير، مسقط، عمان',
      tax_id: 'TAX001',
      bank_name: 'Bank Muscat',
      bank_account_number: '**********',
      bank_iban: 'OM81BMAG123**********456',
      management_fee_percentage: new Decimal('10.00'),
      status: 'ACTIVE' as const,
    },
    {
      name_en: 'Fatima Al-Zahra',
      name_ar: 'فاطمة الزهراء',
      email: '<EMAIL>',
      phone: '+968 2234 5678',
      mobile: '+968 9234 5678',
      address_en: 'Ruwi, Muscat, Oman',
      address_ar: 'روي، مسقط، عمان',
      tax_id: 'TAX002',
      bank_name: 'HSBC Bank Oman',
      bank_account_number: '**********',
      bank_iban: 'OM82HSBC23**********4567',
      management_fee_percentage: new Decimal('8.50'),
      status: 'ACTIVE' as const,
    },
    {
      name_en: 'Mohammed Al-Balushi',
      name_ar: 'محمد البلوشي',
      email: '<EMAIL>',
      phone: '+968 2345 6789',
      mobile: '+968 9345 6789',
      address_en: 'Seeb, Muscat, Oman',
      address_ar: 'السيب، مسقط، عمان',
      tax_id: 'TAX003',
      bank_name: 'National Bank of Oman',
      bank_account_number: '**********',
      bank_iban: 'OM83NBOM3**********45678',
      management_fee_percentage: new Decimal('12.00'),
      status: 'ACTIVE' as const,
    },
    {
      name_en: 'Salim Al-Hinai',
      name_ar: 'سالم الهنائي',
      email: '<EMAIL>',
      phone: '+968 2456 7890',
      mobile: '+968 9456 7890',
      address_en: 'Bausher, Muscat, Oman',
      address_ar: 'بوشر، مسقط، عمان',
      tax_id: 'TAX004',
      bank_name: 'Ahli Bank',
      bank_account_number: '**********',
      bank_iban: '************************',
      management_fee_percentage: new Decimal('9.00'),
      status: 'ACTIVE' as const,
    },
    {
      name_en: 'Aisha Al-Kindi',
      name_ar: 'عائشة الكندي',
      email: '<EMAIL>',
      phone: '+968 2567 8901',
      mobile: '+968 9567 8901',
      address_en: 'Al-Mawaleh, Muscat, Oman',
      address_ar: 'المعولة، مسقط، عمان',
      tax_id: 'TAX005',
      bank_name: 'Bank Dhofar',
      bank_account_number: '**********',
      bank_iban: '************************',
      management_fee_percentage: new Decimal('11.50'),
      status: 'ACTIVE' as const,
    },
  ]

  const createdOwners = []
  for (const owner of propertyOwners) {
    const propertyOwner = await prisma.propertyOwner.create({
      data: {
        ...owner,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    createdOwners.push(propertyOwner)
  }

  // 6. Create Properties
  console.log('🏢 Creating properties...')

  const properties = [
    {
      name_en: 'Luxury Villa in Al-Khuwair',
      name_ar: 'فيلا فاخرة في الخوير',
      address_en: 'Street 123, Al-Khuwair, Muscat, Oman',
      address_ar: 'شارع 123، الخوير، مسقط، عمان',
      property_type_id: createdPropertyTypes[0].id, // Villa
      owner_id: createdOwners[0].id,
      base_rent: new Decimal('1200.000'),
      status: 'AVAILABLE' as const,
      total_area: new Decimal('500.00'),
      floors_count: 2,
      parking_spaces: 3,
    },
    {
      name_en: 'Modern Apartment Complex',
      name_ar: 'مجمع شقق حديث',
      address_en: 'Building 45, Ruwi, Muscat, Oman',
      address_ar: 'مبنى 45، روي، مسقط، عمان',
      property_type_id: createdPropertyTypes[1].id, // Apartment
      owner_id: createdOwners[1].id,
      base_rent: new Decimal('800.000'),
      status: 'RENTED' as const,
      total_area: new Decimal('1200.00'),
      floors_count: 5,
      parking_spaces: 20,
    },
    {
      name_en: 'Commercial Tower',
      name_ar: 'برج تجاري',
      address_en: 'CBD Area, Muscat, Oman',
      address_ar: 'منطقة الأعمال المركزية، مسقط، عمان',
      property_type_id: createdPropertyTypes[2].id, // Commercial
      owner_id: createdOwners[2].id,
      base_rent: new Decimal('2500.000'),
      status: 'RENTED' as const,
      total_area: new Decimal('2000.00'),
      floors_count: 10,
      parking_spaces: 50,
    },
    {
      name_en: 'Family Townhouse',
      name_ar: 'بيت عائلي',
      address_en: 'Seeb Residential Area, Muscat, Oman',
      address_ar: 'منطقة السيب السكنية، مسقط، عمان',
      property_type_id: createdPropertyTypes[3].id, // Townhouse
      owner_id: createdOwners[3].id,
      base_rent: new Decimal('950.000'),
      status: 'AVAILABLE' as const,
      total_area: new Decimal('350.00'),
      floors_count: 3,
      parking_spaces: 2,
    },
    {
      name_en: 'Studio Apartments',
      name_ar: 'شقق استوديو',
      address_en: 'Al-Mawaleh, Muscat, Oman',
      address_ar: 'المعولة، مسقط، عمان',
      property_type_id: createdPropertyTypes[4].id, // Studio
      owner_id: createdOwners[4].id,
      base_rent: new Decimal('400.000'),
      status: 'RENTED' as const,
      total_area: new Decimal('600.00'),
      floors_count: 4,
      parking_spaces: 15,
    },
  ]

  const createdProperties = []
  for (const property of properties) {
    const createdProperty = await prisma.property.create({
      data: {
        ...property,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    createdProperties.push(createdProperty)
  }

  // Add amenities to properties
  for (let i = 0; i < createdProperties.length; i++) {
    const property = createdProperties[i]
    const amenityCount = Math.floor(Math.random() * 5) + 3 // 3-7 amenities per property
    const selectedAmenities = createdAmenities
      .sort(() => 0.5 - Math.random())
      .slice(0, amenityCount)

    for (const amenity of selectedAmenities) {
      await prisma.propertyAmenity.create({
        data: {
          property_id: property.id,
          amenity_id: amenity.id,
        },
      })
    }
  }

  // 7. Create Units
  console.log('🏠 Creating units...')

  const units = []
  for (let propIndex = 0; propIndex < createdProperties.length; propIndex++) {
    const property = createdProperties[propIndex]
    const unitsPerProperty = Math.floor(Math.random() * 8) + 3 // 3-10 units per property

    for (let unitIndex = 1; unitIndex <= unitsPerProperty; unitIndex++) {
      const unit = {
        property_id: property.id,
        unit_number: `${String.fromCharCode(65 + propIndex)}${unitIndex.toString().padStart(2, '0')}`,
        unit_name_en: `Unit ${unitIndex}`,
        unit_name_ar: `وحدة ${unitIndex}`,
        floor_number: Math.floor(Math.random() * property.floors_count!) + 1,
        rooms_count: Math.floor(Math.random() * 4) + 1, // 1-4 rooms
        majalis_count: Math.floor(Math.random() * 2) + 1, // 1-2 majalis
        bathrooms_count: Math.floor(Math.random() * 3) + 1, // 1-3 bathrooms
        area: randomDecimal(50, 200, 2), // 50-200 sqm
        rent_amount: randomDecimal(300, 1500, 3), // 300-1500 OMR
        status: ['AVAILABLE', 'RENTED', 'UNDER_MAINTENANCE'][Math.floor(Math.random() * 3)] as any,
        description_en: `Spacious ${Math.floor(Math.random() * 4) + 1} bedroom unit with modern amenities`,
        description_ar: `وحدة واسعة من ${Math.floor(Math.random() * 4) + 1} غرف نوم مع وسائل الراحة الحديثة`,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      }

      const createdUnit = await prisma.unit.create({
        data: unit,
      })
      units.push(createdUnit)

      // Add amenities to units
      const unitAmenityCount = Math.floor(Math.random() * 4) + 2 // 2-5 amenities per unit
      const selectedUnitAmenities = createdAmenities
        .sort(() => 0.5 - Math.random())
        .slice(0, unitAmenityCount)

      for (const amenity of selectedUnitAmenities) {
        await prisma.unitAmenity.create({
          data: {
            unit_id: createdUnit.id,
            amenity_id: amenity.id,
          },
        })
      }
    }
  }

  // 8. Create Tenants
  console.log('👥 Creating tenants...')

  const tenantNames = [
    { first_en: 'Omar', last_en: 'Al-Salam', first_ar: 'عمر', last_ar: 'السلام', nationality: 'Omani' },
    { first_en: 'Sarah', last_en: 'Johnson', first_ar: 'سارة', last_ar: 'جونسون', nationality: 'American' },
    { first_en: 'Ali', last_en: 'Al-Maskari', first_ar: 'علي', last_ar: 'المسكري', nationality: 'Omani' },
    { first_en: 'Priya', last_en: 'Sharma', first_ar: 'بريا', last_ar: 'شارما', nationality: 'Indian' },
    { first_en: 'Hassan', last_en: 'Al-Balushi', first_ar: 'حسن', last_ar: 'البلوشي', nationality: 'Omani' },
    { first_en: 'Emma', last_en: 'Wilson', first_ar: 'إيما', last_ar: 'ويلسون', nationality: 'British' },
    { first_en: 'Ahmed', last_en: 'Al-Hinai', first_ar: 'أحمد', last_ar: 'الهنائي', nationality: 'Omani' },
    { first_en: 'Maria', last_en: 'Garcia', first_ar: 'ماريا', last_ar: 'غارسيا', nationality: 'Spanish' },
    { first_en: 'Khalid', last_en: 'Al-Rashid', first_ar: 'خالد', last_ar: 'الراشد', nationality: 'Omani' },
    { first_en: 'Lisa', last_en: 'Chen', first_ar: 'ليزا', last_ar: 'تشين', nationality: 'Chinese' },
    { first_en: 'Saeed', last_en: 'Al-Kindi', first_ar: 'سعيد', last_ar: 'الكندي', nationality: 'Omani' },
    { first_en: 'Anna', last_en: 'Mueller', first_ar: 'آنا', last_ar: 'مولر', nationality: 'German' },
    { first_en: 'Youssef', last_en: 'Al-Zahra', first_ar: 'يوسف', last_ar: 'الزهراء', nationality: 'Omani' },
    { first_en: 'Sophie', last_en: 'Martin', first_ar: 'صوفي', last_ar: 'مارتن', nationality: 'French' },
    { first_en: 'Rashid', last_en: 'Al-Busaidi', first_ar: 'راشد', last_ar: 'البوسعيدي', nationality: 'Omani' },
  ]

  const createdTenants = []
  for (let i = 0; i < tenantNames.length; i++) {
    const name = tenantNames[i]
    const tenant = await prisma.tenant.create({
      data: {
        first_name: name.first_en,
        last_name: name.last_en,
        email: `${name.first_en.toLowerCase()}.${name.last_en.toLowerCase()}@email.com`,
        phone: `+968 ${Math.floor(Math.random() * 9000) + 1000} ${Math.floor(Math.random() * 9000) + 1000}`,
        national_id: `${Math.floor(Math.random() * ********) + ********}`,
        national_id_expiry: randomDate(new Date(2025, 0, 1), new Date(2030, 11, 31)),
        date_of_birth: randomDate(new Date(1970, 0, 1), new Date(2000, 11, 31)),
        nationality: name.nationality,
        occupation: ['Engineer', 'Doctor', 'Teacher', 'Manager', 'Consultant', 'Analyst'][Math.floor(Math.random() * 6)],
        company_name: ['Petroleum Development Oman', 'Oman Air', 'Bank Muscat', 'Omantel', 'OQ Group'][Math.floor(Math.random() * 5)],
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    createdTenants.push(tenant)

    // Add emergency contact for each tenant
    await prisma.emergencyContact.create({
      data: {
        tenant_id: tenant.id,
        name: `Emergency Contact for ${name.first_en}`,
        relationship: ['Father', 'Mother', 'Brother', 'Sister', 'Spouse'][Math.floor(Math.random() * 5)],
        phone: `+968 ${Math.floor(Math.random() * 9000) + 1000} ${Math.floor(Math.random() * 9000) + 1000}`,
        email: `emergency.${name.first_en.toLowerCase()}@email.com`,
        address: 'Emergency contact address',
        is_primary: true,
      },
    })
  }

  // 9. Create Contracts
  console.log('📋 Creating contracts...')

  const rentedUnits = units.filter(unit => unit.status === 'RENTED')
  const createdContracts = []

  for (let i = 0; i < Math.min(rentedUnits.length, createdTenants.length); i++) {
    const unit = rentedUnits[i]
    const tenant = createdTenants[i]

    const startDate = randomDate(new Date(2023, 0, 1), new Date(2024, 6, 1))
    const endDate = new Date(startDate)
    endDate.setFullYear(endDate.getFullYear() + 1) // 1 year contract

    const contract = await prisma.contract.create({
      data: {
        contract_number: `CON-${String(i + 1).padStart(4, '0')}`,
        property_id: unit.property_id,
        unit_id: unit.id,
        start_date: startDate,
        end_date: endDate,
        monthly_rent: unit.rent_amount,
        payment_due_day: Math.floor(Math.random() * 28) + 1, // 1-28
        security_deposit: new Decimal(unit.rent_amount.toNumber() * 2), // 2 months rent
        insurance_amount: randomDecimal(50, 200, 3),
        insurance_due_date: randomDate(startDate, endDate),
        terms_and_conditions: 'Standard rental agreement terms and conditions apply.',
        status: 'ACTIVE',
        auto_renew: Math.random() > 0.5,
        renewal_notice_days: 30,
        notes: 'Contract created during seeding process',
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    createdContracts.push(contract)

    // Link tenant to contract
    await prisma.contractTenant.create({
      data: {
        contract_id: contract.id,
        tenant_id: tenant.id,
        is_primary: true,
      },
    })
  }

  // 10. Create Invoices
  console.log('🧾 Creating invoices...')

  const createdInvoices = []
  for (const contract of createdContracts) {
    // Create 6-12 monthly invoices for each contract
    const invoiceCount = Math.floor(Math.random() * 7) + 6

    for (let month = 0; month < invoiceCount; month++) {
      const invoiceDate = new Date(contract.start_date)
      invoiceDate.setMonth(invoiceDate.getMonth() + month)

      const dueDate = new Date(invoiceDate)
      dueDate.setDate(contract.payment_due_day)

      const isOverdue = dueDate < new Date() && Math.random() > 0.8 // 20% chance of overdue
      const isPaid = !isOverdue && Math.random() > 0.3 // 70% chance of being paid if not overdue

      const lateFee = isOverdue ? randomDecimal(10, 50, 3) : new Decimal('0.000')
      const totalAmount = new Decimal(contract.monthly_rent.toNumber() + lateFee.toNumber())
      const paidAmount = isPaid ? totalAmount : new Decimal('0.000')
      const balanceAmount = new Decimal(totalAmount.toNumber() - paidAmount.toNumber())

      let status: any = 'PENDING'
      if (isPaid) status = 'PAID'
      else if (isOverdue) status = 'OVERDUE'
      else if (paidAmount.toNumber() > 0) status = 'PARTIALLY_PAID'

      const invoice = await prisma.invoice.create({
        data: {
          invoice_number: `INV-${contract.contract_number}-${String(month + 1).padStart(2, '0')}`,
          contract_id: contract.id,
          tenant_id: (await prisma.contractTenant.findFirst({ where: { contract_id: contract.id } }))!.tenant_id,
          property_id: contract.property_id!,
          unit_id: contract.unit_id!,
          invoice_date: invoiceDate,
          due_date: dueDate,
          original_amount: contract.monthly_rent,
          late_fee: lateFee,
          total_amount: totalAmount,
          paid_amount: paidAmount,
          balance_amount: balanceAmount,
          status: status,
          notes: month === 0 ? 'First month rent' : `Month ${month + 1} rent`,
          created_by: adminUser.id,
          updated_by: adminUser.id,
        },
      })
      createdInvoices.push(invoice)

      // Create invoice items
      await prisma.invoiceItem.create({
        data: {
          invoice_id: invoice.id,
          description_en: 'Monthly Rent',
          description_ar: 'إيجار شهري',
          quantity: 1,
          unit_price: contract.monthly_rent,
          amount: contract.monthly_rent,
        },
      })

      if (lateFee.toNumber() > 0) {
        await prisma.invoiceItem.create({
          data: {
            invoice_id: invoice.id,
            description_en: 'Late Payment Fee',
            description_ar: 'رسوم التأخير',
            quantity: 1,
            unit_price: lateFee,
            amount: lateFee,
          },
        })
      }
    }
  }

  // 11. Create Payments
  console.log('💰 Creating payments...')

  const paidInvoices = createdInvoices.filter(inv => inv.status === 'PAID' || inv.status === 'PARTIALLY_PAID')

  for (const invoice of paidInvoices) {
    const paymentMethods = ['BANK_TRANSFER', 'CASH', 'CHECK', 'CREDIT_CARD', 'DEBIT_CARD']
    const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)]

    const payment = await prisma.payment.create({
      data: {
        payment_number: `PAY-${invoice.invoice_number}`,
        invoice_id: invoice.id,
        tenant_id: invoice.tenant_id,
        property_id: invoice.property_id,
        unit_id: invoice.unit_id,
        amount: invoice.paid_amount,
        payment_method: paymentMethod as any,
        payment_date: randomDate(invoice.invoice_date, new Date()),
        reference_number: paymentMethod === 'BANK_TRANSFER' ? `REF${Math.floor(Math.random() * 1000000)}` : null,
        bank_name: paymentMethod === 'BANK_TRANSFER' ? 'Bank Muscat' : null,
        notes: `Payment for ${invoice.invoice_number}`,
        status: 'COMPLETED',
        created_by: adminUser.id,
      },
    })

    // Create payment allocation
    await prisma.paymentAllocation.create({
      data: {
        payment_id: payment.id,
        invoice_id: invoice.id,
        allocated_amount: invoice.paid_amount,
      },
    })
  }

  // 12. Create Maintenance Requests
  console.log('🔧 Creating maintenance requests...')

  const maintenanceCategories = ['ELECTRICAL', 'PLUMBING', 'HVAC', 'STRUCTURAL', 'APPLIANCES', 'PAINTING', 'CLEANING']
  const maintenancePriorities = ['LOW', 'MEDIUM', 'HIGH', 'EMERGENCY']
  const maintenanceStatuses = ['REPORTED', 'ACKNOWLEDGED', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD']

  const maintenanceTitles = [
    { en: 'Air Conditioning Not Working', ar: 'التكييف لا يعمل' },
    { en: 'Water Leak in Bathroom', ar: 'تسرب مياه في الحمام' },
    { en: 'Electrical Socket Issue', ar: 'مشكلة في المقبس الكهربائي' },
    { en: 'Door Lock Repair', ar: 'إصلاح قفل الباب' },
    { en: 'Window Glass Replacement', ar: 'استبدال زجاج النافذة' },
    { en: 'Kitchen Sink Blockage', ar: 'انسداد حوض المطبخ' },
    { en: 'Paint Touch-up Required', ar: 'مطلوب تجديد الطلاء' },
    { en: 'Ceiling Fan Repair', ar: 'إصلاح مروحة السقف' },
    { en: 'Tile Replacement', ar: 'استبدال البلاط' },
    { en: 'Light Fixture Installation', ar: 'تركيب وحدة إضاءة' },
  ]

  for (let i = 0; i < 25; i++) { // Create 25 maintenance requests
    const unit = units[Math.floor(Math.random() * units.length)]
    const title = maintenanceTitles[Math.floor(Math.random() * maintenanceTitles.length)]
    const category = maintenanceCategories[Math.floor(Math.random() * maintenanceCategories.length)]
    const priority = maintenancePriorities[Math.floor(Math.random() * maintenancePriorities.length)]
    const status = maintenanceStatuses[Math.floor(Math.random() * maintenanceStatuses.length)]

    const reportedDate = randomDate(new Date(2024, 0, 1), new Date())
    const assignedDate = status !== 'REPORTED' ? randomDate(reportedDate, new Date()) : null
    const completedDate = status === 'COMPLETED' ? randomDate(assignedDate || reportedDate, new Date()) : null

    await prisma.maintenanceRequest.create({
      data: {
        request_number: `MNT-${String(i + 1).padStart(4, '0')}`,
        property_id: unit.property_id,
        unit_id: unit.id,
        tenant_id: unit.status === 'RENTED' ? createdTenants[Math.floor(Math.random() * createdTenants.length)].id : null,
        title: title.en,
        description: `${title.en} - Detailed description of the maintenance issue that needs to be addressed.`,
        priority: priority as any,
        category: category as any,
        status: status as any,
        reported_by: adminUser.id,
        reported_date: reportedDate,
        assigned_to: assignedDate ? managerUser.id : null,
        assigned_date: assignedDate,
        scheduled_date: assignedDate ? randomDate(assignedDate, new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)) : null,
        completed_date: completedDate,
        estimated_cost: randomDecimal(50, 500, 3),
        actual_cost: completedDate ? randomDecimal(40, 600, 3) : null,
        contractor_name: status !== 'REPORTED' ? 'Al-Rashid Maintenance Services' : null,
        contractor_phone: status !== 'REPORTED' ? '+968 9876 5432' : null,
        internal_notes: 'Internal maintenance notes',
        resolution_notes: completedDate ? 'Issue resolved successfully' : null,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
  }

  console.log('🎉 Comprehensive database seeding completed successfully!')
  console.log(`✅ Created:`)
  console.log(`   - ${createdPropertyTypes.length} property types`)
  console.log(`   - ${createdAmenities.length} amenities`)
  console.log(`   - ${createdOwners.length} property owners`)
  console.log(`   - ${createdProperties.length} properties`)
  console.log(`   - ${units.length} units`)
  console.log(`   - ${createdTenants.length} tenants`)
  console.log(`   - ${createdContracts.length} contracts`)
  console.log(`   - ${createdInvoices.length} invoices`)
  console.log(`   - ${paidInvoices.length} payments`)
  console.log(`   - 25 maintenance requests`)
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
