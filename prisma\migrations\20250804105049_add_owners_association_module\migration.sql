-- CreateTable
CREATE TABLE `owners_associations` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name_en` VARCHAR(255) NOT NULL,
    `name_ar` VARCHAR(255) NOT NULL,
    `property_id` INTEGER NOT NULL,
    `establishment_date` DATE NOT NULL,
    `total_members` INTEGER NOT NULL DEFAULT 0,
    `president_name` VARCHAR(255) NOT NULL,
    `management_term_duration` INTEGER NOT NULL,
    `contact_email` VARCHAR(255) NULL,
    `contact_phone` VARCHAR(20) NULL,
    `created_by` INTEGER NULL,
    `updated_by` INTEGER NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `owners_associations_property_id_idx`(`property_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `association_members` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `association_id` INTEGER NOT NULL,
    `full_name` VARCHAR(255) NOT NULL,
    `unit_number` VARCHAR(50) NOT NULL,
    `ownership_percentage` DECIMAL(5, 2) NOT NULL,
    `phone` VARCHAR(20) NULL,
    `email` VARCHAR(255) NULL,
    `join_date` DATE NOT NULL,
    `is_board_member` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `association_members_association_id_idx`(`association_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `association_subscriptions` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `association_id` INTEGER NOT NULL,
    `name_en` VARCHAR(255) NOT NULL,
    `name_ar` VARCHAR(255) NOT NULL,
    `amount` DECIMAL(10, 3) NOT NULL,
    `frequency` ENUM('MONTHLY', 'QUARTERLY', 'YEARLY') NOT NULL DEFAULT 'MONTHLY',
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `association_subscriptions_association_id_idx`(`association_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `subscription_payments` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `subscription_id` INTEGER NOT NULL,
    `member_id` INTEGER NOT NULL,
    `payment_date` DATE NOT NULL,
    `due_date` DATE NOT NULL,
    `amount` DECIMAL(10, 3) NOT NULL,
    `status` ENUM('PAID', 'UNPAID', 'OVERDUE', 'PARTIALLY_PAID') NOT NULL DEFAULT 'UNPAID',
    `payment_method` ENUM('CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'CHECK', 'OTHER') NULL,
    `reference_number` VARCHAR(100) NULL,
    `notes` TEXT NULL,
    `created_by` INTEGER NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `subscription_payments_subscription_id_idx`(`subscription_id`),
    INDEX `subscription_payments_member_id_idx`(`member_id`),
    INDEX `subscription_payments_status_idx`(`status`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `association_transactions` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `association_id` INTEGER NOT NULL,
    `transaction_date` DATE NOT NULL,
    `type` ENUM('EXPENSE', 'INCOME') NOT NULL,
    `category` VARCHAR(100) NOT NULL,
    `description` TEXT NOT NULL,
    `amount` DECIMAL(10, 3) NOT NULL,
    `payment_method` ENUM('CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'CHECK', 'OTHER') NOT NULL,
    `notes` TEXT NULL,
    `attachment_url` VARCHAR(500) NULL,
    `attachment_name` VARCHAR(255) NULL,
    `created_by` INTEGER NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `association_transactions_association_id_idx`(`association_id`),
    INDEX `association_transactions_type_idx`(`type`),
    INDEX `association_transactions_transaction_date_idx`(`transaction_date`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `owners_associations` ADD CONSTRAINT `owners_associations_property_id_fkey` FOREIGN KEY (`property_id`) REFERENCES `properties`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `owners_associations` ADD CONSTRAINT `owners_associations_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `owners_associations` ADD CONSTRAINT `owners_associations_updated_by_fkey` FOREIGN KEY (`updated_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `association_members` ADD CONSTRAINT `association_members_association_id_fkey` FOREIGN KEY (`association_id`) REFERENCES `owners_associations`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `association_subscriptions` ADD CONSTRAINT `association_subscriptions_association_id_fkey` FOREIGN KEY (`association_id`) REFERENCES `owners_associations`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `subscription_payments` ADD CONSTRAINT `subscription_payments_subscription_id_fkey` FOREIGN KEY (`subscription_id`) REFERENCES `association_subscriptions`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `subscription_payments` ADD CONSTRAINT `subscription_payments_member_id_fkey` FOREIGN KEY (`member_id`) REFERENCES `association_members`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `subscription_payments` ADD CONSTRAINT `subscription_payments_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `association_transactions` ADD CONSTRAINT `association_transactions_association_id_fkey` FOREIGN KEY (`association_id`) REFERENCES `owners_associations`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `association_transactions` ADD CONSTRAINT `association_transactions_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
