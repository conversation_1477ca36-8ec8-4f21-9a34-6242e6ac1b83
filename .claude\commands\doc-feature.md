---
allowed-tools: Read(*), Write(*), MultiEdit(*), Grep(*), Glob(*)
argument-hint: [feature-name]
description: Update documentation for a specific feature
model: claude-3-5-sonnet-20241022
---

## Context

Feature to document: $ARGUMENTS

### Find Related Code Files
!`find . -name "*.ts" -o -name "*.tsx" | xargs grep -l "$ARGUMENTS" 2>/dev/null | head -10`

## Your Task

Document the specified feature by:

1. **Analyzing Implementation**:
   - Find all files related to the feature
   - Understand the feature's architecture
   - Identify key components and APIs

2. **Update Documentation**:
   - Add feature description to README.md
   - Update CLAUDE.md with implementation patterns
   - Create feature-specific documentation if needed

3. **Include**:
   - Feature overview and purpose
   - Usage examples
   - API documentation
   - Configuration options
   - Integration guidelines

Focus specifically on the feature: **$ARGUMENTS**