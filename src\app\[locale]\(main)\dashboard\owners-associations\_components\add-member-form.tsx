"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { format } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { apiClient } from "@/lib/api-client";
import { associationMemberSchema, type AssociationMemberInput } from "@/types/owners-association";
import { useRTL } from "@/hooks/use-rtl";

interface AddMemberFormProps {
  associationId: number;
  onSuccess: () => void;
  onCancel: () => void;
}

export function AddMemberForm({ associationId, onSuccess, onCancel }: AddMemberFormProps) {
  const t = useTranslations("ownersAssociations.members");
  const { isRTL } = useRTL();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<AssociationMemberInput>({
    resolver: zodResolver(associationMemberSchema),
    defaultValues: {
      association_id: associationId,
      full_name: "",
      unit_number: "",
      ownership_percentage: "",
      phone: "",
      email: "",
      join_date: format(new Date(), "yyyy-MM-dd"),
      is_board_member: false,
    },
  });

  const onSubmit = async (data: AssociationMemberInput) => {
    try {
      setIsLoading(true);
      
      const response = await apiClient.post(
        `/api/owners-associations/${associationId}/members`,
        data
      );

      if (response.success) {
        toast.success(t("addSuccess"));
        onSuccess();
      }
    } catch (error: any) {
      console.error("Error adding member:", error);
      
      if (error.response?.data?.code === "ALREADY_EXISTS") {
        toast.error(t("unitAlreadyExists"));
      } else if (error.response?.data?.code === "INVALID_OWNERSHIP") {
        toast.error(error.response.data.error);
      } else {
        toast.error(t("addError"));
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="full_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("fullName")}</FormLabel>
              <FormControl>
                <Input {...field} placeholder={t("fullNamePlaceholder")} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="unit_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("unitNumber")}</FormLabel>
                <FormControl>
                  <Input {...field} placeholder={t("unitNumberPlaceholder")} />
                </FormControl>
                <FormDescription>{t("unitNumberDescription")}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="ownership_percentage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("ownershipPercentage")}</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="0.00" />
                </FormControl>
                <FormDescription>{t("ownershipPercentageDescription")}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("phone")}</FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    value={field.value || ""} 
                    placeholder={t("phonePlaceholder")} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("email")}</FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    value={field.value || ""} 
                    type="email" 
                    placeholder={t("emailPlaceholder")} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="join_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>{t("joinDate")}</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !field.value && "text-muted-foreground",
                        isRTL && "flex-row-reverse"
                      )}
                    >
                      <CalendarIcon className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                      {field.value ? (
                        format(new Date(field.value), "PPP")
                      ) : (
                        <span>{t("selectDate")}</span>
                      )}
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value ? new Date(field.value) : undefined}
                    onSelect={(date) => {
                      field.onChange(date ? format(date, "yyyy-MM-dd") : "");
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="is_board_member"
          render={({ field }) => (
            <FormItem className={cn("flex flex-row items-start space-x-3 space-y-0", isRTL && "flex-row-reverse space-x-reverse")}>
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>{t("isBoardMember")}</FormLabel>
                <FormDescription>
                  {t("isBoardMemberDescription")}
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        <div className={cn("flex gap-3", isRTL && "flex-row-reverse")}>
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isLoading}
          >
            {t("cancel")}
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className={cn("h-4 w-4 animate-spin", isRTL ? "ml-2" : "mr-2")} />}
            {t("addMember")}
          </Button>
        </div>
      </form>
    </Form>
  );
}