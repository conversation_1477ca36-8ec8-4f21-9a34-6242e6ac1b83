# Fix for 404 Errors After Admin Password Update

## The Issue
After updating the admin password, the old authentication token becomes invalid, but the middleware still sees the cookie and tries to validate it, causing routing issues.

## Quick Fix Steps:

### 1. Clear Browser Data
- Open browser DevTools (F12)
- Go to Application/Storage tab
- Clear all cookies for localhost:3002
- Clear localStorage
- Refresh the page

### 2. Login Again
Use these credentials:
- **Username**: `admin` (NOT the email)
- **Password**: `123456`

### 3. Alternative - Use Incognito Mode
- Open a new incognito/private browser window
- Navigate to: http://localhost:3002
- Login with the credentials above

## Why This Happens
When you update a user's password, their existing JWT tokens become invalid. The browser still has the old token cookie, which causes the authentication to fail, but the middleware sees a token exists and doesn't redirect to login, causing a routing loop that appears as 404 errors.

## Permanent Solution
The application should handle this better by:
1. Clearing the auth token when it's invalid
2. Redirecting to login when token validation fails
3. Showing a proper error message

## If Problems Persist
Run this command to verify the admin user exists and has proper permissions:
```bash
node scripts/check-user-roles.js
```