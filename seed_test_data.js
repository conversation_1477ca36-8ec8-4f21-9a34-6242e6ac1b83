const { PrismaClient } = require('@prisma/client');
const { Decimal } = require('@prisma/client/runtime/library');

const prisma = new PrismaClient();

async function seedTestData() {
  try {
    console.log('Seeding test data...');

    // Create property types if they don't exist
    const propertyType = await prisma.propertyType.upsert({
      where: { id: 1 },
      update: {},
      create: {
        name_en: 'Villa',
        name_ar: 'فيلا',
        description_en: 'Standalone villa property',
        description_ar: 'عقار فيلا منفصلة',
        created_by: 1,
        updated_by: 1,
      },
    });
    console.log('Property type created:', propertyType.id);

    // Create property owners if they don't exist
    const owner = await prisma.propertyOwner.upsert({
      where: { id: 1 },
      update: {},
      create: {
        name_en: '<PERSON>-<PERSON>',
        name_ar: 'أحمد الراشد',
        email: '<EMAIL>',
        phone: '+968 9123 4567',
        mobile: '+968 9123 4567',
        address_en: '123 Main Street, Muscat',
        address_ar: '123 الشارع الرئيسي، مسقط',
        tax_id: 'TAX123456',
        bank_name: 'Bank Muscat',
        bank_account: '**********',
        iban: 'OM123456789',
        status: 'ACTIVE',
        created_by: 1,
        updated_by: 1,
      },
    });
    console.log('Property owner created:', owner.id);

    // Create a test user if needed
    const user = await prisma.user.upsert({
      where: { id: 1 },
      update: {},
      create: {
        username: 'admin',
        email: '<EMAIL>',
        password_hash: '$2b$10$dummy.hash.for.testing.purposes.only',
        first_name: 'Admin',
        last_name: 'User',
        is_active: true,
        role_id: 1,
      },
    });
    console.log('Test user created:', user.id);

    console.log('✅ Test data seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedTestData();