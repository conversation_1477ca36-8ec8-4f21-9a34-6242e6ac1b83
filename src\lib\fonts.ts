import { Inter } from 'next/font/google';

// Inter font for Latin text (English)
export const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});

// Tajawal font for Arabic text - using Google Fonts with local fallback
// Note: We'll preload this font and use CSS to apply it conditionally
export const tajawal = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-tajawal',
});

// Font class names for different locales
export const getFontClassName = (locale: string) => {
  if (locale === 'ar') {
    return `${tajawal.variable} font-tajawal`;
  }
  return `${inter.variable} font-inter`;
};

// CSS variables for font families
export const fontVariables = `${inter.variable} ${tajawal.variable}`;
