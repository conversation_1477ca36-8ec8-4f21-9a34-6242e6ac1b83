// API client utilities for authenticated requests

interface FetchOptions extends RequestInit {
  // Additional options if needed
}

export async function authenticatedFetch(url: string, options: FetchOptions = {}) {
  // Don't set Content-Type header if body is FormData
  const isFormData = options.body instanceof FormData;
  
  // Check if body is a string (JSON) but no Content-Type is set
  const isJsonString = typeof options.body === 'string';
  
  const defaultHeaders: HeadersInit = isFormData 
    ? {} 
    : isJsonString || options.body
    ? { 'Content-Type': 'application/json' }
    : {};
  
  const defaultOptions: RequestInit = {
    credentials: 'include', // Always include cookies for authentication
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  const mergedOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, mergedOptions);
    
    // Handle authentication errors globally
    if (response.status === 401) {
      // Redirect to login page on authentication error
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname;
        // Only redirect if not already on login page
        if (!currentPath.includes('/auth/login')) {
          // Extract locale from current path
          const pathSegments = currentPath.split('/');
          const locale = pathSegments[1] || 'en';
          window.location.href = `/${locale}/auth/login`;
        }
      }
      throw new Error('Authentication required');
    }
    
    // Check content type before parsing
    const contentType = response.headers.get('content-type');
    const isJson = contentType && contentType.includes('application/json');
    
    // If response is not JSON, throw appropriate error
    if (!isJson) {
      if (!response.ok) {
        throw new Error(`Server error: ${response.status} ${response.statusText}`);
      }
      throw new Error('Invalid response format: Expected JSON');
    }
    
    // Parse the response
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error('Failed to parse JSON response:', jsonError);
      throw new Error(`Failed to parse server response: ${response.status} ${response.statusText}`);
    }
    
    // Check if the API returned an error in the standard format
    if (!response.ok || (data.success === false)) {
      throw new Error(data.error?.message || data.error || `HTTP error! status: ${response.status}`);
    }
    
    // Return the full response for successful requests
    return data;
  } catch (error) {
    // Re-throw the error for the calling code to handle
    throw error;
  }
}

// Convenience methods
export const apiClient = {
  get: (url: string, options?: Omit<FetchOptions, 'body'>) => 
    authenticatedFetch(url, { ...options, method: 'GET' }),
  
  post: (url: string, data?: any, options?: Omit<FetchOptions, 'body'>) => 
    authenticatedFetch(url, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    }),
  
  put: (url: string, data?: any, options?: Omit<FetchOptions, 'body'>) => 
    authenticatedFetch(url, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    }),
  
  patch: (url: string, data?: any, options?: Omit<FetchOptions, 'body'>) => 
    authenticatedFetch(url, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    }),
  
  delete: (url: string, options?: Omit<FetchOptions, 'body'>) => 
    authenticatedFetch(url, { ...options, method: 'DELETE' }),

  // Resource-specific methods
  properties: {
    list: (params?: Record<string, any>) => {
      const searchParams = params ? new URLSearchParams(params).toString() : '';
      return apiClient.get(`/api/properties${searchParams ? `?${searchParams}` : ''}`);
    },
    get: (id: number) => apiClient.get(`/api/properties/${id}`),
    create: (data: any) => apiClient.post('/api/properties', data),
    update: (id: number, data: any) => apiClient.put(`/api/properties/${id}`, data),
    delete: (id: number) => apiClient.delete(`/api/properties/${id}`),
  },

  units: {
    list: (params?: Record<string, any>) => {
      const searchParams = params ? new URLSearchParams(params).toString() : '';
      return apiClient.get(`/api/units${searchParams ? `?${searchParams}` : ''}`);
    },
    get: (id: number) => apiClient.get(`/api/units/${id}`),
    create: (data: any) => apiClient.post('/api/units', data),
    update: (id: number, data: any) => apiClient.put(`/api/units/${id}`, data),
    delete: (id: number) => apiClient.delete(`/api/units/${id}`),
  },

  tenants: {
    list: (params?: Record<string, any>) => {
      const searchParams = params ? new URLSearchParams(params).toString() : '';
      return apiClient.get(`/api/tenants${searchParams ? `?${searchParams}` : ''}`);
    },
    get: (id: number) => apiClient.get(`/api/tenants/${id}`),
    create: (data: any) => apiClient.post('/api/tenants', data),
    update: (id: number, data: any) => apiClient.put(`/api/tenants/${id}`, data),
    delete: (id: number) => apiClient.delete(`/api/tenants/${id}`),
  },

  maintenance: {
    list: (params?: Record<string, any>) => {
      const searchParams = params ? new URLSearchParams(params).toString() : '';
      return apiClient.get(`/api/maintenance${searchParams ? `?${searchParams}` : ''}`);
    },
    get: (id: number) => apiClient.get(`/api/maintenance/${id}`),
    create: (data: any) => apiClient.post('/api/maintenance', data),
    update: (id: number, data: any) => apiClient.put(`/api/maintenance/${id}`, data),
    delete: (id: number) => apiClient.delete(`/api/maintenance/${id}`),
  },

  ownersAssociations: {
    list: (params?: Record<string, any>) => {
      const searchParams = params ? new URLSearchParams(params).toString() : '';
      return apiClient.get(`/api/owners-associations${searchParams ? `?${searchParams}` : ''}`);
    },
    get: (id: number) => apiClient.get(`/api/owners-associations/${id}`),
    create: (data: any) => apiClient.post('/api/owners-associations', data),
    update: (id: number, data: any) => apiClient.patch(`/api/owners-associations/${id}`, data),
    delete: (id: number) => apiClient.delete(`/api/owners-associations/${id}`),
    
    // Members management
    members: {
      list: (associationId: number, params?: Record<string, any>) => {
        const searchParams = params ? new URLSearchParams(params).toString() : '';
        return apiClient.get(`/api/owners-associations/${associationId}/members${searchParams ? `?${searchParams}` : ''}`);
      },
      get: (associationId: number, memberId: number) => 
        apiClient.get(`/api/owners-associations/${associationId}/members/${memberId}`),
      create: (associationId: number, data: any) => 
        apiClient.post(`/api/owners-associations/${associationId}/members`, data),
      update: (associationId: number, memberId: number, data: any) => 
        apiClient.put(`/api/owners-associations/${associationId}/members/${memberId}`, data),
      delete: (associationId: number, memberId: number) => 
        apiClient.delete(`/api/owners-associations/${associationId}/members/${memberId}`),
    },
    
    // Subscriptions management
    subscriptions: {
      list: (associationId: number, params?: Record<string, any>) => {
        const searchParams = params ? new URLSearchParams(params).toString() : '';
        return apiClient.get(`/api/owners-associations/${associationId}/subscriptions${searchParams ? `?${searchParams}` : ''}`);
      },
      get: (associationId: number, subscriptionId: number) => 
        apiClient.get(`/api/owners-associations/${associationId}/subscriptions/${subscriptionId}`),
      create: (associationId: number, data: any) => 
        apiClient.post(`/api/owners-associations/${associationId}/subscriptions`, data),
      update: (associationId: number, subscriptionId: number, data: any) => 
        apiClient.put(`/api/owners-associations/${associationId}/subscriptions/${subscriptionId}`, data),
      delete: (associationId: number, subscriptionId: number) => 
        apiClient.delete(`/api/owners-associations/${associationId}/subscriptions/${subscriptionId}`),
    },
    
    // Transactions management
    transactions: {
      list: (associationId: number, params?: Record<string, any>) => {
        const searchParams = params ? new URLSearchParams(params).toString() : '';
        return apiClient.get(`/api/owners-associations/${associationId}/transactions${searchParams ? `?${searchParams}` : ''}`);
      },
      get: (associationId: number, transactionId: number) => 
        apiClient.get(`/api/owners-associations/${associationId}/transactions/${transactionId}`),
      create: (associationId: number, data: any) => 
        apiClient.post(`/api/owners-associations/${associationId}/transactions`, data),
      update: (associationId: number, transactionId: number, data: any) => 
        apiClient.put(`/api/owners-associations/${associationId}/transactions/${transactionId}`, data),
      delete: (associationId: number, transactionId: number) => 
        apiClient.delete(`/api/owners-associations/${associationId}/transactions/${transactionId}`),
    },
    
    // Reports
    reports: {
      get: (associationId: number, params?: Record<string, any>) => {
        const searchParams = params ? new URLSearchParams(params).toString() : '';
        return apiClient.get(`/api/owners-associations/${associationId}/reports${searchParams ? `?${searchParams}` : ''}`);
      },
      export: (associationId: number, params?: Record<string, any>) => {
        const searchParams = params ? new URLSearchParams(params).toString() : '';
        return authenticatedFetch(`/api/owners-associations/${associationId}/reports/export${searchParams ? `?${searchParams}` : ''}`);
      },
    },
  },
};