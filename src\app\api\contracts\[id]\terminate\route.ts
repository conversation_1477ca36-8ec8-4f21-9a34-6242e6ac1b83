import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ApiResponseBuilder } from "@/lib/api-response";
import { z } from "zod";


const terminateSchema = z.object({
  termination_date: z.string().min(1, "Termination date is required"),
  reason: z.string().optional(),
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for contracts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "contracts", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create contracts");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const contractId = parseInt(id);
    if (isNaN(contractId)) {
      return ApiResponseBuilder.error("Invalid contract ID", "BAD_REQUEST", 400);
    }

    const body = await request.json();
    const validatedData = terminateSchema.parse(body);

    // Check if contract exists and is active
    const contract = await prisma.contract.findUnique({
      where: { id: contractId },
    });

    if (!contract) {
      return ApiResponseBuilder.error("Contract not found", "NOT_FOUND", 404);
    }

    if (contract.status !== "ACTIVE") {
      return ApiResponseBuilder.error("Only active contracts can be terminated", "BAD_REQUEST", 400);
    }

    const terminationDate = new Date(validatedData.termination_date);
    const startDate = new Date(contract.start_date);

    if (terminationDate < startDate) {
      return ApiResponseBuilder.error("Termination date cannot be before contract start date", "BAD_REQUEST", 400);
    }

    // Update contract status to terminated
    const updatedContract = await prisma.contract.update({
      where: { id: contractId },
      data: {
        status: "TERMINATED",
        end_date: terminationDate,
        notes: validatedData.reason 
          ? `${contract.notes ? contract.notes + "\n\n" : ""}Termination Reason: ${validatedData.reason}`
          : contract.notes,
        updated_by: decoded.id,
      },
      include: {
        property: true,
        unit: true,
        tenants: {
          include: {
            tenant: true,
          },
        },
      },
    });

    // Check if unit should be marked as available
    if (contract.unit_id) {
      const otherActiveContracts = await prisma.contract.count({
        where: {
          id: { not: contractId },
          unit_id: contract.unit_id,
          status: "ACTIVE",
        },
      });

      if (otherActiveContracts === 0) {
        await prisma.unit.update({
          where: { id: contract.unit_id },
          data: { status: "AVAILABLE" },
        });
      }
    }

    return ApiResponseBuilder.success({
      message: "Contract terminated successfully",
      contract: updatedContract,
    });
  } catch (error) {
    console.error("Error terminating contract:", error);
    return ApiResponseBuilder.error("Failed to terminate contract", "INTERNAL_ERROR", 500);
  }
}