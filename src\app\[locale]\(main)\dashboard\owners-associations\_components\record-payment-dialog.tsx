"use client";

import { useState } from "react";
import { useTranslations, useLocale } from "next-intl";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { CalendarIcon, AlertCircle, Banknote } from "lucide-react";
import { apiClient } from "@/lib/api-client";
import { formatCurrency } from "@/lib/utils";
import { type SubscriptionPaymentWithRelations } from "@/types/owners-association";
import { useRTL } from "@/hooks/use-rtl";

const recordPaymentFormSchema = z.object({
  amount: z.string().regex(/^\d+(\.\d{1,3})?$/, "Invalid amount format"),
  payment_date: z.date({
    required_error: "Payment date is required",
  }),
  payment_method: z.enum(["CASH", "BANK_TRANSFER", "CREDIT_CARD", "DEBIT_CARD", "CHECK", "OTHER"]),
  installment: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  create_transaction: z.boolean().default(true),
});

type RecordPaymentFormValues = z.infer<typeof recordPaymentFormSchema>;

interface RecordPaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  payment: SubscriptionPaymentWithRelations;
  associationId: number;
  onSuccess: () => void;
}

export function RecordPaymentDialog({
  open,
  onOpenChange,
  payment,
  associationId,
  onSuccess,
}: RecordPaymentDialogProps) {
  const locale = useLocale();
  const { isRTL } = useRTL();
  const t = useTranslations("ownersAssociations.payments.recordPayment");
  const dateLocale = locale === 'ar' ? ar : enUS;
  const [loading, setLoading] = useState(false);

  const amountDue = parseFloat(payment.amount_due?.toString() || payment.amount.toString());
  const amountPaid = parseFloat(payment.amount_paid?.toString() || "0");
  const remainingBalance = amountDue - amountPaid;
  const paymentPercentage = amountDue > 0 ? (amountPaid / amountDue) * 100 : 0;

  const form = useForm<RecordPaymentFormValues>({
    resolver: zodResolver(recordPaymentFormSchema),
    defaultValues: {
      amount: remainingBalance.toFixed(3),
      payment_date: new Date(),
      payment_method: "CASH",
      installment: "",
      notes: "",
      create_transaction: true,
    },
  });

  const onSubmit = async (values: RecordPaymentFormValues) => {
    try {
      setLoading(true);

      const response = await apiClient.post(
        `/api/owners-associations/${associationId}/subscription-payments/${payment.id}`,
        {
          ...values,
          payment_date: format(values.payment_date, "yyyy-MM-dd"),
          installment: values.installment ? parseInt(values.installment) : null,
          notes: values.notes || null,
        }
      );

      if (response.success) {
        toast.success(t("success"));
        onSuccess();
      } else {
        toast.error(response.error || t("error"));
      }
    } catch (error) {
      console.error("Error recording payment:", error);
      toast.error(t("error"));
    } finally {
      setLoading(false);
    }
  };

  const amountValue = form.watch("amount");
  const paymentAmount = parseFloat(amountValue || "0");
  const isValidAmount = paymentAmount > 0 && paymentAmount <= remainingBalance;
  const isPartialPayment = isValidAmount && paymentAmount < remainingBalance;
  const newPaymentPercentage = amountDue > 0 ? ((amountPaid + paymentAmount) / amountDue) * 100 : 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>
            {t("description", {
              member: payment.member.full_name,
              unit: payment.member.unit_number,
            })}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Payment Summary */}
            <div className="rounded-lg border p-4 space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t("amountDue")}:</span>
                <span className="font-medium">{formatCurrency(amountDue)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t("amountPaid")}:</span>
                <span className="font-medium text-green-600">{formatCurrency(amountPaid)}</span>
              </div>
              <div className="flex justify-between text-sm font-medium">
                <span>{t("remainingBalance")}:</span>
                <span className="text-red-600">{formatCurrency(remainingBalance)}</span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{t("currentProgress")}:</span>
                  <span>{paymentPercentage.toFixed(1)}%</span>
                </div>
                <Progress value={paymentPercentage} className="h-2" />
              </div>
            </div>

            {/* Payment Amount */}
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("paymentAmount")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Banknote className={cn(
                        "absolute top-2.5 h-4 w-4 text-muted-foreground",
                        isRTL ? "right-3" : "left-3"
                      )} />
                      <Input
                        {...field}
                        placeholder="0.000"
                        className={isRTL ? "pr-9" : "pl-9"}
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    {isPartialPayment && (
                      <span className="text-amber-600">
                        {t("partialPaymentNote", {
                          percentage: newPaymentPercentage.toFixed(1),
                        })}
                      </span>
                    )}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Date */}
            <FormField
              control={form.control}
              name="payment_date"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>{t("paymentDate")}</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP", { locale: dateLocale })
                          ) : (
                            <span>{t("selectDate")}</span>
                          )}
                          <CalendarIcon className={cn(
                            "ml-auto h-4 w-4 opacity-50",
                            isRTL && "ml-0 mr-auto"
                          )} />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Method */}
            <FormField
              control={form.control}
              name="payment_method"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("paymentMethod")}</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t("selectPaymentMethod")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="CASH">{t("methods.cash")}</SelectItem>
                      <SelectItem value="BANK_TRANSFER">{t("methods.bankTransfer")}</SelectItem>
                      <SelectItem value="CREDIT_CARD">{t("methods.creditCard")}</SelectItem>
                      <SelectItem value="DEBIT_CARD">{t("methods.debitCard")}</SelectItem>
                      <SelectItem value="CHECK">{t("methods.check")}</SelectItem>
                      <SelectItem value="OTHER">{t("methods.other")}</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Installment Number */}
            <FormField
              control={form.control}
              name="installment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("installmentNumber")}</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      value={field.value || ""}
                      type="number"
                      placeholder={t("installmentPlaceholder")}
                      min="1"
                    />
                  </FormControl>
                  <FormDescription>
                    {t("installmentDescription")}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("notes")}</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      value={field.value || ""}
                      placeholder={t("notesPlaceholder")}
                      className="resize-none"
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Create Transaction */}
            <FormField
              control={form.control}
              name="create_transaction"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>{t("createTransaction")}</FormLabel>
                    <FormDescription>
                      {t("createTransactionDescription")}
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            {!isValidAmount && amountValue && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {paymentAmount <= 0
                    ? t("invalidAmount")
                    : t("amountExceedsBalance", {
                        max: formatCurrency(remainingBalance),
                      })}
                </AlertDescription>
              </Alert>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                {t("cancel")}
              </Button>
              <Button type="submit" disabled={loading || !isValidAmount}>
                {loading ? t("processing") : t("recordPayment")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}