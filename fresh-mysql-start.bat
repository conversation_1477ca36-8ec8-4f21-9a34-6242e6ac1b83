@echo off
echo ========================================
echo Fresh MySQL Start (Nuclear Option)
echo ========================================
echo.

echo WARNING: This will completely reset MySQL
echo All databases will be lost!
echo.
set /p confirm="Are you sure? Type YES to continue: "
if not "%confirm%"=="YES" goto :end

echo.
echo 1. Stopping MySQL...
taskkill /f /im mysqld.exe 2>nul
timeout /t 5 /nobreak >nul

echo 2. Backing up property_management if it exists...
if exist "C:\xampp\mysql\data\property_management" (
    if not exist "C:\xampp\mysql\db_backup" mkdir "C:\xampp\mysql\db_backup"
    xcopy "C:\xampp\mysql\data\property_management" "C:\xampp\mysql\db_backup\property_management\" /E /I /Y
    echo ✅ Database backed up to db_backup folder
)

echo 3. Removing entire data directory...
cd "C:\xampp\mysql"
if exist "data" (
    rmdir /s /q "data"
    echo ✅ Data directory removed
)

echo 4. Creating fresh data directory...
mkdir "data"
cd "data"

echo 5. Initializing fresh MySQL system...
cd "..\bin"
mysqld --initialize-insecure --basedir=C:\xampp\mysql --datadir=C:\xampp\mysql\data

echo 6. Creating minimal configuration...
echo [mysqld] > my.ini
echo port=3306 >> my.ini
echo basedir=C:/xampp/mysql >> my.ini
echo datadir=C:/xampp/mysql/data >> my.ini
echo skip-grant-tables >> my.ini
echo skip-networking >> my.ini

echo.
echo ========================================
echo Fresh MySQL initialization complete!
echo Try starting with: mysqld --console
echo ========================================

:end
pause
