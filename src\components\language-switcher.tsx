'use client';

import { useLocale, useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { usePathname, useRouter } from '@/i18n/navigation';
import { routing } from '@/i18n/routing';
import { useRTL } from '@/hooks/use-rtl';

const languageConfig = {
  en: {
    name: 'English',
    flag: '🇺🇸',
  },
  ar: {
    name: 'العربية',
    flag: '🇴🇲',
  },
} as const;

export function LanguageSwitcher() {
  const t = useTranslations('language');
  const locale = useLocale();
  const pathname = usePathname();
  const router = useRouter();
  const { isRTL, direction } = useRTL();

  const switchLanguage = (newLocale: string) => {
    // Use next-intl's navigation API for proper locale switching
    router.push(pathname, { locale: newLocale });
  };

  const currentLanguage = languageConfig[locale as keyof typeof languageConfig];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="icon">
          <span className="text-base" role="img" aria-label={currentLanguage.name}>
            {currentLanguage.flag}
          </span>
          <span className="sr-only">{t('switchLanguage')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={isRTL ? "start" : "end"}>
        {routing.locales.map((lang) => {
          const language = languageConfig[lang as keyof typeof languageConfig];
          return (
            <DropdownMenuItem
              key={lang}
              onClick={() => switchLanguage(lang)}
              className={locale === lang ? 'bg-accent' : ''}
            >
              <span className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <span className="text-lg" role="img" aria-label={language.name}>
                  {language.flag}
                </span>
                {language.name}
                {locale === lang && (
                  <span className="text-xs text-muted-foreground">
                    ({t('current')})
                  </span>
                )}
              </span>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
