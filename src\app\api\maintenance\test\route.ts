import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { ApiResponseBuilder } from "@/lib/api-response";

// GET /api/maintenance/test - Test database connection and table existence
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for maintenance
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "maintenance", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view maintenance");
    }

    // Test basic database connection
    const count = await db.maintenanceRequest.count();
    
    // Get table info
    const tableExists = true; // If we get here, table exists
    
    // Try to get a sample record
    const sample = await db.maintenanceRequest.findFirst({
      take: 1,
      include: {
        property: true,
        unit: true,
        tenant: true,
      }
    });
    
    return NextResponse.json({
      success: true,
      data: {
        tableExists,
        recordCount: count,
        sampleRecord: sample,
        message: "Database connection successful"
      }
    });
  } catch (error) {
    console.error("Database test error:", error);
    
    return NextResponse.json({
      success: false,
      error: {
        message: error instanceof Error ? error.message : "Unknown error",
        type: error instanceof Error ? error.constructor.name : "UnknownError",
        details: error instanceof Error ? error.toString() : "No details available"
      }
    }, { status: 500 });
  }
}