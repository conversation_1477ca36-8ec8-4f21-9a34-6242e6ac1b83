import { Suspense } from "react";
import { Metada<PERSON> } from "next";
import { getTranslations } from "next-intl/server";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { OwnerPayoutsDataTable } from "./_components/owner-payouts-data-table";
import { getOwnerPayoutColumns } from "./_components/owner-payout-columns";
import Link from "next/link";

interface OwnerPayoutsPageProps {
  params: Promise<{
    locale: string;
  }>;
}

export async function generateMetadata({ params }: OwnerPayoutsPageProps): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "ownerPayouts" });
  
  return {
    title: t("title"),
    description: t("description"),
  };
}

export default async function OwnerPayoutsPage({ params }: OwnerPayoutsPageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale });

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t("ownerPayouts.title")}
          </h1>
          <p className="text-muted-foreground">
            {t("ownerPayouts.description")}
          </p>
        </div>
        <Button asChild>
          <Link href={`/${locale}/dashboard/owner-payouts/new`}>
            <Plus className="mr-2 h-4 w-4" />
            {t("ownerPayouts.createPayout")}
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("ownerPayouts.allPayouts")}</CardTitle>
          <CardDescription>
            {t("ownerPayouts.viewManagePayouts")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div>{t("common.loading")}</div>}>
            <OwnerPayoutsDataTable columns={getOwnerPayoutColumns} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}