import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { associationSubscriptionUpdateSchema } from "@/types/owners-association";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, checkUserPermission } from "@/lib/auth";
import { Decimal } from "@prisma/client/runtime/library";

// GET /api/owners-associations/[id]/subscriptions/[subscriptionId] - Get a single subscription
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; subscriptionId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await checkUserPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view association subscriptions");
    }

    const { id: idParam, subscriptionId: subscriptionIdParam } = await params;
    const associationId = parseInt(idParam);
    const subscriptionId = parseInt(subscriptionIdParam);

    if (isNaN(associationId) || isNaN(subscriptionId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    const subscription = await db.associationSubscription.findFirst({
      where: {
        id: subscriptionId,
        association_id: associationId,
      },
      include: {
        association: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
          },
        },
        _count: {
          select: {
            payments: true,
          },
        },
        payments: {
          where: {
            status: {
              in: ["PAID", "PARTIALLY_PAID"],
            },
          },
          select: {
            amount: true,
          },
        },
      },
    });

    if (!subscription) {
      return ApiResponseBuilder.notFound("Subscription not found");
    }

    // Calculate total collected
    const totalCollected = subscription.payments.reduce((sum, payment) => {
      return sum + parseFloat(payment.amount.toString());
    }, 0);

    // Transform subscription to handle Decimal serialization
    const transformedSubscription = {
      ...subscription,
      amount: subscription.amount.toString(),
      total_collected: totalCollected.toFixed(3),
      payments: undefined, // Remove payments array from response
    };

    return ApiResponseBuilder.success(transformedSubscription);
  } catch (error: any) {
    console.error("Error fetching subscription:", error);
    return ApiResponseBuilder.error("Failed to fetch subscription", "INTERNAL_ERROR", 500);
  }
}

// PATCH /api/owners-associations/[id]/subscriptions/[subscriptionId] - Update a subscription
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; subscriptionId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for owners-associations
    const canUpdate = await checkUserPermission(decoded.id, "owners-associations", "UPDATE");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update association subscriptions");
    }

    const { id: idParam, subscriptionId: subscriptionIdParam } = await params;
    const associationId = parseInt(idParam);
    const subscriptionId = parseInt(subscriptionIdParam);

    if (isNaN(associationId) || isNaN(subscriptionId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    const body = await request.json();
    const validatedData = associationSubscriptionUpdateSchema.parse(body);

    // Check if subscription exists
    const existingSubscription = await db.associationSubscription.findFirst({
      where: {
        id: subscriptionId,
        association_id: associationId,
      },
    });

    if (!existingSubscription) {
      return ApiResponseBuilder.notFound("Subscription not found");
    }

    // Prepare update data
    const updateData: any = {};
    
    if (validatedData.name_en !== undefined) updateData.name_en = validatedData.name_en;
    if (validatedData.name_ar !== undefined) updateData.name_ar = validatedData.name_ar;
    if (validatedData.frequency !== undefined) updateData.frequency = validatedData.frequency;
    if (validatedData.is_active !== undefined) updateData.is_active = validatedData.is_active;
    if (validatedData.amount !== undefined) updateData.amount = new Decimal(validatedData.amount);

    const updatedSubscription = await db.associationSubscription.update({
      where: { id: subscriptionId },
      data: updateData,
    });

    // Transform subscription to handle Decimal serialization
    const transformedSubscription = {
      ...updatedSubscription,
      amount: updatedSubscription.amount.toString(),
    };

    return ApiResponseBuilder.success(transformedSubscription);
  } catch (error: any) {
    console.error("Error updating subscription:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to update subscription", "INTERNAL_ERROR", 500);
  }
}

// PUT /api/owners-associations/[id]/subscriptions/[subscriptionId] - Update a subscription (alias for PATCH)
export async function PUT(
  request: NextRequest,
  params: { params: Promise<{ id: string; subscriptionId: string }> }
) {
  return PATCH(request, params);
}

// DELETE /api/owners-associations/[id]/subscriptions/[subscriptionId] - Delete a subscription
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; subscriptionId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for owners-associations
    const canDelete = await checkUserPermission(decoded.id, "owners-associations", "DELETE");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete association subscriptions");
    }

    const { id: idParam, subscriptionId: subscriptionIdParam } = await params;
    const associationId = parseInt(idParam);
    const subscriptionId = parseInt(subscriptionIdParam);

    if (isNaN(associationId) || isNaN(subscriptionId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    // Check if subscription exists
    const existingSubscription = await db.associationSubscription.findFirst({
      where: {
        id: subscriptionId,
        association_id: associationId,
      },
      include: {
        _count: {
          select: {
            payments: true,
          },
        },
      },
    });

    if (!existingSubscription) {
      return ApiResponseBuilder.notFound("Subscription not found");
    }

    // Check if subscription has any payments
    if (existingSubscription._count.payments > 0) {
      return ApiResponseBuilder.error(
        "Cannot delete subscription with existing payments",
        "HAS_DEPENDENCIES",
        400
      );
    }

    // Delete the subscription
    await db.associationSubscription.delete({
      where: { id: subscriptionId },
    });

    return ApiResponseBuilder.success({ message: "Subscription deleted successfully" });
  } catch (error: any) {
    console.error("Error deleting subscription:", error);
    return ApiResponseBuilder.error("Failed to delete subscription", "INTERNAL_ERROR", 500);
  }
}