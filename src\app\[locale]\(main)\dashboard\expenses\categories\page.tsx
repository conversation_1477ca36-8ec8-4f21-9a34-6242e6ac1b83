import { getTranslations } from "next-intl/server";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import Link from "next/link";
import { db } from "@/lib/db";
import { CategoriesList } from "./_components/categories-list";

interface ExpenseCategoriesPageProps {
  params: Promise<{ locale: string }>;
}

async function getExpenseCategories() {
  try {
    const categories = await db.expenseCategory.findMany({
      where: { is_active: true },
      orderBy: [
        { sort_order: "asc" },
        { name_en: "asc" }
      ],
      include: {
        _count: {
          select: {
            expenses: true
          }
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });

    return categories;
  } catch (error) {
    console.error('Error fetching expense categories:', error);
    return [];
  }
}

export default async function ExpenseCategoriesPage({ params }: ExpenseCategoriesPageProps) {
  const { locale } = await params;
  const t = await getTranslations("expenses");
  const categories = await getExpenseCategories();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t("categories.title")}
          </h1>
          <p className="text-muted-foreground">
            {t("categories.description")}
          </p>
        </div>
        <Button asChild>
          <Link href={`/${locale}/dashboard/expenses/categories/create`}>
            <Plus className="mr-2 h-4 w-4" />
            {t("categories.addCategory")}
          </Link>
        </Button>
      </div>

      {/* Categories List */}
      <CategoriesList categories={categories} />
    </div>
  );
}
