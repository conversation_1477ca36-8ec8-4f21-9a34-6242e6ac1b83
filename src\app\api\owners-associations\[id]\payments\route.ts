import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { recordPaymentSchema, paymentFilterSchema } from "@/types/owners-association";
import { z } from "zod";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";
import { Decimal } from "@prisma/client/runtime/library";

// GET /api/owners-associations/[id]/payments - List all payments for an association
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await hasPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view association payments");
    }

    const { id: idParam } = await params;
    const associationId = parseInt(idParam);

    if (isNaN(associationId)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    // Check if association exists
    const association = await db.ownersAssociation.findUnique({
      where: { id: associationId },
    });

    if (!association) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    const searchParams = request.nextUrl.searchParams;
    
    // Parse filters
    const filters = paymentFilterSchema.parse({
      status: searchParams.get("status") || undefined,
      member_id: searchParams.get("member_id") ? parseInt(searchParams.get("member_id")!) : undefined,
      date_from: searchParams.get("date_from") || undefined,
      date_to: searchParams.get("date_to") || undefined,
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
      pageSize: searchParams.get("pageSize") ? parseInt(searchParams.get("pageSize")!) : 10,
      sortBy: searchParams.get("sortBy") || "due_date",
      sortOrder: searchParams.get("sortOrder") || "desc",
    });

    // Build where clause
    const where: any = {
      subscription: {
        association_id: associationId,
      },
    };

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.member_id) {
      where.member_id = filters.member_id;
    }

    if (filters.date_from || filters.date_to) {
      where.due_date = {};
      if (filters.date_from) {
        where.due_date.gte = new Date(filters.date_from);
      }
      if (filters.date_to) {
        where.due_date.lte = new Date(filters.date_to);
      }
    }

    // Calculate pagination
    const page = filters.page || 1;
    const pageSize = filters.pageSize || 10;
    const skip = (page - 1) * pageSize;

    // Update overdue statuses
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    await db.subscriptionPayment.updateMany({
      where: {
        subscription: {
          association_id: associationId,
        },
        status: "UNPAID",
        due_date: {
          lt: today,
        },
      },
      data: {
        status: "OVERDUE",
      },
    });

    // Fetch payments
    const [payments, total] = await Promise.all([
      db.subscriptionPayment.findMany({
        where,
        include: {
          subscription: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
              frequency: true,
            },
          },
          member: {
            select: {
              id: true,
              full_name: true,
              unit_number: true,
            },
          },
          creator: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || "due_date"]: filters.sortOrder || "desc",
        },
        skip,
        take: pageSize,
      }),
      db.subscriptionPayment.count({ where }),
    ]);

    // Transform payments to handle Decimal serialization
    const transformedPayments = payments.map(payment => ({
      ...payment,
      amount: payment.amount.toString(),
    }));

    // Calculate summary statistics
    const summaryStats = await db.subscriptionPayment.aggregate({
      where,
      _sum: {
        amount: true,
      },
      _count: {
        _all: true,
      },
    });

    const statusCounts = await db.subscriptionPayment.groupBy({
      by: ["status"],
      where: {
        subscription: {
          association_id: associationId,
        },
      },
      _count: {
        _all: true,
      },
    });

    const totalAmount = summaryStats._sum.amount || new Decimal(0);
    const stats = {
      total_amount: totalAmount.toString(),
      total_count: summaryStats._count._all,
      status_breakdown: statusCounts.reduce((acc, item) => {
        acc[item.status.toLowerCase()] = item._count._all;
        return acc;
      }, {} as Record<string, number>),
    };

    return ApiResponseBuilder.success({
      payments: transformedPayments,
      stats,
    }, {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error: any) {
    console.error("Error fetching association payments:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }
    
    return ApiResponseBuilder.error("Failed to fetch association payments", "INTERNAL_ERROR", 500);
  }
}

// POST /api/owners-associations/[id]/payments - Record a payment for a subscription
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for owners-associations
    const canCreate = await hasPermission(decoded.id, "owners-associations", "CREATE");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to record association payments");
    }

    const { id: idParam } = await params;
    const associationId = parseInt(idParam);

    if (isNaN(associationId)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    // Check if association exists
    const association = await db.ownersAssociation.findUnique({
      where: { id: associationId },
    });

    if (!association) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    const body = await request.json();
    
    // Create a modified schema that works with basic fields for backward compatibility
    const basicRecordPaymentSchema = z.object({
      member_id: z.number().int().positive(),
      amount: z.string().regex(/^\d+(\.\d{1,3})?$/, "Invalid amount format"),
      payment_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
      payment_method: z.enum(["CASH", "BANK_TRANSFER", "CREDIT_CARD", "DEBIT_CARD", "CHECK", "OTHER"]),
      reference_number: z.string().optional().nullable(),
      notes: z.string().optional().nullable(),
    });
    
    const validatedData = basicRecordPaymentSchema.parse(body);

    // Check if member exists and belongs to the association
    const member = await db.associationMember.findFirst({
      where: {
        id: validatedData.member_id,
        association_id: associationId,
      },
    });

    if (!member) {
      return ApiResponseBuilder.error("Member not found in this association", "NOT_FOUND", 404);
    }

    // Find active subscription for the association
    const activeSubscription = await db.associationSubscription.findFirst({
      where: {
        association_id: associationId,
        is_active: true,
      },
      orderBy: {
        created_at: "desc",
      },
    });

    if (!activeSubscription) {
      return ApiResponseBuilder.error("No active subscription found for this association", "NOT_FOUND", 404);
    }

    // Find the most recent unpaid or overdue payment for this member
    const unpaidPayment = await db.subscriptionPayment.findFirst({
      where: {
        subscription_id: activeSubscription.id,
        member_id: validatedData.member_id,
        status: {
          in: ["UNPAID", "OVERDUE"],
        },
      },
      orderBy: {
        due_date: "asc",
      },
    });

    const paymentAmount = new Decimal(validatedData.amount);
    const paymentDate = new Date(validatedData.payment_date);

    if (unpaidPayment) {
      // Update existing payment
      const amountDue = new Decimal(unpaidPayment.amount.toString());
      const isFullPayment = paymentAmount.equals(amountDue) || paymentAmount.greaterThan(amountDue);

      const updatedPayment = await db.subscriptionPayment.update({
        where: { id: unpaidPayment.id },
        data: {
          payment_date: paymentDate,
          status: isFullPayment ? "PAID" : "PARTIALLY_PAID",
          payment_method: validatedData.payment_method,
          reference_number: validatedData.reference_number,
          notes: validatedData.notes,
        },
        include: {
          subscription: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
            },
          },
          member: {
            select: {
              id: true,
              full_name: true,
              unit_number: true,
            },
          },
        },
      });

      // Transform payment to handle Decimal serialization
      const transformedPayment = {
        ...updatedPayment,
        amount: updatedPayment.amount.toString(),
      };

      return ApiResponseBuilder.success(transformedPayment);
    } else {
      // Create new payment record
      const currentDate = new Date();
      const dueDate = new Date();
      
      // Calculate due date based on subscription frequency
      switch (activeSubscription.frequency) {
        case "MONTHLY":
          dueDate.setMonth(dueDate.getMonth() + 1);
          break;
        case "QUARTERLY":
          dueDate.setMonth(dueDate.getMonth() + 3);
          break;
        case "YEARLY":
          dueDate.setFullYear(dueDate.getFullYear() + 1);
          break;
      }

      const payment = await db.subscriptionPayment.create({
        data: {
          subscription_id: activeSubscription.id,
          member_id: validatedData.member_id,
          payment_date: paymentDate,
          due_date: dueDate,
          amount: paymentAmount,
          status: "PAID",
          payment_method: validatedData.payment_method,
          reference_number: validatedData.reference_number,
          notes: validatedData.notes,
          created_by: decoded.id,
        },
        include: {
          subscription: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
            },
          },
          member: {
            select: {
              id: true,
              full_name: true,
              unit_number: true,
            },
          },
        },
      });

      // Transform payment to handle Decimal serialization
      const transformedPayment = {
        ...payment,
        amount: payment.amount.toString(),
      };

      return ApiResponseBuilder.success(transformedPayment);
    }
  } catch (error: any) {
    console.error("Error recording payment:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to record payment", "INTERNAL_ERROR", 500);
  }
}