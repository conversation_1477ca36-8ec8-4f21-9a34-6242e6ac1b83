{"tenants": {"form": {"nationalId": "National ID", "nationalIdExpiry": "National ID Expiry", "dateOfBirth": "Date of Birth", "nationality": "Nationality", "occupation": "Occupation", "companyName": "Company Name", "placeholders": {"nationalId": "Enter national ID", "nationality": "Enter nationality", "occupation": "Enter occupation", "companyName": "Enter company name"}}, "table": {"nationalId": "National ID", "nationality": "Nationality", "documents": "Documents", "emergencyContact": "Emergency Contact"}, "documents": {"title": "Documents", "addDocument": "Add Document", "noDocuments": "No documents uploaded", "uploadDocument": "Upload Document", "documentType": "Document Type", "documentNumber": "Document Number", "issueDate": "Issue Date", "expiryDate": "Expiry Date", "uploadedBy": "Uploaded By", "uploadedAt": "Uploaded At", "fileSize": "File Size", "actions": "Actions", "download": "Download", "delete": "Delete", "types": {"NATIONAL_ID": "National ID", "PASSPORT": "Passport", "DRIVING_LICENSE": "Driving License", "EMPLOYMENT_CONTRACT": "Employment Contract", "SALARY_CERTIFICATE": "Salary Certificate", "BANK_STATEMENT": "Bank Statement", "OTHER": "Other"}, "validation": {"fileRequired": "Please select a file", "documentTypeRequired": "Document type is required", "fileSizeLimit": "File size must be less than 10MB"}}, "emergencyContacts": {"title": "Emergency Contacts", "addContact": "Add Emergency Contact", "editContact": "Edit Emergency Contact", "noContacts": "No emergency contacts added", "name": "Name", "relationship": "Relationship", "phone": "Phone", "email": "Email", "address": "Address", "isPrimary": "Primary Contact", "placeholders": {"name": "Enter contact name", "relationship": "e.g., Spouse, Parent, Sibling", "phone": "Enter phone number", "email": "Enter email address", "address": "Enter address"}, "validation": {"nameRequired": "Name is required", "relationshipRequired": "Relationship is required", "phoneRequired": "Phone number is required"}}, "tabs": {"overview": "Overview", "documents": "Documents", "emergencyContacts": "Emergency Contacts", "rentalHistory": "Rental History"}}}