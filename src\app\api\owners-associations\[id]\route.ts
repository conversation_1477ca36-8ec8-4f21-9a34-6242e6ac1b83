import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ownersAssociationUpdateSchema } from "@/types/owners-association";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, checkUserPermission } from "@/lib/auth";

// GET /api/owners-associations/[id] - Get a single owners association
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await checkUserPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view owners associations");
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    const association = await db.ownersAssociation.findUnique({
      where: { id },
      include: {
        property: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            address_en: true,
            address_ar: true,
            property_type: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
              },
            },
          },
        },
        members: {
          orderBy: { full_name: "asc" },
        },
        subscriptions: {
          where: { is_active: true },
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        _count: {
          select: {
            members: true,
            subscriptions: true,
            transactions: true,
          },
        },
      },
    });

    if (!association) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    return ApiResponseBuilder.success(association);
  } catch (error: any) {
    console.error("Error fetching owners association:", error);
    return ApiResponseBuilder.error("Failed to fetch owners association", "INTERNAL_ERROR", 500);
  }
}

// PATCH /api/owners-associations/[id] - Update an owners association
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for owners-associations
    const canUpdate = await checkUserPermission(decoded.id, "owners-associations", "UPDATE");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update owners associations");
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    const body = await request.json();
    const validatedData = ownersAssociationUpdateSchema.parse(body);

    // Check if association exists
    const existingAssociation = await db.ownersAssociation.findUnique({
      where: { id },
    });

    if (!existingAssociation) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    // If property_id is being updated, verify the new property exists
    if (validatedData.property_id && validatedData.property_id !== existingAssociation.property_id) {
      const property = await db.property.findUnique({
        where: { id: validatedData.property_id },
      });

      if (!property) {
        return ApiResponseBuilder.error("Property not found", "NOT_FOUND", 404);
      }

      // Check if another association exists for the new property
      const conflictingAssociation = await db.ownersAssociation.findFirst({
        where: {
          property_id: validatedData.property_id,
          id: { not: id },
        },
      });

      if (conflictingAssociation) {
        return ApiResponseBuilder.error(
          "An owners association already exists for this property",
          "ALREADY_EXISTS",
          400
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      ...validatedData,
      updated_by: decoded.id,
    };

    // Convert date strings to Date objects
    if (validatedData.establishment_date) {
      updateData.establishment_date = new Date(validatedData.establishment_date);
    }

    const updatedAssociation = await db.ownersAssociation.update({
      where: { id },
      data: updateData,
      include: {
        property: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            address_en: true,
            address_ar: true,
          },
        },
        _count: {
          select: {
            members: true,
            subscriptions: true,
            transactions: true,
          },
        },
      },
    });

    return ApiResponseBuilder.success(updatedAssociation);
  } catch (error: any) {
    console.error("Error updating owners association:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to update owners association", "INTERNAL_ERROR", 500);
  }
}

// DELETE /api/owners-associations/[id] - Delete an owners association
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for owners-associations
    const canDelete = await checkUserPermission(decoded.id, "owners-associations", "DELETE");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete owners associations");
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    // Check if association exists
    const existingAssociation = await db.ownersAssociation.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            members: true,
            subscriptions: true,
            transactions: true,
          },
        },
      },
    });

    if (!existingAssociation) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    // Check if association has any related data
    if (
      existingAssociation._count.members > 0 ||
      existingAssociation._count.subscriptions > 0 ||
      existingAssociation._count.transactions > 0
    ) {
      return ApiResponseBuilder.error(
        "Cannot delete association with existing members, subscriptions, or transactions",
        "HAS_DEPENDENCIES",
        400
      );
    }

    // Delete the association
    await db.ownersAssociation.delete({
      where: { id },
    });

    return ApiResponseBuilder.success({ message: "Owners association deleted successfully" });
  } catch (error: any) {
    console.error("Error deleting owners association:", error);
    return ApiResponseBuilder.error("Failed to delete owners association", "INTERNAL_ERROR", 500);
  }
}