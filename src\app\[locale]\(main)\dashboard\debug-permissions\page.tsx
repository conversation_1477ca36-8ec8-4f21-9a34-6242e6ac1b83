"use client";

import { usePermissions } from "@/hooks/use-permissions";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function DebugPermissionsPage() {
  const { user, permissions, isLoading, hasModuleAccess } = usePermissions();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  const allModules = [
    'dashboard', 'properties', 'property-types', 'property-owners', 'units',
    'tenants', 'contracts', 'invoices', 'payments', 'owners-associations', 'owner-payouts',
    'maintenance', 'expenses', 'expense-categories', 'users', 'roles',
    'reports', 'settings', 'amenities'
  ];

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <h1 className="text-3xl font-bold">Debug Permissions</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>User Information</CardTitle>
        </CardHeader>
        <CardContent>
          {user ? (
            <div className="space-y-2">
              <p><strong>ID:</strong> {user.id}</p>
              <p><strong>Username:</strong> {user.username}</p>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Status:</strong> {user.status}</p>
            </div>
          ) : (
            <p>No user data</p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Raw Permissions Object</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 p-4 rounded overflow-auto text-xs">
            {JSON.stringify(permissions, null, 2)}
          </pre>
          <p className="mt-2">
            <strong>Total modules with permissions:</strong> {Object.keys(permissions).length}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Module Access Check</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {allModules.map(module => {
              const hasAccess = hasModuleAccess(module);
              const perms = permissions[module];
              return (
                <div key={module} className="border p-3 rounded">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{module}</span>
                    <Badge variant={hasAccess ? "default" : "secondary"}>
                      {hasAccess ? "✓" : "✗"}
                    </Badge>
                  </div>
                  {perms && (
                    <div className="text-xs space-y-1">
                      <div>Read: {perms.read ? "✓" : "✗"}</div>
                      <div>Create: {perms.create ? "✓" : "✗"}</div>
                      <div>Update: {perms.update ? "✓" : "✗"}</div>
                      <div>Delete: {perms.delete ? "✓" : "✗"}</div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}