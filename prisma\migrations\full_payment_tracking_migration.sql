-- Full Payment Tracking Migration for Owners Association System
-- This migration adds comprehensive payment tracking with partial payment support

-- Step 1: Add new columns to SubscriptionPayment table
ALTER TABLE subscription_payments
ADD COLUMN amount_due DECIMAL(10, 3) NOT NULL DEFAULT 0 AFTER subscription_id,
ADD COLUMN amount_paid DECIMAL(10, 3) NOT NULL DEFAULT 0 AFTER amount_due,
ADD COLUMN remaining_balance DECIMAL(10, 3) AFTER amount_paid,
ADD COLUMN transaction_id INT AFTER remaining_balance,
ADD INDEX idx_subscription_payment_transaction (transaction_id),
ADD CONSTRAINT fk_subscription_payment_transaction 
    FOREIGN KEY (transaction_id) REFERENCES association_transactions(id) ON DELETE SET NULL;

-- Step 2: Create SubscriptionPaymentInstallment table for tracking partial payments
CREATE TABLE IF NOT EXISTS subscription_payment_installments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    subscription_payment_id INT NOT NULL,
    transaction_id INT NOT NULL,
    amount DECIMAL(10, 3) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method ENUM('CASH', 'BANK_TRANSFER', 'CHECK', 'CREDIT_CARD', 'OTHER') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_payment_installment_subscription (subscription_payment_id),
    INDEX idx_payment_installment_transaction (transaction_id),
    INDEX idx_payment_installment_date (payment_date),
    
    CONSTRAINT fk_payment_installment_subscription 
        FOREIGN KEY (subscription_payment_id) REFERENCES subscription_payments(id) ON DELETE CASCADE,
    CONSTRAINT fk_payment_installment_transaction 
        FOREIGN KEY (transaction_id) REFERENCES association_transactions(id) ON DELETE CASCADE
);

-- Step 3: Add member tracking to AssociationTransaction table
ALTER TABLE association_transactions
ADD COLUMN member_id INT AFTER association_id,
ADD COLUMN subscription_payment_id INT AFTER member_id,
ADD COLUMN payment_installment_id INT AFTER subscription_payment_id,
ADD INDEX idx_transaction_member (member_id),
ADD INDEX idx_transaction_subscription_payment (subscription_payment_id),
ADD INDEX idx_transaction_payment_installment (payment_installment_id),
ADD CONSTRAINT fk_transaction_member 
    FOREIGN KEY (member_id) REFERENCES association_members(id) ON DELETE SET NULL,
ADD CONSTRAINT fk_transaction_subscription_payment 
    FOREIGN KEY (subscription_payment_id) REFERENCES subscription_payments(id) ON DELETE SET NULL,
ADD CONSTRAINT fk_transaction_payment_installment 
    FOREIGN KEY (payment_installment_id) REFERENCES subscription_payment_installments(id) ON DELETE SET NULL;

-- Step 4: Update existing subscription payments with amount_due
UPDATE subscription_payments sp
JOIN association_subscriptions s ON sp.subscription_id = s.id
SET sp.amount_due = s.amount,
    sp.amount_paid = 0,
    sp.remaining_balance = s.amount
WHERE sp.payment_status = 'PENDING';

-- Step 5: For paid subscriptions, set the paid amount
UPDATE subscription_payments sp
JOIN association_subscriptions s ON sp.subscription_id = s.id
SET sp.amount_due = s.amount,
    sp.amount_paid = s.amount,
    sp.remaining_balance = 0
WHERE sp.payment_status = 'PAID';

-- Step 6: Create indexes for performance
CREATE INDEX idx_subscription_payment_status ON subscription_payments(payment_status);
CREATE INDEX idx_subscription_payment_member ON subscription_payments(member_id, subscription_id);
CREATE INDEX idx_association_transaction_type ON association_transactions(type, association_id);
CREATE INDEX idx_association_transaction_date ON association_transactions(transaction_date);

-- Step 7: Add triggers to automatically update remaining_balance
DELIMITER $$

CREATE TRIGGER update_subscription_payment_balance
BEFORE UPDATE ON subscription_payments
FOR EACH ROW
BEGIN
    SET NEW.remaining_balance = NEW.amount_due - NEW.amount_paid;
END$$

CREATE TRIGGER insert_subscription_payment_balance
BEFORE INSERT ON subscription_payments
FOR EACH ROW
BEGIN
    IF NEW.remaining_balance IS NULL THEN
        SET NEW.remaining_balance = NEW.amount_due - NEW.amount_paid;
    END IF;
END$$

DELIMITER ;

-- Verify the migration
SELECT 'Migration completed successfully!' as status;