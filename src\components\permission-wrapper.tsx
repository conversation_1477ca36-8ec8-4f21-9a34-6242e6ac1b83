"use client";

import { ReactNode } from "react";
import { usePermissions } from "@/hooks/use-permissions";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Shield, AlertCircle } from "lucide-react";

interface PermissionWrapperProps {
  module: string;
  action: "create" | "read" | "update" | "delete";
  children: ReactNode;
  fallback?: ReactNode;
  showError?: boolean;
}

interface ModuleAccessWrapperProps {
  module: string;
  children: ReactNode;
  fallback?: ReactNode;
  showError?: boolean;
}

/**
 * Wrapper component that conditionally renders children based on user permissions
 */
export function PermissionWrapper({ 
  module, 
  action, 
  children, 
  fallback = null, 
  showError = false 
}: PermissionWrapperProps) {
  const { hasPermission, isLoading, error } = usePermissions();

  if (isLoading) {
    return fallback;
  }

  if (error && showError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to check permissions: {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!hasPermission(module, action)) {
    if (showError) {
      return (
        <Alert variant="destructive">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            You don't have permission to {action} {module}.
          </AlertDescription>
        </Alert>
      );
    }
    return fallback;
  }

  return <>{children}</>;
}

/**
 * Wrapper component that conditionally renders children based on module access
 */
export function ModuleAccessWrapper({ 
  module, 
  children, 
  fallback = null, 
  showError = false 
}: ModuleAccessWrapperProps) {
  const { hasModuleAccess, isLoading, error } = usePermissions();

  if (isLoading) {
    return fallback;
  }

  if (error && showError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to check permissions: {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!hasModuleAccess(module)) {
    if (showError) {
      return (
        <Alert variant="destructive">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            You don't have access to the {module} module.
          </AlertDescription>
        </Alert>
      );
    }
    return fallback;
  }

  return <>{children}</>;
}

/**
 * Higher-order component for protecting entire pages
 */
export function withPermission<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  module: string,
  action: "create" | "read" | "update" | "delete"
) {
  return function PermissionProtectedComponent(props: P) {
    return (
      <PermissionWrapper module={module} action={action} showError>
        <WrappedComponent {...props} />
      </PermissionWrapper>
    );
  };
}

/**
 * Higher-order component for protecting pages by module access
 */
export function withModuleAccess<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  module: string
) {
  return function ModuleProtectedComponent(props: P) {
    return (
      <ModuleAccessWrapper module={module} showError>
        <WrappedComponent {...props} />
      </ModuleAccessWrapper>
    );
  };
}
