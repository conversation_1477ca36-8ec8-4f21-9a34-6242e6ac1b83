"use client";

import { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations, useLocale } from "next-intl";
import { toast } from "sonner";
import { format } from "date-fns";
import { CalendarIcon, Loader2, Users } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { apiClient } from "@/lib/api-client";
import { 
  associationTransactionSchema, 
  type AssociationTransactionInput,
  EXPENSE_CATEGORIES,
  INCOME_CATEGORIES
} from "@/types/owners-association";
import { z } from "zod";
import { useRTL } from "@/hooks/use-rtl";

interface Member {
  id: number;
  full_name: string;
  unit_number: string;
  email: string | null;
}

// Extended form schema that includes UI-specific fields
const extendedTransactionSchema = associationTransactionSchema.extend({
  member_id: z.number().optional(),
  is_member_related: z.boolean().default(false),
});

type ExtendedTransactionInput = z.infer<typeof extendedTransactionSchema>;

interface AddTransactionFormProps {
  associationId: number;
  onSuccess: () => void;
  onCancel: () => void;
}

export function AddTransactionForm({ associationId, onSuccess, onCancel }: AddTransactionFormProps) {
  const t = useTranslations("ownersAssociations.transactions");
  const locale = useLocale();
  const { isRTL } = useRTL();
  const [isLoading, setIsLoading] = useState(false);
  const [members, setMembers] = useState<Member[]>([]);
  const [loadingMembers, setLoadingMembers] = useState(false);

  const form = useForm<ExtendedTransactionInput>({
    resolver: zodResolver(extendedTransactionSchema),
    defaultValues: {
      association_id: associationId,
      transaction_date: format(new Date(), "yyyy-MM-dd"),
      type: "EXPENSE",
      category: "",
      description: "",
      amount: "",
      payment_method: "CASH",
      notes: "",
      member_id: undefined,
      is_member_related: false,
    },
  });

  const fetchMembers = useCallback(async () => {
    try {
      setLoadingMembers(true);
      const response = await apiClient.get(
        `/api/owners-associations/${associationId}/members`
      );
      
      if (response.success) {
        console.log("Members API response:", response);
        const membersList = response.data?.members || response.data || [];
        console.log("Setting members:", membersList);
        setMembers(membersList);
      }
    } catch (error) {
      console.error("Error fetching members:", error);
    } finally {
      setLoadingMembers(false);
    }
  }, [associationId]);

  useEffect(() => {
    fetchMembers();
  }, [fetchMembers]);

  const transactionType = form.watch("type");
  const isMemberRelated = form.watch("is_member_related");
  const categories = transactionType === "EXPENSE" ? EXPENSE_CATEGORIES : INCOME_CATEGORIES;

  const onSubmit = async (data: ExtendedTransactionInput) => {
    // Extract only the fields that match AssociationTransactionInput
    const { member_id, is_member_related, ...transactionData } = data;
    const apiData: AssociationTransactionInput = transactionData;
    try {
      setIsLoading(true);
      
      const response = await apiClient.post(
        `/api/owners-associations/${associationId}/transactions`,
        apiData
      );

      if (response.success) {
        toast.success(t("addSuccess"));
        onSuccess();
      }
    } catch (error: any) {
      console.error("Error adding transaction:", error);
      toast.error(t("addError"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="transaction_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>{t("date")}</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !field.value && "text-muted-foreground",
                          isRTL && "flex-row-reverse"
                        )}
                      >
                        <CalendarIcon className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                        {field.value ? (
                          format(new Date(field.value), "PPP")
                        ) : (
                          <span>{t("selectDate")}</span>
                        )}
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value ? new Date(field.value) : undefined}
                      onSelect={(date) => {
                        field.onChange(date ? format(date, "yyyy-MM-dd") : "");
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("typeLabel")}</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectType")} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="INCOME">{t("type.income")}</SelectItem>
                    <SelectItem value="EXPENSE">{t("type.expense")}</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Member Related Transaction */}
        <FormField
          control={form.control}
          name="is_member_related"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={(checked) => {
                    field.onChange(checked);
                    if (!checked) {
                      form.setValue("member_id", undefined);
                    }
                  }}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                  <Users className="h-4 w-4" />
                  {t("memberRelated")}
                </FormLabel>
                <FormDescription>
                  {t("memberRelatedDescription")}
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        {/* Member Selection */}
        {isMemberRelated && (
          <FormField
            control={form.control}
            name="member_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("member")}</FormLabel>
                <Select 
                  onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)} 
                  value={field.value?.toString()}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectMember")} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {loadingMembers ? (
                      <SelectItem value="loading" disabled>
                        <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          {t("loadingMembers")}
                        </div>
                      </SelectItem>
                    ) : members.length > 0 ? (
                      members.map((member) => (
                        <SelectItem key={member.id} value={member.id.toString()}>
                          <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                            <Badge variant="outline" className="text-xs">
                              {t("unit")} {member.unit_number}
                            </Badge>
                            <span>{member.full_name}</span>
                          </div>
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="empty" disabled>
                        {t("noMembers")}
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("category")}</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t("selectCategory")} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {locale === 'ar' ? category.label_ar : category.label_en}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("description")}</FormLabel>
              <FormControl>
                <Input {...field} placeholder={t("descriptionPlaceholder")} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("amount")}</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="0.000" />
                </FormControl>
                <FormDescription>{t("amountDescription")}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="payment_method"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("paymentMethodLabel")}</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectPaymentMethod")} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="CASH">{t("paymentMethod.cash")}</SelectItem>
                    <SelectItem value="BANK_TRANSFER">{t("paymentMethod.bank_transfer")}</SelectItem>
                    <SelectItem value="CREDIT_CARD">{t("paymentMethod.credit_card")}</SelectItem>
                    <SelectItem value="DEBIT_CARD">{t("paymentMethod_debit_card")}</SelectItem>
                    <SelectItem value="CHECK">{t("paymentMethod.check")}</SelectItem>
                    <SelectItem value="OTHER">{t("paymentMethod_other")}</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("notes")} <span className="text-muted-foreground">({t("optional")})</span></FormLabel>
              <FormControl>
                <Textarea 
                  {...field} 
                  value={field.value || ""} 
                  placeholder={t("notesPlaceholder")} 
                  rows={3}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className={cn("flex gap-3", isRTL && "flex-row-reverse")}>
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isLoading}
          >
            {t("cancel")}
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className={cn("h-4 w-4 animate-spin", isRTL ? "ml-2" : "mr-2")} />}
            {t("addTransaction")}
          </Button>
        </div>
      </form>
    </Form>
  );
}