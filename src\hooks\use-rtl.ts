'use client';

import { useLocale } from 'next-intl';
import { getLangDir } from 'rtl-detect';

/**
 * Hook to detect if the current locale uses right-to-left (RTL) text direction
 * Based on the official next-intl documentation for RTL support
 * @returns Object containing direction ('ltr' | 'rtl') and isRTL boolean
 */
export function useRTL() {
  const locale = useLocale();
  const direction = getLangDir(locale);
  const isRTL = direction === 'rtl';

  return {
    direction,
    isRTL,
    locale
  };
}
