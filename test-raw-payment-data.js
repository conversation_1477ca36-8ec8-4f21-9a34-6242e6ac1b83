const { PrismaClient } = require('./src/generated/prisma');
const prisma = new PrismaClient();

async function testRawPaymentData() {
  try {
    // Use raw query to avoid Prisma schema issues
    const payments = await prisma.$queryRaw`
      SELECT 
        sp.id,
        sp.amount,
        sp.status,
        sp.payment_date,
        sp.payment_method,
        m.full_name as member_name,
        m.unit_number,
        s.name_en as subscription_name
      FROM subscription_payments sp
      JOIN association_members m ON sp.member_id = m.id
      JOIN association_subscriptions s ON sp.subscription_id = s.id
      WHERE sp.status IN ('PARTIALLY_PAID', 'PAID')
      LIMIT 5
    `;
    
    console.log('Payment records:');
    payments.forEach(p => {
      console.log(`\nPayment #${p.id}:`);
      console.log(`- Member: ${p.member_name} (Unit ${p.unit_number})`);
      console.log(`- Subscription: ${p.subscription_name}`);
      console.log(`- Amount: ${p.amount}`);
      console.log(`- Status: ${p.status}`);
      console.log(`- Payment Method: ${p.payment_method || 'N/A'}`);
      console.log(`- Payment Date: ${p.payment_date}`);
    });
    
    // Check if we have any transactions linked
    const transactionCount = await prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM association_transactions
      WHERE type = 'INCOME' 
      AND category = 'subscription'
    `;
    
    console.log(`\nTotal subscription income transactions: ${transactionCount[0].count}`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testRawPaymentData();