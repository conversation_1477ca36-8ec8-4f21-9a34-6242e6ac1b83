const { PrismaClient } = require('../src/generated/prisma');

const prisma = new PrismaClient();

async function checkUserRoles() {
  try {
    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        user_roles: {
          include: {
            role: true
          }
        }
      }
    });

    if (!user) {
      console.log('User not found');
      return;
    }

    console.log('User:', {
      id: user.id,
      username: user.username,
      email: user.email,
      status: user.status
    });

    console.log('\nUser Roles:');
    if (user.user_roles.length === 0) {
      console.log('No roles assigned');
    } else {
      user.user_roles.forEach(ur => {
        console.log(`- ${ur.role.name} (ID: ${ur.role.id})`);
        if (ur.role.description) {
          console.log(`  Description: ${ur.role.description}`);
        }
      });
    }

    // Check all available roles
    console.log('\n\nAll Available Roles:');
    const allRoles = await prisma.role.findMany();
    allRoles.forEach(role => {
      console.log(`- ${role.name} (ID: ${role.id})`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUserRoles();