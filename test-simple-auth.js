// Simple auth test without curl dependencies
const http = require('http');

function makeRequest(options, data) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(data);
    }
    req.end();
  });
}

async function testAuth() {
  try {
    console.log("Testing login endpoint...");
    
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    };
    
    const loginData = JSON.stringify({
      username: 'ali',
      password: 'password123'
    });
    
    const loginResponse = await makeRequest(loginOptions, loginData);
    
    console.log("Login status:", loginResponse.status);
    if (loginResponse.status === 200) {
      console.log("✅ Login successful");
      
      // Extract auth token from Set-Cookie header
      const setCookie = loginResponse.headers['set-cookie'];
      if (setCookie && setCookie[0]) {
        const tokenMatch = setCookie[0].match(/auth-token=([^;]+)/);
        if (tokenMatch) {
          const token = tokenMatch[1];
          console.log("✅ Token extracted");
          
          // Test properties endpoint
          console.log("\nTesting properties endpoint...");
          const propertiesOptions = {
            hostname: 'localhost',
            port: 3000,
            path: '/api/properties',
            method: 'GET',
            headers: {
              'Cookie': `auth-token=${token}`
            }
          };
          
          const propertiesResponse = await makeRequest(propertiesOptions);
          
          console.log("Properties status:", propertiesResponse.status);
          if (propertiesResponse.status === 200) {
            console.log("✅ Properties endpoint accessible - FIXED!");
            const data = JSON.parse(propertiesResponse.body);
            console.log("Properties returned:", data.data ? data.data.length : 0, "items");
          } else {
            console.log("❌ Properties endpoint failed");
            console.log("Response:", propertiesResponse.body.substring(0, 200));
          }
        } else {
          console.log("❌ No token found in cookie");
        }
      } else {
        console.log("❌ No Set-Cookie header found");
      }
    } else {
      console.log("❌ Login failed");
      console.log("Response:", loginResponse.body.substring(0, 200));
    }
    
  } catch (error) {
    console.error("Test error:", error.message);
  }
}

testAuth();