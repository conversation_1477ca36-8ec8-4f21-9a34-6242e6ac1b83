// JWT payload interface
interface JWTPayload {
  id: number;
  username: string;
  email: string;
  iat?: number;
  exp?: number;
}
import { db } from "./db";
import { APPLICATION_MODULES, type Permission } from "@/types/user";

export type PermissionAction = "READ" | "CREATE" | "UPDATE" | "DELETE";

interface UserPermission {
  module: string;
  actions: PermissionAction[];
}

/**
 * Check if user is a superuser (has admin/superadmin role)
 */
async function isSuperuser(userId: number): Promise<boolean> {
  const userRoles = await db.userRole.findMany({
    where: {
      user_id: userId,
    },
    include: {
      role: true
    }
  });
  
  // Check if any role name contains admin or superuser (case-insensitive)
  const isAdmin = userRoles.some(ur => {
    const roleName = ur.role.name.toLowerCase();
    return roleName.includes('admin') || 
           roleName.includes('superuser') || 
           roleName === 'administrator' ||
           roleName === 'super admin';
  });
  
  return isAdmin;
}

/**
 * Get all permissions for a user based on their roles
 */
export async function getUserPermissions(userId: number): Promise<UserPermission[]> {
  try {
    // Check if user is superuser first
    const isSuperuserResult = await isSuperuser(userId);
    
    if (isSuperuserResult) {
      // Return all possible permissions for all modules
      const allModules = [
        'dashboard', 'properties', 'property-types', 'property-owners', 'units',
        'tenants', 'contracts', 'invoices', 'payments', 'owners-associations', 'owner-payouts',
        'maintenance', 'expenses', 'expense-categories', 'amenities', 'users', 'roles',
        'reports', 'settings'
      ];
      
      return allModules.map(module => ({
        module,
        actions: ['READ', 'CREATE', 'UPDATE', 'DELETE'] as PermissionAction[]
      }));
    }

    // For non-superusers, get permissions from roles
    const userRoles = await db.userRole.findMany({
      where: { user_id: userId },
      include: {
        role: {
          include: {
            role_permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    // Aggregate permissions from all roles
    const permissionMap = new Map<string, Set<PermissionAction>>();

    for (const userRole of userRoles) {
      for (const rolePermission of userRole.role.role_permissions) {
        const module = rolePermission.permission.module;
        const action = rolePermission.permission.action;
        const actions = permissionMap.get(module) || new Set<PermissionAction>();
        
        actions.add(action);
        
        permissionMap.set(module, actions);
      }
    }

    // Convert map to array
    return Array.from(permissionMap.entries()).map(([module, actions]) => ({
      module,
      actions: Array.from(actions),
    }));
  } catch (error) {
    console.error("Error fetching user permissions:", error);
    return [];
  }
}

/**
 * Check if a user has a specific permission
 */
export async function hasPermission(
  userId: number,
  module: string,
  action: PermissionAction
): Promise<boolean> {
  // Check if user is superuser first - they have all permissions
  const isSuperuserResult = await isSuperuser(userId);
  if (isSuperuserResult) {
    return true;
  }

  const permissions = await getUserPermissions(userId);
  const modulePermission = permissions.find(p => p.module === module);
  
  if (!modulePermission) return false;
  return modulePermission.actions.includes(action);
}

/**
 * Check multiple permissions at once
 */
export async function checkPermissions(
  userId: number,
  checks: Array<{ module: string; action: PermissionAction }>
): Promise<Record<string, boolean>> {
  const permissions = await getUserPermissions(userId);
  const result: Record<string, boolean> = {};

  for (const check of checks) {
    const key = `${check.module}:${check.action}`;
    const modulePermission = permissions.find(p => p.module === check.module);
    result[key] = modulePermission ? modulePermission.actions.includes(check.action) : false;
  }

  return result;
}

/**
 * Get accessible modules for a user (modules where they have at least READ permission)
 */
export async function getAccessibleModules(userId: number): Promise<string[]> {
  // Check if user is superuser first
  const isSuperuserResult = await isSuperuser(userId);
  if (isSuperuserResult) {
    // Return all modules
    return [
      'dashboard', 'properties', 'property-types', 'property-owners', 'units',
      'tenants', 'contracts', 'invoices', 'payments', 'owners-associations', 'owner-payouts',
      'maintenance', 'expenses', 'expense-categories', 'amenities', 'users', 'roles',
      'reports', 'settings'
    ];
  }

  const permissions = await getUserPermissions(userId);
  return permissions
    .filter(p => p.actions.includes("READ"))
    .map(p => p.module);
}

/**
 * Permission middleware for API routes
 */
export function requirePermission(module: string, action: PermissionAction) {
  return async (request: Request, context: any) => {
    try {
      // Extract user from JWT token in cookie or header
      const cookieHeader = request.headers.get("cookie");
      const authHeader = request.headers.get("authorization");
      
      let token: string | null = null;
      
      if (cookieHeader) {
        const cookies = Object.fromEntries(
          cookieHeader.split("; ").map(cookie => {
            const [key, value] = cookie.split("=");
            return [key, value];
          })
        );
        token = cookies["auth-token"] || null;
      }
      
      if (!token && authHeader) {
        token = authHeader.replace("Bearer ", "");
      }

      if (!token) {
        return new Response(
          JSON.stringify({ error: "Authentication required" }),
          { status: 401, headers: { "Content-Type": "application/json" } }
        );
      }

      // Verify token and get user
      const { verifyToken } = await import("./auth");
      const decoded = verifyToken(token) as JWTPayload;
      
      if (!decoded || !decoded.id) {
        return new Response(
          JSON.stringify({ error: "Invalid authentication token" }),
          { status: 401, headers: { "Content-Type": "application/json" } }
        );
      }

      // Check permission
      const hasAccess = await hasPermission(decoded.id, module, action);
      
      if (!hasAccess) {
        return new Response(
          JSON.stringify({ 
            error: "Access denied", 
            details: `You don't have ${action} permission for ${module}` 
          }),
          { status: 403, headers: { "Content-Type": "application/json" } }
        );
      }

      // Add user info to context for use in the route handler
      context.user = decoded;
      
      // Permission granted, continue to route handler
      return null;
    } catch (error) {
      console.error("Permission middleware error:", error);
      return new Response(
        JSON.stringify({ error: "Internal server error" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  };
}

/**
 * Extract user from request (for use in API routes)
 */
export async function getUserFromRequest(request: Request): Promise<JWTPayload | null> {
  try {
    const cookieHeader = request.headers.get("cookie");
    const authHeader = request.headers.get("authorization");
    
    let token: string | null = null;
    
    if (cookieHeader) {
      const cookies = Object.fromEntries(
        cookieHeader.split("; ").map(cookie => {
          const [key, value] = cookie.split("=");
          return [key, value];
        })
      );
      token = cookies["auth-token"] || null;
    }
    
    if (!token && authHeader) {
      token = authHeader.replace("Bearer ", "");
    }

    if (!token) return null;

    const { verifyToken } = await import("./auth");
    const decoded = verifyToken(token) as JWTPayload;
    
    // Return a consistent user object with userId field for backward compatibility
    return decoded ? { ...decoded, userId: decoded.id } as any : null;
  } catch (error) {
    console.error("Error extracting user from request:", error);
    return null;
  }
}