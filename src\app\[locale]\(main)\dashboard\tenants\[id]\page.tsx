import { Metadata } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { cookies, headers } from "next/headers";
import { ArrowLeft, Edit } from "lucide-react";
import { getTranslations } from "next-intl/server";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { TenantOverview } from "./_components/tenant-overview";
import { TenantContracts } from "./_components/tenant-contracts";
import { TenantInvoices } from "./_components/tenant-invoices";
import { TenantPayments } from "./_components/tenant-payments";
import { TenantDocuments } from "./_components/tenant-documents";
import { TenantEmergencyContacts } from "./_components/tenant-emergency-contacts";

interface TenantDetailPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

export async function generateMetadata({
  params,
}: TenantDetailPageProps): Promise<Metadata> {
  const { locale, id } = await params;
  const t = await getTranslations({ locale, namespace: "tenants" });
  
  try {
    // Get dynamic host for multi-port development
    const headersList = await headers();
    const host = headersList.get('host') || 'localhost:3001';
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}`;
    
    // Get cookies for server-side authentication
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');
      
    const response = await fetch(
      `${baseUrl}/api/tenants/${id}`,
      { 
        cache: 'no-store',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': authToken ? `auth-token=${authToken.value}` : '',
        }
      }
    );
    
    if (!response.ok) {
      return {
        title: t("tenantNotFound"),
      };
    }
    
    const { data: tenant } = await response.json();
    const fullName = `${tenant.first_name} ${tenant.last_name}`;
    
    return {
      title: `${fullName} - ${t("title")}`,
      description: t("viewDescription", { name: fullName }),
    };
  } catch (error) {
    return {
      title: t("tenantNotFound"),
    };
  }
}

async function getTenant(id: string) {
  try {
    // Get dynamic host for multi-port development
    const headersList = await headers();
    const host = headersList.get('host') || 'localhost:3001';
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}`;
    
    // Get cookies for server-side authentication
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');
    
    const response = await fetch(
      `${baseUrl}/api/tenants/${id}`,
      { 
        cache: 'no-store',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': authToken ? `auth-token=${authToken.value}` : '',
        }
      }
    );

    if (!response.ok) {
      console.error(`Failed to fetch tenant: ${response.status} ${response.statusText}`);
      return null;
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error("Error fetching tenant:", error);
    return null;
  }
}

export default async function TenantDetailPage({ params }: TenantDetailPageProps) {
  const { id, locale } = await params;
  const tenant = await getTenant(id);
  const t = await getTranslations({ locale, namespace: "tenants" });
  const tCommon = await getTranslations({ locale, namespace: "common" });

  if (!tenant) {
    notFound();
  }

  const fullName = `${tenant.first_name} ${tenant.last_name}`;

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href={`/${locale}/dashboard/tenants`}>
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">{tCommon("back")}</span>
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{fullName}</h1>
            <p className="text-muted-foreground">{t("tenantDetails")}</p>
          </div>
        </div>
        <Button asChild>
          <Link href={`/${locale}/dashboard/tenants/${tenant.id}/edit`}>
            <Edit className="mr-2 h-4 w-4" />
            {t("editTenant")}
          </Link>
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-4" dir={locale === 'ar' ? 'rtl' : 'ltr'}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">{t("tabs.overview")}</TabsTrigger>
          <TabsTrigger value="contracts">{t("tabs.contracts")}</TabsTrigger>
          <TabsTrigger value="invoices">{t("tabs.invoices")}</TabsTrigger>
          <TabsTrigger value="payments">{t("tabs.payments")}</TabsTrigger>
          <TabsTrigger value="documents">{t("tabs.documents")}</TabsTrigger>
          <TabsTrigger value="emergency">{t("tabs.emergency")}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <TenantOverview tenant={tenant} />
        </TabsContent>

        <TabsContent value="contracts" className="space-y-4">
          <TenantContracts tenantId={tenant.id} />
        </TabsContent>

        <TabsContent value="invoices" className="space-y-4">
          <TenantInvoices tenantId={tenant.id} />
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <TenantPayments tenantId={tenant.id} />
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <TenantDocuments tenantId={tenant.id} />
        </TabsContent>

        <TabsContent value="emergency" className="space-y-4">
          <TenantEmergencyContacts tenantId={tenant.id} />
        </TabsContent>
      </Tabs>
    </div>
  );
}