import { NextRequest, NextResponse } from "next/server";
import { verifyToken } from "@/lib/auth";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { z } from "zod";

const updateAmenitiesSchema = z.object({
  amenity_ids: z.array(z.number()).min(0),
});

// GET /api/properties/[id]/amenities - Get property amenities
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify authentication
    const token = request.cookies.get("auth-token")?.value;
    if (!token) {
      return ApiResponseBuilder.unauthorized("No authentication token provided");
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return ApiResponseBuilder.unauthorized("Invalid authentication token");
    }

    const { id } = await params;
    const propertyId = parseInt(id);
    if (isNaN(propertyId)) {
      return ApiResponseBuilder.badRequest("Invalid property ID");
    }

    // Check if property exists
    const property = await db.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      return ApiResponseBuilder.notFound("Property");
    }

    // Get property amenities
    const propertyAmenities = await db.propertyAmenity.findMany({
      where: { property_id: propertyId },
      include: {
        amenity: true,
      },
    });

    const amenities = propertyAmenities.map((pa) => pa.amenity);

    return ApiResponseBuilder.success(amenities);
  } catch (error) {
    console.error("Error fetching property amenities:", error);
    return ApiResponseBuilder.error("Failed to fetch property amenities", "INTERNAL_ERROR", 500);
  }
}

// PUT /api/properties/[id]/amenities - Update property amenities
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify authentication
    const token = request.cookies.get("auth-token")?.value;
    if (!token) {
      return ApiResponseBuilder.unauthorized("No authentication token provided");
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return ApiResponseBuilder.unauthorized("Invalid authentication token");
    }

    const { id } = await params;
    const propertyId = parseInt(id);
    if (isNaN(propertyId)) {
      return ApiResponseBuilder.badRequest("Invalid property ID");
    }

    // Check if property exists
    const property = await db.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      return ApiResponseBuilder.notFound("Property");
    }

    // Parse request body
    const body = await request.json();
    const validation = updateAmenitiesSchema.safeParse(body);

    if (!validation.success) {
      return ApiResponseBuilder.validationError(validation.error);
    }

    const { amenity_ids } = validation.data;

    // Verify all amenity IDs exist
    const amenities = await db.amenity.findMany({
      where: {
        id: { in: amenity_ids },
      },
    });

    if (amenities.length !== amenity_ids.length) {
      return ApiResponseBuilder.badRequest("One or more amenity IDs are invalid", { amenity_ids });
    }

    // Update property amenities in a transaction
    await db.$transaction(async (tx) => {
      // Delete existing amenities
      await tx.propertyAmenity.deleteMany({
        where: { property_id: propertyId },
      });

      // Add new amenities
      if (amenity_ids.length > 0) {
        await tx.propertyAmenity.createMany({
          data: amenity_ids.map((amenity_id) => ({
            property_id: propertyId,
            amenity_id,
          })),
        });
      }
    });

    // Fetch updated amenities
    const updatedAmenities = await db.propertyAmenity.findMany({
      where: { property_id: propertyId },
      include: {
        amenity: true,
      },
    });

    const amenityList = updatedAmenities.map((pa) => pa.amenity);

    return ApiResponseBuilder.success(amenityList);
  } catch (error) {
    console.error("Error updating property amenities:", error);
    return ApiResponseBuilder.error("Failed to update property amenities", "INTERNAL_ERROR", 500);
  }
}