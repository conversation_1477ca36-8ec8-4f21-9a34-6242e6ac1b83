const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Pattern to find authorization header code
const OLD_AUTH_PATTERN = `    // Check authentication
    const authHeader = request.headers.get("authorization");
    const token = authHeader?.replace("Bearer ", "");`;

const NEW_AUTH_PATTERN = `    // Check authentication
    const token = request.cookies.get("auth-token")?.value;`;

// Find all route.ts and [id]/route.ts files in the API directory
const apiFiles = glob.sync('src/app/api/**/route.ts');

console.log(`Found ${apiFiles.length} API route files to check\n`);

let updatedFiles = 0;
let skippedFiles = 0;

apiFiles.forEach(file => {
  // Skip auth routes as they have different handling
  if (file.includes('/api/auth/')) {
    console.log(`Skipping auth route: ${file}`);
    skippedFiles++;
    return;
  }

  const content = fs.readFileSync(file, 'utf8');
  
  // Check if file has the old pattern
  if (content.includes('request.headers.get("authorization")')) {
    console.log(`Updating: ${file}`);
    
    // Replace the pattern
    let updatedContent = content.replace(
      /const authHeader = request\.headers\.get\("authorization"\);\s*\n\s*const token = authHeader\?\.replace\("Bearer ", ""\);/g,
      'const token = request.cookies.get("auth-token")?.value;'
    );
    
    // Write the updated content
    fs.writeFileSync(file, updatedContent);
    updatedFiles++;
  } else if (content.includes('request.cookies.get("auth-token")')) {
    console.log(`Already updated: ${file}`);
    skippedFiles++;
  } else {
    console.log(`No auth found in: ${file}`);
    skippedFiles++;
  }
});

console.log(`\nSummary:`);
console.log(`- Updated: ${updatedFiles} files`);
console.log(`- Skipped: ${skippedFiles} files`);
console.log(`- Total: ${apiFiles.length} files`);

if (updatedFiles > 0) {
  console.log('\nIMPORTANT: All API routes have been updated to use cookie-based authentication.');
  console.log('Please restart your Next.js development server for the changes to take effect.');
}