import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { propertyUpdateSchema } from "@/types/property";

import { Decimal } from "@prisma/client/runtime/library";


interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/properties/:id - Get a single property
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for properties
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "properties", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view properties");
    }

    const propertyId = parseInt(id);
    
    if (isNaN(propertyId)) {
      return ApiResponseBuilder.badRequest("Invalid property ID");
    }

    const property = await db.property.findUnique({
      where: { id: propertyId },
      include: {
        property_type: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
          },
        },
        owner: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
          },
        },
        amenities: {
          include: {
            amenity: true,
          },
        },
        creator: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    if (!property) {
      return ApiResponseBuilder.notFound("Property");
    }

    // Transform property to ensure all Decimal values are serialized
    const transformedProperty = {
      ...property,
      base_rent: property.base_rent.toString(),
      total_area: property.total_area?.toString() || null,
      amenities: property.amenities.map(pa => pa.amenity)
    };
    
    return ApiResponseBuilder.success(transformedProperty);
  } catch (error) {
    console.error("Get Property Error:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch property",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// PUT /api/properties/:id - Update a property
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for properties
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "properties", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update properties");
    }

    // TODO: Check permissions for property update
    
    const propertyId = parseInt(id);
    
    if (isNaN(propertyId)) {
      return ApiResponseBuilder.badRequest("Invalid property ID");
    }

    const body = await request.json();
    console.log("Update Property - Raw body:", body);
    
    // Validate the request body
    const validationResult = propertyUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      console.log("Validation errors:", validationResult.error);
      return ApiResponseBuilder.validationError(validationResult.error);
    }

    const validatedData = validationResult.data;
    console.log("Update Property - Validated data:", validatedData);

    // Check if property exists
    const existing = await db.property.findUnique({
      where: { id: propertyId },
    });

    if (!existing) {
      return ApiResponseBuilder.notFound("Property");
    }

    // If property type is being updated, check if it exists
    if (validatedData.property_type_id) {
      const propertyType = await db.propertyType.findUnique({
        where: { id: validatedData.property_type_id },
      });

      if (!propertyType) {
        return ApiResponseBuilder.badRequest(
          "Invalid property type",
          { property_type_id: validatedData.property_type_id },
          request.url
        );
      }
    }

    // If owner is being updated, validate owner exists
    if (validatedData.owner_id !== undefined && validatedData.owner_id !== null && validatedData.owner_id > 0) {
      const owner = await db.propertyOwner.findUnique({
        where: { id: validatedData.owner_id },
      });

      if (!owner) {
        return ApiResponseBuilder.badRequest(
          "Invalid owner ID",
          { owner_id: validatedData.owner_id },
          request.url
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      ...validatedData,
      updated_by: decoded.id,
    };

    // Convert numeric fields to Decimal if provided
    if (validatedData.base_rent !== undefined) {
      updateData.base_rent = new Decimal(validatedData.base_rent);
    }
    if (validatedData.total_area !== undefined) {
      updateData.total_area = validatedData.total_area ? new Decimal(validatedData.total_area) : null;
    }
    
    // Handle nullable integer fields
    if (validatedData.floors_count !== undefined) {
      updateData.floors_count = validatedData.floors_count === 0 ? null : validatedData.floors_count;
    }
    if (validatedData.parking_spaces !== undefined) {
      updateData.parking_spaces = validatedData.parking_spaces === 0 ? null : validatedData.parking_spaces;
    }
    if (validatedData.owner_id !== undefined) {
      updateData.owner_id = validatedData.owner_id === 0 ? null : validatedData.owner_id;
    }

    console.log("Update Property - Final updateData:", updateData);
    console.log("Update Property - owner_id in updateData:", updateData.owner_id);

    // Update the property
    const property = await db.property.update({
      where: { id: propertyId },
      data: updateData,
      include: {
        property_type: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
          },
        },
        owner: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
          },
        },
        creator: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    // Transform property to ensure all Decimal values are serialized
    const transformedProperty = {
      ...property,
      base_rent: property.base_rent.toString(),
      total_area: property.total_area?.toString() || null,
    };
    
    return ApiResponseBuilder.success(transformedProperty);
  } catch (error) {
    console.error("Update Property Error:", error);
    return ApiResponseBuilder.error(
      "Failed to update property",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// DELETE /api/properties/:id - Delete a property
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for properties
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "properties", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete properties");
    }
    
    const propertyId = parseInt(id);
    
    if (isNaN(propertyId)) {
      return ApiResponseBuilder.badRequest("Invalid property ID");
    }

    // Check if property exists
    const existing = await db.property.findUnique({
      where: { id: propertyId },
    });

    if (!existing) {
      return ApiResponseBuilder.notFound("Property");
    }

    // TODO: Check if property has units or active contracts
    // For now, we'll allow deletion if property is not rented
    if (existing.status === "RENTED") {
      return ApiResponseBuilder.conflict(
        "Cannot delete a property that is currently rented",
        { status: existing.status },
        request.url
      );
    }

    // Delete the property
    await db.property.delete({
      where: { id: propertyId },
    });

    return ApiResponseBuilder.success({ message: "Property deleted successfully" });
  } catch (error) {
    console.error("Delete Property Error:", error);
    return ApiResponseBuilder.error(
      "Failed to delete property",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}