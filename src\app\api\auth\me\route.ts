import { NextRequest, NextResponse } from "next/server";
import { verifyToken, getAuthUser } from "@/lib/auth";

// GET /api/auth/me - Get current user information
export async function GET(request: NextRequest) {
  try {
    // Get token from cookie
    const token = request.cookies.get("auth-token")?.value;

    if (!token) {
      return NextResponse.json(
        { error: "No authentication token provided" },
        { status: 401 }
      );
    }

    // Verify token
    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      );
    }

    // Get current user with permissions
    const user = await getAuthUser(decoded.id);
    if (!user) {
      return NextResponse.json(
        { error: "User not found or inactive" },
        { status: 401 }
      );
    }
    
    return NextResponse.json({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        status: user.status,
        permissions: user.permissions,
      },
    });
  } catch (error) {
    console.error("Get current user error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
