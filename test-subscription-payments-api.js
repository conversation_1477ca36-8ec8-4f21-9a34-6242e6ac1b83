const fetch = require('node-fetch');
const fs = require('fs');

async function testAPI() {
  try {
    // Read cookies from file and extract just the cookie line
    const cookieFile = fs.readFileSync('cookies.txt', 'utf8');
    const cookieLine = cookieFile.split('\n').find(line => line.includes('auth-token'));
    const cookies = cookieLine ? cookieLine.split('\t').slice(-1)[0] : '';
    
    // Try to fetch subscription payments
    const response = await fetch('http://localhost:3000/api/owners-associations/1/subscription-payments', {
      headers: {
        'Cookie': cookies,
        'Accept': 'application/json',
      }
    });
    
    console.log('Status:', response.status);
    console.log('Status Text:', response.statusText);
    
    const data = await response.json();
    console.log('Response:', JSON.stringify(data, null, 2));
    
  } catch (error) {
    console.error('Error:', error);
  }
}

testAPI();