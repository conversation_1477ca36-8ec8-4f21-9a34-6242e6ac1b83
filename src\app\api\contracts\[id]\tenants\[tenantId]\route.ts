import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ApiResponseBuilder } from "@/lib/api-response";
import { z } from "zod";


const updateSchema = z.object({
  is_primary: z.boolean(),
});

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; tenantId: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for contracts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "contracts", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update contracts");
    }

    // User is already authenticated via token above

    const { id, tenantId: tenantIdParam } = await params;

    const contractId = parseInt(id);
    const tenantId = parseInt(tenantIdParam);

    if (isNaN(contractId) || isNaN(tenantId)) {
      return ApiResponseBuilder.error("Invalid contract or tenant ID", "BAD_REQUEST", 400);
    }

    const body = await request.json();
    const validatedData = updateSchema.parse(body);

    // Check if contract tenant exists
    const contractTenant = await prisma.contractTenant.findFirst({
      where: {
        contract_id: contractId,
        tenant_id: tenantId,
      },
    });

    if (!contractTenant) {
      return ApiResponseBuilder.error("Contract tenant not found", "NOT_FOUND", 404);
    }

    // If setting as primary, unset other primary tenants
    if (validatedData.is_primary) {
      await prisma.contractTenant.updateMany({
        where: {
          contract_id: contractId,
          tenant_id: { not: tenantId },
          is_primary: true,
        },
        data: {
          is_primary: false,
        },
      });
    }

    // Update contract tenant
    const updatedContractTenant = await prisma.contractTenant.update({
      where: {
        id: contractTenant.id,
      },
      data: {
        is_primary: validatedData.is_primary,
      },
      include: {
        tenant: true,
      },
    });

    // Update contract to reflect changes
    await prisma.contract.update({
      where: { id: contractId },
      data: {
        updated_by: decoded.id,
      },
    });

    return ApiResponseBuilder.success(updatedContractTenant);
  } catch (error) {
    console.error("Error updating contract tenant:", error);
    return ApiResponseBuilder.error("Failed to update contract tenant", "INTERNAL_ERROR", 500);
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; tenantId: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for contracts
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "contracts", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete contracts");
    }

    // User is already authenticated via token above

    const { id, tenantId: tenantIdParam } = await params;

    const contractId = parseInt(id);
    const tenantId = parseInt(tenantIdParam);

    if (isNaN(contractId) || isNaN(tenantId)) {
      return ApiResponseBuilder.error("Invalid contract or tenant ID", "BAD_REQUEST", 400);
    }

    // Check if contract tenant exists
    const contractTenant = await prisma.contractTenant.findFirst({
      where: {
        contract_id: contractId,
        tenant_id: tenantId,
      },
    });

    if (!contractTenant) {
      return ApiResponseBuilder.error("Contract tenant not found", "NOT_FOUND", 404);
    }

    // Check if contract exists
    const contract = await prisma.contract.findUnique({
      where: { id: contractId },
    });

    if (!contract) {
      return ApiResponseBuilder.error("Contract not found", "NOT_FOUND", 404);
    }

    // Check if contract is in a state that allows removing tenants
    if (["TERMINATED", "EXPIRED"].includes(contract.status)) {
      return ApiResponseBuilder.error("Cannot remove tenants from terminated or expired contracts", "BAD_REQUEST", 400);
    }

    // Don't allow removing the last tenant from an active contract
    const tenantCount = await prisma.contractTenant.count({
      where: { contract_id: contractId },
    });

    if (tenantCount === 1 && contract.status === "ACTIVE") {
      return ApiResponseBuilder.error("Cannot remove the last tenant from an active contract", "BAD_REQUEST", 400);
    }

    // If removing primary tenant, set another tenant as primary
    if (contractTenant.is_primary && tenantCount > 1) {
      const nextTenant = await prisma.contractTenant.findFirst({
        where: {
          contract_id: contractId,
          tenant_id: { not: tenantId },
        },
        orderBy: { created_at: "asc" },
      });

      if (nextTenant) {
        await prisma.contractTenant.update({
          where: { id: nextTenant.id },
          data: { is_primary: true },
        });
      }
    }

    // Delete contract tenant
    await prisma.contractTenant.delete({
      where: { id: contractTenant.id },
    });

    // Update contract to reflect changes
    await prisma.contract.update({
      where: { id: contractId },
      data: {
        updated_by: decoded.id,
      },
    });

    return ApiResponseBuilder.success({ message: "Tenant removed from contract successfully" });
  } catch (error) {
    console.error("Error removing tenant from contract:", error);
    return ApiResponseBuilder.error("Failed to remove tenant from contract", "INTERNAL_ERROR", 500);
  }
}