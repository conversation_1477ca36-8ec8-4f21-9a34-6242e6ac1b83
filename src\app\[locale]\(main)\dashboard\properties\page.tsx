import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import { PropertiesPageWrapper } from "./_components/properties-page-wrapper";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "properties" });

  return {
    title: t("title"),
    description: t("description"),
  };
}

export default async function PropertiesPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  return <PropertiesPageWrapper />;
}