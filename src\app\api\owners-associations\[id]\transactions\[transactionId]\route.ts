import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { associationTransactionUpdateSchema } from "@/types/owners-association";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, checkUserPermission } from "@/lib/auth";
import { Decimal } from "@prisma/client/runtime/library";

// GET /api/owners-associations/[id]/transactions/[transactionId] - Get a single transaction
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; transactionId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await checkUserPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view association transactions");
    }

    const { id: idParam, transactionId: transactionIdParam } = await params;
    const associationId = parseInt(idParam);
    const transactionId = parseInt(transactionIdParam);

    if (isNaN(associationId) || isNaN(transactionId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    const transaction = await db.associationTransaction.findFirst({
      where: {
        id: transactionId,
        association_id: associationId,
      },
      include: {
        association: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
          },
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    if (!transaction) {
      return ApiResponseBuilder.notFound("Transaction not found");
    }

    // Transform transaction to handle Decimal serialization
    const transformedTransaction = {
      ...transaction,
      amount: transaction.amount.toString(),
    };

    return ApiResponseBuilder.success(transformedTransaction);
  } catch (error: any) {
    console.error("Error fetching transaction:", error);
    return ApiResponseBuilder.error("Failed to fetch transaction", "INTERNAL_ERROR", 500);
  }
}

// PATCH /api/owners-associations/[id]/transactions/[transactionId] - Update a transaction
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; transactionId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for owners-associations
    const canUpdate = await checkUserPermission(decoded.id, "owners-associations", "UPDATE");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update association transactions");
    }

    const { id: idParam, transactionId: transactionIdParam } = await params;
    const associationId = parseInt(idParam);
    const transactionId = parseInt(transactionIdParam);

    if (isNaN(associationId) || isNaN(transactionId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    const body = await request.json();
    const validatedData = associationTransactionUpdateSchema.parse(body);

    // Check if transaction exists
    const existingTransaction = await db.associationTransaction.findFirst({
      where: {
        id: transactionId,
        association_id: associationId,
      },
    });

    if (!existingTransaction) {
      return ApiResponseBuilder.notFound("Transaction not found");
    }

    // Prepare update data
    const updateData: any = {};
    
    if (validatedData.transaction_date !== undefined) {
      updateData.transaction_date = new Date(validatedData.transaction_date);
    }
    if (validatedData.type !== undefined) updateData.type = validatedData.type;
    if (validatedData.category !== undefined) updateData.category = validatedData.category;
    if (validatedData.description !== undefined) updateData.description = validatedData.description;
    if (validatedData.amount !== undefined) updateData.amount = new Decimal(validatedData.amount);
    if (validatedData.payment_method !== undefined) updateData.payment_method = validatedData.payment_method;
    if (validatedData.notes !== undefined) updateData.notes = validatedData.notes;
    if (validatedData.attachment_url !== undefined) updateData.attachment_url = validatedData.attachment_url;
    if (validatedData.attachment_name !== undefined) updateData.attachment_name = validatedData.attachment_name;

    const updatedTransaction = await db.associationTransaction.update({
      where: { id: transactionId },
      data: updateData,
      include: {
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    // Transform transaction to handle Decimal serialization
    const transformedTransaction = {
      ...updatedTransaction,
      amount: updatedTransaction.amount.toString(),
    };

    return ApiResponseBuilder.success(transformedTransaction);
  } catch (error: any) {
    console.error("Error updating transaction:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to update transaction", "INTERNAL_ERROR", 500);
  }
}

// DELETE /api/owners-associations/[id]/transactions/[transactionId] - Delete a transaction
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; transactionId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for owners-associations
    const canDelete = await checkUserPermission(decoded.id, "owners-associations", "DELETE");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete association transactions");
    }

    const { id: idParam, transactionId: transactionIdParam } = await params;
    const associationId = parseInt(idParam);
    const transactionId = parseInt(transactionIdParam);

    if (isNaN(associationId) || isNaN(transactionId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    // Check if transaction exists
    const existingTransaction = await db.associationTransaction.findFirst({
      where: {
        id: transactionId,
        association_id: associationId,
      },
    });

    if (!existingTransaction) {
      return ApiResponseBuilder.notFound("Transaction not found");
    }

    // Delete the transaction
    await db.associationTransaction.delete({
      where: { id: transactionId },
    });

    return ApiResponseBuilder.success({ message: "Transaction deleted successfully" });
  } catch (error: any) {
    console.error("Error deleting transaction:", error);
    return ApiResponseBuilder.error("Failed to delete transaction", "INTERNAL_ERROR", 500);
  }
}