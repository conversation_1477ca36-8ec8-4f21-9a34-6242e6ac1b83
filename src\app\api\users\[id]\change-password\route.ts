import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { hashPassword, verifyPassword, validatePasswordStrength } from "@/lib/auth";
import { changePasswordSchema } from "@/types/user";

// PUT /api/users/[id]/change-password - Change user password
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid user ID" },
        { status: 400 }
      );
    }

    const body = await request.json();
    
    // Validate the request body
    const validatedData = changePasswordSchema.parse(body);

    // Check if user exists
    const existingUser = await db.user.findUnique({
      where: { id },
      select: {
        id: true,
        password_hash: true,
        status: true,
      },
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    if (existingUser.status !== "ACTIVE") {
      return NextResponse.json(
        { error: "Cannot change password for inactive user" },
        { status: 400 }
      );
    }

    // Verify current password
    const isCurrentPasswordValid = await verifyPassword(
      validatedData.current_password,
      existingUser.password_hash
    );

    if (!isCurrentPasswordValid) {
      return NextResponse.json(
        { error: "Current password is incorrect" },
        { status: 400 }
      );
    }

    // Validate new password strength
    const passwordValidation = validatePasswordStrength(validatedData.new_password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { error: passwordValidation.message },
        { status: 400 }
      );
    }

    // Hash new password
    const newPasswordHash = await hashPassword(validatedData.new_password);

    // Update password
    await db.user.update({
      where: { id },
      data: {
        password_hash: newPasswordHash,
        updated_at: new Date(),
      },
    });

    return NextResponse.json({
      message: "Password changed successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error changing password:", error);
    return NextResponse.json(
      { error: "Failed to change password" },
      { status: 500 }
    );
  }
}
