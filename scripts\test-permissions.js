const { PrismaClient } = require('../src/generated/prisma');

const prisma = new PrismaClient();

async function testPermissions() {
  try {
    // Test isSuperuser function
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!user) {
      console.log('User not found');
      return;
    }

    console.log('Testing permissions for user:', user.email);
    console.log('User ID:', user.id);

    // Check if user has admin role
    const adminRole = await prisma.userRole.findFirst({
      where: {
        user_id: user.id,
        role: {
          OR: [
            { name: { contains: 'admin', mode: 'insensitive' } },
            { name: { equals: 'Admin' } },
          ]
        }
      },
      include: {
        role: true
      }
    });

    console.log('\nAdmin role check:');
    if (adminRole) {
      console.log('✓ User has admin role:', adminRole.role.name);
    } else {
      console.log('✗ User does not have admin role');
    }

    // Test the permissions for specific modules
    console.log('\n\nChecking module permissions:');
    const modules = ['properties', 'users', 'roles', 'tenants', 'contracts'];
    
    // Get user's actual permissions
    const userRoles = await prisma.userRole.findMany({
      where: { user_id: user.id },
      include: {
        role: {
          include: {
            role_permissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    });

    console.log('\nRole permissions breakdown:');
    userRoles.forEach(ur => {
      console.log(`\nRole: ${ur.role.name}`);
      console.log('Permissions:');
      if (ur.role.role_permissions.length === 0) {
        console.log('  - No permissions assigned to this role');
      } else {
        ur.role.role_permissions.forEach(rp => {
          const perms = [];
          if (rp.can_read) perms.push('READ');
          if (rp.can_create) perms.push('CREATE');
          if (rp.can_update) perms.push('UPDATE');
          if (rp.can_delete) perms.push('DELETE');
          console.log(`  - ${rp.permission.module}: ${perms.join(', ')}`);
        });
      }
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPermissions();