import { PrismaClient } from "../generated/prisma";

const prisma = new PrismaClient();

const propertyTypes = [
  {
    name_en: "Land",
    name_ar: "أرض",
    description_en: "Vacant land for development or investment",
    description_ar: "أرض فضاء للتطوير أو الاستثمار",
  },
  {
    name_en: "Building",
    name_ar: "مبنى",
    description_en: "Complete building structure with multiple units",
    description_ar: "مبنى كامل متعدد الوحدات",
  },
  {
    name_en: "Villa",
    name_ar: "فيلا",
    description_en: "Standalone residential villa",
    description_ar: "فيلا سكنية مستقلة",
  },
  {
    name_en: "Compound",
    name_ar: "مجمع",
    description_en: "Residential compound with multiple villas or units",
    description_ar: "مجمع سكني متعدد الفلل أو الوحدات",
  },
  {
    name_en: "Apartment",
    name_ar: "شقة",
    description_en: "Individual apartment unit in a building",
    description_ar: "وحدة سكنية في مبنى",
  },
  {
    name_en: "Shop",
    name_ar: "محل",
    description_en: "Commercial shop or retail space",
    description_ar: "محل تجاري أو مساحة للبيع بالتجزئة",
  },
  {
    name_en: "Office",
    name_ar: "مكتب",
    description_en: "Office space for business operations",
    description_ar: "مساحة مكتبية للأعمال التجارية",
  },
  {
    name_en: "Warehouse",
    name_ar: "مستودع",
    description_en: "Storage and warehouse facility",
    description_ar: "مرفق للتخزين والمستودعات",
  },
  {
    name_en: "Farm",
    name_ar: "مزرعة",
    description_en: "Agricultural land or farm property",
    description_ar: "أرض زراعية أو مزرعة",
  },
  {
    name_en: "Hotel",
    name_ar: "فندق",
    description_en: "Hotel or hospitality property",
    description_ar: "فندق أو عقار للضيافة",
  },
];

async function seedPropertyTypes() {
  console.log("🌱 Seeding property types...");

  try {
    // Check if property types already exist
    const existingCount = await prisma.propertyType.count();
    
    if (existingCount > 0) {
      console.log(`✅ Property types already exist (${existingCount} types found). Skipping seed.`);
      return;
    }

    // Create property types
    for (const propertyType of propertyTypes) {
      await prisma.propertyType.create({
        data: propertyType,
      });
      console.log(`✅ Created property type: ${propertyType.name_en} / ${propertyType.name_ar}`);
    }

    console.log(`🎉 Successfully seeded ${propertyTypes.length} property types!`);
  } catch (error) {
    console.error("❌ Error seeding property types:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeder
seedPropertyTypes()
  .catch((error) => {
    console.error("Failed to seed property types:", error);
    process.exit(1);
  });