import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { getTranslations } from 'next-intl/server';

import { Button } from "@/components/ui/button";
import { ErrorBoundary } from "@/components/error-boundary";
import { TenantForm } from "../_components/tenant-form";

export default async function CreateTenantPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'tenants' });
  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild>
          <Link href={`/${locale}/dashboard/tenants`}>
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">{t('backToTenants')}</span>
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('createTenant')}</h1>
          <p className="text-muted-foreground">
            {t('createDescription')}
          </p>
        </div>
      </div>

      <ErrorBoundary>
        <TenantForm />
      </ErrorBoundary>
    </div>
  );
}
