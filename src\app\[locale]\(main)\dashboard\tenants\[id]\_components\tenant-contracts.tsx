"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import { format } from "date-fns";
import { Plus, Eye, FileText } from "lucide-react";
import { toast } from "sonner";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/localization";

interface TenantContractsProps {
  tenantId: string;
}

export function TenantContracts({ tenantId }: TenantContractsProps) {
  const locale = useLocale();
  const t = useTranslations("tenants");
  const tContracts = useTranslations("contracts");
  const tCommon = useTranslations("common");
  
  const [contracts, setContracts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchContracts();
  }, [tenantId]);

  const fetchContracts = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/contracts?tenantId=${tenantId}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch contracts");
      }

      const result = await response.json();
      if (result.success) {
        setContracts(result.data);
      }
    } catch (error) {
      console.error("Error fetching contracts:", error);
      toast.error(t("contracts.fetchError"));
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variant = 
      status === "active" ? "default" :
      status === "expired" ? "secondary" :
      status === "terminated" ? "destructive" :
      "outline";
    
    return (
      <Badge variant={variant}>
        {tContracts(`status.${status}`)}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("contracts.title")}</CardTitle>
          <CardDescription>{t("contracts.description")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t("contracts.title")}</CardTitle>
            <CardDescription>{t("contracts.description")}</CardDescription>
          </div>
          <Button asChild>
            <Link href={`/${locale}/dashboard/contracts/new?tenantId=${tenantId}`}>
              <Plus className="mr-2 h-4 w-4" />
              {tContracts("addContract")}
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {contracts.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">{t("contracts.noContracts")}</p>
            <Button asChild className="mt-4" variant="outline">
              <Link href={`/${locale}/dashboard/contracts/new?tenantId=${tenantId}`}>
                <Plus className="mr-2 h-4 w-4" />
                {t("contracts.createFirst")}
              </Link>
            </Button>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{tContracts("table.contractNumber")}</TableHead>
                  <TableHead>{tContracts("table.property")}</TableHead>
                  <TableHead>{tContracts("table.startDate")}</TableHead>
                  <TableHead>{tContracts("table.endDate")}</TableHead>
                  <TableHead>{tContracts("table.monthlyRent")}</TableHead>
                  <TableHead>{tContracts("table.status")}</TableHead>
                  <TableHead>{tCommon("actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {contracts.map((contract) => (
                  <TableRow key={contract.id}>
                    <TableCell className="font-medium">
                      {contract.contract_number}
                    </TableCell>
                    <TableCell>
                      {contract.property?.name || contract.unit?.name}
                    </TableCell>
                    <TableCell>
                      {format(new Date(contract.start_date), "dd/MM/yyyy")}
                    </TableCell>
                    <TableCell>
                      {format(new Date(contract.end_date), "dd/MM/yyyy")}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(contract.monthly_rent)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(contract.status)}
                    </TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/${locale}/dashboard/contracts/${contract.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          {tCommon("view")}
                        </Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}