"use client";

import { useState, useEffect, useCallback } from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Settings2, Search, Filter } from "lucide-react";
import { MaintenanceRequestWithRelations } from "@/types/maintenance";
import { useTranslations } from "next-intl";
import { DataTablePagination } from "@/components/data-table/data-table-pagination";
import { DataTable } from "@/components/data-table/data-table";

interface MaintenanceDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
}

export function MaintenanceDataTable<TData, TValue>({
  columns,
}: MaintenanceDataTableProps<TData, TValue>) {
  const [data, setData] = useState<TData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [priorityFilter, setPriorityFilter] = useState<string>("all");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [totalPages, setTotalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);

  const t = useTranslations();

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching maintenance requests...');

      const params = new URLSearchParams({
        page: (pagination.pageIndex + 1).toString(),
        pageSize: pagination.pageSize.toString(),
      });

      if (globalFilter) {
        params.append("search", globalFilter);
      }

      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }

      if (priorityFilter !== "all") {
        params.append("priority", priorityFilter);
      }

      if (categoryFilter !== "all") {
        params.append("category", categoryFilter);
      }

      if (sorting.length > 0) {
        params.append("sortBy", sorting[0].id);
        params.append("sortOrder", sorting[0].desc ? "desc" : "asc");
      }

      console.log('Fetching from:', `/api/maintenance?${params}`);
      
      const response = await fetch(`/api/maintenance?${params}`, {
        credentials: 'include'
      });
      
      console.log('Response status:', response.status);
      
      if (!response.ok) {
        let errorData = null;
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        
        try {
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            errorData = await response.json();
            console.error('Error response data:', errorData);
            errorMessage = errorData?.error?.message || errorData?.error || errorData?.message || errorMessage;
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError);
        }
        
        // Handle authentication errors specifically
        if (response.status === 401) {
          throw new Error("Authentication required. Please log in again.");
        }
        
        throw new Error(errorMessage);
      }

      const result = await response.json();
      console.log('API response:', result);

      if (result.success) {
        setData(result.data || []);
        setTotalPages(result.meta?.totalPages || 0);
        setTotalCount(result.meta?.total || 0);
      } else {
        throw new Error("Failed to fetch maintenance requests");
      }
    } catch (error) {
      console.error("Error in fetchData:", error);
      console.error("Error stack:", error instanceof Error ? error.stack : "No stack trace");
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  }, [pagination, globalFilter, statusFilter, priorityFilter, categoryFilter, sorting]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    manualPagination: true,
    pageCount: totalPages,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      pagination,
    },
  });

  if (error) {
    return (
      <div className="rounded-md border p-8 text-center">
        <p className="text-destructive">Error: {error}</p>
        <Button
          variant="outline"
          onClick={fetchData}
          className="mt-4"
        >
          {t("common.retry")}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t("maintenance.searchPlaceholder")}
              value={globalFilter}
              onChange={(event) => setGlobalFilter(event.target.value)}
              className="pl-8 max-w-sm"
            />
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t("maintenance.allStatuses")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("maintenance.allStatuses")}</SelectItem>
              <SelectItem value="REPORTED">{t("maintenance.statuses.reported")}</SelectItem>
              <SelectItem value="ACKNOWLEDGED">{t("maintenance.statuses.acknowledged")}</SelectItem>
              <SelectItem value="IN_PROGRESS">{t("maintenance.statuses.in_progress")}</SelectItem>
              <SelectItem value="ON_HOLD">{t("maintenance.statuses.on_hold")}</SelectItem>
              <SelectItem value="COMPLETED">{t("maintenance.statuses.completed")}</SelectItem>
              <SelectItem value="CANCELLED">{t("maintenance.statuses.cancelled")}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t("maintenance.allPriorities")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("maintenance.allPriorities")}</SelectItem>
              <SelectItem value="LOW">{t("maintenance.priorities.low")}</SelectItem>
              <SelectItem value="MEDIUM">{t("maintenance.priorities.medium")}</SelectItem>
              <SelectItem value="HIGH">{t("maintenance.priorities.high")}</SelectItem>
              <SelectItem value="EMERGENCY">{t("maintenance.priorities.emergency")}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t("maintenance.allCategories")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("maintenance.allCategories")}</SelectItem>
              <SelectItem value="ELECTRICAL">{t("maintenance.categories.electrical")}</SelectItem>
              <SelectItem value="PLUMBING">{t("maintenance.categories.plumbing")}</SelectItem>
              <SelectItem value="HVAC">{t("maintenance.categories.hvac")}</SelectItem>
              <SelectItem value="STRUCTURAL">{t("maintenance.categories.structural")}</SelectItem>
              <SelectItem value="APPLIANCES">{t("maintenance.categories.appliances")}</SelectItem>
              <SelectItem value="PAINTING">{t("maintenance.categories.painting")}</SelectItem>
              <SelectItem value="CLEANING">{t("maintenance.categories.cleaning")}</SelectItem>
              <SelectItem value="LANDSCAPING">{t("maintenance.categories.landscaping")}</SelectItem>
              <SelectItem value="SECURITY">{t("maintenance.categories.security")}</SelectItem>
              <SelectItem value="OTHER">{t("maintenance.categories.other")}</SelectItem>
            </SelectContent>
          </Select>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Settings2 className="mr-2 h-4 w-4" />
                {t("common.columns")}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Results count */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {loading ? (
            t("common.loading")
          ) : (
            t("common.showing", {
              from: pagination.pageIndex * pagination.pageSize + 1,
              to: Math.min(
                (pagination.pageIndex + 1) * pagination.pageSize,
                totalCount
              ),
              total: totalCount,
            })
          )}
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border overflow-hidden">
        <div className="w-full overflow-x-auto [&_[data-slot=table-container]]:overflow-x-visible">
          <DataTable table={table} columns={columns} loading={loading} />
        </div>
      </div>

      {/* Pagination */}
      <DataTablePagination table={table} />
    </div>
  );
}