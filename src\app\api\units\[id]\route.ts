import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { unitUpdateSchema } from "@/types/unit";

import { Decimal } from "@prisma/client/runtime/library";


interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/units/:id - Get a single unit
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for units
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "units", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view units");
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.badRequest("Invalid unit ID");
    }

    const unit = await db.unit.findUnique({
      where: { id },
      include: {
        property: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            property_type: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
              },
            },
          },
        },
        amenities: {
          include: {
            amenity: true,
          },
        },
        creator: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
        _count: {
          select: {
            contracts: true,
            invoices: true,
            payments: true,
            maintenance_requests: true,
          },
        },
      },
    });

    if (!unit) {
      return ApiResponseBuilder.notFound("Unit");
    }

    // Transform the response
    const transformedUnit = {
      ...unit,
      rent_amount: unit.rent_amount.toString(),
      area: unit.area?.toString() || null,
      amenities: unit.amenities.map(ua => ua.amenity),
      contracts_count: unit._count.contracts,
      invoices_count: unit._count.invoices,
      payments_count: unit._count.payments,
      maintenance_requests_count: unit._count.maintenance_requests,
    };

    return ApiResponseBuilder.success(transformedUnit);
  } catch (error) {
    console.error("Get Unit Error:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch unit",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// PUT /api/units/:id - Update a unit
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
        // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for units
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "units", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update units");
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.badRequest("Invalid unit ID");
    }

    const body = await request.json();
    
    // Validate the request body
    const validationResult = unitUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return ApiResponseBuilder.validationError(validationResult.error);
    }

    const validatedData = validationResult.data;

    // Check if unit exists
    const existing = await db.unit.findUnique({
      where: { id },
    });

    if (!existing) {
      return ApiResponseBuilder.notFound("Unit");
    }

    // If unit number is being changed, check if it's unique for this property
    if (validatedData.unit_number && validatedData.unit_number !== existing.unit_number) {
      const duplicateUnit = await db.unit.findUnique({
        where: {
          property_id_unit_number: {
            property_id: existing.property_id,
            unit_number: validatedData.unit_number,
          },
        },
      });

      if (duplicateUnit) {
        return ApiResponseBuilder.conflict(
          "Unit number already exists for this property",
          { unit_number: validatedData.unit_number },
          request.url
        );
      }
    }

    // Prepare update data
    const { amenity_ids, ...unitData } = validatedData;
    const updateData: any = {
      ...unitData,
      updated_by: decoded.id,
    };

    // Convert numeric fields to Decimal if provided
    if (validatedData.rent_amount !== undefined) {
      updateData.rent_amount = new Decimal(validatedData.rent_amount);
    }
    if (validatedData.area !== undefined) {
      updateData.area = validatedData.area ? new Decimal(validatedData.area) : null;
    }
    
    // Handle nullable integer fields - convert 0 to null
    if (validatedData.floor_number !== undefined) {
      updateData.floor_number = validatedData.floor_number === 0 ? null : validatedData.floor_number;
    }
    if (validatedData.rooms_count !== undefined) {
      updateData.rooms_count = validatedData.rooms_count === 0 ? null : validatedData.rooms_count;
    }
    if (validatedData.majalis_count !== undefined) {
      updateData.majalis_count = validatedData.majalis_count === 0 ? null : validatedData.majalis_count;
    }
    if (validatedData.bathrooms_count !== undefined) {
      updateData.bathrooms_count = validatedData.bathrooms_count === 0 ? null : validatedData.bathrooms_count;
    }

    // Update unit and amenities in a transaction
    const unit = await db.$transaction(async (tx) => {
      // Update unit
      const updatedUnit = await tx.unit.update({
        where: { id },
        data: updateData,
      });

      // Update amenities if provided
      if (amenity_ids !== undefined) {
        // Delete existing amenities
        await tx.unitAmenity.deleteMany({
          where: { unit_id: id },
        });

        // Create new amenities
        if (amenity_ids.length > 0) {
          await tx.unitAmenity.createMany({
            data: amenity_ids.map((amenity_id: number) => ({
              unit_id: id,
              amenity_id,
            })),
          });
        }
      }

      return updatedUnit;
    });

    // Fetch the updated unit with relations
    const unitWithRelations = await db.unit.findUnique({
      where: { id },
      include: {
        property: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            property_type: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
              },
            },
          },
        },
        amenities: {
          include: {
            amenity: true,
          },
        },
        creator: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    // Transform the response
    const transformedUnit = {
      ...unitWithRelations!,
      rent_amount: unitWithRelations!.rent_amount.toString(),
      area: unitWithRelations!.area?.toString() || null,
      amenities: unitWithRelations!.amenities.map(ua => ua.amenity),
    };

    return ApiResponseBuilder.success(transformedUnit);
  } catch (error) {
    console.error("Update Unit Error:", error);
    return ApiResponseBuilder.error(
      "Failed to update unit",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// DELETE /api/units/:id - Delete a unit
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
        // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for units
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "units", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete units");
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.badRequest("Invalid unit ID");
    }

    // Check if unit exists
    const existing = await db.unit.findUnique({
      where: { id },
    });

    if (!existing) {
      return ApiResponseBuilder.notFound("Unit");
    }

    // TODO: Check if unit has active contracts
    // For now, we'll allow deletion if unit is not rented
    if (existing.status === "RENTED") {
      return ApiResponseBuilder.conflict(
        "Cannot delete a unit that is currently rented",
        { status: existing.status },
        request.url
      );
    }

    // Delete the unit
    await db.unit.delete({
      where: { id },
    });

    return ApiResponseBuilder.success({ message: "Unit deleted successfully" });
  } catch (error) {
    console.error("Delete Unit Error:", error);
    return ApiResponseBuilder.error(
      "Failed to delete unit",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}