const { PrismaClient } = require('./src/generated/prisma');
const prisma = new PrismaClient();

async function checkStructure() {
  try {
    // Check the subscription_payments table structure
    const result = await prisma.$queryRaw`DESCRIBE subscription_payments`;
    
    console.log('Current subscription_payments table structure:');
    console.log('================================================');
    result.forEach(col => {
      console.log(`${col.Field.padEnd(25)} ${col.Type.padEnd(30)} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
    });
    
    // Check if enhanced columns exist
    const enhancedColumns = ['amount_due', 'amount_paid', 'remaining_balance', 'transaction_id'];
    const existingColumns = result.map(col => col.<PERSON>);
    
    console.log('\n\nEnhanced columns check:');
    console.log('========================');
    enhancedColumns.forEach(col => {
      const exists = existingColumns.includes(col);
      console.log(`${col.padEnd(20)} ${exists ? '✓ EXISTS' : '✗ MISSING'}`);
    });
    
    // Check if subscription_payment_installments table exists
    try {
      await prisma.$queryRaw`SELECT 1 FROM subscription_payment_installments LIMIT 1`;
      console.log('\n✓ subscription_payment_installments table EXISTS');
    } catch (error) {
      console.log('\n✗ subscription_payment_installments table MISSING');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkStructure();