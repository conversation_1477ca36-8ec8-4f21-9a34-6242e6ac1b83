// Test script to check API error
const jwt = require('jsonwebtoken');

// Create a test token
const payload = {
  id: 1,
  username: 'admin',
  email: '<EMAIL>'
};

const token = jwt.sign(payload, process.env.JWT_SECRET || 'your-secret-key-change-in-production', { expiresIn: '7d' });

console.log('Test token:', token);
console.log('\nUse this curl command to test:');
console.log(`curl -H "Cookie: auth-token=${token}" http://localhost:3000/api/owners-associations/1/subscription-payments`);