import { db } from "@/lib/db";

export default async function DebugPage() {
  // Check if we have property owners
  const owners = await db.propertyOwner.findMany({
    take: 5,
    select: {
      id: true,
      name_en: true,
      name_ar: true,
      status: true,
    },
  });

  // Check if we have property types
  const propertyTypes = await db.propertyType.findMany({
    take: 5,
    select: {
      id: true,
      name_en: true,
      name_ar: true,
    },
  });

  // Check recent properties
  const properties = await db.property.findMany({
    take: 5,
    include: {
      property_type: true,
      owner: true,
    },
    orderBy: {
      created_at: 'desc',
    },
  });

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Debug Information</h1>
      
      <div className="space-y-4">
        <div>
          <h2 className="text-xl font-semibold">Property Owners ({owners.length})</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(owners, null, 2)}
          </pre>
        </div>

        <div>
          <h2 className="text-xl font-semibold">Property Types ({propertyTypes.length})</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(propertyTypes, null, 2)}
          </pre>
        </div>

        <div>
          <h2 className="text-xl font-semibold">Recent Properties ({properties.length})</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(properties.map(p => ({
              id: p.id,
              name_en: p.name_en,
              property_type: p.property_type.name_en,
              owner: p.owner ? {
                owner_id: p.owner.id,
                owner_name: p.owner.name_en,
              } : null,
            })), null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}