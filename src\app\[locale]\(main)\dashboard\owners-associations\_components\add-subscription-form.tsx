"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations, useLocale } from "next-intl";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { apiClient } from "@/lib/api-client";
import { associationSubscriptionSchema, type AssociationSubscriptionInput } from "@/types/owners-association";
import { useRTL } from "@/hooks/use-rtl";

interface AddSubscriptionFormProps {
  associationId: number;
  onSuccess: () => void;
  onCancel: () => void;
}

export function AddSubscriptionForm({ associationId, onSuccess, onCancel }: AddSubscriptionFormProps) {
  const t = useTranslations("ownersAssociations.subscriptions");
  const locale = useLocale();
  const { isRTL } = useRTL();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<AssociationSubscriptionInput>({
    resolver: zodResolver(associationSubscriptionSchema),
    defaultValues: {
      association_id: associationId,
      name_en: "",
      name_ar: "",
      amount: "",
      frequency: "MONTHLY",
      is_active: true,
    },
  });

  const onSubmit = async (data: AssociationSubscriptionInput) => {
    try {
      setIsLoading(true);
      
      const response = await apiClient.post(
        `/api/owners-associations/${associationId}/subscriptions`,
        data
      );

      if (response.success) {
        toast.success(t("addSuccess"));
        onSuccess();
      }
    } catch (error: any) {
      console.error("Error adding subscription:", error);
      toast.error(t("addError"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name_en"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("nameEn")}</FormLabel>
              <FormControl>
                <Input {...field} placeholder={t("nameEnPlaceholder")} />
              </FormControl>
              <FormDescription>{t("nameEnDescription")}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="name_ar"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("nameAr")}</FormLabel>
              <FormControl>
                <Input {...field} placeholder={t("nameArPlaceholder")} dir="rtl" />
              </FormControl>
              <FormDescription>{t("nameArDescription")}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("amount")}</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="0.000" />
                </FormControl>
                <FormDescription>{t("amountDescription")}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="frequency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("frequencyLabel")}</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectFrequency")} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="MONTHLY">{t("frequency.monthly")}</SelectItem>
                    <SelectItem value="QUARTERLY">{t("frequency.quarterly")}</SelectItem>
                    <SelectItem value="YEARLY">{t("frequency.yearly")}</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>{t("frequencyDescription")}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="is_active"
          render={({ field }) => (
            <FormItem className={cn("flex flex-row items-start space-x-3 space-y-0", isRTL && "flex-row-reverse space-x-reverse")}>
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>{t("isActive")}</FormLabel>
                <FormDescription>
                  {t("isActiveDescription")}
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        <div className={cn("flex gap-3", isRTL && "flex-row-reverse")}>
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isLoading}
          >
            {t("cancel")}
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className={cn("h-4 w-4 animate-spin", isRTL ? "ml-2" : "mr-2")} />}
            {t("addSubscription")}
          </Button>
        </div>
      </form>
    </Form>
  );
}