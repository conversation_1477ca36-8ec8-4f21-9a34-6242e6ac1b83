import { z } from "zod";
import type {
  Payment,
  PaymentStatus,
  PaymentMethod,
  PaymentAllocation,
  Invoice,
  Tenant,
  User
} from "@/generated/prisma";

// Payment Schema
export const paymentSchema = z.object({
  payment_number: z.string().optional(), // Auto-generated if not provided
  invoice_id: z.number().optional().nullable(),
  tenant_id: z.number().min(1, "Tenant is required"),
  amount: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format"),
  payment_method: z.nativeEnum({
    CASH: "CASH",
    BANK_TRANSFER: "BANK_TRANSFER",
    CREDIT_CARD: "CREDIT_CARD",
    DEBIT_CARD: "DEBIT_CARD",
    CHECK: "CHECK",
    OTHER: "OTHER"
  } as const),
  payment_date: z.string().min(1, "Payment date is required"),
  reference_number: z.string().optional().nullable(),
  bank_name: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  status: z.nativeEnum({
    PENDING: "PENDING",
    COMPLETED: "COMPLETED",
    FAILED: "FAILED",
    REFUNDED: "REFUNDED",
    CANCELLED: "CANCELLED"
  } as const).default("COMPLETED"),
  installment: z.number().min(1).optional().nullable(),
  allocations: z.array(z.object({
    invoice_id: z.number().min(1, "Invoice is required"),
    allocated_amount: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format"),
  })).optional()
});

// Payment Allocation Schema
export const paymentAllocationSchema = z.object({
  invoice_id: z.number().min(1, "Invoice is required"),
  allocated_amount: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format"),
});

// Payment Filter Schema
export const paymentFilterSchema = z.object({
  invoice_id: z.number().optional(),
  tenant_id: z.number().optional(),
  payment_method: z.nativeEnum({
    CASH: "CASH",
    BANK_TRANSFER: "BANK_TRANSFER",
    CREDIT_CARD: "CREDIT_CARD",
    DEBIT_CARD: "DEBIT_CARD",
    CHECK: "CHECK",
    OTHER: "OTHER"
  } as const).optional(),
  status: z.nativeEnum({
    PENDING: "PENDING",
    COMPLETED: "COMPLETED",
    FAILED: "FAILED",
    REFUNDED: "REFUNDED",
    CANCELLED: "CANCELLED"
  } as const).optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  search: z.string().optional(),
});

// Types for API requests
export type PaymentInput = z.infer<typeof paymentSchema>;
export type PaymentAllocationInput = z.infer<typeof paymentAllocationSchema>;
export type PaymentFilter = z.infer<typeof paymentFilterSchema>;

// Extended types with relations
export interface PaymentWithRelations extends Payment {
  invoice?: Invoice | null;
  tenant: Tenant;
  creator?: User | null;
  allocations: (PaymentAllocation & {
    invoice: Invoice;
  })[];
}

export interface PaymentAllocationWithRelations extends PaymentAllocation {
  payment: Payment;
  invoice: Invoice;
}

// Helper function to generate payment number
export function generatePaymentNumber(year: number, month: number, sequence: number): string {
  const yearStr = year.toString();
  const monthStr = month.toString().padStart(2, '0');
  const sequenceStr = sequence.toString().padStart(4, '0');
  return `PAY-${yearStr}-${monthStr}-${sequenceStr}`;
}

// Helper function to validate payment allocation
export function validatePaymentAllocation(
  paymentAmount: number | string,
  allocations: PaymentAllocationInput[]
): { valid: boolean; message?: string } {
  const totalPayment = typeof paymentAmount === 'string' ? parseFloat(paymentAmount) : paymentAmount;
  
  const totalAllocated = allocations.reduce((sum, allocation) => {
    const amount = parseFloat(allocation.allocated_amount);
    return sum + amount;
  }, 0);
  
  if (Math.abs(totalPayment - totalAllocated) > 0.001) {
    return {
      valid: false,
      message: `Total allocated amount (${totalAllocated.toFixed(3)}) does not match payment amount (${totalPayment.toFixed(3)})`
    };
  }
  
  return { valid: true };
}

// Helper function to get payment method label
export function getPaymentMethodLabel(method: PaymentMethod): string {
  const labels = {
    CASH: 'Cash',
    BANK_TRANSFER: 'Bank Transfer',
    CREDIT_CARD: 'Credit Card',
    DEBIT_CARD: 'Debit Card',
    CHECK: 'Check',
    OTHER: 'Other'
  };
  return labels[method] || method;
}

// Helper function to get payment status color
export function getPaymentStatusColor(status: PaymentStatus): string {
  const colors = {
    PENDING: 'warning',
    COMPLETED: 'success',
    FAILED: 'destructive',
    REFUNDED: 'info',
    CANCELLED: 'muted'
  };
  return colors[status] || 'secondary';
}

// Helper function to format payment reference
export function formatPaymentReference(payment: Payment): string {
  if (payment.reference_number) {
    return payment.reference_number;
  }
  
  const methodAbbr = {
    CASH: 'CSH',
    BANK_TRANSFER: 'BNK',
    CREDIT_CARD: 'CRD',
    DEBIT_CARD: 'DBT',
    CHECK: 'CHK',
    OTHER: 'OTH'
  };
  
  const dateStr = new Date(payment.payment_date).toISOString().split('T')[0].replace(/-/g, '');
  return `${methodAbbr[payment.payment_method] || 'PAY'}-${dateStr}-${payment.id}`;
}