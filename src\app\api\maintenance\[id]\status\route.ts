import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";



const updateStatusSchema = z.object({
  status: z.enum([
    "REPORTED",
    "ACKNOWLEDGED",
    "IN_PROGRESS",
    "ON_HOLD",
    "COMPLETED",
    "CANCELLED"
  ]),
  notes: z.string().optional(),
  actual_cost: z.number().optional(),
  completion_date: z.string().optional(),
});

// PUT /api/maintenance/[id]/status - Update maintenance request status
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for maintenance
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "maintenance", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update maintenance");
    }

    const { id: idParam } = await params;


    const id = parseInt(idParam);
    if (isNaN(id)) {
      return ApiResponseBuilder.error("Invalid maintenance request ID", "INVALID_ID", 400);
    }

    const body = await request.json();
    const validatedData = updateStatusSchema.parse(body);

    // Check if maintenance request exists
    const existingRequest = await db.maintenanceRequest.findUnique({
      where: { id },
    });

    if (!existingRequest) {
      return ApiResponseBuilder.notFound("Maintenance request not found");
    }

    // Update the maintenance request
    const updatedRequest = await db.maintenanceRequest.update({
      where: { id },
      data: {
        status: validatedData.status,
        actual_cost: validatedData.actual_cost,
        completed_date: validatedData.completion_date 
          ? new Date(validatedData.completion_date)
          : undefined,
        updated_at: new Date(),
        updated_by: decoded.id,
      },
    });

    // Create status history entry
    await db.maintenanceStatusHistory.create({
      data: {
        request_id: id,
        from_status: existingRequest.status,
        to_status: validatedData.status,
        changed_by: decoded.id,
        notes: validatedData.notes,
      },
    });

    // Fetch the updated request with relations
    const requestWithRelations = await db.maintenanceRequest.findUnique({
      where: { id },
      include: {
        property: true,
        unit: true,
        tenant: true,
        assignee: true,
        attachments: true,
        status_history: {
          include: {
            changer: true,
          },
          orderBy: {
            created_at: "desc",
          },
        },
      },
    });

    return ApiResponseBuilder.success(requestWithRelations);
  } catch (error) {
    console.error("Error updating maintenance request status:", error);
    
    if (error instanceof z.ZodError) {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error(
      "Failed to update maintenance request status",
      "UPDATE_FAILED",
      500
    );
  }
}