"use client";

import { useState, useEffect, useCallback } from "react";
import { useTranslations, useLocale } from "next-intl";
import { toast } from "sonner";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { 
  Plus, 
  Edit, 
  Trash2, 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  ToggleLeft,
  ToggleRight
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useRTL } from "@/hooks/use-rtl";
import { apiClient } from "@/lib/api-client";
import { formatCurrency } from "@/lib/utils";
import type { AssociationSubscriptionWithRelations } from "@/types/owners-association";
import { AddSubscriptionForm } from "./add-subscription-form";
import { EditSubscriptionForm } from "./edit-subscription-form";

interface AssociationSubscriptionsTableProps {
  associationId: number;
}

export function AssociationSubscriptionsTable({ associationId }: AssociationSubscriptionsTableProps) {
  const locale = useLocale();
  const { isRTL } = useRTL();
  const t = useTranslations("ownersAssociations.subscriptions");
  const tCommon = useTranslations("common");
  
  const [subscriptions, setSubscriptions] = useState<AssociationSubscriptionWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingSubscription, setEditingSubscription] = useState<AssociationSubscriptionWithRelations | null>(null);

  const fetchSubscriptions = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(
        `/api/owners-associations/${associationId}/subscriptions`
      );
      
      if (response.success) {
        setSubscriptions(response.data);
      }
    } catch (error) {
      console.error("Error fetching subscriptions:", error);
      toast.error(t("serverError"));
    } finally {
      setLoading(false);
    }
  }, [associationId, t]);

  useEffect(() => {
    fetchSubscriptions();
  }, [fetchSubscriptions]);

  const handleToggleStatus = async (subscriptionId: number, currentStatus: boolean) => {
    try {
      await apiClient.put(`/api/owners-associations/${associationId}/subscriptions/${subscriptionId}`, {
        is_active: !currentStatus
      });
      
      toast.success(t("statusUpdateSuccess"));
      fetchSubscriptions();
    } catch (error) {
      console.error("Error updating subscription status:", error);
      toast.error(t("statusUpdateError"));
    }
  };

  const handleDeleteSubscription = async (subscriptionId: number) => {
    try {
      await apiClient.delete(`/api/owners-associations/${associationId}/subscriptions/${subscriptionId}`);
      toast.success(t("deleteSuccess"));
      fetchSubscriptions();
    } catch (error) {
      console.error("Error deleting subscription:", error);
      toast.error(t("deleteError"));
    }
  };

  const getFrequencyLabel = (frequency: string) => {
    const frequencyMap: Record<string, string> = {
      MONTHLY: t("frequency.monthly"),
      QUARTERLY: t("frequency.quarterly"),
      YEARLY: t("frequency.yearly"),
    };
    return frequencyMap[frequency] || frequency;
  };

  const getFrequencyColor = (frequency: string) => {
    const colorMap: Record<string, string> = {
      MONTHLY: "bg-blue-100 text-blue-800",
      QUARTERLY: "bg-orange-100 text-orange-800",
      YEARLY: "bg-green-100 text-green-800",
    };
    return colorMap[frequency] || "bg-gray-100 text-gray-800";
  };

  const columns: ColumnDef<AssociationSubscriptionWithRelations>[] = [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("name")} />
      ),
      cell: ({ row }) => {
        const subscription = row.original;
        const name = locale === 'ar' ? subscription.name_ar : subscription.name_en;
        
        return (
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="font-medium">{name}</div>
            <Badge 
              variant="outline" 
              className={cn("text-xs", getFrequencyColor(subscription.frequency))}
            >
              <Clock className={cn("h-3 w-3", isRTL ? "ml-1" : "mr-1")} />
              {getFrequencyLabel(subscription.frequency)}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: "amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("amount")} />
      ),
      cell: ({ row }) => {
        const amount = parseFloat(row.original.amount.toString());
        return (
          <div className={cn("font-medium flex items-center gap-1", isRTL && "flex-row-reverse")}>
            <span className="text-sm text-muted-foreground">OMR</span>
            {amount.toFixed(3)}
          </div>
        );
      },
    },
    {
      accessorKey: "payments_count",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("paymentsCount")} />
      ),
      cell: ({ row }) => {
        const count = row.original._count?.payments || 0;
        return (
          <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{count}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("status")} />
      ),
      cell: ({ row }) => {
        const subscription = row.original;
        
        return (
          <Badge variant={subscription.is_active ? "default" : "secondary"}>
            {subscription.is_active ? t("statusActive") : t("statusInactive")}
          </Badge>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className={cn("text-right", isRTL && "text-left")}>{tCommon("actions")}</div>,
      cell: ({ row }) => {
        const subscription = row.original;

        return (
          <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleToggleStatus(subscription.id, subscription.is_active)}
              title={subscription.is_active ? t("deactivate") : t("activate")}
            >
              {subscription.is_active ? (
                <ToggleRight className="h-4 w-4 text-green-600" />
              ) : (
                <ToggleLeft className="h-4 w-4 text-gray-400" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditingSubscription(subscription)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeleteSubscription(subscription.id)}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: subscriptions,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  // Calculate summary stats
  const activeSubscriptions = subscriptions.filter(s => s.is_active);
  const totalMonthlyRevenue = activeSubscriptions.reduce((total, sub) => {
    const amount = parseFloat(sub.amount.toString());
    switch (sub.frequency) {
      case 'MONTHLY':
        return total + amount;
      case 'QUARTERLY':
        return total + (amount / 3);
      case 'YEARLY':
        return total + (amount / 12);
      default:
        return total;
    }
  }, 0);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-96" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[400px] w-full" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className={cn("flex items-center justify-between", isRTL && "flex-row-reverse")}>
          <div>
            <CardTitle className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
              <FileText className="h-5 w-5" />
              {t("title")}
            </CardTitle>
            <CardDescription>
              {t("description")}
            </CardDescription>
          </div>
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                {t("addSubscription")}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t("addSubscriptionDialog.title")}</DialogTitle>
                <DialogDescription>
                  {t("addSubscriptionDialog.description")}
                </DialogDescription>
              </DialogHeader>
              <AddSubscriptionForm 
                associationId={associationId}
                onSuccess={() => {
                  setShowAddDialog(false);
                  fetchSubscriptions();
                }}
                onCancel={() => setShowAddDialog(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary Stats */}
        <div className="grid gap-4 md:grid-cols-3">
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="text-sm font-medium">{t("totalSubscriptions")}</div>
            <div className="text-2xl font-bold">{subscriptions.length}</div>
          </div>
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="text-sm font-medium">{t("activeSubscriptions")}</div>
            <div className="text-2xl font-bold text-green-600">{activeSubscriptions.length}</div>
          </div>
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="text-sm font-medium">{t("monthlyRevenue")}</div>
            <div className="text-2xl font-bold">{formatCurrency(totalMonthlyRevenue)}</div>
          </div>
        </div>

        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id} className={cn(isRTL && "text-right")}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} className="hover:bg-muted/50">
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className={cn(isRTL && "text-right")}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    {t("noSubscriptions")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* Edit Subscription Dialog */}
      <Dialog open={!!editingSubscription} onOpenChange={(open) => !open && setEditingSubscription(null)}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>{t("editSubscriptionDialog.title")}</DialogTitle>
            <DialogDescription>
              {t("editSubscriptionDialog.description")}
            </DialogDescription>
          </DialogHeader>
          {editingSubscription && (
            <EditSubscriptionForm 
              associationId={associationId}
              subscription={editingSubscription}
              onSuccess={() => {
                setEditingSubscription(null);
                fetchSubscriptions();
              }}
              onCancel={() => setEditingSubscription(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
}