"use client";

import * as React from "react";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useLocale, useTranslations } from "next-intl";

interface Owner {
  id: number;
  name_en: string;
  name_ar: string;
  phone?: string;
  mobile?: string;
  email?: string;
}

interface OwnerSelectProps {
  value?: number;
  onChange: (value: number | undefined) => void;
  owners: Owner[];
  loading?: boolean;
  disabled?: boolean;
}

export function OwnerSelect({
  value,
  onChange,
  owners,
  loading = false,
  disabled = false,
}: OwnerSelectProps) {
  const [open, setOpen] = React.useState(false);
  const locale = useLocale();
  const t = useTranslations("properties.form");

  const selectedOwner = owners.find((owner) => owner.id === value);

  const displayName = (owner: Owner) => {
    return locale === "ar" ? owner.name_ar : owner.name_en;
  };

  if (loading) {
    return (
      <Button
        variant="outline"
        role="combobox"
        className="w-full justify-between"
        disabled
      >
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        {t("loadingOwners")}
      </Button>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled}
        >
          {selectedOwner ? displayName(selectedOwner) : t("selectOwner")}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0">
        <Command>
          <CommandInput
            placeholder={t("searchOwner")}
          />
          <CommandEmpty>
            {t("noOwnersFound")}
          </CommandEmpty>
          <CommandGroup>
            <CommandItem
              value="none"
              onSelect={() => {
                onChange(undefined);
                setOpen(false);
              }}
            >
              <Check
                className={cn(
                  "mr-2 h-4 w-4",
                  !value ? "opacity-100" : "opacity-0"
                )}
              />
              {t("noOwner")}
            </CommandItem>
            {owners.map((owner) => {
              const searchValue = `${owner.name_en} ${owner.name_ar} ${owner.phone || ''} ${owner.mobile || ''} ${owner.email || ''}`.toLowerCase();
              return (
                <CommandItem
                  key={owner.id}
                  value={searchValue}
                  onSelect={() => {
                    onChange(owner.id);
                    setOpen(false);
                  }}
                  className="flex flex-col items-start"
                >
                <div className="flex w-full items-center">
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === owner.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex-1">
                    <div className="font-medium">{displayName(owner)}</div>
                    {(owner.phone || owner.mobile) && (
                      <div className="text-sm text-muted-foreground">
                        {owner.phone || owner.mobile}
                      </div>
                    )}
                  </div>
                </div>
              </CommandItem>
              );
            })}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}