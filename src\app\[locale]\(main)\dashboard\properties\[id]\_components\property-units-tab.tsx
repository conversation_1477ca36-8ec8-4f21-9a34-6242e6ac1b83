"use client";

import Link from "next/link";
import { Plus, Building2 } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useTranslations } from "next-intl";

interface PropertyUnitsTabProps {
  property: any;
  locale: string;
}

export function PropertyUnitsTab({ property, locale }: PropertyUnitsTabProps) {
  const t = useTranslations("properties.tabs.units");
  const tStatus = useTranslations("units.status");
  const getStatusVariant = (status: string) => {
    switch (status) {
      case "AVAILABLE":
        return "success";
      case "RENTED":
        return "default";
      case "UNDER_MAINTENANCE":
        return "warning";
      default:
        return "default";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "AVAILABLE":
        return tStatus("available");
      case "RENTED":
        return tStatus("rented");
      case "UNDER_MAINTENANCE":
        return tStatus("underMaintenance");
      default:
        return status;
    }
  };

  return (
    <Card>
      <CardHeader className={`flex flex-row items-center justify-between ${locale === "ar" ? "flex-row-reverse" : ""}`}>
        <div className={locale === "ar" ? "text-right" : ""}>
          <CardTitle className={`flex items-center gap-2 ${locale === "ar" ? "flex-row-reverse" : ""}`}>
            <Building2 className="h-5 w-5" />
            {t("title")} ({property.units.length})
          </CardTitle>
          <CardDescription>
            {t("description")}
          </CardDescription>
        </div>
        <Button asChild>
          <Link href={`/${locale}/dashboard/units/create?property=${property.id}`}>
            <Plus className={`${locale === "ar" ? "ml-2" : "mr-2"} h-4 w-4`} />
            {t("addUnit")}
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        {property.units.length === 0 ? (
          <div className="text-center py-8">
            <Building2 className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-semibold">{t("noUnits")}</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              {t("noUnitsDescription")}
            </p>
            <Button asChild className="mt-4">
              <Link href={`/${locale}/dashboard/units/create?property=${property.id}`}>
                <Plus className="mr-2 h-4 w-4" />
                {t("addFirstUnit")}
              </Link>
            </Button>
          </div>
        ) : (
          <div className={`space-y-4 ${locale === "ar" ? "direction-rtl" : ""}`} dir={locale === "ar" ? "rtl" : "ltr"}>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className={locale === "ar" ? "text-right" : ""}>{t("unitNumber")}</TableHead>
                  <TableHead className={locale === "ar" ? "text-right" : ""}>{t("unitName")}</TableHead>
                  <TableHead className="text-center">{t("rooms")}</TableHead>
                  <TableHead className="text-center">{t("majalis")}</TableHead>
                  <TableHead className="text-center">{t("bathrooms")}</TableHead>
                  <TableHead className="text-center">{t("floor")}</TableHead>
                  <TableHead className="text-center">{t("area")}</TableHead>
                  <TableHead className="text-center">{t("rent")}</TableHead>
                  <TableHead className="text-center">{t("status")}</TableHead>
                  <TableHead className={locale === "ar" ? "text-right" : ""}>{t("amenities")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {property.units.map((unit: any) => (
                  <TableRow key={unit.id}>
                    <TableCell className={`font-medium ${locale === "ar" ? "text-right" : ""}`}>
                      <Link 
                        href={`/${locale}/dashboard/units/${unit.id}`}
                        className="text-blue-600 hover:underline"
                      >
                        {unit.unit_number}
                      </Link>
                    </TableCell>
                    <TableCell className={locale === "ar" ? "text-right" : ""}>
                      {locale === "ar" ? unit.unit_name_ar || "-" : unit.unit_name_en || "-"}
                    </TableCell>
                    <TableCell className="text-center">{unit.rooms_count || "-"}</TableCell>
                    <TableCell className="text-center">{unit.majalis_count || "-"}</TableCell>
                    <TableCell className="text-center">{unit.bathrooms_count || "-"}</TableCell>
                    <TableCell className="text-center">{unit.floor_number ?? "-"}</TableCell>
                    <TableCell className="text-center">
                      {unit.area ? `${parseFloat(unit.area).toFixed(2)} m²` : "-"}
                    </TableCell>
                    <TableCell className="text-center font-medium">
                      {parseFloat(unit.rent_amount).toFixed(3)} OMR
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant={getStatusVariant(unit.status) as any}>
                        {getStatusText(unit.status)}
                      </Badge>
                    </TableCell>
                    <TableCell className={locale === "ar" ? "text-right" : ""}>
                      <div className={`flex flex-wrap gap-1 max-w-[200px] ${locale === "ar" ? "justify-end" : ""}`}>
                        {unit.amenities.slice(0, 2).map((amenity: any) => (
                          <Badge key={amenity.id} variant="outline" className="text-xs">
                            {locale === "ar" ? amenity.name_ar : amenity.name_en}
                          </Badge>
                        ))}
                        {unit.amenities.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{unit.amenities.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}