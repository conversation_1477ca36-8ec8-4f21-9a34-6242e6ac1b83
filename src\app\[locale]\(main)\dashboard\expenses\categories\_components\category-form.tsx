"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import { z } from "zod";
import { Loader2, AlertCircle, CheckCircle2, ArrowLeft } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { expenseCategoryFormSchema, type ExpenseCategory } from "@/types/expense";
import { ErrorState, InlineErrorState, getErrorVariant } from "@/components/error-state";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface CategoryFormProps {
  category?: ExpenseCategory;
  mode: "create" | "edit";
  locale: string;
}

type FormData = z.infer<typeof expenseCategoryFormSchema>;

export function CategoryForm({ category, mode, locale }: CategoryFormProps) {
  const router = useRouter();
  const t = useTranslations("expenses.categories");
  const tCommon = useTranslations("common");
  const tMessages = useTranslations("expenses.messages");

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const form = useForm<FormData>({
    resolver: zodResolver(expenseCategoryFormSchema),
    mode: "onChange",
    defaultValues: {
      name_en: category?.name_en || "",
      name_ar: category?.name_ar || "",
      description: category?.description || "",
      is_active: category?.is_active ?? true,
      sort_order: category?.sort_order ?? 0,
    },
  });

  const { formState: { errors, isDirty } } = form;

  const onSubmit = async (data: FormData) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      setValidationErrors({});

      const url = mode === "create" 
        ? "/api/expense-categories"
        : `/api/expense-categories/${category?.id}`;
      
      const method = mode === "create" ? "POST" : "PUT";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Handle validation errors
        if (response.status === 400 && errorData.details) {
          const fieldErrors: Record<string, string> = {};
          errorData.details.forEach((detail: any) => {
            if (detail.path && detail.path.length > 0) {
              fieldErrors[detail.path[0]] = detail.message;
            }
          });
          setValidationErrors(fieldErrors);

          const errorMessages = errorData.details.map((detail: any) => detail.message);
          throw new Error(`Validation failed: ${errorMessages.join(", ")}`);
        }

        throw new Error(errorData.error || `Failed to ${mode} category`);
      }

      const result = await response.json();
      
      toast.success(
        mode === "create" 
          ? tMessages("categoryCreateSuccess")
          : tMessages("categoryUpdateSuccess")
      );

      router.push(`/${locale}/dashboard/expenses/categories`);
      router.refresh();

    } catch (error) {
      console.error(`Error ${mode === "create" ? "creating" : "updating"} category:`, error);
      
      const errorMessage = error instanceof Error ? error.message : `Failed to ${mode} category`;
      setSubmitError(errorMessage);
      
      toast.error(
        mode === "create" 
          ? tMessages("categoryCreateError") || "Failed to create category"
          : tMessages("categoryUpdateError") || "Failed to update category"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    router.push(`/${locale}/dashboard/expenses/categories`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {tCommon("back")}
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {mode === "create" ? t("addCategory") : t("editCategory")}
          </h1>
          <p className="text-muted-foreground">
            {mode === "create" 
              ? t("addCategoryDescription") 
              : t("editCategoryDescription")
            }
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>
            {mode === "create" ? t("categoryDetails") : t("updateCategoryDetails")}
          </CardTitle>
          <CardDescription>
            {mode === "create" 
              ? t("fillCategoryDetails") 
              : t("updateCategoryDetailsDescription")
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Submit Error */}
          {submitError && (
            <InlineErrorState
              error={submitError}
              variant={getErrorVariant(submitError)}
              className="mb-6"
            />
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Names */}
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="name_en"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        {t("nameEn")}
                        {errors.name_en && <AlertCircle className="h-4 w-4 text-destructive" />}
                        {!errors.name_en && field.value && isDirty && <CheckCircle2 className="h-4 w-4 text-green-500" />}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("nameEnPlaceholder")}
                          {...field}
                          className={errors.name_en || validationErrors.name_en ? "border-destructive" : ""}
                        />
                      </FormControl>
                      <FormMessage />
                      {validationErrors.name_en && (
                        <p className="text-sm text-destructive">{validationErrors.name_en}</p>
                      )}
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="name_ar"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        {t("nameAr")}
                        {errors.name_ar && <AlertCircle className="h-4 w-4 text-destructive" />}
                        {!errors.name_ar && field.value && isDirty && <CheckCircle2 className="h-4 w-4 text-green-500" />}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("nameArPlaceholder")}
                          {...field}
                          className={errors.name_ar || validationErrors.name_ar ? "border-destructive" : ""}
                          dir="rtl"
                        />
                      </FormControl>
                      <FormMessage />
                      {validationErrors.name_ar && (
                        <p className="text-sm text-destructive">{validationErrors.name_ar}</p>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("description")}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t("descriptionPlaceholder")}
                        {...field}
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Settings */}
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">{t("isActive")}</FormLabel>
                        <div className="text-sm text-muted-foreground">
                          {t("isActiveDescription")}
                        </div>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sort_order"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("sortOrder")}</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value, 10) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                      <div className="text-sm text-muted-foreground">
                        {t("sortOrderDescription")}
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              {/* Submit Buttons */}
              <div className="flex items-center justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleBack}
                  disabled={isSubmitting}
                >
                  {tCommon("cancel")}
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      {mode === "create" ? t("creating") : t("updating")}
                    </div>
                  ) : (
                    mode === "create" ? t("createCategory") : t("updateCategory")
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
