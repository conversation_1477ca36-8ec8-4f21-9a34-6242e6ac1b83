import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { paymentFilterSchema, subscriptionPaymentSchema } from "@/types/owners-association";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";
import { Decimal } from "@prisma/client/runtime/library";

// GET /api/owners-associations/[id]/subscription-payments - List all subscription payments with status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await hasPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view subscription payments");
    }

    const { id: idParam } = await params;
    const associationId = parseInt(idParam);

    if (isNaN(associationId)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    // Check if association exists
    const association = await db.ownersAssociation.findUnique({
      where: { id: associationId },
    });

    if (!association) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    const searchParams = request.nextUrl.searchParams;
    
    // Parse filters
    const filters = paymentFilterSchema.parse({
      status: searchParams.get("status") || undefined,
      member_id: searchParams.get("member_id") ? parseInt(searchParams.get("member_id")!) : undefined,
      date_from: searchParams.get("date_from") || undefined,
      date_to: searchParams.get("date_to") || undefined,
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
      pageSize: searchParams.get("pageSize") ? parseInt(searchParams.get("pageSize")!) : 10,
      sortBy: searchParams.get("sortBy") || "due_date",
      sortOrder: searchParams.get("sortOrder") || "desc",
    });

    // Build where clause
    const where: any = {
      subscription: {
        association_id: associationId,
      },
    };

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.member_id) {
      where.member_id = filters.member_id;
    }

    if (filters.date_from || filters.date_to) {
      where.due_date = {};
      if (filters.date_from) {
        where.due_date.gte = new Date(filters.date_from);
      }
      if (filters.date_to) {
        where.due_date.lte = new Date(filters.date_to);
      }
    }

    // Calculate pagination
    const page = filters.page || 1;
    const pageSize = filters.pageSize || 10;
    const skip = (page - 1) * pageSize;

    // Update overdue statuses
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    await db.subscriptionPayment.updateMany({
      where: {
        subscription: {
          association_id: associationId,
        },
        status: "UNPAID",
        due_date: {
          lt: today,
        },
      },
      data: {
        status: "OVERDUE",
      },
    });

    // Fetch payments - handle both old and new schema gracefully
    let payments, total;
    
    try {
      // Try fetching with enhanced schema first
      [payments, total] = await Promise.all([
        db.subscriptionPayment.findMany({
          where,
          include: {
            subscription: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
                amount: true,
                frequency: true,
              },
            },
            member: {
              select: {
                id: true,
                full_name: true,
                unit_number: true,
                email: true,
                phone: true,
              },
            },
            creator: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
              },
            },
          },
          orderBy: {
            [filters.sortBy || "due_date"]: filters.sortOrder || "desc",
          },
          skip,
          take: pageSize,
        }),
        db.subscriptionPayment.count({ where }),
      ]);
    } catch (error: any) {
      // If enhanced schema fails, fall back to basic schema
      console.warn('Enhanced schema not available, using basic schema:', error.message);
      [payments, total] = await Promise.all([
        db.subscriptionPayment.findMany({
          where,
          include: {
            subscription: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
                amount: true,
                frequency: true,
              },
            },
            member: {
              select: {
                id: true,
                full_name: true,
                unit_number: true,
                email: true,
                phone: true,
              },
            },
            creator: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
              },
            },
          },
          orderBy: {
            [filters.sortBy || "due_date"]: filters.sortOrder || "desc",
          },
          skip,
          take: pageSize,
        }),
        db.subscriptionPayment.count({ where }),
      ]);
    }

    // Transform payments to handle Decimal serialization and calculate summaries
    const transformedPayments = await Promise.all(payments.map(async payment => {
      // Handle both old and new schema gracefully
      const hasEnhancedSchema = 'amount_due' in payment;
      const amountDue = hasEnhancedSchema 
        ? new Decimal((payment as any).amount_due?.toString() || (payment as any).amount.toString())
        : new Decimal((payment as any).amount.toString());
      
      let amountPaid = new Decimal("0");
      
      if (hasEnhancedSchema) {
        // Use the amount_paid column if available
        amountPaid = new Decimal((payment as any).amount_paid?.toString() || "0");
      } else {
        // Calculate from transactions if enhanced schema not available
        if ((payment as any).status === 'PAID') {
          amountPaid = new Decimal((payment as any).amount.toString());
        } else if ((payment as any).status === 'PARTIALLY_PAID') {
          // Try to calculate from transaction history
          try {
            const transactions = await db.associationTransaction.findMany({
              where: {
                association_id: associationId,
                type: 'INCOME',
                category: 'subscription',
                transaction_date: {
                  gte: (payment as any).due_date
                },
                description: {
                  contains: (payment as any).member.full_name
                }
              }
            });
            
            // Sum up transactions that likely relate to this payment
            const relevantTransactions = transactions.filter(t => 
              t.description.includes((payment as any).subscription.name_en) &&
              t.description.includes((payment as any).member.unit_number)
            );
            
            amountPaid = relevantTransactions.reduce((sum, t) => 
              sum.plus(new Decimal(t.amount.toString())), new Decimal("0")
            );
          } catch (error) {
            console.warn('Could not calculate amount from transactions:', error);
            // Default to 50% for partially paid if we can't calculate
            amountPaid = amountDue.times(0.5);
          }
        }
      }
      
      const remainingBalance = amountDue.minus(amountPaid);

      return {
        ...payment,
        amount: (payment as any).amount.toString(),
        amount_due: amountDue.toString(),
        amount_paid: amountPaid.toString(),
        remaining_balance: remainingBalance.toString(),
        payment_percentage: amountDue.isZero() ? 0 : parseFloat(amountPaid.dividedBy(amountDue).times(100).toFixed(2)),
        subscription: {
          ...payment.subscription,
          amount: payment.subscription.amount.toString(),
        },
        // Handle transaction and installments only if they exist
        transaction: (payment as any).transaction ? {
          ...(payment as any).transaction,
          amount: (payment as any).transaction.amount.toString(),
        } : null,
        installments: (payment as any).installments ? (payment as any).installments.map((inst: any) => ({
          ...inst,
          amount: inst.amount.toString(),
        })) : [],
        installments_count: (payment as any).installments?.length || 0,
        // Add schema compatibility flag
        enhanced_schema: hasEnhancedSchema,
      };
    }));

    // Calculate summary statistics - handle both schemas
    let summaryStats;
    try {
      // Try enhanced schema aggregation
      summaryStats = await db.subscriptionPayment.aggregate({
        where,
        _sum: {
          amount: true, // Use basic amount field for compatibility
        },
        _count: {
          _all: true,
        },
      });
    } catch (error) {
      // Fallback to basic aggregation
      summaryStats = await db.subscriptionPayment.aggregate({
        where,
        _sum: {
          amount: true,
        },
        _count: {
          _all: true,
        },
      });
    }

    const statusCounts = await db.subscriptionPayment.groupBy({
      by: ["status"],
      where: {
        subscription: {
          association_id: associationId,
        },
      },
      _count: {
        _all: true,
      },
    });

    // Calculate totals based on transformed payments for accuracy
    const totalDue = transformedPayments.reduce((sum, payment) => 
      sum.plus(new Decimal(payment.amount_due)), new Decimal(0)
    );
    const totalPaid = transformedPayments.reduce((sum, payment) => 
      sum.plus(new Decimal(payment.amount_paid)), new Decimal(0)
    );
    const totalRemaining = totalDue.minus(totalPaid);

    const stats = {
      total_due: totalDue.toString(),
      total_paid: totalPaid.toString(),
      total_remaining: totalRemaining.toString(),
      collection_rate: totalDue.isZero() ? 0 : parseFloat(totalPaid.dividedBy(totalDue).times(100).toFixed(2)),
      total_count: summaryStats._count._all,
      status_breakdown: statusCounts.reduce((acc, item) => {
        acc[item.status.toLowerCase()] = item._count._all;
        return acc;
      }, {} as Record<string, number>),
    };

    return ApiResponseBuilder.success({
      payments: transformedPayments,
      stats,
    }, {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error: any) {
    console.error("Error fetching subscription payments:", error);
    console.error("Error stack:", error.stack);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }
    
    // Check for Prisma errors
    if (error.code === 'P2025') {
      return ApiResponseBuilder.notFound("Record not found");
    }
    
    if (error.code === 'P2021') {
      return ApiResponseBuilder.error("Database schema mismatch. Column might not exist.", "SCHEMA_ERROR", 500);
    }
    
    return ApiResponseBuilder.error(
      error.message || "Failed to fetch subscription payments", 
      "INTERNAL_ERROR", 
      500
    );
  }
}

// POST /api/owners-associations/[id]/subscription-payments - Create subscription payment records for members
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for owners-associations
    const canCreate = await hasPermission(decoded.id, "owners-associations", "CREATE");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create subscription payments");
    }

    const { id: idParam } = await params;
    const associationId = parseInt(idParam);

    if (isNaN(associationId)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    const body = await request.json();
    const validatedData = subscriptionPaymentSchema.parse(body);

    // Validate subscription belongs to association
    const subscription = await db.associationSubscription.findFirst({
      where: {
        id: validatedData.subscription_id,
        association_id: associationId,
        is_active: true,
      },
    });

    if (!subscription) {
      return ApiResponseBuilder.error("Subscription not found or not active", "NOT_FOUND", 404);
    }

    // Validate member belongs to association
    const member = await db.associationMember.findFirst({
      where: {
        id: validatedData.member_id,
        association_id: associationId,
      },
    });

    if (!member) {
      return ApiResponseBuilder.error("Member not found in this association", "NOT_FOUND", 404);
    }

    // Check for existing unpaid payment for same member and subscription
    const existingPayment = await db.subscriptionPayment.findFirst({
      where: {
        subscription_id: validatedData.subscription_id,
        member_id: validatedData.member_id,
        status: {
          in: ["UNPAID", "OVERDUE", "PARTIALLY_PAID"],
        },
      },
    });

    if (existingPayment) {
      return ApiResponseBuilder.error(
        "Member already has an outstanding payment for this subscription",
        "DUPLICATE_PAYMENT",
        400
      );
    }

    // Create the subscription payment - handle both schemas
    let payment;
    try {
      // Try creating with enhanced schema
      payment = await db.subscriptionPayment.create({
        data: {
          subscription_id: validatedData.subscription_id,
          member_id: validatedData.member_id,
          payment_date: validatedData.payment_date ? new Date(validatedData.payment_date) : new Date(),
          due_date: new Date(validatedData.due_date),
          amount: new Decimal(validatedData.amount_due),
          status: validatedData.status,
          payment_method: validatedData.payment_method,
          reference_number: validatedData.reference_number,
          notes: validatedData.notes,
          created_by: decoded.id,
        },
        include: {
          subscription: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
              amount: true,
            },
          },
          member: {
            select: {
              id: true,
              full_name: true,
              unit_number: true,
            },
          },
        },
      });
    } catch (error: any) {
      console.error('Error creating payment with enhanced schema, falling back to basic:', error.message);
      // Fallback to basic schema
      payment = await db.subscriptionPayment.create({
        data: {
          subscription_id: validatedData.subscription_id,
          member_id: validatedData.member_id,
          payment_date: validatedData.payment_date ? new Date(validatedData.payment_date) : new Date(),
          due_date: new Date(validatedData.due_date),
          amount: new Decimal(validatedData.amount_due),
          status: validatedData.status,
          payment_method: validatedData.payment_method,
          reference_number: validatedData.reference_number,
          notes: validatedData.notes,
          created_by: decoded.id,
        },
        include: {
          subscription: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
              amount: true,
            },
          },
          member: {
            select: {
              id: true,
              full_name: true,
              unit_number: true,
            },
          },
        },
      });
    }

    // Transform payment to handle Decimal serialization
    const hasEnhancedSchema = 'amount_due' in payment;
    const amountDue = hasEnhancedSchema 
      ? new Decimal((payment as any).amount_due?.toString() || payment.amount.toString())
      : new Decimal(payment.amount.toString());
    const amountPaid = hasEnhancedSchema
      ? new Decimal((payment as any).amount_paid?.toString() || "0")
      : new Decimal("0");
    
    const transformedPayment = {
      ...payment,
      amount: payment.amount.toString(),
      amount_due: amountDue.toString(),
      amount_paid: amountPaid.toString(),
      remaining_balance: amountDue.minus(amountPaid).toString(),
      subscription: {
        ...payment.subscription,
        amount: payment.subscription.amount.toString(),
      },
      enhanced_schema: hasEnhancedSchema,
    };

    return ApiResponseBuilder.success(transformedPayment);
  } catch (error: any) {
    console.error("Error creating subscription payment:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to create subscription payment", "INTERNAL_ERROR", 500);
  }
}