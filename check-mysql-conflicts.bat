@echo off
echo ========================================
echo Checking for MySQL Conflicts
echo ========================================
echo.

echo 1. Checking for running MySQL processes...
tasklist | findstr mysql
echo.

echo 2. Checking for MySQL services...
sc query | findstr -i mysql
echo.

echo 3. Checking what's using port 3306...
netstat -ano | findstr :3306
echo.

echo 4. Checking for other database services...
sc query | findstr -i "sql\|maria\|postgres"
echo.

echo 5. Checking XAMPP MySQL service specifically...
sc query mysql 2>nul
if %errorlevel% == 0 (
    echo ✅ XAMPP MySQL service found
    sc query mysql
) else (
    echo ❌ XAMPP MySQL service not found
)
echo.

echo 6. Checking for MySQL installations...
if exist "C:\Program Files\MySQL" (
    echo ⚠️  MySQL installation found in Program Files
    dir "C:\Program Files\MySQL"
) else (
    echo ✅ No MySQL in Program Files
)
echo.

if exist "C:\ProgramData\MySQL" (
    echo ⚠️  MySQL data found in ProgramData
) else (
    echo ✅ No MySQL in ProgramData
)

echo.
echo ========================================
echo Conflict check complete
echo ========================================
pause
