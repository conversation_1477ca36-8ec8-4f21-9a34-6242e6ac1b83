import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { Prisma } from "@prisma/client";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

// GET /api/tenants/[id]/financial-summary - Get financial summary for a tenant
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for tenants
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "tenants", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to read tenants");
    }

    const { id } = await params;
    const tenantId = parseInt(id);
    if (isNaN(tenantId)) {
      return ApiResponseBuilder.error(
        "Invalid tenant ID",
        "INVALID_ID",
        400
      );
    }

    // Check if tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
    });

    if (!tenant) {
      return ApiResponseBuilder.error(
        "Tenant not found",
        "NOT_FOUND",
        404
      );
    }

    // Get all invoices for the tenant
    const invoices = await db.invoice.findMany({
      where: {
        tenant_id: tenantId,
      },
      select: {
        id: true,
        total_amount: true,
        status: true,
        due_date: true,
      },
    });

    // Get all payments for the tenant
    const payments = await db.payment.findMany({
      where: {
        tenant_id: tenantId,
        status: "COMPLETED",
      },
      select: {
        amount: true,
      },
    });

    // Calculate financial statistics
    const totalInvoices = invoices.length;
    const totalInvoiceAmount = invoices.reduce((sum, inv) => sum + Number(inv.total_amount), 0);
    
    const paidInvoices = invoices.filter(inv => inv.status === "PAID");
    const unpaidInvoices = invoices.filter(inv => inv.status === "PENDING");
    const partiallyPaidInvoices = invoices.filter(inv => inv.status === "PARTIALLY_PAID");
    const overdueInvoices = invoices.filter(inv => 
      inv.status === "PENDING" && 
      inv.due_date && 
      new Date(inv.due_date) < new Date()
    );

    const totalPaid = payments.reduce((sum, payment) => sum + Number(payment.amount), 0);
    const totalPending = unpaidInvoices.reduce((sum, inv) => sum + Number(inv.total_amount), 0);
    const totalOverdue = overdueInvoices.reduce((sum, inv) => sum + Number(inv.total_amount), 0);

    // Get active contracts count
    const activeContracts = await db.contractTenant.count({
      where: {
        tenant_id: tenantId,
        contract: {
          status: "ACTIVE",
        },
      },
    });

    // Get last payment date
    const lastPayment = await db.payment.findFirst({
      where: {
        tenant_id: tenantId,
        status: "COMPLETED",
      },
      orderBy: {
        payment_date: "desc",
      },
      select: {
        payment_date: true,
      },
    });

    // Calculate payment percentage
    const paymentPercentage = totalInvoiceAmount > 0 
      ? Math.round((totalPaid / totalInvoiceAmount) * 100) 
      : 0;

    const summary = {
      invoices: {
        total: totalInvoices,
        paid: paidInvoices.length,
        pending: unpaidInvoices.length,
        partiallyPaid: partiallyPaidInvoices.length,
        overdue: overdueInvoices.length,
      },
      amounts: {
        totalInvoiced: totalInvoiceAmount,
        totalPaid: totalPaid,
        totalPending: totalPending,
        totalOverdue: totalOverdue,
        balance: totalInvoiceAmount - totalPaid,
      },
      statistics: {
        paymentPercentage,
        activeContracts,
        lastPaymentDate: lastPayment?.payment_date || null,
      },
    };

    return ApiResponseBuilder.success(summary);
  } catch (error) {
    console.error("Error fetching tenant financial summary:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch financial summary",
      "FETCH_ERROR",
      500,
      error
    );
  }
}