"use client";

import { useEffect, useState } from "react";
import type { AuthUser, UserPermissions } from "@/types/user";

interface UsePermissionsReturn {
  user: AuthUser | null;
  permissions: UserPermissions;
  isLoading: boolean;
  error: string | null;
  hasPermission: (module: string, action: "create" | "read" | "update" | "delete") => boolean;
  hasModuleAccess: (module: string) => boolean;
  isAuthenticated: boolean;
  refetch: () => Promise<void>;
}

export function usePermissions(): UsePermissionsReturn {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [permissions, setPermissions] = useState<UserPermissions>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUser = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch("/api/auth/me", {
        credentials: "include", // Ensure cookies are included
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          // User is not authenticated
          setUser(null);
          setPermissions({});
          return;
        }
        throw new Error("Failed to fetch user information");
      }

      const data = await response.json();
      setUser(data.user);
      setPermissions(data.user.permissions);
    } catch (error) {
      console.error("Error fetching user:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch user");
      setUser(null);
      setPermissions({});
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUser();
  }, []);

  const hasPermission = (module: string, action: "create" | "read" | "update" | "delete"): boolean => {
    return permissions[module]?.[action] || false;
  };

  const hasModuleAccess = (module: string): boolean => {
    const modulePerms = permissions[module];
    if (!modulePerms) return false;
    
    return modulePerms.create || modulePerms.read || modulePerms.update || modulePerms.delete;
  };

  const isAuthenticated = user !== null;

  return {
    user,
    permissions,
    isLoading,
    error,
    hasPermission,
    hasModuleAccess,
    isAuthenticated,
    refetch: fetchUser,
  };
}

// Hook for checking specific permission
export function usePermission(module: string, action: "create" | "read" | "update" | "delete") {
  const { hasPermission, isLoading, error } = usePermissions();
  
  return {
    hasPermission: hasPermission(module, action),
    isLoading,
    error,
  };
}

// Hook for checking module access
export function useModuleAccess(module: string) {
  const { hasModuleAccess, isLoading, error } = usePermissions();
  
  return {
    hasAccess: hasModuleAccess(module),
    isLoading,
    error,
  };
}
