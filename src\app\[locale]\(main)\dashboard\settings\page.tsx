"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { Upload, Building2, Save, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useRTL } from "@/hooks/use-rtl";
import { cn } from "@/lib/utils";

type CompanySettingsForm = {
  company_name: string;
  logo_url?: string;
};

interface CompanySettings {
  id: number;
  company_name: string;
  logo_url: string | null;
  created_at: string;
  updated_at: string;
}

export default function SettingsPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [settings, setSettings] = useState<CompanySettings | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const t = useTranslations("settings");
  const { isRTL } = useRTL();

  // Form schema with translations
  const companySettingsSchema = z.object({
    company_name: z.string()
      .min(1, t("company.companyNameRequired"))
      .max(255, t("company.companyNameTooLong")),
    logo_url: z.string().optional(),
  });

  const form = useForm<CompanySettingsForm>({
    resolver: zodResolver(companySettingsSchema),
    defaultValues: {
      company_name: "",
      logo_url: "",
    },
  });

  // Fetch company settings
  const fetchSettings = async () => {
    try {
      const response = await fetch("/api/company-settings");
      const result = await response.json();

      if (result.success) {
        setSettings(result.data);
        form.reset({
          company_name: result.data.company_name,
          logo_url: result.data.logo_url || "",
        });
        setLogoPreview(result.data.logo_url);
      } else {
        toast.error(t("company.loadFailed"));
      }
    } catch (error) {
      console.error("Error fetching settings:", error);
      toast.error("Failed to load company settings");
    }
  };

  // Load settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Handle form submission
  const onSubmit = async (data: CompanySettingsForm) => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/company-settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        setSettings(result.data);
        toast.success(t("company.updateSuccess"));
      } else {
        toast.error(result.error || t("company.updateFailed"));
      }
    } catch (error) {
      console.error("Error updating settings:", error);
      toast.error(t("company.updateFailed"));
    } finally {
      setIsLoading(false);
    }
  };

  // Handle logo upload
  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("logo", file);

      const response = await fetch("/api/upload/logo", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        const logoUrl = result.data.logo_url;
        form.setValue("logo_url", logoUrl);
        setLogoPreview(logoUrl);
        toast.success(t("company.logoUploadSuccess"));
      } else {
        toast.error(result.error || t("company.logoUploadFailed"));
      }
    } catch (error) {
      console.error("Error uploading logo:", error);
      toast.error(t("company.logoUploadFailed"));
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{t("title")}</h1>
        <p className="text-muted-foreground">
          {t("description")}
        </p>
      </div>

      <Tabs defaultValue="company" className="space-y-4">
        <TabsList>
          <TabsTrigger value="company">{t("tabs.companyInformation")}</TabsTrigger>
        </TabsList>

        <TabsContent value="company" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <Building2 className="h-5 w-5" />
                {t("company.title")}
              </CardTitle>
              <CardDescription>
                {t("company.description")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="company_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("company.companyName")}</FormLabel>
                        <FormControl>
                          <Input placeholder={t("company.companyNamePlaceholder")} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-4">
                    <FormLabel>{t("company.companyLogo")}</FormLabel>
                    <div className={cn("flex items-start gap-4", isRTL && "flex-row-reverse")}>
                      {logoPreview && (
                        <div className="flex-shrink-0">
                          <img
                            src={logoPreview}
                            alt="Company Logo"
                            className="h-20 w-20 rounded-lg border object-contain"
                          />
                        </div>
                      )}
                      <div className="flex-1 space-y-2">
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={handleLogoUpload}
                          disabled={isUploading}
                          className="cursor-pointer"
                        />
                        <p className="text-sm text-muted-foreground">
                          {t("company.uploadLogoDescription")}
                        </p>
                        {isUploading && (
                          <div className={cn("flex items-center gap-2 text-sm text-muted-foreground", isRTL && "flex-row-reverse")}>
                            <Loader2 className="h-4 w-4 animate-spin" />
                            {t("company.uploadingLogo")}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className={cn("flex", isRTL ? "justify-start" : "justify-end")}>
                    <Button type="submit" disabled={isLoading}>
                      {isLoading ? (
                        <span className={cn("flex items-center", isRTL && "flex-row-reverse")}>
                          <Loader2 className={cn("h-4 w-4 animate-spin", isRTL ? "ml-2" : "mr-2")} />
                          {t("company.saving")}
                        </span>
                      ) : (
                        <span className={cn("flex items-center", isRTL && "flex-row-reverse")}>
                          <Save className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                          {t("company.saveChanges")}
                        </span>
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
