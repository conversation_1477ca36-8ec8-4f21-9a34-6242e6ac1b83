-- Create a view that calculates amount_paid from transaction history
-- This is a workaround if you can't add the amount_due and amount_paid columns

CREATE OR REPLACE VIEW subscription_payments_with_amounts AS
SELECT 
    sp.*,
    sp.amount as amount_due,
    COALESCE(
        (SELECT SUM(at.amount) 
         FROM association_transactions at
         WHERE at.type = 'INCOME' 
         AND at.category = 'subscription'
         AND at.description LIKE CONCAT('%', s.name_en, '%')
         AND at.description LIKE CONCAT('%', m.full_name, '%')
         AND at.transaction_date >= sp.due_date
        ), 0
    ) as amount_paid
FROM subscription_payments sp
JOIN association_members m ON sp.member_id = m.id
JOIN association_subscriptions s ON sp.subscription_id = s.id;