const fetch = require('node-fetch');
const jwt = require('jsonwebtoken');
const { PrismaClient } = require('./src/generated/prisma');
const prisma = new PrismaClient();

async function testPaymentApiWorkaround() {
  try {
    // Get admin user
    const adminUser = await prisma.user.findFirst({
      where: { username: 'admin' }
    });
    
    if (!adminUser) {
      console.log('Admin user not found!');
      return;
    }
    
    // Create JWT token
    const token = jwt.sign(
      { id: adminUser.id, username: adminUser.username, email: adminUser.email },
      process.env.JWT_SECRET || 'your-secret-key-change-in-production',
      { expiresIn: '7d' }
    );
    
    // Test fetching payments
    console.log('Testing payment API with workaround...\n');
    
    const response = await fetch('http://localhost:3000/api/owners-associations/1/subscription-payments', {
      headers: {
        'Cookie': `auth-token=${token}`,
        'Accept': 'application/json',
      }
    });
    
    const result = await response.json();
    
    if (!result.success) {
      console.log('API Error:', result.error);
      return;
    }
    
    console.log('Payment API Response:');
    console.log(`Total payments: ${result.data.payments.length}`);
    
    // Show payments with different statuses
    const paidPayments = result.data.payments.filter(p => p.status === 'PAID');
    const partialPayments = result.data.payments.filter(p => p.status === 'PARTIALLY_PAID');
    const unpaidPayments = result.data.payments.filter(p => p.status === 'UNPAID');
    
    console.log(`\nPayment Status Summary:`);
    console.log(`- Paid: ${paidPayments.length}`);
    console.log(`- Partially Paid: ${partialPayments.length}`);
    console.log(`- Unpaid: ${unpaidPayments.length}`);
    
    // Show details of partially paid payments
    if (partialPayments.length > 0) {
      console.log('\nPartially Paid Payments:');
      partialPayments.forEach(payment => {
        console.log(`\nMember: ${payment.member.full_name} (Unit ${payment.member.unit_number})`);
        console.log(`Subscription: ${payment.subscription.name_en}`);
        console.log(`Amount Due: ${payment.amount_due} OMR`);
        console.log(`Amount Paid: ${payment.amount_paid} OMR`);
        console.log(`Remaining: ${payment.remaining_balance} OMR`);
        console.log(`Progress: ${payment.payment_percentage}%`);
        console.log(`Using Enhanced Schema: ${payment.enhanced_schema}`);
      });
    }
    
    // Show overall statistics
    if (result.data.stats) {
      console.log('\nOverall Statistics:');
      console.log(`Total Due: ${result.data.stats.total_due} OMR`);
      console.log(`Total Paid: ${result.data.stats.total_paid} OMR`);
      console.log(`Outstanding: ${result.data.stats.total_remaining} OMR`);
      console.log(`Collection Rate: ${result.data.stats.collection_rate}%`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPaymentApiWorkaround();