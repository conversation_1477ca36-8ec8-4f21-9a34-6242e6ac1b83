/**
 * Utility functions for handling and formatting errors in the tenant management system
 */

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

/**
 * Converts various error types into a standardized ApiError format
 */
export function normalizeError(error: unknown): ApiError {
  if (error instanceof Error) {
    return {
      message: error.message,
      status: (error as any).status,
      code: (error as any).code,
      details: (error as any).details,
    };
  }

  if (typeof error === "string") {
    return { message: error };
  }

  if (error && typeof error === "object" && "message" in error) {
    return {
      message: (error as any).message || "Unknown error",
      status: (error as any).status,
      code: (error as any).code,
      details: (error as any).details,
    };
  }

  return { message: "An unexpected error occurred" };
}

/**
 * Generates user-friendly error messages based on error context
 */
export function getUserFriendlyErrorMessage(error: unknown, context: string): string {
  const normalizedError = normalizeError(error);
  const { message, status } = normalizedError;

  // Network errors
  if (message.toLowerCase().includes("fetch") || message.toLowerCase().includes("network")) {
    return `Unable to connect to the server. Please check your internet connection and try again.`;
  }

  // Server errors (5xx)
  if (status && status >= 500) {
    return `The server is experiencing issues. Please try again in a few moments.`;
  }

  // Client errors (4xx)
  if (status && status >= 400 && status < 500) {
    switch (status) {
      case 400:
        return `Invalid request. Please check your input and try again.`;
      case 401:
        return `You are not authorized to perform this action. Please log in and try again.`;
      case 403:
        return `You don't have permission to perform this action.`;
      case 404:
        return `The requested ${context} was not found.`;
      case 409:
        return `This action conflicts with existing data. Please check for duplicates.`;
      case 422:
        return `The provided data is invalid. Please check your input.`;
      default:
        return message || `Failed to ${context}`;
    }
  }

  // Context-specific messages
  switch (context.toLowerCase()) {
    case "create tenant":
    case "creating tenant":
      if (message.toLowerCase().includes("email")) {
        return "A tenant with this email address already exists. Please use a different email.";
      }
      return "Failed to create tenant. Please check your input and try again.";

    case "update tenant":
    case "updating tenant":
      return "Failed to update tenant information. Please try again.";

    case "delete tenant":
    case "deleting tenant":
      return "Failed to delete tenant. The tenant may have active dependencies.";

    case "load tenant":
    case "loading tenant":
      return "Failed to load tenant information. The tenant may not exist.";

    case "load tenants":
    case "loading tenants":
      return "Failed to load tenant list. Please refresh the page and try again.";

    case "load properties":
    case "loading properties":
      return "Failed to load property information. Some features may not work correctly.";

    default:
      return message || `Failed to ${context}`;
  }
}

/**
 * Determines if an error is recoverable (user can retry)
 */
export function isRecoverableError(error: unknown): boolean {
  const normalizedError = normalizeError(error);
  const { message, status } = normalizedError;

  // Network errors are usually recoverable
  if (message.toLowerCase().includes("fetch") || message.toLowerCase().includes("network")) {
    return true;
  }

  // Server errors are usually recoverable
  if (status && status >= 500) {
    return true;
  }

  // Some client errors are recoverable
  if (status && status >= 400 && status < 500) {
    switch (status) {
      case 408: // Request Timeout
      case 429: // Too Many Requests
        return true;
      default:
        return false;
    }
  }

  return true; // Default to recoverable
}

/**
 * Extracts validation errors from API response
 */
export function extractValidationErrors(error: unknown): Record<string, string> {
  const normalizedError = normalizeError(error);
  
  if (normalizedError.details && Array.isArray(normalizedError.details)) {
    const fieldErrors: Record<string, string> = {};
    
    normalizedError.details.forEach((detail: any) => {
      if (detail.path && detail.path.length > 0) {
        fieldErrors[detail.path[0]] = detail.message;
      }
    });
    
    return fieldErrors;
  }

  return {};
}

/**
 * Logs errors with context for debugging
 */
export function logError(error: unknown, context: string, additionalInfo?: any) {
  const normalizedError = normalizeError(error);
  
  console.error(`[${context}] Error:`, {
    message: normalizedError.message,
    status: normalizedError.status,
    code: normalizedError.code,
    details: normalizedError.details,
    additionalInfo,
    timestamp: new Date().toISOString(),
  });
}

/**
 * Creates a retry function with exponential backoff
 */
export function createRetryFunction(
  fn: () => Promise<any>,
  maxRetries: number = 3,
  baseDelay: number = 1000
) {
  return async function retryWithBackoff(): Promise<any> {
    let lastError: unknown;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          throw error;
        }

        // Don't retry non-recoverable errors
        if (!isRecoverableError(error)) {
          throw error;
        }

        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  };
}
