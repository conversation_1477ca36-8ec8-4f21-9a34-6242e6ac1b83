import { NextRequest, NextResponse } from "next/server";
import { verifyToken } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";
import { db } from "@/lib/db";
import { Decimal } from "@prisma/client/runtime/library";

// GET - Fetch all payment records (transactions) for a subscription payment
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string; paymentId: string }> }
) {
  console.log("Payment records route GET called");
  
  try {
    // Await the params promise first
    const params = await context.params;
    console.log("Route params:", params);
    
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: "Invalid or expired token" },
        { status: 401 }
      );
    }

    // Check if user has READ permission for owners-associations
    const canRead = await hasPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return NextResponse.json(
        { success: false, error: "You don't have permission to view payment records" },
        { status: 403 }
      );
    }

    const associationId = parseInt(params.id);
    const paymentId = parseInt(params.paymentId);

    if (isNaN(associationId) || isNaN(paymentId)) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters" },
        { status: 400 }
      );
    }

    // Verify the payment belongs to this association
    const payment = await db.subscriptionPayment.findFirst({
      where: {
        id: paymentId,
        subscription: {
          association_id: associationId
        }
      },
      include: {
        member: true,
        subscription: true
      }
    });

    if (!payment) {
      return NextResponse.json(
        { success: false, error: "Payment not found" },
        { status: 404 }
      );
    }

    // For now, return an empty array until we properly set up the transaction relations
    const records: any[] = [];

    // If there's a linked transaction, include it
    if (payment.transaction_id) {
      try {
        const transaction = await db.associationTransaction.findUnique({
          where: { id: payment.transaction_id }
        });
        
        if (transaction) {
          records.push({
            id: transaction.id,
            payment_id: paymentId,
            amount: transaction.amount,
            payment_date: transaction.transaction_date,
            payment_method: transaction.payment_method || 'CASH',
            reference_number: transaction.reference_number,
            notes: transaction.description,
            created_by: transaction.created_by,
            created_at: transaction.created_at,
            updated_at: transaction.updated_at
          });
        }
      } catch (error) {
        console.error("Error fetching linked transaction:", error);
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        records: records
      }
    });
  } catch (error) {
    console.error("Error fetching payment records:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch payment records" },
      { status: 500 }
    );
  }
}

// POST - Create a new payment record (transaction)
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string; paymentId: string }> }
) {
  try {
    const params = await context.params;
    
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: "Invalid or expired token" },
        { status: 401 }
      );
    }

    // Check if user has CREATE permission for owners-associations
    const canCreate = await hasPermission(decoded.id, "owners-associations", "CREATE");
    if (!canCreate) {
      return NextResponse.json(
        { success: false, error: "You don't have permission to create payment records" },
        { status: 403 }
      );
    }

    const associationId = parseInt(params.id);
    const paymentId = parseInt(params.paymentId);

    if (isNaN(associationId) || isNaN(paymentId)) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { amount, payment_date, payment_method, reference_number, notes } = body;

    // Validate required fields
    if (!amount || !payment_date || !payment_method) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Verify the payment belongs to this association and get current amounts
    const payment = await db.subscriptionPayment.findFirst({
      where: {
        id: paymentId,
        subscription: {
          association_id: associationId
        }
      },
      include: {
        subscription: true,
        member: true
      }
    });

    if (!payment) {
      return NextResponse.json(
        { success: false, error: "Payment not found" },
        { status: 404 }
      );
    }

    // Get the current amount paid (if any)
    const currentAmountPaid = payment.amount_paid || new Decimal(0);
    
    // Calculate amount due
    const amountDue = payment.amount_due || payment.amount;
    const remainingAmount = new Decimal(amountDue).sub(currentAmountPaid);

    // Validate payment amount doesn't exceed remaining
    if (new Decimal(amount).gt(remainingAmount)) {
      return NextResponse.json(
        { success: false, error: `Payment amount exceeds remaining balance of ${remainingAmount.toFixed(3)}` },
        { status: 400 }
      );
    }

    // Create the payment transaction
    const transaction = await db.associationTransaction.create({
      data: {
        association_id: associationId,
        type: 'INCOME',
        category: 'SUBSCRIPTION_PAYMENT',
        amount: new Decimal(amount),
        transaction_date: new Date(payment_date),
        description: notes || `${payment.subscription.name_en} - ${payment.member.full_name}`,
        payment_method,
        reference_number,
        member_id: payment.member_id,
        created_by: decoded.id
      }
    });

    // Update the subscription payment's amount_paid and status
    const newTotalPaid = currentAmountPaid.add(new Decimal(amount));
    const newStatus = newTotalPaid.gte(amountDue) 
      ? 'PAID' 
      : newTotalPaid.gt(0) 
        ? 'PARTIALLY_PAID' 
        : 'UNPAID';

    await db.subscriptionPayment.update({
      where: { id: paymentId },
      data: {
        amount_paid: newTotalPaid,
        status: newStatus,
        payment_method: payment_method,
        transaction_id: transaction.id
      }
    });

    // Transform to payment record format for response
    const newRecord = {
      id: transaction.id,
      payment_id: paymentId,
      amount: transaction.amount,
      payment_date: transaction.transaction_date,
      payment_method,
      reference_number,
      notes: transaction.description,
      created_by: decoded.id,
      created_at: transaction.created_at,
      updated_at: transaction.updated_at
    };

    return NextResponse.json({
      success: true,
      data: newRecord
    });
  } catch (error) {
    console.error("Error creating payment record:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create payment record" },
      { status: 500 }
    );
  }
}