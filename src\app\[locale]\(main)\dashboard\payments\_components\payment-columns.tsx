"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Eye, Edit, Trash, FileText } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useState } from "react";
import { PaymentWithRelations } from "@/types/payment";
import { formatOMR } from "@/lib/format";

export function getPaymentColumns(
  translations: any,
  locale: string,
  onRefresh?: () => void
): ColumnDef<PaymentWithRelations>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label={translations.selectAll}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={translations.selectRow}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "paymentNumber",
      accessorKey: "payment_number",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.paymentNumber} />
      ),
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {row.original.payment_number}
          </div>
        );
      },
    },
    {
      id: "tenant",
      accessorKey: "tenant",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.tenant} />
      ),
      cell: ({ row }) => {
        const tenant = row.original.tenant;
        if (!tenant) return "-";
        const name = `${tenant.first_name} ${tenant.last_name}`;
        return <div>{name}</div>;
      },
    },
    {
      id: "paymentDate",
      accessorKey: "payment_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.paymentDate} />
      ),
      cell: ({ row }) => {
        return (
          <div className="text-sm">
            {format(new Date(row.original.payment_date), "dd/MM/yyyy")}
          </div>
        );
      },
    },
    {
      id: "amount",
      accessorKey: "amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.amount} />
      ),
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {formatOMR(row.original.amount.toString())}
          </div>
        );
      },
    },
    {
      id: "method",
      accessorKey: "payment_method",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.method} />
      ),
      cell: ({ row }) => {
        const method = row.original.payment_method;
        
        const methodConfig = {
          CASH: { label: translations.methodCash, variant: "default" as const },
          BANK_TRANSFER: { label: translations.methodBankTransfer, variant: "secondary" as const },
          CHECK: { label: translations.methodCheck, variant: "outline" as const },
          CREDIT_CARD: { label: translations.methodCard, variant: "default" as const },
          DEBIT_CARD: { label: translations.methodDebitCard || "Debit Card", variant: "default" as const },
          OTHER: { label: translations.methodOther || "Other", variant: "outline" as const },
        };

        const config = methodConfig[method] || { label: method, variant: "outline" as const };

        return (
          <Badge variant={config.variant}>
            {config.label}
          </Badge>
        );
      },
    },
    {
      id: "status",
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.status} />
      ),
      cell: ({ row }) => {
        const status = row.original.status;
        
        const statusConfig = {
          PENDING: { label: translations.statusPending, variant: "secondary" as const },
          COMPLETED: { label: translations.statusCompleted, variant: "default" as const },
          FAILED: { label: translations.statusFailed, variant: "destructive" as const },
          REFUNDED: { label: translations.statusRefunded, variant: "secondary" as const },
          CANCELLED: { label: translations.statusCancelled || "Cancelled", variant: "outline" as const },
        };

        const config = statusConfig[status];

        return (
          <Badge variant={config.variant}>
            {config.label}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "reference",
      accessorKey: "reference_number",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.reference} />
      ),
      cell: ({ row }) => {
        const reference = row.original.reference_number;
        return reference ? (
          <div className="text-sm font-mono">{reference}</div>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      id: "installment",
      accessorKey: "installment",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.installment} />
      ),
      cell: ({ row }) => {
        const installment = row.original.installment;
        return installment ? (
          <Badge variant="outline" className="font-mono">
            #{installment}
          </Badge>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className="text-center">{translations.actions}</div>,
      cell: ({ row }) => {
        const payment = row.original;
        const [showDeleteDialog, setShowDeleteDialog] = useState(false);
        const [isDeleting, setIsDeleting] = useState(false);

        const handleDelete = async () => {
          try {
            setIsDeleting(true);
            const response = await fetch(`/api/payments/${payment.id}`, {
              method: "DELETE",
            });

            if (!response.ok) {
              const error = await response.json();
              throw new Error(error.error?.message || "Failed to delete payment");
            }

            toast.success("Payment deleted successfully");
            setShowDeleteDialog(false);
            if (onRefresh) {
              onRefresh();
            }
          } catch (error) {
            console.error("Delete error:", error);
            toast.error(error instanceof Error ? error.message : "Failed to delete payment");
          } finally {
            setIsDeleting(false);
          }
        };

        return (
          <div className="text-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  aria-label={translations.openMenu}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>{translations.actions}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href={`/dashboard/payments/${payment.id}`}>
                    <Eye className="mr-2 h-4 w-4" />
                    {translations.viewDetails}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem 
                  asChild
                  disabled={payment.status === "COMPLETED" || payment.status === "REFUNDED"}
                >
                  <Link href={`/dashboard/payments/${payment.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    {translations.editPayment}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem 
                  asChild
                  disabled={payment.status !== "COMPLETED"}
                >
                  <Link href={`/dashboard/payments/${payment.id}/receipt`}>
                    <FileText className="mr-2 h-4 w-4" />
                    {translations.generateReceipt}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-destructive"
                  disabled={payment.status === "COMPLETED"}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {translations.deletePayment}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete payment
                    "{payment.payment_number}".
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {isDeleting ? "Deleting..." : "Delete"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        );
      },
    },
  ];
}