"use client";

import { useState, useEffect } from "react";
import { useTranslations, useLocale } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  CalendarIcon,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  CreditCard,
  FileText,
  DollarSign,
  User,
  Building,
  Receipt,
  Clock,
  CheckCircle,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useRTL } from "@/hooks/use-rtl";
import { apiClient } from "@/lib/api-client";
import { formatCurrency } from "@/lib/utils";
import type { SubscriptionPaymentWithRelations } from "@/types/owners-association";

interface PaymentRecord {
  id: number;
  payment_id: number;
  amount: number;
  payment_date: string;
  payment_method: string;
  reference_number?: string;
  notes?: string;
  created_at: string;
  created_by?: number;
}

const paymentRecordSchema = z.object({
  amount: z.string().min(1, "Amount is required").refine(
    (val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0,
    "Amount must be greater than 0"
  ),
  payment_date: z.date({
    required_error: "Payment date is required",
  }),
  payment_method: z.string().min(1, "Payment method is required"),
  reference_number: z.string().optional(),
  notes: z.string().optional(),
});

type PaymentRecordFormData = z.infer<typeof paymentRecordSchema>;

interface PaymentRecordsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  payment: SubscriptionPaymentWithRelations | null;
  associationId: number;
  onSuccess?: () => void;
}

export function PaymentRecordsModal({
  open,
  onOpenChange,
  payment,
  associationId,
  onSuccess,
}: PaymentRecordsModalProps) {
  const locale = useLocale();
  const { isRTL } = useRTL();
  const t = useTranslations("ownersAssociations");
  const tCommon = useTranslations("common");
  const dateLocale = locale === 'ar' ? ar : enUS;

  const [records, setRecords] = useState<PaymentRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingRecord, setEditingRecord] = useState<PaymentRecord | null>(null);
  const [deletingRecord, setDeletingRecord] = useState<PaymentRecord | null>(null);
  const [activeTab, setActiveTab] = useState("list");

  const form = useForm<PaymentRecordFormData>({
    resolver: zodResolver(paymentRecordSchema),
    defaultValues: {
      amount: "",
      payment_date: new Date(),
      payment_method: "BANK_TRANSFER",
      reference_number: "",
      notes: "",
    },
  });

  useEffect(() => {
    if (open && payment) {
      fetchPaymentRecords();
      setActiveTab("list");
      form.reset({
        amount: "",
        payment_date: new Date(),
        payment_method: "BANK_TRANSFER",
        reference_number: "",
        notes: "",
      });
    }
  }, [open, payment]);

  const fetchPaymentRecords = async () => {
    if (!payment) return;

    try {
      setLoading(true);
      const url = `/api/owners-associations/${associationId}/subscription-payments/${payment.id}/records`;
      console.log("Fetching from URL:", url);
      console.log("Full URL would be:", window.location.origin + url);
      
      const response = await apiClient.get(url);

      if (response.success) {
        setRecords(response.data.records || []);
      }
    } catch (error) {
      console.error("Error fetching payment records:", error);
      toast.error(t("paymentRecords.fetchError"));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (data: PaymentRecordFormData) => {
    if (!payment) return;

    try {
      setLoading(true);
      const endpoint = editingRecord
        ? `/api/owners-associations/${associationId}/subscription-payments/${payment.id}/records/${editingRecord.id}`
        : `/api/owners-associations/${associationId}/subscription-payments/${payment.id}/records`;

      const method = editingRecord ? "PUT" : "POST";

      const response = await apiClient[method.toLowerCase() as 'post' | 'put'](endpoint, {
        amount: parseFloat(data.amount),
        payment_date: data.payment_date.toISOString(),
        payment_method: data.payment_method,
        reference_number: data.reference_number || undefined,
        notes: data.notes || undefined,
      });

      if (response.success) {
        toast.success(editingRecord ? t("paymentRecords.updateSuccess") : t("paymentRecords.createSuccess"));
        fetchPaymentRecords();
        form.reset();
        setEditingRecord(null);
        setActiveTab("list");
        onSuccess?.();
      }
    } catch (error: any) {
      console.error("Error saving payment record:", error);
      toast.error(error.response?.data?.error || t("paymentRecords.saveError"));
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (record: PaymentRecord) => {
    setEditingRecord(record);
    form.reset({
      amount: record.amount.toString(),
      payment_date: new Date(record.payment_date),
      payment_method: record.payment_method,
      reference_number: record.reference_number || "",
      notes: record.notes || "",
    });
    setActiveTab("form");
  };

  const handleDelete = async () => {
    if (!payment || !deletingRecord) return;

    try {
      setLoading(true);
      const response = await apiClient.delete(
        `/api/owners-associations/${associationId}/subscription-payments/${payment.id}/records/${deletingRecord.id}`
      );

      if (response.success) {
        toast.success(t("paymentRecords.deleteSuccess"));
        fetchPaymentRecords();
        onSuccess?.();
      }
    } catch (error) {
      console.error("Error deleting payment record:", error);
      toast.error(t("paymentRecords.deleteError"));
    } finally {
      setLoading(false);
      setDeletingRecord(null);
    }
  };

  const cancelEdit = () => {
    setEditingRecord(null);
    form.reset({
      amount: "",
      payment_date: new Date(),
      payment_method: "BANK_TRANSFER",
      reference_number: "",
      notes: "",
    });
    setActiveTab("list");
  };

  if (!payment) return null;

  const amountDue = parseFloat(payment.amount_due?.toString() || payment.amount.toString());
  const totalPaid = records.reduce((sum, record) => sum + parseFloat(record.amount.toString()), 0);
  const remainingAmount = amountDue - totalPaid;
  const progressPercentage = amountDue > 0 ? Math.min((totalPaid / amountDue) * 100, 100) : 0;

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
              <Receipt className="h-5 w-5" />
              {t("paymentRecords.title")}
            </DialogTitle>
            <DialogDescription>
              {t("paymentRecords.description")}
            </DialogDescription>
          </DialogHeader>

          {/* Payment Summary Card */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">{t("paymentRecords.paymentSummary")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className={cn("space-y-1", isRTL && "text-right")}>
                  <div className="text-sm text-muted-foreground">{t("paymentRecords.member")}</div>
                  <div className="font-medium">{payment.member.full_name}</div>
                  <div className="text-xs text-muted-foreground">
                    {t("paymentRecords.unit")}: {payment.member.unit_number}
                  </div>
                </div>
                <div className={cn("space-y-1", isRTL && "text-right")}>
                  <div className="text-sm text-muted-foreground">{t("paymentRecords.subscription")}</div>
                  <div className="font-medium">
                    {locale === 'ar' ? payment.subscription.name_ar : payment.subscription.name_en}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {t("paymentRecords.dueDate")}: {format(new Date(payment.due_date), "dd/MM/yyyy", { locale: dateLocale })}
                  </div>
                </div>
                <div className={cn("space-y-1", isRTL && "text-right")}>
                  <div className="text-sm text-muted-foreground">{t("paymentRecords.paymentProgress")}</div>
                  <Progress value={progressPercentage} className="h-2 mb-1" />
                  <div className="text-xs space-y-1">
                    <div>{t("paymentRecords.paid")}: {formatCurrency(totalPaid)}</div>
                    <div>{t("paymentRecords.remaining")}: {formatCurrency(remainingAmount)}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="list">{t("paymentRecords.recordsList")}</TabsTrigger>
              <TabsTrigger value="form">
                {editingRecord ? t("paymentRecords.editRecord") : t("paymentRecords.addRecord")}
              </TabsTrigger>
            </TabsList>

            {/* Records List Tab */}
            <TabsContent value="list" className="space-y-4">
              {loading ? (
                <div className="space-y-2">
                  {[...Array(3)].map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                  ))}
                </div>
              ) : records.length === 0 ? (
                <Card>
                  <CardContent className="py-8">
                    <div className="text-center space-y-3">
                      <CreditCard className="h-12 w-12 text-muted-foreground mx-auto" />
                      <p className="text-muted-foreground">{t("paymentRecords.noRecords")}</p>
                      <Button
                        size="sm"
                        onClick={() => setActiveTab("form")}
                        className={cn("gap-2", isRTL && "flex-row-reverse")}
                      >
                        <Plus className="h-4 w-4" />
                        {t("paymentRecords.addFirstRecord")}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className={cn(isRTL && "text-right")}>{t("paymentRecords.date")}</TableHead>
                        <TableHead className={cn(isRTL && "text-right")}>{t("paymentRecords.amount")}</TableHead>
                        <TableHead className={cn(isRTL && "text-right")}>{t("paymentRecords.method")}</TableHead>
                        <TableHead className={cn(isRTL && "text-right")}>{t("paymentRecords.reference")}</TableHead>
                        <TableHead className={cn(isRTL && "text-right")}>{t("paymentRecords.notes")}</TableHead>
                        <TableHead className={cn("text-right", isRTL && "text-left")}>
                          {tCommon("actions")}
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {records.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell className={cn(isRTL && "text-right")}>
                            {format(new Date(record.payment_date), "dd/MM/yyyy", { locale: dateLocale })}
                          </TableCell>
                          <TableCell className={cn("font-medium", isRTL && "text-right")}>
                            {formatCurrency(record.amount)}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {t(`paymentRecords.paymentMethod.${record.payment_method.toLowerCase()}`)}
                            </Badge>
                          </TableCell>
                          <TableCell className={cn(isRTL && "text-right")}>
                            {record.reference_number || "-"}
                          </TableCell>
                          <TableCell className={cn(isRTL && "text-right")}>
                            {record.notes || "-"}
                          </TableCell>
                          <TableCell>
                            <div className={cn("flex items-center gap-2 justify-end", isRTL && "flex-row-reverse justify-start")}>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEdit(record)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setDeletingRecord(record)}
                                className="text-destructive hover:text-destructive"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}

              {records.length > 0 && remainingAmount > 0 && (
                <div className="flex justify-end">
                  <Button
                    onClick={() => setActiveTab("form")}
                    className={cn("gap-2", isRTL && "flex-row-reverse")}
                  >
                    <Plus className="h-4 w-4" />
                    {t("paymentRecords.addRecord")}
                  </Button>
                </div>
              )}
            </TabsContent>

            {/* Add/Edit Form Tab */}
            <TabsContent value="form" className="space-y-4">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="amount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("paymentRecords.amount")}</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <DollarSign className={cn(
                                "absolute top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground",
                                isRTL ? "right-3" : "left-3"
                              )} />
                              <Input
                                {...field}
                                type="number"
                                step="0.001"
                                placeholder="0.000"
                                className={cn(isRTL ? "pr-9" : "pl-9")}
                                max={remainingAmount + (editingRecord?.amount || 0)}
                              />
                            </div>
                          </FormControl>
                          {remainingAmount > 0 && !editingRecord && (
                            <p className="text-xs text-muted-foreground">
                              {t("paymentRecords.maxAmount")}: {formatCurrency(remainingAmount)}
                            </p>
                          )}
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="payment_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("paymentRecords.paymentDate")}</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full justify-start text-left font-normal",
                                    !field.value && "text-muted-foreground",
                                    isRTL && "flex-row-reverse"
                                  )}
                                >
                                  <CalendarIcon className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                                  {field.value ? (
                                    format(field.value, "PPP", { locale: dateLocale })
                                  ) : (
                                    <span>{t("paymentRecords.selectDate")}</span>
                                  )}
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => date > new Date()}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="payment_method"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("paymentRecords.method")}</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder={t("paymentRecords.selectMethod")} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="CASH">{t("paymentRecords.paymentMethod.cash")}</SelectItem>
                              <SelectItem value="BANK_TRANSFER">{t("paymentRecords.paymentMethod.bank_transfer")}</SelectItem>
                              <SelectItem value="CHECK">{t("paymentRecords.paymentMethod.check")}</SelectItem>
                              <SelectItem value="CREDIT_CARD">{t("paymentRecords.paymentMethod.credit_card")}</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="reference_number"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("paymentRecords.referenceNumber")}</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder={t("paymentRecords.referenceNumberPlaceholder")} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("paymentRecords.notes")}</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder={t("paymentRecords.notesPlaceholder")}
                            rows={3}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                    <Button
                      type="submit"
                      disabled={loading}
                      className={cn("gap-2", isRTL && "flex-row-reverse")}
                    >
                      <Save className="h-4 w-4" />
                      {editingRecord ? t("paymentRecords.updateRecord") : t("paymentRecords.saveRecord")}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={cancelEdit}
                      className={cn("gap-2", isRTL && "flex-row-reverse")}
                    >
                      <X className="h-4 w-4" />
                      {tCommon("cancel")}
                    </Button>
                  </div>
                </form>
              </Form>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingRecord} onOpenChange={() => setDeletingRecord(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("paymentRecords.deleteDialog.title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("paymentRecords.deleteDialog.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{tCommon("cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              {tCommon("delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}