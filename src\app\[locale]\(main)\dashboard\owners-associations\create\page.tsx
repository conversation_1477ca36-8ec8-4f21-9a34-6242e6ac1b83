import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { getTranslations } from 'next-intl/server';

import { Button } from "@/components/ui/button";
import { ErrorBoundary } from "@/components/error-boundary";
import { OwnersAssociationForm } from "../_components/owners-association-form";

export default async function CreateOwnersAssociationPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'ownersAssociations' });
  
  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild>
          <Link href={`/${locale}/dashboard/owners-associations`}>
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">{t('backToAssociations')}</span>
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('createAssociation')}</h1>
          <p className="text-muted-foreground">
            {t('createDescription')}
          </p>
        </div>
      </div>

      <ErrorBoundary>
        <OwnersAssociationForm />
      </ErrorBoundary>
    </div>
  );
}