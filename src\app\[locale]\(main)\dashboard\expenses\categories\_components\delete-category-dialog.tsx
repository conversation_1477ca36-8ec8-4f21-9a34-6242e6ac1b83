"use client";

import { useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import type { ExpenseCategory } from "@/types/expense";

interface DeleteCategoryDialogProps {
  category: ExpenseCategory & { _count?: { expenses: number } };
  children: React.ReactNode;
  onSuccess?: () => void;
}

export function DeleteCategoryDialog({ category, children, onSuccess }: DeleteCategoryDialogProps) {
  const t = useTranslations("expenses.categories");
  const tMessages = useTranslations("expenses.messages");
  const tCommon = useTranslations("common");
  const router = useRouter();

  const [isDeleting, setIsDeleting] = useState(false);
  const [open, setOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const hasExpenses = (category._count?.expenses ?? 0) > 0;

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      setError(null);

      const response = await fetch(`/api/expense-categories/${category.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        
        if (response.status === 409) {
          throw new Error(t("cannotDeleteWithExpenses"));
        }
        
        if (response.status === 404) {
          throw new Error(t("categoryNotFound"));
        }
        
        throw new Error(errorData.error || t("deleteError"));
      }

      toast.success(tMessages("categoryDeleteSuccess"));
      setOpen(false);
      
      if (onSuccess) {
        onSuccess();
      } else {
        router.refresh();
      }

    } catch (error) {
      console.error("Error deleting category:", error);
      const errorMessage = error instanceof Error ? error.message : t("deleteError");
      setError(errorMessage);
      
      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        {children}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            {t("deleteCategory")}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {t("deleteConfirmation")} <strong>{category.name_en}</strong>?
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-4">
          {/* Warning about expenses */}
          {hasExpenses && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {t("deleteWarningWithExpenses", { count: category._count?.expenses ?? 0 })}
              </AlertDescription>
            </Alert>
          )}

          {/* General warning */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {t("deleteWarning")}
            </AlertDescription>
          </Alert>

          {/* Error display */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>
            {tCommon("cancel")}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting || hasExpenses}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                {t("deleting")}
              </div>
            ) : (
              t("confirmDelete")
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
