<!DOCTYPE html>
<html>
<head>
    <title>Debug Client-Side Authentication</title>
</head>
<body>
    <h1>Authentication Debug Tool</h1>
    <div id="status">Testing...</div>
    <div id="results"></div>
    
    <script>
        async function debugAuth() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            try {
                statusDiv.textContent = 'Step 1: Testing /api/auth/me endpoint...';
                
                // Test /api/auth/me endpoint
                const meResponse = await fetch('/api/auth/me', {
                    credentials: 'include'
                });
                
                resultsDiv.innerHTML += `<p><strong>Me endpoint status:</strong> ${meResponse.status}</p>`;
                
                if (meResponse.status === 200) {
                    const meData = await meResponse.json();
                    resultsDiv.innerHTML += `<p><strong>User:</strong> ${meData.user.username}</p>`;
                    resultsDiv.innerHTML += `<p><strong>Permissions modules:</strong> ${Object.keys(meData.user.permissions).length}</p>`;
                    resultsDiv.innerHTML += `<p><strong>Properties permission:</strong> ${!!meData.user.permissions.properties?.read}</p>`;
                    
                    statusDiv.textContent = 'Step 2: Testing dashboard navigation...';
                    
                    // Test if we can navigate to dashboard
                    const dashboardResponse = await fetch('/en/dashboard/properties', {
                        credentials: 'include'
                    });
                    
                    resultsDiv.innerHTML += `<p><strong>Dashboard status:</strong> ${dashboardResponse.status}</p>`;
                    
                    if (dashboardResponse.status === 200) {
                        statusDiv.textContent = 'Authentication is working correctly!';
                        resultsDiv.innerHTML += `<p style="color: green;"><strong>✓ All tests passed!</strong></p>`;
                        resultsDiv.innerHTML += `<p><a href="/en/dashboard/properties">Try accessing dashboard</a></p>`;
                    } else {
                        statusDiv.textContent = 'Dashboard access failed';
                        resultsDiv.innerHTML += `<p style="color: red;"><strong>✗ Dashboard access failed</strong></p>`;
                    }
                } else if (meResponse.status === 401) {
                    statusDiv.textContent = 'Not authenticated. Trying to login...';
                    
                    // Try to login
                    const loginResponse = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            username: '<EMAIL>',
                            password: '123456'
                        }),
                        credentials: 'include'
                    });
                    
                    if (loginResponse.status === 200) {
                        resultsDiv.innerHTML += `<p style="color: green;"><strong>✓ Login successful!</strong></p>`;
                        statusDiv.textContent = 'Login successful! Refreshing page...';
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        const loginData = await loginResponse.json();
                        resultsDiv.innerHTML += `<p style="color: red;"><strong>✗ Login failed:</strong> ${loginData.error}</p>`;
                    }
                } else {
                    statusDiv.textContent = 'Unexpected error';
                    resultsDiv.innerHTML += `<p style="color: red;"><strong>✗ Unexpected error:</strong> ${meResponse.status}</p>`;
                }
                
            } catch (error) {
                statusDiv.textContent = 'Error occurred';
                resultsDiv.innerHTML += `<p style="color: red;"><strong>Error:</strong> ${error.message}</p>`;
            }
        }
        
        // Run the debug test
        debugAuth();
    </script>
</body>
</html>