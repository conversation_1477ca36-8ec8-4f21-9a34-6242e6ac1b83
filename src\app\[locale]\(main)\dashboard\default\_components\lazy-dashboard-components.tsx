"use client";

import { Suspense, lazy } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

// Lazy load the heavy components
const RevenueChart = lazy(() => import("./revenue-chart").then(mod => ({ default: mod.RevenueChart })));
const PropertyOverview = lazy(() => import("./property-overview").then(mod => ({ default: mod.PropertyOverview })));
const RecentActivities = lazy(() => import("./recent-activities").then(mod => ({ default: mod.RecentActivities })));
const UpcomingPayments = lazy(() => import("./upcoming-payments").then(mod => ({ default: mod.UpcomingPayments })));
const MaintenanceOverview = lazy(() => import("./maintenance-overview").then(mod => ({ default: mod.MaintenanceOverview })));

// Loading skeleton components
function ChartSkeleton() {
  return (
    <Card className="lg:col-span-4">
      <CardHeader>
        <CardTitle>
          <Skeleton className="h-6 w-32" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Skeleton className="h-64 w-full" />
      </CardContent>
    </Card>
  );
}

function OverviewSkeleton() {
  return (
    <Card className="lg:col-span-3">
      <CardHeader>
        <CardTitle>
          <Skeleton className="h-6 w-40" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="flex items-center justify-between">
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-24" />
              </div>
              <Skeleton className="h-8 w-16" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function ActivitySkeleton() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Skeleton className="h-6 w-32" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-4">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-3 w-20" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export function LazyDashboardComponents() {
  return (
    <>
      {/* Charts and Overview */}
      <div className="grid gap-4 md:gap-6 grid-cols-1 lg:grid-cols-7">
        <Suspense fallback={<ChartSkeleton />}>
          <RevenueChart />
        </Suspense>
        <Suspense fallback={<OverviewSkeleton />}>
          <PropertyOverview />
        </Suspense>
      </div>
      
      {/* Activity and Payments */}
      <div className="grid gap-4 md:gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        <Suspense fallback={<ActivitySkeleton />}>
          <RecentActivities />
        </Suspense>
        <Suspense fallback={<ActivitySkeleton />}>
          <UpcomingPayments />
        </Suspense>
        <Suspense fallback={<ActivitySkeleton />}>
          <MaintenanceOverview />
        </Suspense>
      </div>
    </>
  );
}
