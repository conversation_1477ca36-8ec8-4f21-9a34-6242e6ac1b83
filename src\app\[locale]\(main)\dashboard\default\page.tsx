import { setRequestLocale } from 'next-intl/server';
import { PropertyStatsCards } from "./_components/property-stats-cards";
import { LazyDashboardComponents } from "./_components/lazy-dashboard-components";
import { routing } from '@/i18n/routing';

interface PageProps {
  params: Promise<{ locale: string }>;
}

// Generate static params for all supported locales
export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

export default async function Page({ params }: PageProps) {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      {/* Key Metrics - Load immediately */}
      <PropertyStatsCards />

      {/* Other components - Load lazily */}
      <LazyDashboardComponents />
    </div>
  );
}