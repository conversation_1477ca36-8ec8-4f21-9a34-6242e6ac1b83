"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { OwnerPayoutWithRelations } from "@/types/owner-payout";
import { formatOMR } from "@/lib/format";

interface ApprovePayoutDialogProps {
  payout: OwnerPayoutWithRelations;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ApprovePayoutDialog({
  payout,
  open,
  onOpenChange,
}: ApprovePayoutDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [action, setAction] = useState<"APPROVE" | "REJECT" | null>(null);
  const [notes, setNotes] = useState("");
  const router = useRouter();
  const t = useTranslations();

  const handleSubmit = async () => {
    if (!action) return;

    try {
      setIsLoading(true);

      const response = await fetch(`/api/owner-payouts/${payout.id}/approve`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action,
          notes: notes.trim() || undefined,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(action === "APPROVE" 
          ? t("ownerPayouts.approveSuccess")
          : t("ownerPayouts.rejectSuccess")
        );

        router.refresh();
        onOpenChange(false);
        setNotes("");
        setAction(null);
      } else {
        toast.error(result.message || t("common.unexpectedError"));
      }
    } catch (error) {
      console.error("Error processing payout approval:", error);
      toast.error(t("common.unexpectedError"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t("ownerPayouts.approvalTitle")}</DialogTitle>
          <DialogDescription>
            {t("ownerPayouts.approvalDescription", {
              payoutNumber: payout.payout_number,
              amount: formatOMR(payout.net_amount.toString()),
            })}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label>{t("ownerPayouts.action")}</Label>
            <div className="flex gap-2">
              <Button
                type="button"
                variant={action === "APPROVE" ? "default" : "outline"}
                onClick={() => setAction("APPROVE")}
                disabled={isLoading}
                className="flex-1"
              >
                {t("ownerPayouts.approve")}
              </Button>
              <Button
                type="button"
                variant={action === "REJECT" ? "destructive" : "outline"}
                onClick={() => setAction("REJECT")}
                disabled={isLoading}
                className="flex-1"
              >
                {t("ownerPayouts.reject")}
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">{t("ownerPayouts.approvalNotes")}</Label>
            <Textarea
              id="notes"
              placeholder={t("ownerPayouts.approvalNotesPlaceholder")}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              disabled={isLoading}
              rows={4}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            {t("common.cancel")}
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={isLoading || !action}
            variant={action === "REJECT" ? "destructive" : "default"}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {action === "APPROVE" ? t("ownerPayouts.confirmApprove") : t("ownerPayouts.confirmReject")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}