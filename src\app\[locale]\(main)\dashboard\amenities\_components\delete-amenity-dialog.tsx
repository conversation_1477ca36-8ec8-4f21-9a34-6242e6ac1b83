"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AmenityWithRelations } from "@/types/amenity";

interface DeleteAmenityDialogProps {
  amenity: AmenityWithRelations;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DeleteAmenityDialog({
  amenity,
  open,
  onOpenChange,
}: DeleteAmenityDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const t = useTranslations();

  const handleDelete = async () => {
    try {
      setIsLoading(true);

      const response = await fetch(`/api/amenities/${amenity.id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (result.success) {
        toast.success(t("amenities.deleteSuccess"));

        router.refresh();
        onOpenChange(false);
      } else {
        toast.error(result.message || t("common.unexpectedError"));
      }
    } catch (error) {
      console.error("Error deleting amenity:", error);
      toast.error(t("common.unexpectedError"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t("amenities.deleteTitle")}</AlertDialogTitle>
          <AlertDialogDescription>
            {t("amenities.deleteDescription", {
              name: amenity.name_en,
            })}
            {(amenity._count?.property_amenities ?? 0) > 0 && (
              <span className="block mt-2 text-destructive">
                {t("amenities.deleteWarning", {
                  count: amenity._count?.property_amenities ?? 0,
                })}
              </span>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            {t("common.cancel")}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isLoading || (amenity._count?.property_amenities ?? 0) > 0}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t("common.delete")}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}