import { notFound } from "next/navigation";
import Link from "next/link";
import { cookies, headers } from "next/headers";
import { ArrowLeft } from "lucide-react";
import { getTranslations } from "next-intl/server";

import { Button } from "@/components/ui/button";
import { RoleForm } from "../../_components/role-form";
import type { RoleWithPermissions } from "@/types/user";

interface EditRolePageProps {
  params: Promise<{
    locale: string;
    id: string;
  }>;
}

async function getRole(id: number): Promise<RoleWithPermissions | null> {
  try {
    // Get dynamic host for multi-port development
    const headersList = await headers();
    const host = headersList.get('host') || 'localhost:3001';
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}`;
    
    // Get cookies for server-side authentication
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');

    const response = await fetch(`${baseUrl}/api/roles/${id}`, {
      cache: 'no-store',
      headers: {
        'Cookie': authToken ? `auth-token=${authToken.value}` : '',
      },
    });

    if (!response.ok) {
      return null;
    }

    return response.json();
  } catch (error) {
    console.error('Error fetching role:', error);
    return null;
  }
}

export default async function EditRolePage({ params }: EditRolePageProps) {
  const { locale, id } = await params;
  const roleId = parseInt(id);
  const t = await getTranslations('roles');

  if (isNaN(roleId)) {
    notFound();
  }

  const role = await getRole(roleId);

  if (!role) {
    notFound();
  }

  if (role.is_system) {
    return (
      <div className="@container/main flex flex-col gap-4 md:gap-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/${locale}/dashboard/roles/${role.id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t('backToRole')}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('cannotEditSystemRole')}</h1>
            <p className="text-muted-foreground">{t('systemRoleProtected')}</p>
          </div>
        </div>

        <div className="max-w-md mx-auto text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-xl font-semibold mb-2">{t('systemRoleProtectedTitle')}</h2>
          <p className="text-muted-foreground mb-6">
            {t('systemRoleProtectedDesc', { name: role.name })}
          </p>
          <Button asChild>
            <Link href={`/${locale}/dashboard/roles/${role.id}`}>
              {t('viewRoleDetails')}
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${locale}/dashboard/roles/${role.id}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('backToRole')}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('editRole')}</h1>
          <p className="text-muted-foreground">{t('updateRoleInfo')}</p>
        </div>
      </div>

      <RoleForm role={role} mode="edit" />
    </div>
  );
}
