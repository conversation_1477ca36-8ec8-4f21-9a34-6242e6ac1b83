"use client";

import { Suspense } from "react";
import Link from "next/link";
import { Plus } from "lucide-react";
import { useTranslations, useLocale } from 'next-intl';

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ErrorBoundary } from "@/components/error-boundary";
import { ModulePageWrapper } from "@/components/auth/module-page-wrapper";
import { PermissionButton } from "@/components/auth/permission-button";

import { UserDataTable } from "./user-data-table";

export function UsersPageWrapper() {
  const locale = useLocale();
  const t = useTranslations('users');
  const tCommon = useTranslations('common');

  return (
    <ModulePageWrapper module="users">
      <div className="@container/main flex flex-col gap-4 md:gap-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
            <p className="text-muted-foreground">{t('description')}</p>
          </div>
          <PermissionButton
            module="users"
            action="create"
            hideOnNoPermission
            asChild
          >
            <Link href={`/${locale}/dashboard/users/create`}>
              <Plus className="mr-2 h-4 w-4" />
              {t('addUser')}
            </Link>
          </PermissionButton>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{t('allUsers')}</CardTitle>
            <CardDescription>
              {t('viewManage')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ErrorBoundary>
              <Suspense fallback={<div>{tCommon('loading')}</div>}>
                <UserDataTable />
              </Suspense>
            </ErrorBoundary>
          </CardContent>
        </Card>
      </div>
    </ModulePageWrapper>
  );
}