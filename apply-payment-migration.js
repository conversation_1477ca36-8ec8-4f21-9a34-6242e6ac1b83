const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Applying Payment Tracking Migration...\n');

// Step 1: Backup the database first
console.log('📦 Creating database backup...');
try {
  execSync('mysqldump -u root -p property_management > backup_before_payment_migration.sql', { stdio: 'inherit' });
  console.log('✅ Database backed up to backup_before_payment_migration.sql\n');
} catch (error) {
  console.error('❌ Failed to backup database. Please backup manually before proceeding.');
  process.exit(1);
}

// Step 2: Apply the migration
console.log('🔧 Applying migration...');
try {
  execSync('mysql -u root -p property_management < prisma/migrations/full_payment_tracking_migration.sql', { stdio: 'inherit' });
  console.log('✅ Migration applied successfully\n');
} catch (error) {
  console.error('❌ Failed to apply migration:', error.message);
  process.exit(1);
}

// Step 3: Generate Prisma client
console.log('🔄 Regenerating Prisma client...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Prisma client regenerated\n');
} catch (error) {
  console.error('❌ Failed to generate Prisma client:', error.message);
  process.exit(1);
}

// Step 4: Restore the member relation in the API
console.log('📝 Restoring member relation in API...');
const apiFilePath = path.join(__dirname, 'src/app/api/owners-associations/[id]/transactions/route.ts');
try {
  let content = fs.readFileSync(apiFilePath, 'utf8');
  
  // Uncomment the member relation
  content = content.replace(
    /\/\/ TODO: Uncomment after running member_transaction_links migration\n\s*\/\/ member: {[\s\S]*?\/\/ },/,
    `member: {
            select: {
              id: true,
              full_name: true,
              unit_number: true,
              email: true,
              phone: true,
            },
          },`
  );
  
  fs.writeFileSync(apiFilePath, content);
  console.log('✅ API route updated\n');
} catch (error) {
  console.error('❌ Failed to update API route:', error.message);
  console.log('Please manually uncomment the member relation in:', apiFilePath);
}

console.log('🎉 Migration completed successfully!');
console.log('\nNext steps:');
console.log('1. Test the application to ensure everything works');
console.log('2. The transactions table now shows which member made each payment');
console.log('3. You can now track partial payments for subscriptions');
console.log('\nIf you encounter any issues, restore from backup:');
console.log('mysql -u root -p property_management < backup_before_payment_migration.sql');