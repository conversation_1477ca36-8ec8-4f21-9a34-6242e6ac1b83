#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Pattern to find duplicate auth imports
const DUPLICATE_IMPORT_PATTERN = /import\s*{\s*verifyToken\s*}\s*from\s*"@\/lib\/auth";\s*[\s\S]*?import\s*{\s*verifyToken,\s*getUserPermissions,\s*hasPermission\s*}\s*from\s*"@\/lib\/auth";/g;

// Pattern to find any auth import from @/lib/auth
const AUTH_IMPORT_PATTERN = /import\s*{[^}]+}\s*from\s*"@\/lib\/auth";/g;

// Find all API route files
const apiDir = path.join(__dirname, '..', 'src', 'app', 'api');
const routeFiles = glob.sync('**/route.ts', { cwd: apiDir });

console.log(`Found ${routeFiles.length} API route files to check`);

let fixedFiles = 0;
let checkedFiles = 0;

routeFiles.forEach(file => {
  const filePath = path.join(apiDir, file);
  let content = fs.readFileSync(filePath, 'utf8');
  checkedFiles++;

  // Find all auth imports
  const authImports = content.match(AUTH_IMPORT_PATTERN);
  
  if (authImports && authImports.length > 1) {
    console.log(`Found duplicate imports in: ${file}`);
    
    // Extract all imported items
    const importedItems = new Set();
    authImports.forEach(importStatement => {
      const match = importStatement.match(/import\s*{\s*([^}]+)\s*}\s*from/);
      if (match) {
        const items = match[1].split(',').map(item => item.trim());
        items.forEach(item => importedItems.add(item));
      }
    });

    // Remove all auth imports
    content = content.replace(AUTH_IMPORT_PATTERN, '');

    // Find the position after other imports (before the first export or function)
    const firstExportIndex = content.search(/export\s+(async\s+)?function|export\s+async|\/\/ GET|\/\/ POST/);
    
    // Create a single combined import
    const combinedImport = `import { ${Array.from(importedItems).join(', ')} } from "@/lib/auth";\n`;
    
    // Insert the combined import at the right position
    if (firstExportIndex !== -1) {
      // Find the last import statement before the export
      const beforeExport = content.substring(0, firstExportIndex);
      const lastImportMatch = beforeExport.match(/import[^;]+;[\s]*$/);
      
      if (lastImportMatch) {
        const insertPosition = beforeExport.lastIndexOf(lastImportMatch[0]) + lastImportMatch[0].length;
        content = content.substring(0, insertPosition) + '\n' + combinedImport + content.substring(insertPosition);
      } else {
        // If no imports found, add at the beginning
        content = combinedImport + '\n' + content;
      }
    } else {
      // If no export found, add at the beginning
      content = combinedImport + '\n' + content;
    }

    // Write the fixed content
    fs.writeFileSync(filePath, content);
    console.log(`✅ Fixed: ${file}`);
    fixedFiles++;
  }
});

console.log(`\n✅ Fixed ${fixedFiles} files with duplicate imports`);
console.log(`✓ Checked ${checkedFiles} total files`);
console.log(`\nDuplicate import cleanup complete!`);