"use client";

import { useState, useCallback } from "react";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PropertyOwnersDataTable } from "./_components/property-owners-data-table";
import { getPropertyOwnerColumns } from "./_components/property-owner-columns";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";

export default function PropertyOwnersPage() {
  const locale = useLocale();
  const t = useTranslations();
  const [refreshKey, setRefreshKey] = useState(0);

  const handleRefresh = useCallback(() => {
    setRefreshKey(prev => prev + 1);
  }, []);

  // Get translations for columns
  const columnTranslations = {
    id: t("propertyOwners.table.id"),
    name: t("propertyOwners.table.name"),
    contact: t("propertyOwners.table.contact"),
    properties: t("propertyOwners.table.properties"),
    managementFee: t("propertyOwners.table.managementFee"),
    status: t("propertyOwners.table.status"),
    payouts: t("propertyOwners.table.payouts"),
    actions: t("propertyOwners.table.actions"),
    selectAll: t("propertyOwners.table.selectAll"),
    selectRow: t("propertyOwners.table.selectRow"),
    openMenu: t("propertyOwners.table.openMenu"),
    viewDetails: t("propertyOwners.table.viewDetails"),
    editOwner: t("propertyOwners.table.editOwner"),
    deleteOwner: t("propertyOwners.table.deleteOwner"),
    manageProperties: t("propertyOwners.table.manageProperties"),
    statusActive: t("propertyOwners.status.active"),
    statusInactive: t("propertyOwners.status.inactive"),
  };

  const columns = getPropertyOwnerColumns(columnTranslations, locale, handleRefresh);

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">
            {t("propertyOwners.title")}
          </h1>
          <p className="text-sm text-muted-foreground sm:text-base">
            {t("propertyOwners.description")}
          </p>
        </div>
        <Button asChild className="w-full sm:w-auto">
          <Link href={`/${locale}/dashboard/property-owners/new`}>
            <Plus className="mr-2 h-4 w-4" />
            <span className="sm:inline">{t("propertyOwners.addOwner")}</span>
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("propertyOwners.allOwners")}</CardTitle>
          <CardDescription>
            {t("propertyOwners.viewManage")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PropertyOwnersDataTable key={refreshKey} columns={columns} />
        </CardContent>
      </Card>
    </div>
  );
}