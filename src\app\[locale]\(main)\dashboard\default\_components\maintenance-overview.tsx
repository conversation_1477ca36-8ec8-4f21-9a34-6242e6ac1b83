"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Legend, Tooltip } from "recharts";

interface MaintenanceData {
  category: string;
  count: number;
  color: string;
}

export function MaintenanceOverview() {
  const t = useTranslations();
  const [data, setData] = useState<MaintenanceData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data - in real app, fetch from API
    const mockData: MaintenanceData[] = [
      { category: t("maintenance.categories.electrical"), count: 8, color: "#f59e0b" },
      { category: t("maintenance.categories.plumbing"), count: 12, color: "#3b82f6" },
      { category: t("maintenance.categories.hvac"), count: 5, color: "#8b5cf6" },
      { category: t("maintenance.categories.painting"), count: 3, color: "#10b981" },
      { category: t("maintenance.categories.other"), count: 7, color: "#6b7280" },
    ];

    setData(mockData);
    setLoading(false);
  }, [t]);

  const total = data.reduce((sum, item) => sum + item.count, 0);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("dashboard.maintenanceByCategory")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] bg-muted animate-pulse rounded" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("dashboard.maintenanceByCategory")}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="mt-4 space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">{t("dashboard.totalRequests")}</span>
            <span className="font-medium">{total}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">{t("dashboard.inProgress")}</span>
            <span className="font-medium text-orange-600">15</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}