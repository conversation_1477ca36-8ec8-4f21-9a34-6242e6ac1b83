import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { verifyToken } from "@/lib/auth";

export async function GET(request: NextRequest) {
  try {
    // Get token from cookie
    const token = request.cookies.get("auth-token")?.value;
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify token
    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    // Use raw SQL for maximum performance
    const [
      propertiesResult,
      unitsResult,
      contractsResult,
      maintenanceResult,
      invoicesResult
    ] = await Promise.all([
      db.$queryRaw`SELECT COUNT(*) as count FROM properties`,
      db.$queryRaw`
        SELECT 
          status,
          COUNT(*) as count 
        FROM units 
        GROUP BY status
      `,
      db.$queryRaw`
        SELECT 
          status,
          COUNT(*) as count,
          SUM(monthly_rent) as total_rent
        FROM contracts 
        GROUP BY status
      `,
      db.$queryRaw`
        SELECT 
          status,
          COUNT(*) as count 
        FROM maintenance_requests 
        WHERE status IN ('REPORTED', 'IN_PROGRESS', 'COMPLETED')
        GROUP BY status
      `,
      db.$queryRaw`
        SELECT 
          status,
          COUNT(*) as count,
          SUM(total_amount) as total_amount
        FROM invoices 
        GROUP BY status
      `
    ]);

    // Process results
    const totalProperties = Number((propertiesResult as any)[0]?.count || 0);
    
    const unitsStats = unitsResult as any[];
    const totalUnits = unitsStats.reduce((acc, stat) => acc + Number(stat.count), 0);
    const occupiedUnits = unitsStats.find(stat => stat.status === 'OCCUPIED')?.count || 0;
    
    const contractsStats = contractsResult as any[];
    const activeContracts = contractsStats.find(stat => stat.status === 'ACTIVE')?.count || 0;
    const totalRevenue = contractsStats.find(stat => stat.status === 'ACTIVE')?.total_rent || 0;
    
    const maintenanceStats = maintenanceResult as any[];
    const pendingMaintenance = maintenanceStats
      .filter(stat => ['REPORTED', 'IN_PROGRESS'].includes(stat.status))
      .reduce((acc, stat) => acc + Number(stat.count), 0);
    
    const invoicesStats = invoicesResult as any[];
    const overdueInvoices = invoicesStats.find(stat => stat.status === 'OVERDUE')?.count || 0;

    const occupancyRate = totalUnits > 0 ? Math.round((Number(occupiedUnits) / totalUnits) * 100) : 0;

    const stats = {
      totalProperties,
      totalUnits,
      occupiedUnits: Number(occupiedUnits),
      occupancyRate,
      activeContracts: Number(activeContracts),
      totalRevenue: Number(totalRevenue),
      pendingMaintenance,
      overdueInvoices: Number(overdueInvoices),
      monthlyGrowth: 12.5,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error("Quick stats API error:", error);
    return NextResponse.json(
      { error: "Failed to fetch quick statistics" },
      { status: 500 }
    );
  }
}
