import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { z } from "zod";

// Validation schema for amenity update
const amenityUpdateSchema = z.object({
  name_en: z.string().min(1, "Name in English is required").max(100).optional(),
  name_ar: z.string().min(1, "Name in Arabic is required").max(100).optional(),
  icon: z.string().max(50).optional().nullable(),
});

// GET /api/amenities/[id] - Get a single amenity
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for amenities
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "amenities", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view amenities");
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.error("Invalid amenity ID", "INVALID_ID", 400);
    }

    const amenity = await db.amenity.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            property_amenities: true,
          },
        },
      },
    });

    if (!amenity) {
      return ApiResponseBuilder.error("Amenity not found", "NOT_FOUND", 404);
    }

    return ApiResponseBuilder.success(amenity);
  } catch (error) {
    console.error("Error fetching amenity:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch amenity",
      "FETCH_ERROR",
      500,
      error
    );
  }
}

// PUT /api/amenities/[id] - Update an amenity
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "amenities", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update amenities");
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.error("Invalid amenity ID", "INVALID_ID", 400);
    }

    const body = await request.json();
    const validatedData = amenityUpdateSchema.parse(body);

    // Check if amenity exists
    const existingAmenity = await db.amenity.findUnique({
      where: { id },
    });

    if (!existingAmenity) {
      return ApiResponseBuilder.error("Amenity not found", "NOT_FOUND", 404);
    }

    // Check for duplicate names
    if (validatedData.name_en || validatedData.name_ar) {
      const duplicate = await db.amenity.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                validatedData.name_en ? { name_en: validatedData.name_en } : {},
                validatedData.name_ar ? { name_ar: validatedData.name_ar } : {},
              ],
            },
          ],
        },
      });

      if (duplicate) {
        return ApiResponseBuilder.error(
          "Amenity with this name already exists",
          "DUPLICATE_ENTRY",
          400
        );
      }
    }

    const amenity = await db.amenity.update({
      where: { id },
      data: validatedData,
    });

    return ApiResponseBuilder.success(amenity);
  } catch (error: any) {
    console.error("Error updating amenity:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error(
      "Failed to update amenity",
      "UPDATE_ERROR",
      500,
      error
    );
  }
}

// DELETE /api/amenities/[id] - Delete an amenity
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "amenities", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete amenities");
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.error("Invalid amenity ID", "INVALID_ID", 400);
    }

    // Check if amenity exists
    const amenity = await db.amenity.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            property_amenities: true,
          },
        },
      },
    });

    if (!amenity) {
      return ApiResponseBuilder.error("Amenity not found", "NOT_FOUND", 404);
    }

    // Check if amenity is being used by properties
    if (amenity._count.property_amenities > 0) {
      return ApiResponseBuilder.error(
        `Cannot delete amenity. It is assigned to ${amenity._count.property_amenities} properties`,
        "IN_USE",
        400
      );
    }

    await db.amenity.delete({
      where: { id },
    });

    return ApiResponseBuilder.success({ message: "Amenity deleted successfully" });
  } catch (error) {
    console.error("Error deleting amenity:", error);
    return ApiResponseBuilder.error(
      "Failed to delete amenity",
      "DELETE_ERROR",
      500,
      error
    );
  }
}