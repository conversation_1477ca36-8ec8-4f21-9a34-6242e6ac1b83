import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ExpenseForm } from "../_components/expense-form";

interface CreateExpensePageProps {
  params: Promise<{
    locale: string;
  }>;
}

export default async function CreateExpensePage({ params }: CreateExpensePageProps) {
  const { locale } = await params;
  const t = await getTranslations("expenses");

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{t("addExpense")}</h1>
        <p className="text-muted-foreground">
          {t("ui.createSubtitle")}
        </p>
      </div>

      {/* Form Card */}
      <Card>
        <CardHeader>
          <CardTitle>{t("expenseDetails")}</CardTitle>
          <CardDescription>
            {t("ui.createInstructions")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ExpenseForm mode="create" locale={locale} />
        </CardContent>
      </Card>
    </div>
  );
}
