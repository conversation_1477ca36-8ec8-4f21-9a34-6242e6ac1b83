<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=property_management', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Checking units table structure:\n";
    $stmt = $pdo->query("DESCRIBE units");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo $row['Field'] . " - " . $row['Type'] . "\n";
    }
    
    echo "\nChecking if units table exists:\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'units'");
    $exists = $stmt->fetch();
    if ($exists) {
        echo "Units table exists\n";
    } else {
        echo "Units table does not exist\n";
    }
    
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage() . "\n";
}
?>