import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

const properties = [
  {
    name: "Sunset Apartments",
    address: "123 Sunset Blvd, Los Angeles, CA 90028",
    type: "Apartment Complex",
    units: 24,
  },
  {
    name: "Downtown Lofts",
    address: "456 Main St, New York, NY 10001",
    type: "Loft Building",
    units: 12,
  },
  {
    name: "Garden View Condos",
    address: "789 Garden Ave, Miami, FL 33101",
    type: "Condominium",
    units: 18,
  },
  {
    name: "Riverside Townhomes",
    address: "321 River Rd, Austin, TX 78701",
    type: "Townhouse",
    units: 8,
  },
  {
    name: "Mountain View Estates",
    address: "654 Mountain Dr, Denver, CO 80202",
    type: "Single Family",
    units: 6,
  },
];

const tenants = [
  {
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0101",
    lease_start_date: new Date("2024-01-01"),
    lease_end_date: new Date("2024-12-31"),
    monthly_rent: 1200.00,
    security_deposit: 1200.00,

  },
  {
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-0102",
    lease_start_date: new Date("2024-02-01"),
    lease_end_date: new Date("2025-01-31"),
    monthly_rent: 1350.00,
    security_deposit: 1350.00,

  },
  {
    first_name: "Michael",
    last_name: "Brown",
    email: "<EMAIL>",
    phone: "******-0103",
    lease_start_date: new Date("2024-03-01"),
    lease_end_date: new Date("2025-02-28"),
    monthly_rent: 1100.00,
    security_deposit: 1100.00,

  },
  {
    first_name: "Emily",
    last_name: "Davis",
    email: "<EMAIL>",
    phone: "******-0104",
    lease_start_date: new Date("2024-04-01"),
    lease_end_date: new Date("2025-03-31"),
    monthly_rent: 1450.00,
    security_deposit: 1450.00,

  },
  {
    first_name: "David",
    last_name: "Wilson",
    email: "<EMAIL>",
    phone: "******-0105",
    lease_start_date: new Date("2024-05-01"),
    lease_end_date: new Date("2025-04-30"),
    monthly_rent: 1300.00,
    security_deposit: 1300.00,

  },
  {
    first_name: "Jessica",
    last_name: "Miller",
    email: "<EMAIL>",
    phone: "******-0106",
    lease_start_date: new Date("2024-06-01"),
    lease_end_date: new Date("2025-05-31"),
    monthly_rent: 1250.00,
    security_deposit: 1250.00,

  },
  {
    first_name: "Robert",
    last_name: "Garcia",
    email: "<EMAIL>",
    phone: "******-0107",
    lease_start_date: new Date("2024-07-01"),
    lease_end_date: new Date("2025-06-30"),
    monthly_rent: 1400.00,
    security_deposit: 1400.00,

  },
  {
    first_name: "Amanda",
    last_name: "Martinez",
    email: "<EMAIL>",
    phone: "******-0108",
    lease_start_date: new Date("2024-08-01"),
    lease_end_date: new Date("2025-07-31"),
    monthly_rent: 1150.00,
    security_deposit: 1150.00,

  },
  {
    first_name: "Christopher",
    last_name: "Anderson",
    email: "<EMAIL>",
    phone: "******-0109",
    lease_start_date: new Date("2024-09-01"),
    lease_end_date: new Date("2025-08-31"),
    monthly_rent: 1500.00,
    security_deposit: 1500.00,

  },
  {
    first_name: "Lisa",
    last_name: "Taylor",
    email: "<EMAIL>",
    phone: "******-0110",
    lease_start_date: new Date("2024-10-01"),
    lease_end_date: new Date("2025-09-30"),
    monthly_rent: 1350.00,
    security_deposit: 1350.00,

  },
  {
    first_name: "Kevin",
    last_name: "Thomas",
    email: "<EMAIL>",
    phone: "******-0111",
    lease_start_date: new Date("2024-11-01"),
    lease_end_date: new Date("2025-10-31"),
    monthly_rent: 1200.00,
    security_deposit: 1200.00,

  },
  {
    first_name: "Michelle",
    last_name: "Jackson",
    email: "<EMAIL>",
    phone: "******-0112",
    lease_start_date: new Date("2024-12-01"),
    lease_end_date: new Date("2025-11-30"),
    monthly_rent: 1450.00,
    security_deposit: 1450.00,

  },
  {
    first_name: "Brian",
    last_name: "White",
    email: "<EMAIL>",
    phone: "******-0113",
    lease_start_date: new Date("2025-01-01"),
    lease_end_date: new Date("2025-12-31"),
    monthly_rent: 1300.00,
    security_deposit: 1300.00,

  },
  {
    first_name: "Nicole",
    last_name: "Harris",
    email: "<EMAIL>",
    phone: "******-0114",
    lease_start_date: new Date("2025-02-01"),
    lease_end_date: new Date("2026-01-31"),
    monthly_rent: 1250.00,
    security_deposit: 1250.00,

  },
  {
    first_name: "Daniel",
    last_name: "Clark",
    email: "<EMAIL>",
    phone: "******-0115",
    lease_start_date: new Date("2023-06-01"),
    lease_end_date: new Date("2024-05-31"),
    monthly_rent: 1100.00,
    security_deposit: 1100.00,

  },
  {
    first_name: "Rachel",
    last_name: "Lewis",
    email: "<EMAIL>",
    phone: "******-0116",
    lease_start_date: new Date("2025-03-01"),
    lease_end_date: new Date("2026-02-28"),
    monthly_rent: 1400.00,
    security_deposit: 1400.00,

  },
  {
    first_name: "Mark",
    last_name: "Robinson",
    email: "<EMAIL>",
    phone: "******-0117",
    lease_start_date: new Date("2024-01-15"),
    lease_end_date: new Date("2025-01-14"),
    monthly_rent: 1350.00,
    security_deposit: 1350.00,

  },
  {
    first_name: "Stephanie",
    last_name: "Walker",
    email: "<EMAIL>",
    phone: "******-0118",
    lease_start_date: new Date("2024-02-15"),
    lease_end_date: new Date("2025-02-14"),
    monthly_rent: 1500.00,
    security_deposit: 1500.00,

  },
  {
    first_name: "Jason",
    last_name: "Hall",
    email: "<EMAIL>",
    phone: "******-0119",
    lease_start_date: new Date("2024-03-15"),
    lease_end_date: new Date("2025-03-14"),
    monthly_rent: 1200.00,
    security_deposit: 1200.00,

  },
  {
    first_name: "Lauren",
    last_name: "Allen",
    email: "<EMAIL>",
    phone: "******-0120",
    lease_start_date: new Date("2024-04-15"),
    lease_end_date: new Date("2025-04-14"),
    monthly_rent: 1300.00,
    security_deposit: 1300.00,

  },
  {
    first_name: "Ryan",
    last_name: "Young",
    email: "<EMAIL>",
    phone: "******-0121",
    lease_start_date: new Date("2024-05-15"),
    lease_end_date: new Date("2025-05-14"),
    monthly_rent: 1450.00,
    security_deposit: 1450.00,

  },
  {
    first_name: "Megan",
    last_name: "King",
    email: "<EMAIL>",
    phone: "******-0122",
    lease_start_date: new Date("2024-06-15"),
    lease_end_date: new Date("2025-06-14"),
    monthly_rent: 1250.00,
    security_deposit: 1250.00,

  },
  {
    first_name: "Andrew",
    last_name: "Wright",
    email: "<EMAIL>",
    phone: "******-0123",
    lease_start_date: new Date("2024-07-15"),
    lease_end_date: new Date("2025-07-14"),
    monthly_rent: 1350.00,
    security_deposit: 1350.00,

  },
  {
    first_name: "Samantha",
    last_name: "Lopez",
    email: "<EMAIL>",
    phone: "******-0124",
    lease_start_date: new Date("2024-08-15"),
    lease_end_date: new Date("2025-08-14"),
    monthly_rent: 1400.00,
    security_deposit: 1400.00,

  },
  {
    first_name: "Tyler",
    last_name: "Hill",
    email: "<EMAIL>",
    phone: "******-0125",
    lease_start_date: new Date("2024-09-15"),
    lease_end_date: new Date("2025-09-14"),
    monthly_rent: 1150.00,
    security_deposit: 1150.00,

  },
  {
    first_name: "Ashley",
    last_name: "Green",
    email: "<EMAIL>",
    phone: "******-0126",
    lease_start_date: new Date("2024-10-15"),
    lease_end_date: new Date("2025-10-14"),
    monthly_rent: 1500.00,
    security_deposit: 1500.00,

  },
  {
    first_name: "Jordan",
    last_name: "Adams",
    email: "<EMAIL>",
    phone: "******-0127",
    lease_start_date: new Date("2024-11-15"),
    lease_end_date: new Date("2025-11-14"),
    monthly_rent: 1300.00,
    security_deposit: 1300.00,

  },
  {
    first_name: "Brittany",
    last_name: "Baker",
    email: "<EMAIL>",
    phone: "******-0128",
    lease_start_date: new Date("2024-12-15"),
    lease_end_date: new Date("2025-12-14"),
    monthly_rent: 1250.00,
    security_deposit: 1250.00,

  },
  {
    first_name: "Nathan",
    last_name: "Nelson",
    email: "<EMAIL>",
    phone: "******-0129",
    lease_start_date: new Date("2025-01-15"),
    lease_end_date: new Date("2026-01-14"),
    monthly_rent: 1400.00,
    security_deposit: 1400.00,

  },
  {
    first_name: "Kimberly",
    last_name: "Carter",
    email: "<EMAIL>",
    phone: "******-0130",
    lease_start_date: new Date("2025-02-15"),
    lease_end_date: new Date("2026-02-14"),
    monthly_rent: 1350.00,
    security_deposit: 1350.00,

  },
];

async function main() {
  console.log("🌱 Starting database seed...");

  // Clear existing data
  console.log("🧹 Clearing existing data...");
  await prisma.tenant.deleteMany();
  await prisma.property.deleteMany();

  // Create properties
  console.log("🏢 Creating properties...");
  const createdProperties = await Promise.all(
    properties.map((property) =>
      prisma.property.create({
        data: property,
      })
    )
  );

  console.log(`✅ Created ${createdProperties.length} properties`);

  // Create tenants with random property assignments
  console.log("👥 Creating tenants...");
  const createdTenants = await Promise.all(
    tenants.map((tenant, index) => {
      const propertyIndex = index % createdProperties.length;
      return prisma.tenant.create({
        data: {
          ...tenant,
          property_id: createdProperties[propertyIndex].id,
        },
      });
    })
  );

  console.log(`✅ Created ${createdTenants.length} tenants`);
  console.log("🎉 Database seeding completed successfully!");
}

main()
  .catch((e) => {
    console.error("❌ Error during seeding:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
