import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { CategoryForm } from "../../_components/category-form";
import type { ExpenseCategory } from "@/types/expense";
import { db } from "@/lib/db";

interface EditCategoryPageProps {
  params: Promise<{ locale: string; id: string }>;
}

async function getCategory(id: string): Promise<ExpenseCategory | null> {
  try {
    const categoryId = parseInt(id, 10);
    if (isNaN(categoryId)) {
      return null;
    }

    const category = await db.expenseCategory.findUnique({
      where: { id: categoryId },
      include: {
        _count: {
          select: {
            expenses: true
          }
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });

    // Serialize the category data to ensure it can be passed to client components
    return category ? JSON.parse(JSON.stringify(category)) : null;
  } catch (error) {
    console.error("Error fetching category:", error);
    return null;
  }
}

export default async function EditCategoryPage({ params }: EditCategoryPageProps) {
  const { locale, id } = await params;
  const category = await getCategory(id);

  if (!category) {
    notFound();
  }

  return (
    <CategoryForm mode="edit" category={category} locale={locale} />
  );
}

export async function generateMetadata({ params }: EditCategoryPageProps) {
  const { locale, id } = await params;
  const t = await getTranslations({ locale, namespace: "expenses.categories" });
  const category = await getCategory(id);

  return {
    title: category ? `${t("editCategory")} - ${category.name_en}` : t("editCategory"),
    description: t("editCategoryDescription"),
  };
}
