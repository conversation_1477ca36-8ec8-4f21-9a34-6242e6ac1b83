"use client";

import { Setting<PERSON>, CircleHelp, Search, Database, ClipboardList, File, Command } from "lucide-react";

import {
  Sidebar,
  <PERSON>barContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { APP_CONFIG } from "@/config/app-config";
import { sidebarItems } from "@/navigation/sidebar/sidebar-items";
import { useCompanySettings } from "@/hooks/use-company-settings";
import { useRTL } from "@/hooks/use-rtl";

import { NavMain } from "./nav-main";
import { NavUser } from "./nav-user";

const data = {
  navSecondary: [
    {
      title: "Settings",
      url: "#",
      icon: Settings,
    },
    {
      title: "Get Help",
      url: "#",
      icon: CircleHelp,
    },
    {
      title: "Search",
      url: "#",
      icon: Search,
    },
  ],
  documents: [
    {
      name: "Data Library",
      url: "#",
      icon: Database,
    },
    {
      name: "Reports",
      url: "#",
      icon: ClipboardList,
    },
    {
      name: "Word Assistant",
      url: "#",
      icon: File,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { settings } = useCompanySettings();
  const { isRTL } = useRTL();

  // In RTL layouts, sidebar should be on the right side
  // In LTR layouts, sidebar should be on the left side
  const side = isRTL ? "right" : "left";

  return (
    <Sidebar side={side} {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              style={{
                padding: "1.5rem",
                height: "120px",
              }}
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="#">
                {settings?.logo_url ? (
                  <img
                    src={settings.logo_url}
                    alt="Company Logo"
                    className=" rounded object-contain"
                    style={{ height: "120px" }}
                  />
                ) : (
                  <>
                    <Command />
                    <span className="text-base font-semibold">
                      {settings?.company_name || APP_CONFIG.name}
                    </span>
                  </>
                )}
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={sidebarItems} />
        {/* <NavDocuments items={data.documents} /> */}
        {/* <NavSecondary items={data.navSecondary} className="mt-auto" /> */}
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
