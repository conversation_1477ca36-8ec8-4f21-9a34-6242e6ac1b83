const fetch = require('node-fetch');
const { CookieJar } = require('tough-cookie');
const { Agent } = require('http');

// Create a cookie jar to persist cookies like a browser
const cookieJar = new CookieJar();

async function simulateBrowserFlow() {
  try {
    console.log('=== Simulating Browser Login Flow ===\n');
    
    // Step 1: Login
    console.log('1. Attempting login...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: '<EMAIL>',
        password: '123456'
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('Login status:', loginResponse.status);
    
    if (loginResponse.status !== 200) {
      console.error('Login failed:', loginData);
      return;
    }
    
    // Extract token from Set-Cookie header
    const cookies = loginResponse.headers.get('set-cookie');
    const tokenMatch = cookies.match(/auth-token=([^;]+)/);
    const token = tokenMatch ? tokenMatch[1] : null;
    
    if (!token) {
      console.error('No token found in login response');
      return;
    }
    
    console.log('✓ Login successful, token extracted');
    console.log('✓ User permissions:', Object.keys(loginData.user.permissions).length, 'modules\n');
    
    // Step 2: Test /api/auth/me with the token
    console.log('2. Testing /api/auth/me endpoint...');
    const meResponse = await fetch('http://localhost:3000/api/auth/me', {
      headers: {
        'Cookie': `auth-token=${token}`
      }
    });
    
    const meData = await meResponse.json();
    console.log('Me endpoint status:', meResponse.status);
    
    if (meResponse.status === 200) {
      console.log('✓ /api/auth/me working correctly');
      console.log('✓ User authenticated as:', meData.user.username);
      console.log('✓ Has properties permission:', !!meData.user.permissions.properties?.read, '\n');
    } else {
      console.error('✗ /api/auth/me failed:', meData);
      return;
    }
    
    // Step 3: Test accessing a dashboard route
    console.log('3. Testing dashboard route access...');
    const dashboardResponse = await fetch('http://localhost:3000/en/dashboard/properties', {
      headers: {
        'Cookie': `auth-token=${token}`,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      },
      redirect: 'manual'
    });
    
    console.log('Dashboard route status:', dashboardResponse.status);
    console.log('Dashboard route headers:', Object.fromEntries(dashboardResponse.headers.entries()));
    
    if (dashboardResponse.status === 200) {
      console.log('✓ Dashboard route accessible');
    } else if (dashboardResponse.status >= 300 && dashboardResponse.status < 400) {
      console.log('✓ Dashboard route redirecting (might be expected)');
      console.log('Redirect location:', dashboardResponse.headers.get('location'));
    } else {
      console.log('✗ Dashboard route failed with status:', dashboardResponse.status);
      const text = await dashboardResponse.text();
      console.log('Response excerpt:', text.substring(0, 500));
    }
    
    console.log('\n=== Test Complete ===');
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

simulateBrowserFlow();