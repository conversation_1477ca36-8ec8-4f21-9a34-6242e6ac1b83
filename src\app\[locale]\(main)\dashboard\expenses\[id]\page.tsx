import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Edit, ArrowLeft, Check, X, FileText, Trash2 } from "lucide-react";
import Link from "next/link";
import { ExpenseWithDetails } from "@/types/expense";
import { formatCurrency, formatDateForDisplay } from "@/lib/localization";
import { DeleteExpenseDialog } from "../_components/delete-expense-dialog";
import { db } from "@/lib/db";

interface ExpenseDetailPageProps {
  params: Promise<{
    locale: string;
    id: string;
  }>;
}

async function getExpense(id: string): Promise<ExpenseWithDetails | null> {
  try {
    const expenseId = parseInt(id, 10);
    if (isNaN(expenseId)) {
      return null;
    }

    const expense = await db.expense.findUnique({
      where: { id: expenseId },
      include: {
        category: true,
        attachments: true,
        approvals: {
          orderBy: { created_at: "desc" }
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        approver: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });

    // Serialize the expense data to ensure it can be passed to client components
    return expense ? JSON.parse(JSON.stringify(expense)) : null;
  } catch (error) {
    console.error("Error fetching expense:", error);
    return null;
  }
}

export default async function ExpenseDetailPage({ params }: ExpenseDetailPageProps) {
  const { locale, id } = await params;
  const t = await getTranslations("expenses");
  const tCommon = await getTranslations("common");

  const expense = await getExpense(id);

  if (!expense) {
    notFound();
  }

  const statusVariant = 
    expense.status === "APPROVED" ? "success" :
    expense.status === "REJECTED" ? "destructive" :
    "secondary";

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href={`/${locale}/dashboard/expenses`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              {tCommon("back")}
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t("expenseDetails")}</h1>
            <p className="text-muted-foreground">
              {t("viewExpense")} #{expense.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {expense.status === "PENDING" && (
            <>
              <Button variant="outline" className="text-green-600 border-green-600 hover:bg-green-50">
                <Check className="mr-2 h-4 w-4" />
                {t("actions.approve")}
              </Button>
              <Button variant="outline" className="text-red-600 border-red-600 hover:bg-red-50">
                <X className="mr-2 h-4 w-4" />
                {t("actions.reject")}
              </Button>
            </>
          )}
          <Link href={`/${locale}/dashboard/expenses/${expense.id}/edit`}>
            <Button>
              <Edit className="mr-2 h-4 w-4" />
              {t("editExpense")}
            </Button>
          </Link>
          <DeleteExpenseDialog
            expense={expense}
            redirectUrl={`/${locale}/dashboard/expenses`}
          >
            <Button variant="outline" className="text-red-600 border-red-600 hover:bg-red-50">
              <Trash2 className="mr-2 h-4 w-4" />
              {t("deleteExpense")}
            </Button>
          </DeleteExpenseDialog>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Details */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>{t("expenseDetails")}</CardTitle>
                <Badge variant={statusVariant as any}>
                  {t(`statuses.${expense.status}`)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("fields.date")}
                  </label>
                  <p className="text-lg font-medium">
                    {formatDateForDisplay(expense.date)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("fields.amount")}
                  </label>
                  <p className="text-lg font-medium">
                    {formatCurrency(Number(expense.amount))}
                  </p>
                </div>
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("fields.description")}
                  </label>
                  <p className="text-lg">{expense.description}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("fields.category")}
                  </label>
                  <p className="text-lg">
                    {locale === "ar" ? expense.category.name_ar : expense.category.name_en}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("fields.paymentMethod")}
                  </label>
                  <p className="text-lg">
                    {t(`paymentMethods.${expense.payment_method}`)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("fields.paidBy")}
                  </label>
                  <p className="text-lg">{expense.paid_by}</p>
                </div>
                {expense.is_recurring && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("fields.recurringFrequency")}
                    </label>
                    <p className="text-lg">
                      {expense.recurring_frequency && t(`frequencies.${expense.recurring_frequency}`)}
                    </p>
                  </div>
                )}
              </div>

              {expense.notes && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("fields.notes")}
                    </label>
                    <p className="text-lg mt-2">{expense.notes}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Attachments */}
          {expense.attachments && expense.attachments.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>{t("fields.attachments")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {expense.attachments.map((attachment) => (
                    <div
                      key={attachment.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <FileText className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{attachment.original_name}</p>
                          <p className="text-sm text-muted-foreground">
                            {(attachment.file_size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        {t("actions.downloadAttachment")}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status & Approval Info */}
          <Card>
            <CardHeader>
              <CardTitle>{t("ui.statusInformation")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t("fields.status")}
                </label>
                <div className="mt-1">
                  <Badge variant={statusVariant as any}>
                    {t(`statuses.${expense.status}`)}
                  </Badge>
                </div>
              </div>

              {expense.approved_by && expense.approved_at && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("fields.approvedBy")}
                    </label>
                    <p className="text-sm">
                      {expense.approver?.first_name} {expense.approver?.last_name}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("fields.approvedAt")}
                    </label>
                    <p className="text-sm">
                      {formatDateForDisplay(expense.approved_at)}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>{t("ui.metadata")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t("fields.createdBy")}
                </label>
                <p className="text-sm">
                  {expense.creator?.first_name} {expense.creator?.last_name}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t("fields.createdAt")}
                </label>
                <p className="text-sm">
                  {formatDateForDisplay(expense.created_at)}
                </p>
              </div>
              {expense.updated_at !== expense.created_at && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("fields.updatedAt")}
                  </label>
                  <p className="text-sm">
                    {formatDateForDisplay(expense.updated_at)}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
