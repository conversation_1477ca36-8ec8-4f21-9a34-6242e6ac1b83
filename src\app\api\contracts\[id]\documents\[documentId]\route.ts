import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ApiResponseBuilder } from "@/lib/api-response";
import { unlink } from "fs/promises";
import { join } from "path";



import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; documentId: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for contracts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "contracts", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete contracts");
    }

    // User is already authenticated via token above

    const { id, documentId: documentIdParam } = await params;

    const contractId = parseInt(id);
    const documentId = parseInt(documentIdParam);

    if (isNaN(contractId) || isNaN(documentId)) {
      return ApiResponseBuilder.error("Invalid contract or document ID", "BAD_REQUEST", 400);
    }

    // Check if document exists and belongs to the contract
    const document = await prisma.contractDocument.findFirst({
      where: {
        id: documentId,
        contract_id: contractId,
      },
    });

    if (!document) {
      return ApiResponseBuilder.error("Document not found", "NOT_FOUND", 404);
    }

    // Delete file from disk
    try {
      const filePath = join(process.cwd(), "public", document.file_path);
      await unlink(filePath);
    } catch (error) {
      console.error("Error deleting file from disk:", error);
      // Continue with database deletion even if file deletion fails
    }

    // Delete document record
    await prisma.contractDocument.delete({
      where: { id: documentId },
    });

    // Update contract to reflect changes
    await prisma.contract.update({
      where: { id: contractId },
      data: {
        updated_by: decoded.id,
      },
    });

    return ApiResponseBuilder.success({ message: "Document deleted successfully" });
  } catch (error) {
    console.error("Error deleting contract document:", error);
    return ApiResponseBuilder.error("Failed to delete contract document", "INTERNAL_ERROR", 500);
  }
}