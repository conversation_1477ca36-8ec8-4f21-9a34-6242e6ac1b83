import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ownerPayoutSchema, ownerPayoutFilterSchema, generatePayoutNumber } from "@/types/owner-payout";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

// GET /api/owner-payouts - List all owner payouts with filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owner-payouts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "owner-payouts", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view owner payouts");
    }

    const searchParams = request.nextUrl.searchParams;
    
    // Parse filters
    const filters = ownerPayoutFilterSchema.parse({
      owner_id: searchParams.get("owner_id") ? parseInt(searchParams.get("owner_id")!) : undefined,
      status: searchParams.get("status") || undefined,
      payment_method: searchParams.get("payment_method") || undefined,
      date_from: searchParams.get("date_from") || undefined,
      date_to: searchParams.get("date_to") || undefined,
      search: searchParams.get("search") || undefined,
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
      pageSize: searchParams.get("pageSize") ? parseInt(searchParams.get("pageSize")!) : 10,
      sortBy: searchParams.get("sortBy") || "created_at",
      sortOrder: searchParams.get("sortOrder") || "desc",
    });

    // Build where clause
    const where: any = {};

    if (filters.owner_id) {
      where.owner_id = filters.owner_id;
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.payment_method) {
      where.payment_method = filters.payment_method;
    }

    if (filters.date_from || filters.date_to) {
      where.payout_date = {};
      if (filters.date_from) {
        where.payout_date.gte = new Date(filters.date_from);
      }
      if (filters.date_to) {
        where.payout_date.lte = new Date(filters.date_to);
      }
    }

    if (filters.search) {
      where.OR = [
        { payout_number: { contains: filters.search } },
        { reference_number: { contains: filters.search } },
        { bank_transfer_ref: { contains: filters.search } },
        { notes: { contains: filters.search } },
        {
          owner: {
            OR: [
              { name_en: { contains: filters.search } },
              { name_ar: { contains: filters.search } },
              { email: { contains: filters.search } },
            ],
          },
        },
      ];
    }

    // Calculate pagination
    const page = filters.page || 1;
    const pageSize = filters.pageSize || 10;
    const skip = (page - 1) * pageSize;

    // Fetch payouts with relations
    const [payouts, total] = await Promise.all([
      db.ownerPayout.findMany({
        where,
        include: {
          owner: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
              email: true,
              phone: true,
              mobile: true,
              management_fee_percentage: true,
            },
          },
          approver: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
          payer: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
          payout_details: {
            include: {
              property: {
                select: {
                  id: true,
                  name_en: true,
                  name_ar: true,
                  address_en: true,
                  address_ar: true,
                },
              },
            },
          },
        },
        orderBy: {
          [filters.sortBy || "created_at"]: filters.sortOrder || "desc",
        },
        skip,
        take: pageSize,
      }),
      db.ownerPayout.count({ where }),
    ]);

    // Transform payouts to handle Decimal serialization
    const transformedPayouts = payouts.map(payout => ({
      ...payout,
      total_rent_collected: payout.total_rent_collected.toString(),
      management_fee: payout.management_fee.toString(),
      other_deductions: payout.other_deductions.toString(),
      net_amount: payout.net_amount.toString(),
      owner: payout.owner ? {
        ...payout.owner,
        management_fee_percentage: payout.owner.management_fee_percentage ? payout.owner.management_fee_percentage.toString() : null,
      } : undefined,
      payout_details: payout.payout_details.map(detail => ({
        ...detail,
        rent_collected: detail.rent_collected.toString(),
        management_fee: detail.management_fee.toString(),
        net_amount: detail.net_amount.toString(),
      })),
    }));

    return ApiResponseBuilder.success(transformedPayouts, {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error) {
    console.error("Error fetching owner payouts:", error);
    return ApiResponseBuilder.error("Failed to fetch owner payouts", "INTERNAL_ERROR", 500);
  }
}

// POST /api/owner-payouts - Create a new owner payout
export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has CREATE permission for owner-payouts
    const canCreate = hasPermission(userPermissions, "owner-payouts", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create owner payouts");
    }

    const body = await request.json();
    const validatedData = ownerPayoutSchema.parse(body);

    // Check if owner exists
    const owner = await db.propertyOwner.findUnique({
      where: { id: validatedData.owner_id },
    });

    if (!owner) {
      return ApiResponseBuilder.error("Property owner not found", "NOT_FOUND", 404);
    }

    // Generate payout number
    const payoutNumber = generatePayoutNumber();

    // Create payout with details
    const payout = await db.ownerPayout.create({
      data: {
        payout_number: payoutNumber,
        owner_id: validatedData.owner_id,
        payout_date: new Date(validatedData.payout_date),
        period_start: new Date(validatedData.period_start),
        period_end: new Date(validatedData.period_end),
        total_rent_collected: validatedData.total_rent_collected,
        management_fee: validatedData.management_fee,
        other_deductions: validatedData.other_deductions,
        net_amount: validatedData.net_amount,
        payment_method: validatedData.payment_method,
        reference_number: validatedData.reference_number,
        bank_transfer_ref: validatedData.bank_transfer_ref,
        notes: validatedData.notes,
        status: validatedData.status,
        created_by: undefined, // TODO: Get from auth
        payout_details: validatedData.payout_details ? {
          create: validatedData.payout_details.map(detail => ({
            property_id: detail.property_id,
            rent_collected: detail.rent_collected,
            management_fee: detail.management_fee,
            net_amount: detail.net_amount,
          })),
        } : undefined,
      },
      include: {
        owner: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            email: true,
            phone: true,
            mobile: true,
          },
        },
        payout_details: {
          include: {
            property: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
                address_en: true,
                address_ar: true,
              },
            },
          },
        },
      },
    });

    // Transform payout to handle Decimal serialization
    const transformedPayout = {
      ...payout,
      total_rent_collected: payout.total_rent_collected.toString(),
      management_fee: payout.management_fee.toString(),
      other_deductions: payout.other_deductions.toString(),
      net_amount: payout.net_amount.toString(),
      payout_details: payout.payout_details.map(detail => ({
        ...detail,
        rent_collected: detail.rent_collected.toString(),
        management_fee: detail.management_fee.toString(),
        net_amount: detail.net_amount.toString(),
      })),
    };

    return ApiResponseBuilder.success(transformedPayout);
  } catch (error: any) {
    console.error("Error creating owner payout:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to create owner payout", "INTERNAL_ERROR", 500);
  }
}