import { Metadata } from "next";
import { notFound } from "next/navigation";
import { MaintenanceForm } from "../../_components/maintenance-form";

export const metadata: Metadata = {
  title: "Edit Maintenance Request",
  description: "Edit maintenance request details",
};

async function getMaintenanceRequest(id: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_APP_URL}/api/maintenance/${id}`,
      {
        cache: "no-store",
      }
    );

    if (!response.ok) {
      return null;
    }

    const result = await response.json();
    return result.success ? result.data : null;
  } catch (error) {
    console.error("Error fetching maintenance request:", error);
    return null;
  }
}

export default async function MaintenanceEditPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const maintenance = await getMaintenanceRequest(id);

  if (!maintenance) {
    notFound();
  }

  return <MaintenanceForm initialData={maintenance} mode="edit" />;
}