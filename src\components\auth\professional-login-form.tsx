"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Eye, EyeOff, Loader2, AlertCircle } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuth } from "@/contexts/auth-context";

// Form validation schema
const createLoginSchema = (t: (key: string) => string) => z.object({
  username: z.string().min(1, { message: t("required") }),
  password: z.string().min(1, { message: t("required") }),
  remember: z.boolean().optional(),
});

interface ProfessionalLoginFormProps {
  locale: string;
}

export function ProfessionalLoginForm({ locale }: ProfessionalLoginFormProps) {
  const t = useTranslations("auth");
  const tForms = useTranslations("forms");
  const router = useRouter();
  const { login } = useAuth();

  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loginSchema = createLoginSchema(tForms);
  type LoginFormData = z.infer<typeof loginSchema>;

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
      remember: false,
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      // Pass username to the authentication system
      await login(data.username, data.password);

      // Small delay to ensure state is updated before redirect
      await new Promise(resolve => setTimeout(resolve, 200));

      // Use router.replace to avoid adding to history
      router.replace(`/${locale}/dashboard/default`);
    } catch (err: any) {
      
      // Handle different error types
      if (err.message && (err.message.includes("Invalid") || err.message.includes("username or password"))) {
        setError(t("invalidCredentials"));
      } else if (err.message && err.message.includes("locked")) {
        setError(t("accountLocked"));
      } else if (err.message && err.message.includes("attempts")) {
        setError(t("tooManyAttempts"));
      } else if (err.message && err.message.includes("Network")) {
        setError(t("networkError"));
      } else {
        setError(t("serverError"));
      }
      
      // Clear the password field on error
      form.setValue("password", "");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit(onSubmit)().catch(() => {
            // Silently catch any unhandled errors to prevent system errors
            setError(t("serverError"));
          });
        }} className="space-y-4">
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("username")}</FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    placeholder={t("usernamePlaceholder")}
                    autoComplete="username"
                    disabled={isLoading}
                    className="h-11"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("password")}</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder={t("passwordPlaceholder")}
                      autoComplete="current-password"
                      disabled={isLoading}
                      className="h-11 ltr:pr-10 rtl:pl-10"
                      {...field}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute ltr:right-0 rtl:left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={isLoading}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="remember"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center space-x-3 space-y-0 rtl:space-x-reverse">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isLoading}
                  />
                </FormControl>
                <FormLabel className="text-sm font-normal">
                  {t("rememberFor30Days")}
                </FormLabel>
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="w-full h-11"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="ltr:mr-2 rtl:ml-2 h-4 w-4 animate-spin" />
                {t("signingIn")}
              </>
            ) : (
              t("signIn")
            )}
          </Button>
        </form>
      </Form>
    </div>
  );
}
