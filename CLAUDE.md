# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive property management system built with Next.js 15, TypeScript, and Prisma. The system supports bilingual operations (English/Arabic) with full RTL support and includes modules for property management, tenant management, contracts, invoices, payments, maintenance requests, and owners associations.

## Common Development Commands

### Development
```bash
npm run dev                    # Start development server (http://localhost:3000)
npm run lint                   # Run ESLint checks
npm run format                 # Format code with Prettier
```

### Database Operations
```bash
npm run db:migrate            # Run Prisma migrations
npm run db:generate           # Generate Prisma client
npm run db:seed               # Run comprehensive seeding
npm run db:seed-minimal       # Run minimal seeding
npm run db:seed-users         # Seed users only
npm run db:reset              # Reset database (CAUTION: Deletes all data)
```

### Build & Production
```bash
npm run build                 # Build for production
npm start                     # Start production server
```

## Architecture Overview

### Technology Stack
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript with strict mode
- **Database**: MySQL with Prisma ORM
- **UI**: Shadcn UI components with Tailwind CSS v4
- **Authentication**: JWT-based with custom middleware
- **Internationalization**: next-intl for Arabic/English support
- **State Management**: Zustand
- **Forms**: React Hook Form with Zod validation

### Directory Structure
```
src/
├── app/[locale]/            # Locale-based routing
│   ├── (auth)/             # Authentication pages
│   ├── (main)/dashboard/   # Main dashboard modules
│   └── api/                # API endpoints
├── components/             # Reusable UI components
├── lib/                    # Core utilities
│   ├── auth.ts            # Authentication utilities
│   ├── db.ts              # Database connection
│   └── permissions.ts     # RBAC utilities
├── hooks/                  # Custom React hooks
├── types/                  # TypeScript definitions
└── i18n/                   # Internationalization config
```

### Key API Patterns

All API endpoints follow RESTful conventions:
- `GET /api/[resource]` - List with pagination
- `GET /api/[resource]/[id]` - Get single item
- `POST /api/[resource]` - Create new item
- `PUT /api/[resource]/[id]` - Update item
- `DELETE /api/[resource]/[id]` - Delete item

Authentication required for all endpoints except `/api/auth/login`.

### Database Schema Highlights

The system uses Prisma with MySQL. Key models include:
- **User/Role/Permission**: RBAC system with granular permissions
- **Property/Unit**: Multi-property management with amenities
- **Tenant/Contract**: Tenant lifecycle with document management
- **Invoice/Payment**: Financial management with allocations and installment tracking
- **MaintenanceRequest**: Maintenance workflow with status tracking
- **OwnersAssociation**: HOA management module with subscription payments and member transactions

All models support:
- Bilingual fields (name_en, name_ar)
- Audit trails (created_by, updated_by)
- Soft deletes where applicable

### Authentication & Permissions

JWT-based authentication with edge runtime support:
- Tokens stored in httpOnly cookies
- Permission-based UI rendering
- Middleware validates all protected routes
- Use `getUser()` from `lib/auth` in server components
- Use `useUser()` hook in client components

### Internationalization

The system supports English and Arabic:
- URL-based locale routing (`/en/*`, `/ar/*`)
- Translation files in `messages/` directory
- RTL layout automatically applied for Arabic
- Use `useTranslations()` hook for translations

### Testing

Currently no automated testing framework is configured. Consider implementing:
- Jest or Vitest for unit tests
- React Testing Library for component tests
- Playwright for E2E tests

### Payment & Transaction Handling

1. **Subscription Payments**:
   - Support for full and partial payments
   - Streamlined payment recording without installments
   - Automatic income transaction creation
   - Real-time payment tracking and statistics

2. **Member Transactions**:
   - Direct linking between payments and transactions
   - Category-based transaction tracking
   - Detailed payment records with history
   - Member-specific transaction statistics

### Important Conventions

1. **File Naming**: Use kebab-case for files and folders
2. **Component Naming**: Use PascalCase for React components
3. **API Responses**: Always return consistent format:
   ```json
   {
     "success": true/false,
     "data": {},
     "error": "Error message if applicable"
   }
   ```
4. **Error Handling**: Use try-catch in API routes with proper error messages
5. **Permissions**: Check permissions using `checkPermission()` utility
6. **File Uploads**: Store in `public/uploads/` with proper subfolder structure

### Common Tasks

1. **Adding a new module**: Create folder in `dashboard/`, add API routes, update permissions
2. **Adding translations**: Update both `messages/en.json` and `messages/ar.json`
3. **Database changes**: Create migration with `npx prisma migrate dev`
4. **Adding permissions**: Update seed files and run `npm run db:seed-users`
5. **Debugging auth**: Use `/dashboard/debug-auth` and `/dashboard/debug-permissions` pages

### Security Considerations

- Never commit `.env` files
- Use environment variables for sensitive data
- Validate all user inputs with Zod
- Sanitize file uploads
- Check permissions on all sensitive operations
- Use bcrypt for password hashing (already configured)

### Performance Tips

- Use React Server Components where possible
- Implement proper loading states
- Use `useMemo` and `useCallback` for expensive operations
- Consider implementing Redis for caching (not currently configured)
- Optimize images and use Next.js Image component

### Known Issues & Limitations

- No automated testing framework
- No error monitoring (consider Sentry)
- No API documentation (consider OpenAPI/Swagger)
- Limited error boundaries implementation
- No caching layer configured