import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

/** @type {import('next').NextConfig} */
const nextConfig = {
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Workaround for Jest worker child process exceptions
  experimental: {
    workerThreads: false,
    cpus: 1,
    webpackBuildWorker: false,
  },
  // Alternative webpack configuration to limit parallelism and memory
  webpack: (config, { isServer, dev }) => {
    // Limit parallelism to prevent worker issues
    config.parallelism = 1;
    
    // Disable source maps in production to reduce memory usage
    if (!dev) {
      config.devtool = false;
    }
    
    // Limit memory usage for webpack
    config.performance = {
      ...config.performance,
      maxAssetSize: 5000000,
      maxEntrypointSize: 5000000,
    };
    
    // Disable minimization temporarily to reduce memory usage
    if (!isServer) {
      config.optimization = {
        ...config.optimization,
        minimize: false,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            default: false,
            vendors: false,
          },
        },
      };
    }
    
    return config;
  },
  // Reduce memory usage
  productionBrowserSourceMaps: false,
  async redirects() {
    return [
      // Redirect /dashboard to default dashboard
      {
        source: "/:locale/dashboard",
        destination: "/:locale/dashboard/default",
        permanent: false,
      },
    ];
  },
}

export default withNextIntl(nextConfig);
