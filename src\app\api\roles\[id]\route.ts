import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { roleFormSchema } from "@/types/user";

// GET /api/roles/[id] - Get a specific role
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid role ID" },
        { status: 400 }
      );
    }

    const role = await db.role.findUnique({
      where: { id },
      include: {
        role_permissions: {
          include: {
            permission: true,
          },
        },
        _count: {
          select: {
            user_roles: true,
          },
        },
      },
    });

    if (!role) {
      return NextResponse.json(
        { error: "Role not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(role);
  } catch (error) {
    console.error("Error fetching role:", error);
    return NextResponse.json(
      { error: "Failed to fetch role" },
      { status: 500 }
    );
  }
}

// PUT /api/roles/[id] - Update a specific role
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid role ID" },
        { status: 400 }
      );
    }

    const body = await request.json();
    
    // Validate the request body
    const validatedData = roleFormSchema.parse(body);

    // Check if role exists
    const existingRole = await db.role.findUnique({
      where: { id },
    });

    if (!existingRole) {
      return NextResponse.json(
        { error: "Role not found" },
        { status: 404 }
      );
    }

    // Check if it's a system role
    if (existingRole.is_system) {
      return NextResponse.json(
        { error: "Cannot modify system roles" },
        { status: 400 }
      );
    }

    // Check for unique name constraint if updating name
    if (validatedData.name !== existingRole.name) {
      const nameExists = await db.role.findUnique({
        where: { name: validatedData.name },
      });

      if (nameExists) {
        return NextResponse.json(
          { error: "A role with this name already exists" },
          { status: 400 }
        );
      }
    }

    // Update the role
    const role = await db.role.update({
      where: { id },
      data: {
        name: validatedData.name,
        description: validatedData.description || null,
      },
    });

    // Update permissions
    if (validatedData.permissions !== undefined) {
      // Remove existing permissions
      await db.rolePermission.deleteMany({
        where: { role_id: id },
      });

      // Add new permissions
      if (validatedData.permissions.length > 0) {
        for (const modulePermission of validatedData.permissions) {
          for (const action of modulePermission.actions) {
            // Find or create permission
            const permission = await db.permission.upsert({
              where: {
                module_action: {
                  module: modulePermission.module,
                  action: action,
                },
              },
              update: {},
              create: {
                module: modulePermission.module,
                action: action,
                description: `${action.toLowerCase()} access for ${modulePermission.module}`,
              },
            });

            // Create role permission
            await db.rolePermission.create({
              data: {
                role_id: id,
                permission_id: permission.id,
              },
            });
          }
        }
      }
    }

    // Fetch the complete updated role
    const completeRole = await db.role.findUnique({
      where: { id },
      include: {
        role_permissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    return NextResponse.json(completeRole);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating role:", error);
    return NextResponse.json(
      { error: "Failed to update role" },
      { status: 500 }
    );
  }
}

// DELETE /api/roles/[id] - Delete a specific role
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid role ID" },
        { status: 400 }
      );
    }

    // Check if role exists
    const existingRole = await db.role.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            user_roles: true,
          },
        },
      },
    });

    if (!existingRole) {
      return NextResponse.json(
        { error: "Role not found" },
        { status: 404 }
      );
    }

    // Check if it's a system role
    if (existingRole.is_system) {
      return NextResponse.json(
        { error: "Cannot delete system roles" },
        { status: 400 }
      );
    }

    // Check if role is assigned to any users
    if (existingRole._count.user_roles > 0) {
      return NextResponse.json(
        { error: "Cannot delete role that is assigned to users" },
        { status: 400 }
      );
    }

    // Delete the role (cascade will handle related records)
    await db.role.delete({
      where: { id },
    });

    return NextResponse.json({
      message: "Role deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting role:", error);
    return NextResponse.json(
      { error: "Failed to delete role" },
      { status: 500 }
    );
  }
}
