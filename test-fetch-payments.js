const fetch = require('node-fetch');
const jwt = require('jsonwebtoken');
const { PrismaClient } = require('./src/generated/prisma');
const prisma = new PrismaClient();

async function testFetchPayments() {
  try {
    // Get admin user
    const adminUser = await prisma.user.findFirst({
      where: { username: 'admin' }
    });
    
    if (!adminUser) {
      console.log('Admin user not found!');
      return;
    }
    
    // Create JWT token
    const token = jwt.sign(
      { id: adminUser.id, username: adminUser.username, email: adminUser.email },
      process.env.JWT_SECRET || 'your-secret-key-change-in-production',
      { expiresIn: '7d' }
    );
    
    // Fetch payments for association 1
    const response = await fetch('http://localhost:3000/api/owners-associations/1/subscription-payments', {
      headers: {
        'Cookie': `auth-token=${token}`,
        'Accept': 'application/json',
      }
    });
    
    console.log('Status:', response.status);
    
    const result = await response.json();
    console.log('Response:', JSON.stringify(result, null, 2));
    
    if (result.success && result.data.payments) {
      console.log(`\nFound ${result.data.payments.length} payments`);
      result.data.payments.forEach(payment => {
        console.log(`\n- Member: ${payment.member.full_name} (Unit ${payment.member.unit_number})`);
        console.log(`  Subscription: ${payment.subscription.name_en}`);
        console.log(`  Amount Due: ${payment.amount_due}`);
        console.log(`  Status: ${payment.status}`);
        console.log(`  Due Date: ${payment.due_date}`);
      });
      
      if (result.data.stats) {
        console.log('\nPayment Statistics:');
        console.log(`Total Due: ${result.data.stats.total_due}`);
        console.log(`Total Paid: ${result.data.stats.total_paid}`);
        console.log(`Outstanding: ${result.data.stats.total_remaining}`);
        console.log(`Collection Rate: ${result.data.stats.collection_rate}%`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testFetchPayments();