import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { maintenanceRequestSchema, maintenanceFilterSchema, generateRequestNumber } from "@/types/maintenance";
import { ApiResponseBuilder } from "@/lib/api-response";




import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
// GET /api/maintenance - List all maintenance requests with filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for maintenance
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "maintenance", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view maintenance");
    }

    const searchParams = request.nextUrl.searchParams;
    
    // Parse filters
    const filters = maintenanceFilterSchema.parse({
      property_id: searchParams.get("property_id") ? parseInt(searchParams.get("property_id")!) : undefined,
      unit_id: searchParams.get("unit_id") ? parseInt(searchParams.get("unit_id")!) : undefined,
      tenant_id: searchParams.get("tenant_id") ? parseInt(searchParams.get("tenant_id")!) : undefined,
      status: searchParams.get("status") || undefined,
      priority: searchParams.get("priority") || undefined,
      category: searchParams.get("category") || undefined,
      assigned_to: searchParams.get("assigned_to") ? parseInt(searchParams.get("assigned_to")!) : undefined,
      date_from: searchParams.get("date_from") || undefined,
      date_to: searchParams.get("date_to") || undefined,
      search: searchParams.get("search") || undefined,
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
      pageSize: searchParams.get("pageSize") ? parseInt(searchParams.get("pageSize")!) : 10,
      sortBy: searchParams.get("sortBy") || "created_at",
      sortOrder: searchParams.get("sortOrder") || "desc",
    });

    // Build where clause
    const where: any = {};

    if (filters.property_id) {
      where.property_id = filters.property_id;
    }

    if (filters.unit_id) {
      where.unit_id = filters.unit_id;
    }

    if (filters.tenant_id) {
      where.tenant_id = filters.tenant_id;
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.priority) {
      where.priority = filters.priority;
    }

    if (filters.category) {
      where.category = filters.category;
    }

    if (filters.assigned_to) {
      where.assigned_to = filters.assigned_to;
    }

    if (filters.date_from || filters.date_to) {
      where.reported_date = {};
      if (filters.date_from) {
        where.reported_date.gte = new Date(filters.date_from);
      }
      if (filters.date_to) {
        where.reported_date.lte = new Date(filters.date_to);
      }
    }

    if (filters.search) {
      where.OR = [
        { request_number: { contains: filters.search, mode: 'insensitive' } },
        { title: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
        { contractor_name: { contains: filters.search, mode: 'insensitive' } },
        { contractor_phone: { contains: filters.search, mode: 'insensitive' } },
        {
          property: {
            OR: [
              { name_en: { contains: filters.search, mode: 'insensitive' } },
              { name_ar: { contains: filters.search, mode: 'insensitive' } },
            ],
          },
        },
        {
          unit: {
            unit_number: { contains: filters.search, mode: 'insensitive' },
          },
        },
        {
          tenant: {
            OR: [
              { first_name: { contains: filters.search, mode: 'insensitive' } },
              { last_name: { contains: filters.search, mode: 'insensitive' } },
              { email: { contains: filters.search, mode: 'insensitive' } },
            ],
          },
        },
      ];
    }

    // Calculate pagination
    const page = filters.page || 1;
    const pageSize = filters.pageSize || 10;
    const skip = (page - 1) * pageSize;

    // Fetch maintenance requests with relations
    const [requests, total] = await Promise.all([
      db.maintenanceRequest.findMany({
        where,
        include: {
          property: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
              address_en: true,
              address_ar: true,
            },
          },
          unit: {
            select: {
              id: true,
              unit_number: true,
              unit_name_en: true,
              unit_name_ar: true,
              floor_number: true,
              rooms_count: true,
              bathrooms_count: true,
              area: true,
              rent_amount: true,
              status: true,
            },
          },
          tenant: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
              phone: true,
            },
          },
          reporter: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
          assignee: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
          attachments: {
            select: {
              id: true,
              file_name: true,
              file_path: true,
              mime_type: true,
              created_at: true,
            },
          },
          _count: {
            select: {
              attachments: true,
              status_history: true,
            },
          },
        },
        orderBy: filters.sortBy ? {
          [filters.sortBy]: filters.sortOrder || "desc",
        } : {
          created_at: "desc",
        },
        skip,
        take: pageSize,
      }),
      db.maintenanceRequest.count({ where }),
    ]);

    return ApiResponseBuilder.success(requests, {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error: any) {
    console.error("Error fetching maintenance requests:", error);
    
    // Handle Zod validation errors
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }
    
    // Handle specific database errors
    if (error instanceof Error) {
      if (error.message.includes("Invalid invocation")) {
        return ApiResponseBuilder.error("Database connection error", "DB_CONNECTION_ERROR", 500);
      }
      if (error.message.includes("P2025")) {
        return ApiResponseBuilder.error("Related records not found", "NOT_FOUND", 404);
      }
    }
    
    return ApiResponseBuilder.error(
      error.message || "Failed to fetch maintenance requests", 
      "INTERNAL_ERROR", 
      500
    );
  }
}

// POST /api/maintenance - Create a new maintenance request
export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has CREATE permission for maintenance
    const canCreate = hasPermission(userPermissions, "maintenance", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create maintenance");
    }

    const body = await request.json();
    const validatedData = maintenanceRequestSchema.parse(body);

    // Check if property exists
    const property = await db.property.findUnique({
      where: { id: validatedData.property_id },
    });

    if (!property) {
      return ApiResponseBuilder.error("Property not found", "NOT_FOUND", 404);
    }

    // Check if unit exists (if provided)
    if (validatedData.unit_id) {
      const unit = await db.unit.findUnique({
        where: { id: validatedData.unit_id },
      });

      if (!unit || unit.property_id !== validatedData.property_id) {
        return ApiResponseBuilder.error("Unit not found or does not belong to the specified property", "NOT_FOUND", 404);
      }
    }

    // Check if tenant exists (if provided)
    if (validatedData.tenant_id) {
      const tenant = await db.tenant.findUnique({
        where: { id: validatedData.tenant_id },
      });

      if (!tenant) {
        return ApiResponseBuilder.error("Tenant not found", "NOT_FOUND", 404);
      }
    }

    // Generate request number
    const requestNumber = generateRequestNumber();

    // Create maintenance request
    const maintenanceRequest = await db.maintenanceRequest.create({
      data: {
        request_number: requestNumber,
        property_id: validatedData.property_id,
        unit_id: validatedData.unit_id,
        tenant_id: validatedData.tenant_id,
        title: validatedData.title,
        description: validatedData.description,
        priority: validatedData.priority,
        category: validatedData.category,
        scheduled_date: validatedData.scheduled_date ? new Date(validatedData.scheduled_date) : null,
        estimated_cost: validatedData.estimated_cost,
        contractor_name: validatedData.contractor_name,
        contractor_phone: validatedData.contractor_phone,
        internal_notes: validatedData.internal_notes,
        reported_by: decoded.id,
        created_by: decoded.id,
        updated_by: decoded.id,
        status_history: {
          create: {
            from_status: null,
            to_status: "REPORTED",
            notes: "Maintenance request created",
            changed_by: decoded.id,
          },
        },
      },
      include: {
        property: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            address_en: true,
            address_ar: true,
          },
        },
        unit: {
          select: {
            id: true,
            unit_number: true,
            unit_name_en: true,
            unit_name_ar: true,
            floor_number: true,
            rooms_count: true,
            bathrooms_count: true,
            area: true,
            rent_amount: true,
            status: true,
          },
        },
        tenant: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            phone: true,
          },
        },
        reporter: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    return ApiResponseBuilder.success(maintenanceRequest);
  } catch (error: any) {
    console.error("Error creating maintenance request:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to create maintenance request", "INTERNAL_ERROR", 500);
  }
}