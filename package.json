{"name": "cloudtech", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "dev:optimized": "next dev", "build": "set NODE_OPTIONS=--max-old-space-size=4096 && next build", "build:safe": "set NODE_OPTIONS=--max-old-space-size=8192 && set NEXT_PRIVATE_WORKER_THREADS=false && next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky", "generate:presets": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' src/scripts/generate-theme-presets.ts", "db:seed": "ts-node --project src/scripts/tsconfig.json src/scripts/seed.ts", "db:seed-users": "ts-node --project prisma/tsconfig.json prisma/seed-users-standalone.ts", "db:seed-property-types": "ts-node --project src/scripts/tsconfig.json src/scripts/seed-property-types.ts", "db:seed-minimal": "ts-node --project prisma/tsconfig.json prisma/minimal-seed.ts", "db:seed-comprehensive": "ts-node --project prisma/tsconfig.json prisma/comprehensive-seed.ts", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:reset": "npx prisma migrate reset --force"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix"]}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.12.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@types/bcryptjs": "^2.4.6", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "embla-carousel-react": "^8.6.0", "eslint-plugin-unicorn": "^56.0.1", "input-otp": "^1.4.2", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "mysql2": "^3.14.2", "next": "^15.4.3", "next-auth": "^4.24.11", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "prisma": "^6.12.0", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.4", "rtl-detect": "^1.1.2", "simple-icons": "^15.7.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tough-cookie": "^5.1.2", "vaul": "^1.1.2", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.16.0", "@tailwindcss/postcss": "^4.1.11", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.16.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/rtl-detect": "^1.0.3", "@typescript-eslint/parser": "^8.26.0", "eslint": "^9.31.0", "eslint-config-next": "^15.4.3", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-security": "^3.0.1", "eslint-plugin-sonarjs": "^3.0.4", "eslint-plugin-unused-imports": "^4.1.4", "glob": "^11.0.3", "globals": "^15.15.0", "husky": "^9.1.7", "lint-staged": "^15.5.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.5", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}}