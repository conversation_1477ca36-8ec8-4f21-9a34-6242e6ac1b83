"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useTranslations, useLocale } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { Loader2, Building2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useRTL } from "@/hooks/use-rtl";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  ownersAssociationSchema, 
  type OwnersAssociationInput, 
  type OwnersAssociationWithRelations 
} from "@/types/owners-association";
import { apiClient } from "@/lib/api-client";

interface Property {
  id: number;
  name_en: string;
  name_ar: string;
  address_en: string;
  address_ar: string;
}

interface OwnersAssociationFormProps {
  association?: OwnersAssociationWithRelations;
  isEdit?: boolean;
}

export function OwnersAssociationForm({ association, isEdit = false }: OwnersAssociationFormProps) {
  const router = useRouter();
  const locale = useLocale();
  const { isRTL } = useRTL();
  const t = useTranslations("ownersAssociations.form");
  const tButtons = useTranslations("ownersAssociations.form.buttons");
  const tValidation = useTranslations("ownersAssociations.form.validation");
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [properties, setProperties] = useState<Property[]>([]);
  const [loadingProperties, setLoadingProperties] = useState(true);

  const form = useForm<OwnersAssociationInput>({
    resolver: zodResolver(ownersAssociationSchema),
    defaultValues: {
      name_en: association?.name_en || "",
      name_ar: association?.name_ar || "",
      property_id: association?.property_id || undefined,
      establishment_date: association?.establishment_date ? 
        new Date(association.establishment_date).toISOString().split('T')[0] : "",
      president_name: association?.president_name || "",
      management_term_duration: association?.management_term_duration || 12,
      contact_email: association?.contact_email || "",
      contact_phone: association?.contact_phone || "",
    },
  });

  // Fetch properties for dropdown
  useEffect(() => {
    const fetchProperties = async () => {
      try {
        setLoadingProperties(true);
        const response = await apiClient.properties.list();
        setProperties(response.data || []);
      } catch (error) {
        console.error("Error fetching properties:", error);
        toast.error(t("messages.propertiesLoadError"));
      } finally {
        setLoadingProperties(false);
      }
    };

    fetchProperties();
  }, [t]);

  const onSubmit = async (data: OwnersAssociationInput) => {
    try {
      setIsSubmitting(true);

      if (isEdit && association) {
        await apiClient.ownersAssociations.update(association.id, data);
        toast.success(t("messages.updateSuccess"));
      } else {
        await apiClient.ownersAssociations.create(data);
        toast.success(t("messages.createSuccess"));
      }

      router.push(`/${locale}/dashboard/owners-associations`);
      router.refresh();
    } catch (error) {
      console.error("Submit error:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : t("messages.genericError")
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
              <Building2 className="h-5 w-5" />
              {t("basicInfo")}
            </CardTitle>
            <CardDescription>
              {t("basicInfoDescription")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="name_en"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("nameEn")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder={t("placeholders.nameEn")}
                        disabled={isSubmitting}
                        dir="ltr"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="name_ar"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("nameAr")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder={t("placeholders.nameAr")}
                        disabled={isSubmitting}
                        dir="rtl"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="property_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("property")}</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    value={field.value ? field.value.toString() : ""}
                    disabled={isSubmitting || loadingProperties}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={
                          loadingProperties 
                            ? t("placeholders.loadingProperties")
                            : t("placeholders.selectProperty")
                        } />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {properties.map((property) => (
                        <SelectItem key={property.id} value={property.id.toString()}>
                          <div className={cn("flex flex-col", isRTL && "items-end")}>
                            <span className="font-medium">
                              {locale === 'ar' ? property.name_ar : property.name_en}
                            </span>
                            <span className="text-sm text-muted-foreground">
                              {locale === 'ar' ? property.address_ar : property.address_en}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="establishment_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("establishmentDate")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="date"
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="management_term_duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("managementTermDuration")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        min="1"
                        placeholder={t("placeholders.managementTermDuration")}
                        disabled={isSubmitting}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      {t("managementTermDurationHelp")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="president_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("presidentName")}</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder={t("placeholders.presidentName")}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="contact_email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("contactEmail")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="email"
                        placeholder={t("placeholders.contactEmail")}
                        disabled={isSubmitting}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="contact_phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("contactPhone")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder={t("placeholders.contactPhone")}
                        disabled={isSubmitting}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <div className={cn(
          "flex items-center justify-end space-x-4",
          isRTL && "flex-row-reverse space-x-reverse"
        )}>
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            {tButtons("cancel")}
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className={cn("h-4 w-4 animate-spin", isRTL ? "ml-2" : "mr-2")} />
                {isEdit ? tButtons("updating") : tButtons("creating")}
              </>
            ) : (
              <>{isEdit ? tButtons("update") : tButtons("create")}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}