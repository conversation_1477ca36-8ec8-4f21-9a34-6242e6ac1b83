import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { invoiceSchema, calculateInvoiceTotals } from "@/types/invoice";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for invoices
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "invoices", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view invoices");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const invoiceId = parseInt(id);
    if (isNaN(invoiceId)) {
      return ApiResponseBuilder.error("Invalid invoice ID", "BAD_REQUEST", 400);
    }

    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        contract: true,
        tenant: true,
        property: true,
        unit: true,
        items: true,
        payments: {
          include: {
            creator: {
              select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
              },
            },
          },
        },
        allocations: {
          include: {
            payment: true,
          },
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    if (!invoice) {
      return ApiResponseBuilder.error("Invoice not found", "NOT_FOUND", 404);
    }

    return ApiResponseBuilder.success(invoice);
  } catch (error) {
    console.error("Error fetching invoice:", error);
    return ApiResponseBuilder.error("Failed to fetch invoice", "INTERNAL_ERROR", 500);
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has UPDATE permission for invoices
    const canUpdate = hasPermission(userPermissions, "invoices", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update invoices");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const invoiceId = parseInt(id);
    if (isNaN(invoiceId)) {
      return ApiResponseBuilder.error("Invalid invoice ID", "BAD_REQUEST", 400);
    }

    const body = await request.json();
    const validatedData = invoiceSchema.parse(body);

    // Check if invoice exists
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        payments: true,
      },
    });

    if (!existingInvoice) {
      return ApiResponseBuilder.error("Invoice not found", "NOT_FOUND", 404);
    }

    // Don't allow editing paid invoices
    if (existingInvoice.status === 'PAID') {
      return ApiResponseBuilder.error("Cannot edit a paid invoice", "BAD_REQUEST", 400);
    }

    // Don't allow editing invoices with payments
    if (existingInvoice.payments.length > 0 && existingInvoice.status !== 'DRAFT') {
      return ApiResponseBuilder.error("Cannot edit an invoice with payments. Please cancel the payments first.", "BAD_REQUEST", 400);
    }

    // Check if tenant exists
    const tenant = await prisma.tenant.findUnique({
      where: { id: validatedData.tenant_id },
    });

    if (!tenant) {
      return ApiResponseBuilder.error("Tenant not found", "NOT_FOUND", 404);
    }

    // Check if property exists
    const property = await prisma.property.findUnique({
      where: { id: validatedData.property_id },
    });

    if (!property) {
      return ApiResponseBuilder.error("Property not found", "NOT_FOUND", 404);
    }

    // Check if unit exists and belongs to the property
    const unit = await prisma.unit.findFirst({
      where: {
        id: validatedData.unit_id,
        property_id: validatedData.property_id,
      },
    });

    if (!unit) {
      return ApiResponseBuilder.error("Unit not found or doesn't belong to the selected property", "NOT_FOUND", 404);
    }

    // If contract_id is provided, verify it exists
    if (validatedData.contract_id) {
      const contract = await prisma.contract.findUnique({
        where: { id: validatedData.contract_id },
      });

      if (!contract) {
        return ApiResponseBuilder.error("Contract not found", "NOT_FOUND", 404);
      }
    }

    // Calculate totals from items
    const { total } = calculateInvoiceTotals(validatedData.items);

    // Update invoice in a transaction
    const updatedInvoice = await prisma.$transaction(async (tx) => {
      // Delete existing items
      await tx.invoiceItem.deleteMany({
        where: { invoice_id: invoiceId },
      });

      // Update invoice and create new items
      return await tx.invoice.update({
        where: { id: invoiceId },
        data: {
          invoice_number: validatedData.invoice_number || existingInvoice.invoice_number,
          contract_id: validatedData.contract_id,
          tenant_id: validatedData.tenant_id,
          property_id: validatedData.property_id,
          unit_id: validatedData.unit_id,
          invoice_date: new Date(validatedData.invoice_date),
          due_date: new Date(validatedData.due_date),
          original_amount: total,
          total_amount: total + parseFloat(existingInvoice.late_fee.toString()),
          balance_amount: total + parseFloat(existingInvoice.late_fee.toString()) - parseFloat(existingInvoice.paid_amount.toString()),
          status: validatedData.status,
          notes: validatedData.notes,
          updated_by: decoded.id,
          items: {
            create: validatedData.items.map(item => ({
              description_en: item.description_en,
              description_ar: item.description_ar,
              quantity: item.quantity,
              unit_price: parseFloat(item.unit_price),
              amount: parseFloat(item.unit_price) * item.quantity,
            })),
          },
        },
        include: {
          contract: true,
          tenant: true,
          property: true,
          unit: true,
          items: true,
          payments: true,
          allocations: true,
          creator: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
          updater: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
        },
      });
    });

    return ApiResponseBuilder.success(updatedInvoice);
  } catch (error) {
    console.error("Error updating invoice:", error);
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return ApiResponseBuilder.error("Invoice number already exists", "BAD_REQUEST", 400);
    }
    return ApiResponseBuilder.error("Failed to update invoice", "INTERNAL_ERROR", 500);
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has DELETE permission for invoices
    const canDelete = hasPermission(userPermissions, "invoices", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete invoices");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const invoiceId = parseInt(id);
    if (isNaN(invoiceId)) {
      return ApiResponseBuilder.error("Invalid invoice ID", "BAD_REQUEST", 400);
    }

    // Check if invoice exists
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        payments: true,
      },
    });

    if (!invoice) {
      return ApiResponseBuilder.error("Invoice not found", "NOT_FOUND", 404);
    }

    // Don't allow deletion of invoices with payments
    if (invoice.payments.length > 0) {
      return ApiResponseBuilder.error("Cannot delete an invoice with payments", "BAD_REQUEST", 400);
    }

    // Only allow deletion of draft or cancelled invoices
    if (!['DRAFT', 'CANCELLED'].includes(invoice.status)) {
      return ApiResponseBuilder.error("Only draft or cancelled invoices can be deleted", "BAD_REQUEST", 400);
    }

    // Delete invoice (cascade will handle items)
    await prisma.invoice.delete({
      where: { id: invoiceId },
    });

    return ApiResponseBuilder.success({ message: "Invoice deleted successfully" });
  } catch (error) {
    console.error("Error deleting invoice:", error);
    return ApiResponseBuilder.error("Failed to delete invoice", "INTERNAL_ERROR", 500);
  }
}