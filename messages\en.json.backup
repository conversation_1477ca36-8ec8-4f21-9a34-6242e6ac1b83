{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "confirm": "Confirm", "close": "Close", "yes": "Yes", "no": "No", "actions": "Actions", "view": "View", "success": "Success", "error": "Error", "unexpectedError": "An unexpected error occurred", "retry": "Retry", "columns": "Columns", "showing": "Showing {from} to {to} of {total} results", "page": "Page", "of": "of", "paymentMethods": {"cash": "Cash", "bankTransfer": "Bank Transfer", "check": "Check", "card": "Card"}, "createdAt": "Created At", "updatedAt": "Updated At", "status": "Status", "name": "Name", "contact": "Contact", "property": "Property", "quickCreate": "Quick Create", "inbox": "Inbox", "soon": "Soon", "unit": "Unit", "id": "ID", "email": "Email", "phone": "Phone", "more": "more", "viewDetails": "View Details", "system": "System", "selectAll": "Select all", "selectRow": "Select row", "statuses": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled"}, "navigation": {"dashboard": "Dashboard", "tenants": "Tenants", "properties": "Properties", "propertyTypes": "Property Types", "units": "Units", "contracts": "Contracts", "owners": "Owners", "amenities": "Amenities", "ownerPayouts": "Owner Payouts", "maintenance": "Maintenance", "invoices": "Invoices", "payments": "Payments", "reports": "Reports", "expenses": "Expenses", "users": "Users", "roles": "Roles", "settings": "Settings", "logout": "Logout", "profile": "Profile"}}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to Property Management", "overview": "Overview", "statistics": "Statistics", "recentActivity": "Recent Activity", "recentActivities": "Recent Activities", "quickActions": "Quick Actions", "totalRevenue": "Total Revenue", "monthlyRevenue": "Monthly Revenue", "totalProperties": "Total Properties", "occupancyRate": "Occupancy Rate", "units": "units", "activeContracts": "Active Contracts", "pendingMaintenance": "Pending Maintenance", "overdueInvoices": "Overdue Invoices", "fromLastMonth": "from last month", "revenueOverview": "Revenue Overview", "revenue": "Revenue", "expenses": "Expenses", "profit": "Profit", "comparison": "Comparison", "propertyOverview": "Property Overview", "occupied": "occupied", "issues": "issues", "upcomingPayments": "Upcoming Payments", "viewAll": "View All", "upcoming": "Upcoming", "overdue": "Overdue", "paid": "Paid", "maintenanceByCategory": "Maintenance by Category", "totalRequests": "Total Requests", "inProgress": "In Progress", "newContractSigned": "New Contract Signed", "maintenanceRequested": "Maintenance Requested", "paymentReceived": "Payment Received", "newTenantAdded": "New Tenant Added", "invoiceOverdue": "Invoice Overdue", "newCustomers": "New Customers", "activeAccounts": "Active Accounts", "growthRate": "Growth Rate", "totalVisitors": "Total Visitors", "trendingUp": "Trending up this month", "visitorsLast6Months": "Visitors for the last 6 months", "downThisPeriod": "Down 20% this period", "acquisitionAttention": "Acquisition needs attention", "strongRetention": "Strong user retention", "engagementTargets": "Engagement exceed targets", "steadyIncrease": "Steady performance increase", "meetsProjections": "Meets growth projections", "totalLast3Months": "Total for the last 3 months", "last3Months": "Last 3 months", "last30Days": "Last 30 days", "last7Days": "Last 7 days", "mobile": "Mobile", "desktop": "Desktop"}, "tenants": {"title": "Tenants", "description": "Manage your property tenants and lease information", "addTenant": "Add Tenant", "createTenant": "Add New Tenant", "createDescription": "Create a new tenant record for your property management system", "editTenant": "Edit Tenant", "editDescription": "Update tenant information and lease details", "deleteTenant": "Delete Tenant", "tenantDetails": "Tenant Details", "allTenants": "All Tenants", "viewManage": "View and manage all tenant records in the system", "backToTenants": "Back to Tenants", "table": {"id": "ID", "tenant": "Tenant", "name": "Name", "email": "Email", "phone": "Phone", "property": "Property", "leaseStart": "Lease Start", "leaseEnd": "Lease End", "monthlyRent": "Monthly Rent", "securityDeposit": "Security Deposit", "status": "Status", "actions": "Actions", "selectAll": "Select all", "selectRow": "Select row", "openMenu": "Open menu", "copyEmail": "Copy email", "viewDetails": "View details", "editTenant": "Edit tenant", "deleteTenant": "Delete tenant", "searchPlaceholder": "Search tenants...", "filterByStatus": "Filter by status", "filterByProperty": "Filter by property", "allStatuses": "All Statuses", "allProperties": "All Properties", "clearFilters": "Clear filters", "noResults": "No results.", "noTenantsFound": "No tenants found.", "loading": "Loading...", "loadingTenants": "Loading tenants...", "errorLoading": "Error loading tenants", "tryAgain": "Try again"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending"}, "form": {"personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone Number", "leaseInfo": "Lease Information", "property": "Property", "selectProperty": "Select property", "leaseStartDate": "Lease Start Date", "leaseEndDate": "Lease End Date", "monthlyRent": "Monthly Rent", "securityDeposit": "Security Deposit", "status": "Status", "selectStatus": "Select status", "placeholders": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+1234567890", "monthlyRent": "1500.00", "securityDeposit": "3000.00"}, "validation": {"firstNameRequired": "First name is required", "firstNameMin": "First name must be at least 2 characters", "lastNameRequired": "Last name is required", "lastNameMin": "Last name must be at least 2 characters", "emailRequired": "Email is required", "emailInvalid": "Invalid email address", "phoneInvalid": "Invalid phone number", "propertyRequired": "Property is required", "leaseStartRequired": "Lease start date is required", "leaseEndRequired": "Lease end date is required", "leaseEndAfterStart": "Lease end date must be after start date", "monthlyRentRequired": "Monthly rent is required", "monthlyRentPositive": "Monthly rent must be greater than 0", "securityDepositRequired": "Security deposit is required", "securityDepositPositive": "Security deposit must be greater than or equal to 0", "statusRequired": "Status is required"}, "buttons": {"save": "Save", "cancel": "Cancel", "create": "Create Tenant", "update": "Update Tenant", "createTenant": "Create Tenant", "updateTenant": "Update Tenant", "creating": "Creating...", "updating": "Updating...", "backToTenants": "Back to Tenants"}}, "details": {"tenantDetails": "Tenant Details", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "leaseInfo": "Lease Information", "propertyInfo": "Property Information", "financialInfo": "Financial Information", "systemInfo": "System Information", "additionalInfo": "Additional Information", "fullName": "Full Name", "email": "Email", "phone": "Phone", "property": "Property", "propertyType": "Property Type", "propertyAddress": "Property Address", "leaseStartDate": "Lease Start Date", "leaseEndDate": "Lease End Date", "startDate": "Start Date", "endDate": "End Date", "leaseDuration": "Lease Duration", "monthlyRent": "Monthly Rent", "securityDeposit": "Security Deposit", "totalPaid": "Total Paid", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "created": "Created", "lastUpdated": "Last Updated", "months": "months", "days": "days", "editTenant": "Edit Tenant", "backToTenants": "Back to Tenants"}, "delete": {"title": "Delete Tenant", "description": "Are you sure you want to delete", "warning": "This action cannot be undone and will permanently remove all tenant data.", "confirmButton": "Yes, delete", "cancelButton": "Cancel", "deleting": "Deleting...", "success": "Tenant deleted successfully", "error": "Failed to delete tenant", "notFound": "Tenant not found - it may have already been deleted", "conflict": "Cannot delete tenant - there are active dependencies", "serverError": "Server error occurred while deleting tenant"}, "messages": {"createSuccess": "Tenant created successfully", "createError": "Failed to create tenant", "updateSuccess": "Tenant updated successfully", "updateError": "Failed to update tenant", "loadError": "Failed to load tenant data", "propertiesLoadError": "Failed to load properties list", "emailCopied": "Email copied to clipboard", "emailCopyError": "Failed to copy email", "validationError": "Please correct the errors in the form", "networkError": "Network error, please try again", "serverError": "Server error, please try again later", "duplicateEmail": "Email address is already in use"}}, "users": {"title": "Users", "description": "Manage user accounts and access permissions", "addUser": "Add User", "createUser": "Create New User", "editUser": "Edit User", "deleteUser": "Delete User", "userDetails": "User Details", "allUsers": "All Users", "viewManage": "View and manage all user accounts in the system", "user": "User", "lastLogin": "Last Login", "noRoles": "No roles", "never": "Never", "copyEmail": "Copy email", "changePassword": "Change password"}, "roles": {"title": "Roles", "description": "Manage roles and permissions for the application", "addRole": "Add Role", "createRole": "Create Role", "createDescription": "Create a new role with specific permissions", "editRole": "Edit Role", "deleteRole": "Delete Role", "roleDetails": "Role Details", "allRoles": "All Roles", "viewManage": "View and manage all roles and their permissions", "permissions": "Permissions", "noPermissions": "No permissions", "user": "user", "copyRoleName": "Copy role name", "backToRoles": "Back to roles"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "email": "Email", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "signIn": "Sign in", "signUp": "Sign up", "welcome": "Welcome back", "loginToContinue": "Login to continue", "loginToAccount": "Login to your account", "enterCredentials": "Please enter your credentials to access your account", "emailPlaceholder": "Enter your email address", "passwordPlaceholder": "Enter your password", "rememberFor30Days": "Remember me for 30 days", "signingIn": "Signing in...", "loginFailed": "<PERSON><PERSON> failed", "invalidCredentials": "Invalid email or password", "accountLocked": "Account is locked", "tooManyAttempts": "Too many login attempts. Please try again later.", "networkError": "Network error. Please check your connection.", "serverError": "Server error. Please try again later.", "loginSuccess": "Login successful", "redirecting": "Redirecting to dashboard...", "noAccount": "Don't have an account?", "createAccount": "Create account", "backToLogin": "Back to login", "changeLanguage": "Change Language", "selectLanguage": "Select Language"}, "forms": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "passwordTooShort": "Password must be at least 8 characters", "passwordsDoNotMatch": "Passwords do not match", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "country": "Country", "postalCode": "Postal Code"}, "errors": {"somethingWentWrong": "Something went wrong", "tryAgain": "Please try again", "notFound": "Page not found", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "serverError": "Internal server error"}, "language": {"english": "English", "arabic": "العربية", "switchLanguage": "Switch Language"}, "ui": {"toggleSidebar": "Toggle Sidebar", "commandPalette": "Command Palette", "searchCommand": "Search for a command to run...", "quickCreate": "Quick Create", "inbox": "Inbox", "view": "View", "columns": "Columns", "addSection": "Add Section", "outline": "Outline", "pastPerformance": "Past Performance", "keyPersonnel": "Key Personnel", "focusDocuments": "Focus Documents", "selectAll": "Select all", "header": "Header", "sectionType": "Section Type", "status": "Status", "target": "Target", "limit": "Limit", "reviewer": "Reviewer", "noResults": "No results.", "of": "of", "rowsSelected": "{selected} of {total} row(s) selected", "rowsPerPage": "Rows per page", "page": "Page", "goToFirstPage": "Go to first page", "goToPreviousPage": "Go to previous page", "goToNextPage": "Go to next page", "goToLastPage": "Go to last page", "pageOf": "Page {page} of {totalPages}", "actions": "Actions", "openMenu": "Open menu", "clear": "Clear", "allStatus": "All Status", "allRoles": "All Roles", "active": "active", "inactive": "inactive", "never": "Never", "searchUsers": "Search users...", "searchTenants": "Search tenants...", "searchRoles": "Search roles...", "id": "ID", "user": "User", "email": "Email", "phone": "Phone", "roles": "Roles", "lastLogin": "Last Login", "created": "Created", "soon": "Soon", "statusInformation": "Status Information", "metadata": "<PERSON><PERSON><PERSON>", "updateExpenseDetails": "Update expense details for", "updateInstructions": "Update the expense information below. All required fields are marked with an asterisk.", "loadingCategories": "Loading categories...", "createInstructions": "Fill in the expense information below. All required fields are marked with an asterisk.", "createSubtitle": "Create a new expense entry with all necessary details", "totalAmount": "Total Amount", "thisMonth": "This Month", "pendingApproval": "Pending Approval", "pagination": {"rowsSelected": "{selected} of {total} row(s) selected", "rowsPerPage": "Rows per page", "page": "Page {current} of {total}", "goToFirstPage": "Go to first page", "goToPreviousPage": "Go to previous page", "goToNextPage": "Go to next page", "goToLastPage": "Go to last page"}}, "navigation": {"dashboard": "Dashboard", "dashboards": "Dashboards", "tenants": "Tenants", "properties": "Properties", "propertyTypes": "Property Types", "units": "Units", "amenities": "Amenities", "propertyOwners": "Property Owners", "ownerPayouts": "Owner Payouts", "contracts": "Contracts", "expenses": "Expenses", "maintenance": "Maintenance", "invoices": "Invoices", "payments": "Payments", "users": "Users", "roles": "Roles", "settings": "Settings", "logout": "Logout", "profile": "Profile", "default": "<PERSON><PERSON><PERSON>", "propertyManagement": "Property Management", "userManagement": "User Management", "misc": "Misc", "quickCreate": "Quick Create", "inbox": "Inbox"}, "expenses": {"title": "Expense Management", "subtitle": "Manage and track all business expenses", "allExpenses": "All Expenses", "viewManageExpenses": "View and manage all expense records", "addExpense": "Add Expense", "editExpense": "Edit Expense", "viewExpense": "View Expense", "deleteExpense": "Delete Expense", "expenseDetails": "Expense Details", "expenseList": "Expense List", "common": {"actions": "Actions"}, "noExpenses": "No expenses found", "searchExpenses": "Search expenses...", "filterExpenses": "<PERSON><PERSON>penses", "exportExpenses": "Export Expenses", "manageCategories": "Manage Categories", "generateReport": "Generate Report", "categories": {"title": "Expense Categories", "description": "Manage and organize expense categories", "addCategory": "Add Category", "addFirstCategory": "Add First Category", "editCategory": "Edit Category", "deleteCategory": "Delete Category", "active": "Active", "inactive": "Inactive", "noDescription": "No description", "createdAt": "Created At", "noCategories": "No categories found", "noCategoriesDescription": "Start by adding a category to organize your expenses", "nameEn": "English Name", "nameAr": "Arabic Name", "isActive": "Active", "sortOrder": "Sort Order", "expenseCount": "Expenses", "nameEnPlaceholder": "Enter category name in English", "nameArPlaceholder": "أد<PERSON>ل اسم الفئة بالعربية", "descriptionPlaceholder": "Enter category description (optional)", "isActiveDescription": "Enable this category for use in expenses", "sortOrderDescription": "Lower numbers appear first in lists", "addCategoryDescription": "Create a new expense category", "editCategoryDescription": "Update category information", "categoryDetails": "Category Details", "updateCategoryDetails": "Update Category Details", "fillCategoryDetails": "Fill in the category information below", "updateCategoryDetailsDescription": "Update the category information below", "createCategory": "Create Category", "updateCategory": "Update Category", "creating": "Creating...", "updating": "Updating...", "deleteConfirmation": "Are you sure you want to delete", "deleteWarning": "This action cannot be undone and will permanently remove the category.", "deleteWarningWithExpenses": "This category has {count} associated expenses and cannot be deleted.", "cannotDeleteWithExpenses": "Cannot delete category with associated expenses", "categoryNotFound": "Category not found", "deleteError": "Failed to delete category", "deleting": "Deleting...", "confirmDelete": "Yes, delete", "predefined": {"OFFICE_SUPPLIES": "Office Supplies", "FUEL_PETROL": "Fuel/Petrol", "MAINTENANCE_TOOLS": "Maintenance Tools", "UTILITIES": "Utilities", "MARKETING_ADVERTISING": "Marketing/Advertising", "SALARIES": "Salaries", "TRAVEL": "Travel", "INSURANCE": "Insurance", "LEGAL_PROFESSIONAL": "Legal/Professional Services", "OTHER": "Other"}}, "fields": {"date": "Date", "description": "Description", "amount": "Amount", "category": "Category", "paymentMethod": "Payment Method", "paidBy": "<PERSON><PERSON>", "notes": "Notes", "status": "Status", "isRecurring": "Recurring", "recurringFrequency": "Frequency", "nextDueDate": "Next Due Date", "attachments": "Attachments", "createdAt": "Created At", "updatedAt": "Updated At", "createdBy": "Created By", "approvedBy": "Approved By", "approvedAt": "Approved At"}, "placeholders": {"date": "Select date", "description": "Enter expense description", "amount": "0.000", "category": "Select category", "paymentMethod": "Select payment method", "paidBy": "Enter staff member name", "notes": "Additional notes (optional)", "recurringFrequency": "Select frequency"}, "paymentMethods": {"CASH": "Cash", "BANK_TRANSFER": "Bank Transfer", "CREDIT_CARD": "Credit Card", "DEBIT_CARD": "Debit Card", "CHECK": "Check", "OTHER": "Other"}, "statuses": {"PENDING": "Pending", "APPROVED": "Approved", "REJECTED": "Rejected"}, "frequencies": {"MONTHLY": "Monthly", "QUARTERLY": "Quarterly", "YEARLY": "Yearly"}, "actions": {"approve": "Approve", "reject": "Reject", "requestChanges": "Request Changes", "duplicate": "Duplicate", "viewAttachments": "View Attachments", "downloadAttachment": "Download", "uploadAttachment": "Upload File"}, "reports": {"title": "Expense Reports", "printReport": "Print Report", "expenseReport": "Expense Report", "generatingReport": "Generating Report...", "reportGenerated": "Report Generated Successfully", "reportError": "Error Generating Report", "reportHeader": "Expense Report", "generatedOn": "Generated On", "dateRange": "Date Range", "appliedFilters": "Applied Filters", "noFiltersApplied": "No Filters Applied", "summary": "Summary", "totalExpenses": "Total Expenses", "grandTotal": "Grand Total", "paymentMethodBreakdown": "Payment Method Breakdown", "statusBreakdown": "Status Breakdown", "categoryBreakdown": "Category Breakdown", "groupedByCategory": "Grouped by Category", "groupedByDate": "Grouped by Date", "subtotal": "Subtotal", "noExpensesFound": "No Expenses Found", "emptyReport": "Empty Report - No Data to Display"}, "validation": {"dateRequired": "Date is required", "descriptionRequired": "Description is required", "descriptionMaxLength": "Description must be less than 255 characters", "amountRequired": "Amount is required", "amountPositive": "Amount must be positive", "categoryRequired": "Category is required", "paymentMethodRequired": "Payment method is required", "paidByRequired": "Paid by is required", "paidByMaxLength": "Paid by must be less than 100 characters", "statusRequired": "Status is required", "recurringFrequencyRequired": "Frequency is required for recurring expenses", "nameEnRequired": "English name is required", "nameArRequired": "Arabic name is required", "nameMaxLength": "Name must be less than 100 characters", "sortOrderNonNegative": "Sort order must be non-negative", "fileSize": "File size must be less than 10MB", "fileType": "Only JPG, PNG, and PDF files are allowed"}, "messages": {"createSuccess": "Expense created successfully", "updateSuccess": "Expense updated successfully", "deleteSuccess": "Expense deleted successfully", "approveSuccess": "Expense approved successfully", "rejectSuccess": "Expense rejected successfully", "categoryCreateSuccess": "Category created successfully", "categoryUpdateSuccess": "Category updated successfully", "categoryDeleteSuccess": "Category deleted successfully", "categoryCreateError": "Failed to create category", "categoryUpdateError": "Failed to update category", "fileUploadSuccess": "File uploaded successfully", "fileDeleteSuccess": "File deleted successfully", "reportGenerateSuccess": "Report generated successfully", "confirmDelete": "Are you sure you want to delete this expense?", "confirmCategoryDelete": "Are you sure you want to delete this category?", "confirmApprove": "Are you sure you want to approve this expense?", "confirmReject": "Are you sure you want to reject this expense?", "loadingError": "Failed to load expenses", "deleteError": "Failed to delete expense", "approveError": "Failed to approve expense", "rejectError": "Failed to reject expense", "categoriesLoadError": "Failed to load expense categories", "tryAgain": "Try Again", "errorLoadingExpenses": "Error Loading Expenses", "expenseNotFound": "Expense not found", "deleting": "Deleting...", "deleteWarning": "This action cannot be undone. This will permanently delete the expense and all associated data."}, "statistics": {"totalExpenses": "Total Expenses", "averageExpense": "Average Expense", "monthlySpending": "Monthly Spending", "categoryBreakdown": "Category Breakdown", "paymentMethodBreakdown": "Payment Method Breakdown", "statusBreakdown": "Status Breakdown", "recurringExpenses": "Recurring Expenses", "pendingApprovals": "Pending Approvals"}, "filters": {"category": "Category", "paymentMethod": "Payment Method", "dateRange": "Date Range", "allCategories": "All Categories", "allPaymentMethods": "All Payment Methods", "quickDatePresets": "Quick Date Presets", "customDateRange": "Custom Date Range", "fromDate": "From Date", "toDate": "To Date", "clearFilters": "Clear Filters", "applyFilters": "Apply Filters", "showFilters": "Show Filters", "hideFilters": "Hide Filters"}, "datePresets": {"today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "thisYear": "This Year", "custom": "Custom"}, "sorting": {"sortBy": "Sort By", "ascending": "Ascending", "descending": "Descending", "sortByDate": "Sort by Date", "sortByAmount": "Sort by Amount", "sortByDescription": "Sort by Description", "sortByCategory": "Sort by Category", "sortByPaymentMethod": "Sort by Payment Method", "sortByStatus": "Sort by Status", "sortByCreatedDate": "Sort by Created Date"}, "ui": {"pendingApproval": "Pending Approval", "thisMonth": "This Month", "totalAmount": "Total Amount"}}, "properties": {"title": "Properties", "description": "Manage your real estate properties and rentals", "allProperties": "All Properties", "viewManageProperties": "View and manage all properties", "addProperty": "Add Property", "editProperty": "Edit Property", "editDescription": "Update property information", "deleteProperty": "Delete Property", "propertyDetails": "Property Details", "delete": {"title": "Delete Property", "description": "Are you sure you want to delete the property \"{name}\"? This action cannot be undone.", "confirm": "Delete", "cancel": "Cancel", "deleting": "Deleting...", "success": "Property deleted successfully", "error": "Failed to delete property", "cannotDelete": "Cannot delete property with active contracts or units"}, "messages": {"createSuccess": "Property created successfully", "createError": "Failed to create property", "updateSuccess": "Property updated successfully", "updateError": "Failed to update property", "deleteError": "Failed to delete property", "loadError": "Failed to load property", "duplicateName": "A property with this name already exists"}, "details": {"basicInformation": "Basic Information", "address": "Address", "locationInformation": "Location Information", "propertyDetails": "Property Details", "financialInformation": "Financial Information", "amenities": "Amenities", "units": "Units", "noAmenities": "No amenities", "systemInformation": "System Information", "updatedAt": "Updated At"}, "propertyTypes": {"title": "Property Types", "description": "Manage different types of properties in your system", "addPropertyType": "Add Property Type", "allPropertyTypes": "All Property Types", "viewManage": "View and manage all property types in the system", "editPropertyType": "Edit Property Type", "deletePropertyType": "Delete Property Type", "createPropertyType": "Create Property Type", "createDescription": "Add a new property type to categorize your properties", "editDescription": "Update property type information", "delete": {"title": "Delete Property Type", "description": "Are you sure you want to delete the property type \"{name}\"? This action cannot be undone.", "confirmButton": "Delete", "cancelButton": "Cancel", "deleting": "Deleting...", "success": "Property type deleted successfully", "error": "Failed to delete property type", "cannotDelete": "Cannot delete property type with associated properties"}, "messages": {"createSuccess": "Property type created successfully", "createError": "Failed to create property type", "updateSuccess": "Property type updated successfully", "updateError": "Failed to update property type", "loadError": "Failed to load property type", "duplicateName": "A property type with this name already exists"}, "details": {"systemInformation": "System Information", "updatedAt": "Updated At"}, "table": {"id": "ID", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "descriptionEn": "Description (English)", "descriptionAr": "Description (Arabic)", "propertiesCount": "Properties", "createdAt": "Created At", "actions": "Actions", "selectAll": "Select all", "selectRow": "Select row", "openMenu": "Open menu", "viewDetails": "View Details", "editPropertyType": "Edit", "deletePropertyType": "Delete", "searchPlaceholder": "Search property types...", "loading": "Loading property types...", "errorLoading": "Failed to load property types", "notFound": "Property types not found", "serverError": "Server error occurred", "unknownError": "Unknown error occurred", "noResults": "No results found"}, "form": {"nameEn": "Name (English)", "nameAr": "Name (Arabic)", "descriptionEn": "Description (English)", "descriptionAr": "Description (Arabic)", "nameEnPlaceholder": "e.g., Villa", "nameArPlaceholder": "مثال: فيلا", "descriptionEnPlaceholder": "Enter description in English...", "descriptionArPlaceholder": "أدخل الوصف بالعربية...", "updateNamesDescription": "Update the property type names in both languages", "enterNamesDescription": "Enter the property type names in both languages", "optionalDescriptions": "Optional descriptions for the property type", "validation": {"nameEnRequired": "English name is required", "nameEnMin": "English name must be at least 2 characters", "nameArRequired": "Arabic name is required", "nameArMin": "Arabic name must be at least 2 characters"}, "buttons": {"save": "Save", "cancel": "Cancel", "create": "Create Property Type", "update": "Update Property Type", "creating": "Creating...", "updating": "Updating..."}}, "land": "Land", "building": "Building", "villa": "Villa", "compound": "Compound", "apartment": "Apartment", "shop": "Shop", "office": "Office"}, "status": {"available": "Available", "rented": "Rented", "underMaintenance": "Under Maintenance", "outOfService": "Out of Service"}, "fields": {"nameEn": "Name (English)", "nameAr": "Name (Arabic)", "addressEn": "Address (English)", "addressAr": "Address (Arabic)", "propertyType": "Property Type", "baseRent": "Base Rent", "totalArea": "Total Area", "floorsCount": "Number of Floors", "parkingSpaces": "Parking Spaces", "amenities": "Amenities"}, "amenities": {"title": "Amenities", "selectDescription": "Select amenities available in this property", "selected": "selected", "swimmingPool": "Swimming Pool", "gym": "Gym", "security": "Security", "parking": "Parking", "elevator": "Elevator", "garden": "Garden", "playground": "Playground", "masjid": "Masjid", "shoppingCenter": "Shopping Center", "publicTransport": "Public Transport", "internetWifi": "Internet/WiFi", "airConditioning": "Air Conditioning", "balcony": "Balcony", "kitchenAppliances": "Kitchen Appliances", "laundry": "<PERSON><PERSON><PERSON>", "storage": "Storage", "petFriendly": "Pet Friendly", "furnished": "Furnished", "maintenanceService": "Maintenance Service", "cctv": "CCTV"}, "table": {"id": "ID", "name": "Property Name", "address": "Address", "propertyType": "Type", "baseRent": "Rent", "status": "Status", "owner": "Owner", "totalArea": "Area", "floorsCount": "Floors", "parkingSpaces": "Parking", "amenities": "Amenities", "amenity": "Amenity", "units": "Units", "createdAt": "Created", "actions": "Actions", "selectAll": "Select all", "selectRow": "Select row", "openMenu": "Open menu", "viewDetails": "View Details", "editProperty": "Edit", "deleteProperty": "Delete", "searchPlaceholder": "Search properties...", "loading": "Loading properties...", "errorLoading": "Failed to load properties", "notFound": "Properties not found", "serverError": "Server error occurred", "unknownError": "Unknown error occurred", "noResults": "No results found", "filterByType": "Filter by type", "filterByStatus": "Filter by status", "allTypes": "All Types", "allStatuses": "All Statuses"}, "form": {"nameEn": "Name (English)", "nameAr": "Name (Arabic)", "addressEn": "Address (English)", "addressAr": "Address (Arabic)", "propertyType": "Property Type", "selectPropertyType": "Select a property type", "baseRent": "Base Rent (OMR)", "status": "Status", "selectStatus": "Select status", "totalArea": "Total Area (m²)", "floorsCount": "Number of Floors", "parkingSpaces": "Parking Spaces", "nameEnPlaceholder": "e.g., Sunset Villa", "nameArPlaceholder": "مثال: فيلا الغروب", "addressEnPlaceholder": "Enter address in English...", "addressArPlaceholder": "أدخل العنوان بالعربية...", "baseRentPlaceholder": "0.000", "totalAreaPlaceholder": "0.00", "floorsCountPlaceholder": "0", "parkingSpacesPlaceholder": "0", "hasUnits": "This property has multiple units", "hasUnitsDescription": "Check this if the property contains multiple rentable units that will be managed separately", "owner": "Property Owner", "selectOwner": "Select owner", "noOwner": "No owner assigned", "ownerDescription": "Select the property owner (optional)", "searchOwner": "Search owner...", "noOwnersFound": "No owners found.", "loadingOwners": "Loading owners...", "validation": {"nameEnRequired": "English name is required", "nameEnMin": "English name must be at least 2 characters", "nameArRequired": "Arabic name is required", "nameArMin": "Arabic name must be at least 2 characters", "addressEnRequired": "English address is required", "addressEnMin": "English address must be at least 5 characters", "addressArRequired": "Arabic address is required", "addressArMin": "Arabic address must be at least 5 characters", "propertyTypeRequired": "Property type is required", "baseRentRequired": "Base rent is required", "baseRentInvalid": "Invalid rent format", "statusRequired": "Status is required", "totalAreaInvalid": "Invalid area format", "floorsCountInvalid": "Floors must be a positive number", "parkingSpacesInvalid": "Parking spaces must be a positive number"}, "buttons": {"save": "Save", "cancel": "Cancel", "create": "Create Property", "update": "Update Property", "creating": "Creating...", "updating": "Updating..."}, "sections": {"basicInformation": "Basic Information", "basicInformationDescription": "Enter the property details in both languages", "location": "Location", "locationDescription": "Property address information", "financialDetails": "Financial & Property Details", "financialDetailsDescription": "Rent amount and property specifications", "amenities": "Amenities", "amenitiesDescription": "Select available amenities for this property", "units": "Property Units", "unitsDescription": "Configure if this property has multiple units"}}, "tabs": {"contracts": {"title": "Contracts", "description": "Manage rental contracts for this property and its units", "createContract": "Create Contract", "noContracts": "No contracts found", "noContractsDescription": "This property doesn't have any rental contracts yet. Create your first contract to get started.", "createFirstContract": "Create First Contract", "contractId": "Contract ID", "tenant": "Tenant", "unit": "Unit", "startDate": "Start Date", "endDate": "End Date", "monthlyRent": "Monthly Rent", "securityDeposit": "Security Deposit", "status": "Status", "actions": "Actions", "entireProperty": "Entire Property"}}}, "units": {"title": "Units", "description": "Manage property units and their details", "allUnits": "All Units", "viewManageUnits": "View and manage all units", "addUnit": "Add Unit", "editUnit": "Edit Unit", "editDescription": "Update unit information", "deleteUnit": "Delete Unit", "unitDetails": "Unit Details", "unitNotFound": "Unit Not Found", "noUnits": "No units found", "searchUnits": "Search units...", "filterByProperty": "Filter by property", "filterByType": "Filter by type", "filterByStatus": "Filter by status", "allProperties": "All Properties", "allTypes": "All Types", "allStatuses": "All Statuses", "fields": {"unitNumber": "Unit Number", "unitNameEn": "Unit Name (English)", "unitNameAr": "Unit Name (Arabic)", "floorNumber": "Floor Number", "property": "Property", "roomsCount": "Number of Rooms", "majalisCount": "Number of Majalis", "bathroomsCount": "Number of Bathrooms", "area": "Area (m²)", "rentAmount": "<PERSON><PERSON>ou<PERSON> (OMR)", "status": "Status", "descriptionEn": "Description (English)", "descriptionAr": "Description (Arabic)", "amenities": "Amenities"}, "status": {"available": "Available", "rented": "Rented", "underMaintenance": "Under Maintenance"}, "table": {"id": "ID", "unitNumber": "Unit #", "unitName": "Unit Name", "property": "Property", "rooms": "Rooms", "majalis": "<PERSON><PERSON>", "bathrooms": "Baths", "area": "Area", "rentAmount": "Rent", "status": "Status", "floorNumber": "Floor", "amenities": "Amenities", "createdAt": "Created At", "actions": "Actions", "selectAll": "Select all", "selectRow": "Select row", "openMenu": "Open menu", "viewDetails": "View Details", "editUnit": "Edit", "deleteUnit": "Delete", "viewUnit": "View unit", "deleteConfirmation": "Are you sure you want to delete unit", "deleteWarning": "This action cannot be undone and will permanently remove the unit.", "deleteWarningRented": "This unit is currently rented and cannot be deleted.", "cannotDeleteRented": "Cannot delete rented unit", "unitNotFound": "Unit not found", "deleteError": "Failed to delete unit", "deleting": "Deleting...", "confirmDelete": "Yes, delete", "loading": "Loading units...", "notFound": "Units not found", "serverError": "Server error occurred", "unknownError": "Unknown error occurred", "noResults": "No results found", "errorLoading": "Failed to load units", "searchPlaceholder": "Search units...", "filterByProperty": "Filter by property", "filterByStatus": "Filter by status", "filterByFloor": "Filter by floor", "allProperties": "All Properties", "allStatuses": "All Statuses", "allFloors": "All Floors", "groundFloor": "Ground Floor", "floor": "Floor {{number}}"}, "form": {"property": "Property", "selectProperty": "Select a property", "unitNumber": "Unit Number", "unitNumberPlaceholder": "e.g., A101", "unitNameEn": "Unit Name (English)", "unitNameEnPlaceholder": "Enter unit name in English", "unitNameAr": "Unit Name (Arabic)", "unitNameArPlaceholder": "أدخل اسم الوحدة بالعربية", "floorNumber": "Floor Number", "floorNumberPlaceholder": "0", "roomsCount": "Number of Rooms", "roomsCountPlaceholder": "0", "majalisCount": "Number of Majalis", "majalisCountPlaceholder": "0", "bathroomsCount": "Number of Bathrooms", "bathroomsCountPlaceholder": "0", "area": "Area (m²)", "areaPlaceholder": "0.00", "rentAmount": "<PERSON><PERSON>ou<PERSON> (OMR)", "rentAmountPlaceholder": "0.000", "status": "Status", "selectStatus": "Select status", "descriptionEn": "Description (English)", "descriptionEnPlaceholder": "Enter unit description in English...", "descriptionAr": "Description (Arabic)", "descriptionArPlaceholder": "Enter unit description in Arabic...", "amenities": "Amenities", "selectAmenities": "Select amenities", "areaDescription": "Unit area in square meters", "rentAmountDescription": "Monthly rent in OMR (3 decimal places)", "validation": {"propertyRequired": "Property is required", "unitNumberRequired": "Unit number is required", "unitNumberMax": "Unit number must not exceed 50 characters", "floorNumberInvalid": "Floor number must be a positive number", "roomsCountInvalid": "Rooms count must be a positive number", "majalisCountInvalid": "Majalis count must be a positive number", "bathroomsCountInvalid": "Bathrooms count must be a positive number", "areaInvalid": "Invalid area format", "rentAmountRequired": "Rent amount is required", "rentAmountInvalid": "Invalid rent format", "statusRequired": "Status is required"}, "buttons": {"save": "Save", "cancel": "Cancel", "create": "Create Unit", "update": "Update Unit", "creating": "Creating...", "updating": "Updating..."}, "sections": {"basicInformation": "Basic Information", "basicInformationDescription": "Enter unit basic details", "unitDetails": "Unit Details", "unitDetailsDescription": "Specify unit specifications and rent", "amenities": "Amenities", "amenitiesDescription": "Select available amenities for this unit"}}, "messages": {"createSuccess": "Unit created successfully", "createError": "Failed to create unit", "updateSuccess": "Unit updated successfully", "updateError": "Failed to update unit", "deleteSuccess": "Unit deleted successfully", "deleteError": "Failed to delete unit", "loadError": "Failed to load unit data", "loadPropertiesError": "Failed to load properties"}, "details": {"monthlyRent": "Monthly Rent", "floor": "Floor", "ground": "Ground", "rooms": "Rooms", "bathrooms": "Bathrooms", "unitNumber": "Unit Number", "totalArea": "Total Area", "majalis": "<PERSON><PERSON>", "description": "Description", "amenities": "Amenities", "createdBy": "Created By", "lastUpdatedBy": "Last Updated By", "propertyInformation": "Property Information", "propertyName": "Property Name", "propertyType": "Property Type", "propertyAddress": "Property Address", "propertyBaseRent": "Property Base Rent", "viewPropertyDetails": "View Property Details"}}, "contracts": {"title": "Contracts", "description": "Manage rental contracts", "addContract": "Add Contract", "newContract": "New Contract", "newContractDescription": "Create a new rental contract", "createTitle": "Create Contract", "createDescription": "Create a new rental contract", "editTitle": "Edit Contract", "editDescription": "Update contract details", "viewTitle": "Contract Details", "createSuccess": "Contract created successfully", "updateSuccess": "Contract updated successfully", "deleteSuccess": "Contract deleted successfully", "table": {"contractNumber": "Contract Number", "property": "Property", "unit": "Unit", "tenants": "Tenants", "period": "Period", "monthlyRent": "Monthly Rent", "status": "Status", "createdAt": "Created", "actions": "Actions", "selectAll": "Select all", "selectRow": "Select row", "openMenu": "Open menu", "viewDetails": "View details", "editContract": "Edit contract", "terminateContract": "Terminate contract", "renewContract": "Renew contract", "deleteContract": "Delete contract", "deleteConfirmation": "Are you sure you want to delete contract", "deleteWarning": "This action cannot be undone. All related data will be permanently deleted.", "deleteError": "Failed to delete contract", "deleting": "Deleting...", "confirmDelete": "Delete Contract", "to": "to", "expiringSoon": "Expiring Soon", "searchPlaceholder": "Search contracts...", "filterByStatus": "Filter by status", "allStatuses": "All statuses", "noResults": "No contracts found", "serverError": "Failed to load contracts", "statusBadges": {"DRAFT": "Draft", "ACTIVE": "Active", "EXPIRED": "Expired", "TERMINATED": "Terminated", "RENEWED": "Renewed"}}, "form": {"contractDetails": "Contract Details", "contractDetailsDescription": "Enter the basic contract information", "financialDetails": "Financial Details", "financialDetailsDescription": "Specify the financial terms of the contract", "additionalInfo": "Additional Information", "basicInformation": "Basic Information", "contractNumber": "Contract Number", "contractNumberDescription": "Leave empty to auto-generate", "property": "Property", "unit": "Unit", "tenants": "Tenants", "status": "Status", "selectProperty": "Select Property", "selectUnit": "Select Unit", "selectTenants": "Select Tenants", "addTenant": "Add Tenant", "removeTenant": "Re<PERSON><PERSON>", "primaryTenant": "Primary", "startDate": "Start Date", "endDate": "End Date", "monthlyRent": "Monthly Rent (OMR)", "paymentDueDay": "Payment Due Day", "paymentDueDayDescription": "Day of the month (1-31)", "securityDeposit": "Security Deposit (OMR)", "insuranceAmount": "Insurance Amount (OMR)", "insuranceAmountDescription": "Optional insurance amount", "insuranceDueDate": "Insurance Due Date", "contractStatus": "Contract Status", "autoRenew": "Auto Renew", "renewalNoticeDays": "Renewal Notice Days", "renewalNoticeDaysDescription": "Days before expiry to send renewal notice", "termsAndConditions": "Terms and Conditions", "notes": "Notes", "notesDescription": "Additional notes about the contract", "placeholders": {"contractNumber": "Enter contract number or leave empty", "property": "Select a property", "unit": "Select a unit", "tenants": "Select tenants", "status": "Select status"}, "statuses": {"DRAFT": "Draft", "ACTIVE": "Active", "EXPIRED": "Expired", "TERMINATED": "Terminated", "RENEWED": "Renewed"}, "buttons": {"save": "Save", "create": "Create Contract", "cancel": "Cancel", "saving": "Saving..."}, "validation": {"contractNumberRequired": "Contract number is required", "propertyRequired": "Property is required", "unitRequired": "Unit is required", "tenantRequired": "At least one tenant is required", "startDateRequired": "Start date is required", "endDateRequired": "End date is required", "endDateAfterStart": "End date must be after start date", "monthlyRentRequired": "Monthly rent is required", "monthlyRentPositive": "Monthly rent must be positive", "securityDepositRequired": "Security deposit is required", "securityDepositPositive": "Security deposit must be positive", "paymentDueDayRange": "Payment due day must be between 1 and 31"}}, "detail": {"contractInformation": "Contract Information", "propertyInformation": "Property Information", "tenantInformation": "Tenant Information", "financialInformation": "Financial Information", "paymentSchedule": "Payment Schedule", "contractDocuments": "Contract Documents", "statusHistory": "Status History", "actions": "Actions", "editContract": "Edit Contract", "terminateContract": "Terminate Contract", "renewContract": "Renew Contract", "generateInvoice": "Generate Invoice", "uploadDocument": "Upload Document", "downloadDocument": "Download", "deleteDocument": "Delete", "noDocuments": "No documents uploaded", "noStatusHistory": "No status changes recorded"}, "renew": {"title": "Renew Contract", "description": "Create a renewal for contract {contractNumber}", "currentContract": "Current Contract", "newContract": "New Contract Terms", "newEndDate": "New End Date", "newMonthlyRent": "New Monthly Rent (OMR)", "newSecurityDeposit": "New Security Deposit (OMR)", "newInsuranceAmount": "New Insurance Amount (OMR)", "newInsuranceDueDate": "New Insurance Due Date", "renewalNotes": "Renewal Notes", "confirmRenewal": "Confirm <PERSON>wal", "renewalSuccess": "Contract renewed successfully", "renewalError": "Failed to renew contract"}, "terminate": {"title": "Terminate Contract", "description": "Terminate contract {contractNumber}", "terminationDate": "Termination Date", "terminationReason": "Reason for Termination", "terminationNotes": "Additional Notes", "confirmTermination": "Confirm Termination", "terminationWarning": "This action cannot be undone. The contract will be terminated and the unit will be marked as available.", "terminationSuccess": "Contract terminated successfully", "terminationError": "Failed to terminate contract"}}, "owners": {"title": "Property Owners", "addOwner": "Add Owner", "editOwner": "Edit Owner", "deleteOwner": "Delete Owner", "ownerDetails": "Owner Details", "fields": {"nameEn": "Name (English)", "nameAr": "Name (Arabic)", "email": "Email", "phone": "Phone", "bankName": "Bank Name", "bankAccount": "Bank Account", "iban": "IBAN", "taxId": "Tax ID", "ownershipPercentage": "Ownership Percentage"}}, "maintenance": {"title": "Maintenance", "description": "Manage property maintenance requests", "allRequests": "All Maintenance Requests", "viewManageRequests": "View and manage all maintenance requests across your properties", "createRequest": "Create Request", "createTitle": "Create Maintenance Request", "createDescription": "Create a new maintenance request for a property", "editTitle": "Edit Maintenance Request", "editDescription": "Update maintenance request details", "requestDetails": "Request Details", "basicInformation": "Basic Information", "locationInformation": "Location Information", "schedulingAndCost": "Scheduling & Cost", "contractorInformation": "Contractor Information", "noResults": "No maintenance requests found", "searchPlaceholder": "Search by request number, title, property...", "allStatuses": "All Statuses", "allPriorities": "All Priorities", "allCategories": "All Categories", "requestNumber": "Request Number", "property": "Property", "unit": "Unit", "tenant": "Tenant", "category": "Category", "priority": "Priority", "assignedTo": "Assigned To", "reportedDate": "Reported Date", "scheduledDate": "Scheduled Date", "completedDate": "Completed Date", "estimatedCost": "Estimated Cost", "actualCost": "Actual Cost", "entireProperty": "Entire Property", "noTenant": "No tenant", "unassigned": "Unassigned", "details": "Details", "history": "History", "statusHistory": "Status History", "statusHistoryDescription": "Track the progress of this maintenance request", "noStatusHistory": "No status history available", "timeMetrics": "Time Metrics", "responseTime": "Response Time", "resolutionTime": "Resolution Time", "totalDuration": "Total Duration", "costInformation": "Cost Information", "scheduling": "Scheduling", "completionDate": "Completion Date", "changedBy": "Changed by", "confirmCancel": "Are you sure you want to cancel this maintenance request?", "cancel": "Cancel Request", "acknowledge": "Acknowledge", "startWork": "Start Work", "putOnHold": "Put on Hold", "markComplete": "Mark Complete", "resumeWork": "Resume Work", "updateStatus": "Update Status", "selectProperty": "Select a property", "selectUnit": "Select a unit", "selectTenant": "Select a tenant", "selectCategory": "Select a category", "selectPriority": "Select a priority", "loading": "Loading...", "pickDate": "Pick a date", "contractorName": "Contractor Name", "contractorPhone": "Contractor Phone", "internalNotes": "Internal Notes", "internalNotesDescription": "Notes visible only to staff", "internalNotesPlaceholder": "Add any internal notes about this request...", "resolutionNotes": "Resolution Notes", "estimatedCostDescription": "Estimated cost in OMR", "form": {"title": "Title", "description": "Description", "category": "Category", "priority": "Priority", "property": "Property", "unit": "Unit", "unitDescription": "Optional - Select if the issue is specific to a unit", "tenant": "Tenant", "tenantDescription": "Optional - Select the affected tenant", "scheduledDate": "Scheduled Date", "scheduledDateDescription": "When the maintenance work is scheduled", "contractorName": "Contractor Name", "contractorPhone": "Contractor Phone", "contractorDescription": "Optional - External contractor details", "estimatedCost": "Estimated Cost (OMR)", "estimatedCostDescription": "Estimated cost in Omani Rial", "internalNotes": "Internal Notes", "internalNotesDescription": "Internal notes visible only to staff", "enterDescription": "Enter the maintenance request details", "updateDescription": "Update the maintenance request details", "selectPropertyDescription": "Select the property and unit for this request", "loading": "Loading...", "noUnit": "No specific unit", "noTenant": "No tenant", "pickDate": "Pick a date", "loadDataError": "Failed to load data", "loadUnitsError": "Failed to load units", "sections": {"basicInfo": "Basic Information", "propertyInfo": "Property & Location", "contractorInfo": "Contractor & Cost"}, "buttons": {"create": "Create Request", "update": "Update Request", "cancel": "Cancel"}, "validation": {"titleRequired": "Title is required", "descriptionRequired": "Description is required", "propertyRequired": "Property is required", "invalidCost": "Invalid cost amount"}}, "messages": {"createSuccess": "Maintenance request created successfully", "updateSuccess": "Maintenance request updated successfully", "deleteSuccess": "Maintenance request deleted successfully", "cancelSuccess": "Maintenance request cancelled successfully", "statusUpdateSuccess": "Status updated successfully", "error": "An error occurred while processing your request", "statusUpdateError": "Failed to update status", "cancelError": "Failed to cancel request", "loadDataError": "Failed to load data"}, "statuses": {"reported": "Reported", "acknowledged": "Acknowledged", "in_progress": "In Progress", "on_hold": "On Hold", "completed": "Completed", "cancelled": "Cancelled"}, "priorities": {"low": "Low", "medium": "Medium", "high": "High", "emergency": "Emergency"}, "categories": {"electrical": "Electrical", "plumbing": "Plumbing", "hvac": "HVAC", "structural": "Structural", "appliances": "Appliances", "painting": "Painting", "cleaning": "Cleaning", "landscaping": "Landscaping", "security": "Security", "other": "Other"}}, "invoices": {"title": "Invoices", "description": "Description", "actions": {"create": "Create Invoice", "edit": "Edit Invoice", "delete": "Delete Invoice", "view": "View Invoice", "recordPayment": "Record Payment", "sendEmail": "Send Email", "download": "Download PDF"}, "status": {"pending": "Pending", "partiallyPaid": "Partially Paid", "paid": "Paid", "overdue": "Overdue", "cancelled": "Cancelled"}, "table": {"id": "ID", "invoiceNumber": "Invoice #", "tenant": "Tenant", "property": "Property", "unit": "Unit", "invoiceDate": "Invoice Date", "dueDate": "Due Date", "totalAmount": "Total", "paidAmount": "Paid", "balanceAmount": "Balance", "amount": "Amount", "status": "Status", "actions": "Actions", "selectAll": "Select all", "selectRow": "Select row", "openMenu": "Open menu", "viewDetails": "View Details", "editInvoice": "Edit", "deleteInvoice": "Delete", "recordPayment": "Record Payment", "searchPlaceholder": "Search invoices...", "loading": "Loading invoices...", "errorLoading": "Failed to load invoices", "notFound": "Invoices not found", "serverError": "Server error occurred", "unknownError": "Unknown error occurred", "noResults": "No results found", "filterByStatus": "Filter by status", "allStatuses": "All Statuses"}, "contract": "Contract", "contractDescription": "Select a contract to auto-fill property, unit and amount", "notes": "Notes", "terms": "Terms & Conditions", "items": "Invoice Items", "addItem": "Add Item", "itemDescription": "Description", "quantity": "Quantity", "rate": "Rate"}, "payments": {"title": "Payments", "description": "Track and manage rental payments", "allPayments": "All Payments", "viewManagePayments": "View and manage all payments", "actions": {"create": "Record Payment", "edit": "Edit Payment", "delete": "Delete Payment", "view": "View Payment", "generateReceipt": "Generate Receipt"}, "status": {"pending": "Pending", "completed": "Completed", "failed": "Failed", "refunded": "Refunded"}, "method": {"cash": "Cash", "bankTransfer": "Bank Transfer", "check": "Check", "card": "Card", "online": "Online Payment"}, "table": {"id": "ID", "paymentNumber": "Payment #", "tenant": "Tenant", "property": "Property", "unit": "Unit", "paymentDate": "Payment Date", "amount": "Amount", "method": "Method", "status": "Status", "reference": "Reference", "actions": "Actions", "selectAll": "Select all", "selectRow": "Select row", "openMenu": "Open menu", "viewDetails": "View Details", "editPayment": "Edit", "deletePayment": "Delete", "generateReceipt": "Generate Receipt", "searchPlaceholder": "Search payments...", "loading": "Loading payments...", "errorLoading": "Failed to load payments", "notFound": "Payments not found", "serverError": "Server error occurred", "unknownError": "Unknown error occurred", "noResults": "No results found", "filterByMethod": "Filter by method", "filterByStatus": "Filter by status", "allMethods": "All Methods", "allStatuses": "All Statuses"}, "selectInvoices": "Select Invoices to Pay", "noUnpaidInvoices": "No unpaid invoices found for this tenant", "notes": "Notes"}, "currency": {"omr": "OMR", "format": "OMR {amount}"}, "propertyOwners": {"title": "Property Owners", "description": "Manage property owners and their properties", "addOwner": "Add Owner", "createTitle": "Add Property Owner", "editTitle": "Edit Property Owner", "createDescription": "Add a new property owner to the system", "editDescription": "Update property owner information", "createSuccess": "Property owner created successfully", "updateSuccess": "Property owner updated successfully", "deleteSuccess": "Property owner deleted successfully", "searchPlaceholder": "Search property owners...", "noResults": "No property owners found", "manageProperties": "Manage Properties", "basicInformation": "Basic Information", "contactInformation": "Contact Information", "businessInformation": "Business Information", "bankingInformation": "Banking Information", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "email": "Email", "phone": "Phone", "mobile": "Mobile", "addressEn": "Address (English)", "addressAr": "Address (Arabic)", "taxId": "Tax ID", "bankName": "Bank Name", "bankAccountNumber": "Account Number", "bankIban": "IBAN", "managementFeePercentage": "Management Fee (%)", "managementFeeDescription": "Default management fee percentage for this owner", "notes": "Notes", "notesPlaceholder": "Add any additional notes about this property owner...", "table": {"id": "ID", "name": "Name", "contact": "Contact", "properties": "Properties", "managementFee": "Fee %", "status": "Status", "payouts": "Payouts", "actions": "Actions", "selectAll": "Select all", "selectRow": "Select row", "openMenu": "Open menu", "viewDetails": "View Details", "editOwner": "Edit", "deleteOwner": "Delete", "manageProperties": "Manage Properties", "searchPlaceholder": "Search owners...", "loading": "Loading owners...", "errorLoading": "Failed to load owners", "notFound": "Owners not found", "serverError": "Server error occurred", "unknownError": "Unknown error occurred", "noResults": "No results found"}, "status": {"active": "Active", "inactive": "Inactive"}, "delete": {"title": "Delete Property Owner", "description": "Are you sure you want to delete the owner \"{name}\"? This action cannot be undone.", "confirm": "Delete", "cancel": "Cancel", "deleting": "Deleting...", "success": "Property owner deleted successfully", "error": "Failed to delete property owner", "cannotDelete": "Cannot delete owner with active properties"}, "messages": {"createSuccess": "Property owner created successfully", "createError": "Failed to create property owner", "updateSuccess": "Property owner updated successfully", "updateError": "Failed to update property owner", "deleteSuccess": "Property owner deleted successfully", "deleteError": "Failed to delete property owner", "fetchError": "Failed to fetch property owner"}, "form": {"validation": {"nameEnRequired": "English name is required", "nameEnMin": "English name must be at least 2 characters", "nameArRequired": "Arabic name is required", "nameArMin": "Arabic name must be at least 2 characters", "emailInvalid": "Invalid email address", "phoneRequired": "Phone number is required"}, "buttons": {"save": "Save", "cancel": "Cancel", "create": "Create Owner", "update": "Update Owner", "creating": "Creating...", "updating": "Updating..."}}, "allOwners": "All Property Owners", "viewManage": "View and manage all property owners"}, "amenities": {"title": "Amenities", "description": "Manage property amenities and features", "addAmenity": "Add Amenity", "createTitle": "Add Amenity", "editTitle": "Edit <PERSON>", "viewDescription": "View amenity details", "createDescription": "Add a new amenity to the system", "editDescription": "Update amenity information", "createSuccess": "Amenity created successfully", "updateSuccess": "Amenity updated successfully", "deleteSuccess": "Amenity deleted successfully", "deleteTitle": "Delete Amenity", "deleteDescription": "Are you sure you want to delete the amenity \"{name}\"? This action cannot be undone.", "deleteWarning": "This amenity is currently assigned to {count} properties and cannot be deleted.", "searchPlaceholder": "Search amenities...", "noResults": "No amenities found", "amenities": "amenities", "basicInformation": "Basic Information", "usageInformation": "Usage Information", "nameEn": "Name (English)", "nameAr": "Name (Arabic)", "icon": "Icon", "iconDescription": "Select an icon to represent this amenity", "associatedProperties": "Associated Properties", "allAmenities": "All Amenities", "viewManageAmenities": "View and manage all property amenities"}, "ownerPayouts": {"title": "Owner Payouts", "description": "Manage payouts to property owners", "allPayouts": "All Payouts", "viewManagePayouts": "View and manage all owner payouts", "createPayout": "Create Payout", "createTitle": "Create Owner Payout", "editTitle": "Edit Owner Payout", "viewDescription": "View payout details and history", "createDescription": "Create a new payout for property owners", "editDescription": "Update payout information", "createSuccess": "Payout created successfully", "updateSuccess": "Payout updated successfully", "approveSuccess": "Payout approved successfully", "rejectSuccess": "Payout rejected successfully", "paymentSuccess": "Payout marked as paid successfully", "searchPlaceholder": "Search payouts...", "noResults": "No payouts found", "allStatuses": "All Statuses", "status": {"pending": "Pending", "approved": "Approved", "paid": "Paid", "cancelled": "Cancelled"}, "approve": "Approve", "reject": "Reject", "markAsPaid": "<PERSON> as <PERSON><PERSON>", "owner": "Property Owner", "selectOwner": "Select a property owner", "payoutNumber": "Payout Number", "payoutDate": "Payout Date", "periodStart": "Period Start", "periodEnd": "Period End", "period": "Period", "rentCollected": "<PERSON><PERSON> Collected", "managementFee": "Mgmt Fee", "netAmount": "Net Amount", "basicInformation": "Basic Information", "propertyDetails": "Property Details", "property": "Property", "totalRentCollected": "Total Rent Collected", "totalManagementFee": "Total Management Fee", "otherDeductions": "Other Deductions", "netPayoutAmount": "Net Payout Amount", "financialSummary": "Financial Summary", "paymentInformation": "Payment Information", "paymentMethod": "Payment Method", "referenceNumber": "Reference Number", "bankTransferRef": "Bank Transfer Reference", "notes": "Notes", "approvalTitle": "Approve or Reject Payout", "approvalDescription": "Review payout {payoutNumber} for {amount}", "action": "Action", "approvalNotes": "Notes (Optional)", "approvalNotesPlaceholder": "Add any notes about this decision...", "confirmApprove": "Confirm Approval", "confirmReject": "Confirm Rejection", "paymentTitle": "<PERSON> as <PERSON><PERSON>", "paymentDescription": "Process payment for payout {payoutNumber} of {amount} to {owner}", "paymentNotes": "Payment Notes", "paymentNotesPlaceholder": "Add any payment details...", "confirmPayment": "Confirm Payment", "ownerInformation": "Owner Information", "ownerName": "Owner Name", "email": "Email", "phone": "Phone", "bankDetails": "Bank Details", "payoutDetails": "Payout Details", "propertyBreakdown": "Property Breakdown", "statusHistory": "Status History", "created": "Created", "approved": "Approved", "paid": "Paid"}}