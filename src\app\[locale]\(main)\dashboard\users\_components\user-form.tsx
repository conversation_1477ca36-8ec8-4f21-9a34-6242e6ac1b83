"use client";

import { useTranslations } from "next-intl";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { toast } from "sonner";
import { z } from "zod";
import { AlertCircle, CheckCircle2, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { userFormSchema, USER_STATUS_VALUES, type UserWithRoles, type Role } from "@/types/user";
import { ErrorState, InlineErrorState, getErrorVariant } from "@/components/error-state";
import { Alert, AlertDescription } from "@/components/ui/alert";

type FormData = z.infer<typeof userFormSchema>;

interface UserFormProps {
  user?: UserWithRoles;
  mode: "create" | "edit";
}

export function UserForm({ user, mode }: UserFormProps) {
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations("users");
  const tCommon = useTranslations("common");
  const [isLoading, setIsLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [roles, setRoles] = useState<Role[]>([]);
  const [rolesError, setRolesError] = useState<string | null>(null);

  // Create schema based on mode - password is required only for create
  const formSchema = mode === "create" 
    ? userFormSchema.extend({
        password: z.string().min(6, "Password must be at least 6 characters"),
      })
    : userFormSchema.extend({
        password: z.string().optional().or(z.literal('')),
      });

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      username: user?.username || "",
      email: user?.email || "",
      first_name: user?.first_name || "",
      last_name: user?.last_name || "",
      phone: user?.phone || "",
      status: user?.status || "ACTIVE",
      password: "", // Always empty for security
      role_ids: user?.user_roles.map(ur => ur.role.id) || [],
    },
  });

  // Watch form state for real-time validation feedback
  const formState = form.formState;
  const { errors, isValid, isDirty } = formState;

  // Fetch roles for the multi-select
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        setRolesError(null);
        console.log("Fetching roles from /api/roles?pageSize=100");
        
        const response = await fetch("/api/roles?pageSize=100", {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        console.log("Response status:", response.status);
        console.log("Response ok:", response.ok);

        if (!response.ok) {
          const errorText = await response.text();
          console.error("Error response:", errorText);
          
          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch (e) {
            errorData = { error: errorText };
          }
          
          throw new Error(errorData?.error || `Failed to fetch roles: ${response.status}`);
        }

        const data = await response.json();
        console.log("Roles data:", data);
        setRoles(data.roles || []);
      } catch (error) {
        console.error("Error fetching roles:", error);
        console.error("Error stack:", error instanceof Error ? error.stack : "No stack");
        setRolesError(error instanceof Error ? error.message : t("messages.fetchError"));
      }
    };

    fetchRoles();
  }, [t]);

  // Clear submit error when form is modified
  useEffect(() => {
    if (submitError && isDirty) {
      setSubmitError(null);
    }
  }, [isDirty, submitError]);

  const onSubmit = async (data: FormData) => {
    try {
      setIsLoading(true);
      setSubmitError(null);
      setValidationErrors({});

      const url = mode === "create" ? "/api/users" : `/api/users/${user?.id}`;
      const method = mode === "create" ? "POST" : "PUT";

      // Prepare data for submission
      const submitData = { ...data };
      
      // Remove password if empty (for edit mode)
      if (mode === "edit" && !data.password) {
        delete submitData.password;
      }

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (e) {
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }

        // Handle validation errors
        if (response.status === 400 && errorData.details) {
          const fieldErrors: Record<string, string> = {};
          if (Array.isArray(errorData.details)) {
            errorData.details.forEach((detail: any) => {
              if (detail.path && detail.path.length > 0) {
                fieldErrors[detail.path[0]] = detail.message;
              }
            });
          }
          setValidationErrors(fieldErrors);

          // Create a more detailed error message
          const errorMessages = Array.isArray(errorData.details) 
            ? errorData.details.map((detail: any) => detail.message)
            : [errorData.error || 'Validation failed'];
          throw new Error(errorMessages.join(", "));
        }

        throw new Error(errorData.error || errorData.message || `Failed to ${mode} user`);
      }

      const result = await response.json();
      
      toast.success(
        mode === "create" 
          ? t("messages.createSuccess") 
          : t("messages.updateSuccess")
      );

      // Clear cache and redirect to user detail page
      router.refresh();
      setTimeout(() => {
        router.push(`/${locale}/dashboard/users/${result.id}`);
      }, 100);
    } catch (error) {
      console.error(`Error ${mode === "create" ? "creating" : "updating"} user:`, error);
      setSubmitError(error instanceof Error ? error.message : `Failed to ${mode} user`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="mx-auto max-w-2xl">
      <CardHeader>
        <CardTitle>
          {mode === "create" ? t("createNewUser") : `${t("editUser")}: ${user?.first_name} ${user?.last_name}`}
        </CardTitle>
        <CardDescription>
          {mode === "create" 
            ? t("createDescription")
            : t("editDescription")
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        {submitError && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

                {rolesError && (
          <InlineErrorState
            error={rolesError}
            variant="server"
            className="mb-6"
          />
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">{t("basicInformation")}</h3>
              
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="first_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("firstName")}</FormLabel>
                      <FormControl>
                        <Input placeholder={t("firstNamePlaceholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                      {validationErrors.first_name && (
                        <p className="text-sm text-destructive">{validationErrors.first_name}</p>
                      )}
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="last_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("lastName")}</FormLabel>
                      <FormControl>
                        <Input placeholder={t("lastNamePlaceholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                      {validationErrors.last_name && (
                        <p className="text-sm text-destructive">{validationErrors.last_name}</p>
                      )}
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("username")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("usernamePlaceholder")} {...field} />
                    </FormControl>
                    <FormMessage />
                    {validationErrors.username && (
                      <p className="text-sm text-destructive">{validationErrors.username}</p>
                    )}
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("email")}</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder={t("emailPlaceholder")} {...field} />
                    </FormControl>
                    <FormMessage />
                    {validationErrors.email && (
                      <p className="text-sm text-destructive">{validationErrors.email}</p>
                    )}
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("phoneOptional")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("phonePlaceholder")} {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                    {validationErrors.phone && (
                      <p className="text-sm text-destructive">{validationErrors.phone}</p>
                    )}
                  </FormItem>
                )}
              />
            </div>

            {/* Security */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">{t("security")}</h3>
              
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("password")} {mode === "edit" && t("leaveEmptyToKeepCurrent")}
                    </FormLabel>
                    <FormControl>
                      <Input 
                        type="password" 
                        placeholder={mode === "create" ? t("passwordPlaceholder") : t("newPasswordPlaceholder")} 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                    {validationErrors.password && (
                      <p className="text-sm text-destructive">{validationErrors.password}</p>
                    )}
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("status")}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("selectStatus")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {USER_STATUS_VALUES.map((status) => (
                          <SelectItem key={status} value={status}>
                            {t(`statuses.${status}`)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                    {validationErrors.status && (
                      <p className="text-sm text-destructive">{validationErrors.status}</p>
                    )}
                  </FormItem>
                )}
              />
            </div>

            {/* Role Assignment */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">{t("roles")}</h3>
              
              <FormField
                control={form.control}
                name="role_ids"
                render={() => (
                  <FormItem>
                    <FormLabel>{t("selectRoles")}</FormLabel>
                    <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                      {roles.map((role) => (
                        <FormField
                          key={role.id}
                          control={form.control}
                          name="role_ids"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={role.id}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(role.id)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...(field.value || []), role.id])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== role.id
                                            )
                                          )
                                    }}
                                  />
                                </FormControl>
                                <div className="space-y-1 leading-none">
                                  <FormLabel className="text-sm font-normal">
                                    {role.name}
                                  </FormLabel>
                                  {role.description && (
                                    <p className="text-xs text-muted-foreground">
                                      {role.description}
                                    </p>
                                  )}
                                </div>
                              </FormItem>
                            )
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                    {validationErrors.role_ids && (
                      <p className="text-sm text-destructive">{validationErrors.role_ids}</p>
                    )}
                  </FormItem>
                )}
              />
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isLoading}
              >
                {tCommon("cancel")}
              </Button>
              <Button type="submit" disabled={isLoading || !isValid}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {mode === "create" ? t("creating") : t("updating")}
                  </>
                ) : (
                  <>
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                    {mode === "create" ? t("createUser") : t("editUser")}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
