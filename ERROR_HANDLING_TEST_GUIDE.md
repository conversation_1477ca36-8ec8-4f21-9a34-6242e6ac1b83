# Error Handling Test Guide

This guide provides comprehensive testing scenarios for the enhanced error handling and validation feedback in the tenant management module.

## 🧪 Test Scenarios

### 1. Tenant List Page Error Handling

#### Network Errors
- **Test**: Disconnect internet while on tenant list page
- **Expected**: Network error message with retry button
- **Verify**: Error state shows "Connection problem" with appropriate icon

#### Server Errors
- **Test**: Temporarily break API endpoint (rename `/api/tenants` to `/api/tenants-broken`)
- **Expected**: Server error message with retry functionality
- **Verify**: User can retry and recover when endpoint is fixed

#### Properties Loading Error
- **Test**: Break `/api/properties` endpoint
- **Expected**: Inline error for properties filter with retry option
- **Verify**: Tenant list still works, only property filter is affected

#### Empty State
- **Test**: Clear all tenant data from database
- **Expected**: Empty state message (not an error)
- **Verify**: User can still access "Add Tenant" functionality

### 2. Form Validation Testing

#### Real-time Validation
- **Test**: Enter invalid email format in tenant form
- **Expected**: Red border, error icon, and inline error message
- **Verify**: Error clears when valid email is entered

#### Required Fields
- **Test**: Leave required fields empty and try to submit
- **Expected**: Form validation summary at top with list of errors
- **Verify**: Submit button is disabled until all required fields are valid

#### Date Validation
- **Test**: Set lease end date before start date
- **Expected**: Custom validation error for date range
- **Verify**: Error message is clear and actionable

#### Email Uniqueness
- **Test**: Try to create tenant with existing email
- **Expected**: Server validation error with specific message
- **Verify**: Error highlights the email field specifically

### 3. CRUD Operation Error Testing

#### Create Tenant Errors
```javascript
// Test duplicate email
POST /api/tenants
{
  "email": "<EMAIL>",
  // ... other fields
}
// Expected: 400 error with "email already exists" message
```

#### Update Tenant Errors
```javascript
// Test updating non-existent tenant
PUT /api/tenants/999999
// Expected: 404 error with "tenant not found" message
```

#### Delete Tenant Errors
```javascript
// Test deleting non-existent tenant
DELETE /api/tenants/999999
// Expected: 404 error with appropriate message in dialog
```

#### View Tenant Errors
- **Test**: Navigate to `/dashboard/tenants/999999`
- **Expected**: Error page with back button and retry option
- **Verify**: User can navigate back to tenant list

### 4. Loading States Testing

#### Form Loading
- **Test**: Submit tenant form and observe loading state
- **Expected**: Button shows spinner and "Creating..." text
- **Verify**: Form is disabled during submission

#### Data Table Loading
- **Test**: Refresh tenant list page
- **Expected**: Loading skeleton or spinner in table area
- **Verify**: Filters are disabled during loading

#### Retry Loading
- **Test**: Click retry button after error
- **Expected**: Retry button shows "Retrying..." state
- **Verify**: Different visual state from initial loading

### 5. Error Boundary Testing

#### Component Crashes
- **Test**: Temporarily add `throw new Error("Test error")` to TenantForm component
- **Expected**: Error boundary catches error and shows fallback UI
- **Verify**: User can retry or refresh without losing navigation

#### JavaScript Errors
- **Test**: Cause a JavaScript runtime error in tenant list
- **Expected**: Error boundary prevents white screen of death
- **Verify**: Error details shown in development mode only

### 6. User Experience Testing

#### Error Message Clarity
- **Test**: Review all error messages for user-friendliness
- **Expected**: No technical jargon, clear next steps
- **Verify**: Messages are actionable and helpful

#### Responsive Error States
- **Test**: View error states on mobile devices
- **Expected**: Error messages and buttons are touch-friendly
- **Verify**: Error dialogs fit properly on small screens

#### Theme Integration
- **Test**: Switch between light and dark themes
- **Expected**: Error states respect theme colors
- **Verify**: Error icons and colors are visible in both themes

## 🔧 Manual Testing Steps

### Step 1: Network Error Simulation
1. Open tenant list page
2. Open browser dev tools → Network tab
3. Set network to "Offline"
4. Try to refresh or filter tenants
5. Verify error message and retry functionality
6. Set network back to "Online"
7. Click retry button
8. Verify data loads successfully

### Step 2: Form Validation Testing
1. Navigate to "Add Tenant" page
2. Try to submit empty form
3. Verify validation summary appears
4. Fill fields one by one
5. Verify real-time validation feedback
6. Enter invalid email format
7. Verify email-specific error message
8. Set lease end date before start date
9. Verify custom date validation error

### Step 3: Server Error Simulation
1. Temporarily rename API route file
2. Try to perform CRUD operations
3. Verify appropriate error messages
4. Restore API route file
5. Verify retry functionality works

### Step 4: Delete Error Testing
1. Open delete confirmation dialog
2. Temporarily break delete API endpoint
3. Confirm deletion
4. Verify error appears in dialog
5. Verify user can retry or cancel
6. Fix API endpoint and retry

## 📊 Error Handling Checklist

### ✅ Tenant List Page
- [ ] Network error handling with retry
- [ ] Server error handling with retry
- [ ] Properties loading error (inline)
- [ ] Loading states for initial load
- [ ] Loading states for retry operations
- [ ] Error states maintain table structure
- [ ] Filters disabled during errors
- [ ] Search functionality error handling

### ✅ Form Validation
- [ ] Real-time field validation
- [ ] Visual validation feedback (icons, colors)
- [ ] Form validation summary
- [ ] Server-side validation errors
- [ ] Field-specific error highlighting
- [ ] Submit button state management
- [ ] Form disabled during submission
- [ ] Loading states with spinners

### ✅ CRUD Operations
- [ ] Create tenant error handling
- [ ] Update tenant error handling
- [ ] Delete tenant error handling
- [ ] View tenant error handling
- [ ] Specific error messages per operation
- [ ] Retry functionality where appropriate
- [ ] Navigation preserved during errors

### ✅ User Experience
- [ ] Error messages are user-friendly
- [ ] Error states are responsive
- [ ] Theme integration maintained
- [ ] Accessibility considerations
- [ ] Error recovery without page refresh
- [ ] Graceful degradation
- [ ] Error boundaries prevent crashes

### ✅ Technical Implementation
- [ ] Error logging for debugging
- [ ] Proper error type detection
- [ ] Validation error extraction
- [ ] Network error detection
- [ ] Server error categorization
- [ ] Client-side error handling
- [ ] Error state persistence

## 🚀 Testing Commands

```bash
# Start development server
npm run dev

# Test database connection
npm run db:generate

# Reset database for testing
npm run db:reset
npm run db:seed

# Check for TypeScript errors
npm run build
```

## 📝 Test Results Documentation

When testing, document:
1. **Error Scenario**: What was tested
2. **Expected Behavior**: What should happen
3. **Actual Behavior**: What actually happened
4. **Status**: ✅ Pass / ❌ Fail
5. **Notes**: Any additional observations

Example:
```
Scenario: Network error on tenant list
Expected: Show network error with retry button
Actual: ✅ Correct error message and retry works
Status: ✅ Pass
Notes: Error message is clear and actionable
```
