import { getTranslations } from "next-intl/server";
import { Metadata } from "next";
import { OwnersAssociationPageWrapper } from "./_components/owners-association-page-wrapper";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "ownersAssociations" });

  return {
    title: t("title"),
    description: t("description"),
  };
}

export default async function OwnersAssociationsPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  return <OwnersAssociationPageWrapper />;
}