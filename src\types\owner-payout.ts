import { z } from "zod";
import type {
  OwnerPayout,
  OwnerPayoutDetail,
  PropertyOwner,
  Property,
  User,
  PayoutStatus,
  PaymentMethod,
} from "@/generated/prisma";

// Owner Payout Types
export interface OwnerPayoutWithRelations extends OwnerPayout {
  owner?: PropertyOwner;
  approver?: User | null;
  payer?: User | null;
  creator?: User | null;
  payout_details?: OwnerPayoutDetailWithRelations[];
}

export interface OwnerPayoutDetailWithRelations extends OwnerPayoutDetail {
  payout?: OwnerPayout;
  property?: Property;
}

// Owner Payout Schemas
export const ownerPayoutSchema = z.object({
  owner_id: z.number().int().positive(),
  payout_date: z.string(), // ISO date string
  period_start: z.string(), // ISO date string
  period_end: z.string(), // ISO date string
  total_rent_collected: z.coerce.number().min(0),
  management_fee: z.coerce.number().min(0),
  other_deductions: z.coerce.number().min(0).default(0),
  net_amount: z.coerce.number().min(0),
  payment_method: z.enum(["CASH", "BANK_TRANSFER", "CHECK", "CREDIT_CARD", "DEBIT_CARD", "OTHER"]),
  reference_number: z.string().max(100).optional().nullable(),
  bank_transfer_ref: z.string().max(100).optional().nullable(),
  notes: z.string().optional().nullable(),
  status: z.enum(["PENDING", "APPROVED", "PAID", "CANCELLED"]).default("PENDING"),
  payout_details: z.array(z.object({
    property_id: z.number().int().positive(),
    rent_collected: z.coerce.number().min(0),
    management_fee: z.coerce.number().min(0),
    net_amount: z.coerce.number().min(0),
  })).optional(),
});

export const ownerPayoutApprovalSchema = z.object({
  action: z.enum(["APPROVE", "REJECT"]),
  notes: z.string().optional(),
});

export const ownerPayoutPaymentSchema = z.object({
  payment_method: z.enum(["CASH", "BANK_TRANSFER", "CHECK", "CREDIT_CARD", "DEBIT_CARD", "OTHER"]),
  reference_number: z.string().max(100).optional().nullable(),
  bank_transfer_ref: z.string().max(100).optional().nullable(),
  notes: z.string().optional(),
});

// Owner Payout Filter Schema
export const ownerPayoutFilterSchema = z.object({
  owner_id: z.number().int().positive().optional(),
  status: z.enum(["PENDING", "APPROVED", "PAID", "CANCELLED"]).optional(),
  payment_method: z.enum(["CASH", "BANK_TRANSFER", "CHECK", "CARD"]).optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  search: z.string().optional(),
  page: z.number().int().positive().default(1),
  pageSize: z.number().int().positive().max(100).default(10),
  sortBy: z.string().default("created_at"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// Type exports
export type OwnerPayoutInput = z.infer<typeof ownerPayoutSchema>;
export type OwnerPayoutApprovalInput = z.infer<typeof ownerPayoutApprovalSchema>;
export type OwnerPayoutPaymentInput = z.infer<typeof ownerPayoutPaymentSchema>;
export type OwnerPayoutFilters = z.infer<typeof ownerPayoutFilterSchema>;

// Helper functions
export function generatePayoutNumber(): string {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, "0");
  return `PAY-${year}${month}-${random}`;
}

export function calculatePayoutTotals(details: { rent_collected: number; management_fee: number; net_amount: number }[]): {
  total_rent_collected: number;
  total_management_fee: number;
  total_net_amount: number;
} {
  return details.reduce(
    (totals, detail) => ({
      total_rent_collected: totals.total_rent_collected + detail.rent_collected,
      total_management_fee: totals.total_management_fee + detail.management_fee,
      total_net_amount: totals.total_net_amount + detail.net_amount,
    }),
    { total_rent_collected: 0, total_management_fee: 0, total_net_amount: 0 }
  );
}

export function canApprovePayout(status: PayoutStatus): boolean {
  return status === "PENDING";
}

export function canPayPayout(status: PayoutStatus): boolean {
  return status === "APPROVED";
}

export function canCancelPayout(status: PayoutStatus): boolean {
  return status === "PENDING" || status === "APPROVED";
}