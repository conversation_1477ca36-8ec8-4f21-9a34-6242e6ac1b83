import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

// DELETE /api/maintenance/[id]/attachments/[attachmentId] - Delete an attachment
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; attachmentId: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for maintenance
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "maintenance", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete maintenance");
    }

    const { id, attachmentId: attachmentIdParam } = await params;


    const requestId = parseInt(id);
    const attachmentId = parseInt(attachmentIdParam);
    
    if (isNaN(requestId) || isNaN(attachmentId)) {
      return ApiResponseBuilder.error("Invalid request or attachment ID", "BAD_REQUEST", 400);
    }

    // Check if attachment exists and belongs to the maintenance request
    const attachment = await db.maintenanceAttachment.findFirst({
      where: {
        id: attachmentId,
        request_id: requestId,
      },
    });

    if (!attachment) {
      return ApiResponseBuilder.error("Attachment not found", "NOT_FOUND", 404);
    }

    // Check if maintenance request is still editable
    const maintenanceRequest = await db.maintenanceRequest.findUnique({
      where: { id: requestId },
    });

    if (!maintenanceRequest) {
      return ApiResponseBuilder.error("Maintenance request not found", "NOT_FOUND", 404);
    }

    if (maintenanceRequest.status === "COMPLETED" || maintenanceRequest.status === "CANCELLED") {
      return ApiResponseBuilder.error(
        "Cannot delete attachments from completed or cancelled requests",
        "BAD_REQUEST",
        400
      );
    }

    // Delete the attachment record
    const deletedAttachment = await db.maintenanceAttachment.delete({
      where: { id: attachmentId },
    });

    // TODO: Delete the actual file from storage
    
    return ApiResponseBuilder.success(deletedAttachment);
  } catch (error) {
    console.error("Error deleting maintenance attachment:", error);
    return ApiResponseBuilder.error("Failed to delete attachment", "INTERNAL_ERROR", 500);
  }
}