import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ApiResponseBuilder } from "@/lib/api-response";
import { z } from "zod";


const cancelSchema = z.object({
  reason: z.string().optional(),
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for payments
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "payments", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create payments");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const paymentId = parseInt(id);
    if (isNaN(paymentId)) {
      return ApiResponseBuilder.error("Invalid payment ID", "BAD_REQUEST", 400);
    }

    const body = await request.json();
    const validatedData = cancelSchema.parse(body);

    // Check if payment exists
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        allocations: true,
      },
    });

    if (!payment) {
      return ApiResponseBuilder.error("Payment not found", "NOT_FOUND", 404);
    }

    // Check if payment is already cancelled or refunded
    if (['CANCELLED', 'REFUNDED'].includes(payment.status)) {
      return ApiResponseBuilder.error(`Payment is already ${payment.status.toLowerCase()}`, "BAD_REQUEST", 400);
    }

    // Only pending or failed payments can be cancelled
    if (!['PENDING', 'FAILED'].includes(payment.status)) {
      return ApiResponseBuilder.error("Only pending or failed payments can be cancelled", "BAD_REQUEST", 400);
    }

    // Cancel the payment
    const cancelledPayment = await prisma.payment.update({
      where: { id: paymentId },
      data: {
        status: 'CANCELLED',
        notes: validatedData.reason 
          ? `${payment.notes ? payment.notes + "\n\n" : ""}Cancellation Reason: ${validatedData.reason}`
          : payment.notes,
      },
      include: {
        invoice: true,
        tenant: true,
        allocations: {
          include: {
            invoice: true,
          },
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    return ApiResponseBuilder.success({
      message: "Payment cancelled successfully",
      payment: cancelledPayment,
    });
  } catch (error) {
    console.error("Error cancelling payment:", error);
    return ApiResponseBuilder.error("Failed to cancel payment", "INTERNAL_ERROR", 500);
  }
}