import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { paymentSchema, paymentFilterSchema, generatePaymentNumber, validatePaymentAllocation } from "@/types/payment";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for payments
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "payments", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view payments");
    }

    // For now, allow public access to view payments
    // TODO: Add authentication when needed

    const searchParams = request.nextUrl.searchParams;
    
    // Parse filters
    const filters = paymentFilterSchema.parse({
      invoice_id: searchParams.get("invoice_id") ? parseInt(searchParams.get("invoice_id")!) : undefined,
      tenant_id: searchParams.get("tenant_id") ? parseInt(searchParams.get("tenant_id")!) : searchParams.get("tenantId") ? parseInt(searchParams.get("tenantId")!) : undefined,
      payment_method: searchParams.get("payment_method") || undefined,
      status: searchParams.get("status") || undefined,
      date_from: searchParams.get("date_from") || undefined,
      date_to: searchParams.get("date_to") || undefined,
      search: searchParams.get("search") || undefined,
    });

    // Build where clause
    const where: any = {};

    if (filters.invoice_id) {
      where.invoice_id = filters.invoice_id;
    }

    if (filters.tenant_id) {
      where.tenant_id = filters.tenant_id;
    }

    if (filters.payment_method) {
      where.payment_method = filters.payment_method;
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.date_from || filters.date_to) {
      where.payment_date = {};
      if (filters.date_from) {
        where.payment_date.gte = new Date(filters.date_from);
      }
      if (filters.date_to) {
        where.payment_date.lte = new Date(filters.date_to);
      }
    }

    if (filters.search) {
      where.OR = [
        { payment_number: { contains: filters.search } },
        { reference_number: { contains: filters.search } },
        { notes: { contains: filters.search } },
      ];
    }

    // Pagination
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const skip = (page - 1) * pageSize;

    // Sorting
    const sortBy = searchParams.get("sortBy") || "created_at";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    // Execute query with pagination
    const [payments, total] = await Promise.all([
      db.payment.findMany({
        where,
        include: {
          invoice: true,
          tenant: true,
          allocations: {
            include: {
              invoice: true,
            },
          },
          creator: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
        },
        skip,
        take: pageSize,
        orderBy: { [sortBy]: sortOrder },
      }),
      db.payment.count({ where }),
    ]);

    return ApiResponseBuilder.success(payments, {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error) {
    console.error("Error fetching payments:", error);
    return ApiResponseBuilder.error("Failed to fetch payments", "INTERNAL_ERROR", 500);
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has CREATE permission for payments
    const canCreate = hasPermission(userPermissions, "payments", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create payments");
    }

    // For now, allow public access to create payments
    // TODO: Add authentication when needed

    const body = await request.json();
    const validatedData = paymentSchema.parse(body);

    // Check if tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: validatedData.tenant_id },
    });

    if (!tenant) {
      return ApiResponseBuilder.error("Tenant not found", "NOT_FOUND", 404);
    }

    // If allocations are provided, validate them
    if (validatedData.allocations && validatedData.allocations.length > 0) {
      const validation = validatePaymentAllocation(validatedData.amount, validatedData.allocations);
      if (!validation.valid) {
        return ApiResponseBuilder.error(validation.message!, "BAD_REQUEST", 400);
      }

      // Verify all allocated invoices exist and belong to the tenant
      const invoiceIds = validatedData.allocations.map(a => a.invoice_id);
      const invoices = await db.invoice.findMany({
        where: {
          id: { in: invoiceIds },
          tenant_id: validatedData.tenant_id,
          status: { in: ['PENDING', 'PARTIALLY_PAID', 'OVERDUE'] },
        },
      });

      if (invoices.length !== invoiceIds.length) {
        return ApiResponseBuilder.error("One or more invoices not found or already paid", "NOT_FOUND", 404);
      }

      // Check if allocated amounts don't exceed invoice balances
      for (const allocation of validatedData.allocations) {
        const invoice = invoices.find(i => i.id === allocation.invoice_id);
        if (invoice) {
          const allocatedAmount = parseFloat(allocation.allocated_amount);
          const balance = parseFloat(invoice.balance_amount.toString());
          
          if (allocatedAmount > balance) {
            return ApiResponseBuilder.error(
              `Allocated amount (${allocatedAmount.toFixed(3)}) exceeds invoice balance (${balance.toFixed(3)}) for invoice ${invoice.invoice_number}`,
              "BAD_REQUEST",
              400
            );
          }
        }
      }
    }

    // If invoice_id is provided but no allocations, auto-allocate full amount to that invoice
    let allocations = validatedData.allocations || [];
    if (validatedData.invoice_id && allocations.length === 0) {
      const invoice = await db.invoice.findUnique({
        where: { id: validatedData.invoice_id },
      });

      if (!invoice) {
        return ApiResponseBuilder.error("Invoice not found", "NOT_FOUND", 404);
      }

      if (invoice.tenant_id !== validatedData.tenant_id) {
        return ApiResponseBuilder.error("Invoice does not belong to the selected tenant", "BAD_REQUEST", 400);
      }

      allocations = [{
        invoice_id: validatedData.invoice_id,
        allocated_amount: validatedData.amount,
      }];
    }

    // Generate payment number if not provided
    let paymentNumber = validatedData.payment_number;
    
    if (!paymentNumber) {
      const paymentDate = new Date(validatedData.payment_date);
      const year = paymentDate.getFullYear();
      const month = paymentDate.getMonth() + 1;
      
      // Get the latest payment for the same year and month
      const latestPayment = await db.payment.findFirst({
        where: {
          payment_number: {
            startsWith: `PAY-${year}-${month.toString().padStart(2, '0')}`,
          },
        },
        orderBy: {
          payment_number: 'desc',
        },
      });
      
      let sequence = 1;
      if (latestPayment) {
        const parts = latestPayment.payment_number.split('-');
        sequence = parseInt(parts[3]) + 1;
      }
      
      paymentNumber = generatePaymentNumber(year, month, sequence);
    }

    // Create payment with allocations in a transaction
    const payment = await db.$transaction(async (tx) => {
      // Create payment
      const newPayment = await tx.payment.create({
        data: {
          payment_number: paymentNumber,
          invoice_id: validatedData.invoice_id,
          tenant_id: validatedData.tenant_id,
          amount: parseFloat(validatedData.amount),
          payment_method: validatedData.payment_method,
          payment_date: new Date(validatedData.payment_date),
          reference_number: validatedData.reference_number,
          bank_name: validatedData.bank_name,
          notes: validatedData.notes,
          status: validatedData.status,
          installment: validatedData.installment,
          created_by: decoded.id,
        },
      });

      // Create allocations and update invoices
      for (const allocation of allocations) {
        const allocatedAmount = parseFloat(allocation.allocated_amount);
        
        // Create allocation
        await tx.paymentAllocation.create({
          data: {
            payment_id: newPayment.id,
            invoice_id: allocation.invoice_id,
            allocated_amount: allocatedAmount,
          },
        });

        // Update invoice
        const invoice = await tx.invoice.findUnique({
          where: { id: allocation.invoice_id },
        });

        if (invoice) {
          const newPaidAmount = parseFloat(invoice.paid_amount.toString()) + allocatedAmount;
          const newBalance = parseFloat(invoice.total_amount.toString()) - newPaidAmount;
          
          let newStatus = invoice.status;
          if (newBalance <= 0) {
            newStatus = 'PAID';
          } else if (newPaidAmount > 0) {
            newStatus = 'PARTIALLY_PAID';
          }

          await tx.invoice.update({
            where: { id: allocation.invoice_id },
            data: {
              paid_amount: newPaidAmount,
              balance_amount: newBalance,
              status: newStatus,
              updated_by: decoded.id,
            },
          });
        }
      }

      // Return payment with relations
      return await tx.payment.findUnique({
        where: { id: newPayment.id },
        include: {
          invoice: true,
          tenant: true,
          allocations: {
            include: {
              invoice: true,
            },
          },
          creator: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
              email: true,
            },
          },
        },
      });
    });

    return ApiResponseBuilder.success(payment, undefined, 201);
  } catch (error) {
    console.error("Error creating payment:", error);
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return ApiResponseBuilder.error("Payment number already exists", "BAD_REQUEST", 400);
    }
    return ApiResponseBuilder.error("Failed to create payment", "INTERNAL_ERROR", 500);
  }
}