import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { propertyTypeSchema, type PropertyTypeFilters } from "@/types/property";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

// GET /api/property-types - List property types with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "property-types", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view property types");
    }

    console.log("Property Types GET: Received request");
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const filters: PropertyTypeFilters = {
      search: searchParams.get("search") || undefined,
      page: parseInt(searchParams.get("page") || "1"),
      pageSize: parseInt(searchParams.get("pageSize") || "10"),
      sortBy: searchParams.get("sortBy") || "name_en",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "asc",
    };

    // Build where clause
    const where: any = {};
    
    if (filters.search) {
      where.OR = [
        { name_en: { contains: filters.search } },
        { name_ar: { contains: filters.search } },
        { description_en: { contains: filters.search } },
        { description_ar: { contains: filters.search } },
      ];
    }

    // Calculate pagination
    const skip = ((filters.page || 1) - 1) * (filters.pageSize || 10);
    const take = filters.pageSize || 10;

    // Build order by clause
    const orderBy: any = {};
    orderBy[filters.sortBy || "name_en"] = filters.sortOrder || "asc";

    // Fetch property types with property count
    const [propertyTypes, total] = await Promise.all([
      db.propertyType.findMany({
        where,
        include: {
          _count: {
            select: { properties: true },
          },
        },
        orderBy,
        skip,
        take,
      }),
      db.propertyType.count({ where }),
    ]);

    const totalPages = Math.ceil(total / take);

    console.log("Property Types GET: Found", propertyTypes.length, "property types");

    return ApiResponseBuilder.success(propertyTypes, {
      page: filters.page || 1,
      pageSize: take,
      total,
      totalPages,
    });
  } catch (error) {
    console.error("Property Types API Error:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch property types",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// POST /api/property-types - Create a new property type
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "property-types", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create property types");
    }
    
    const body = await request.json();
    
    // Validate the request body
    const validationResult = propertyTypeSchema.safeParse(body);
    if (!validationResult.success) {
      return ApiResponseBuilder.validationError(validationResult.error);
    }

    const validatedData = validationResult.data;

    // Check if property type with same name already exists
    const existing = await db.propertyType.findFirst({
      where: {
        OR: [
          { name_en: validatedData.name_en },
          { name_ar: validatedData.name_ar },
        ],
      },
    });

    if (existing) {
      return ApiResponseBuilder.conflict(
        "A property type with this name already exists",
        { existing_names: { name_en: existing.name_en, name_ar: existing.name_ar } },
        request.url
      );
    }

    // Create the property type
    const propertyType = await db.propertyType.create({
      data: {
        name_en: validatedData.name_en,
        name_ar: validatedData.name_ar,
        description_en: validatedData.description_en,
        description_ar: validatedData.description_ar,
      },
      include: {
        _count: {
          select: { properties: true },
        },
      },
    });

    return ApiResponseBuilder.success(propertyType);
  } catch (error) {
    console.error("Create Property Type Error:", error);
    return ApiResponseBuilder.error(
      "Failed to create property type",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}