"use client";

import { useState, useEffect } from "react";
import { authenticatedFetch } from "@/lib/api-client";
import { useTranslations } from "next-intl";
import { format } from "date-fns";
import { FileText, Upload, Download, Trash2, Eye } from "lucide-react";
import { toast } from "sonner";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface TenantDocumentsProps {
  tenantId: string;
}

const DOCUMENT_TYPES = [
  "NATIONAL_ID",
  "PASSPORT",
  "DRIVING_LICENSE",
  "EMPLOYMENT_CONTRACT",
  "SALARY_CERTIFICATE",
  "BANK_STATEMENT",
  "OTHER",
] as const;

export function TenantDocuments({ tenantId }: TenantDocumentsProps) {
  const t = useTranslations("tenants");
  const tCommon = useTranslations("common");
  
  const [documents, setDocuments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const [documentType, setDocumentType] = useState<string>("OTHER");

  useEffect(() => {
    fetchDocuments();
  }, [tenantId]);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      const result = await authenticatedFetch(`/api/tenants/${tenantId}/documents`);
      if (result.success) {
        setDocuments(result.data);
      }
    } catch (error) {
      console.error("Error fetching documents:", error);
      toast.error(t("documents.fetchError"));
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setSelectedFiles(files);
      setUploadDialogOpen(true);
    }
  };

  const handleUpload = async () => {
    if (!selectedFiles) return;

    setUploading(true);
    
    try {
      // Upload each file separately
      for (let i = 0; i < selectedFiles.length; i++) {
        const formData = new FormData();
        formData.append("file", selectedFiles[i]);
        formData.append("document_type", documentType);
        
        await authenticatedFetch(`/api/tenants/${tenantId}/documents`, {
          method: "POST",
          body: formData,
        });
      }

      toast.success(t("documents.uploadSuccess"));
      fetchDocuments();
      
      // Reset state
      setUploadDialogOpen(false);
      setSelectedFiles(null);
      setDocumentType("OTHER");
      
      // Reset the file input
      const fileInput = document.getElementById("file-upload") as HTMLInputElement;
      if (fileInput) {
        fileInput.value = "";
      }
    } catch (error) {
      console.error("Error uploading documents:", error);
      toast.error(error instanceof Error ? error.message : t("documents.uploadError"));
    } finally {
      setUploading(false);
    }
  };

  const handleDelete = async () => {
    if (!deleteId) return;

    try {
      await authenticatedFetch(`/api/tenants/${tenantId}/documents/${deleteId}`, {
        method: "DELETE",
      });

      toast.success(t("documents.deleteSuccess"));
      setDeleteId(null);
      fetchDocuments();
    } catch (error) {
      console.error("Error deleting document:", error);
      toast.error(t("documents.deleteError"));
    }
  };

  const getDocumentTypeBadge = (type: string) => {
    const variant = 
      type === "NATIONAL_ID" ? "default" :
      type === "PASSPORT" ? "secondary" :
      type === "EMPLOYMENT_CONTRACT" ? "outline" :
      type === "DRIVING_LICENSE" ? "secondary" :
      type === "SALARY_CERTIFICATE" ? "outline" :
      type === "BANK_STATEMENT" ? "outline" :
      "default";
    
    return (
      <Badge variant={variant}>
        {t(`documents.types.${type}`)}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("documents.title")}</CardTitle>
          <CardDescription>{t("documents.description")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{t("documents.title")}</CardTitle>
              <CardDescription>{t("documents.description")}</CardDescription>
            </div>
            <div>
              <Label htmlFor="file-upload" className="cursor-pointer">
                <Button asChild>
                  <span>
                    <Upload className="mr-2 h-4 w-4" />
                    {t("documents.uploadDocument")}
                  </span>
                </Button>
              </Label>
              <Input
                id="file-upload"
                type="file"
                multiple
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                className="hidden"
                onChange={handleFileSelect}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {documents.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground">{t("documents.noDocuments")}</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("documents.name")}</TableHead>
                    <TableHead className="text-center">{t("documents.type")}</TableHead>
                    <TableHead>{t("documents.uploadedAt")}</TableHead>
                    <TableHead>{t("documents.size")}</TableHead>
                    <TableHead className="text-center">{tCommon("actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {documents.map((document) => (
                    <TableRow key={document.id}>
                      <TableCell className="font-medium">
                        {document.file_name}
                      </TableCell>
                      <TableCell className="text-center">
                        {getDocumentTypeBadge(document.document_type)}
                      </TableCell>
                      <TableCell>
                        {format(new Date(document.created_at), "dd/MM/yyyy HH:mm")}
                      </TableCell>
                      <TableCell>
                        {(document.file_size / 1024 / 1024).toFixed(2)} {tCommon("megabytes")}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(document.file_path, '_blank')}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const a = document.createElement('a');
                              a.href = document.file_path;
                              a.download = document.file_name;
                              a.click();
                            }}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setDeleteId(document.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("documents.uploadDocument")}</DialogTitle>
            <DialogDescription>
              {t("documents.uploadDescription")}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>{t("documents.selectedFiles")}</Label>
              {selectedFiles && (
                <div className="text-sm text-muted-foreground">
                  {Array.from(selectedFiles).map((file, index) => (
                    <div key={index}>{file.name}</div>
                  ))}
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="document-type">{t("documents.documentType")}</Label>
              <Select value={documentType} onValueChange={setDocumentType}>
                <SelectTrigger id="document-type">
                  <SelectValue placeholder={t("documents.selectType")} />
                </SelectTrigger>
                <SelectContent>
                  {DOCUMENT_TYPES.map((type) => (
                    <SelectItem key={type} value={type}>
                      {t(`documents.types.${type}`)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setUploadDialogOpen(false);
                setSelectedFiles(null);
                setDocumentType("OTHER");
              }}
            >
              {tCommon("cancel")}
            </Button>
            <Button onClick={handleUpload} disabled={uploading}>
              {uploading ? t("documents.uploading") : tCommon("upload")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteId} onOpenChange={() => setDeleteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("documents.deleteTitle")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("documents.deleteDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{tCommon("cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>
              {tCommon("delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}