import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { contractTenantSchema } from "@/types/contract";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for contracts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "contracts", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to read contracts");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const contractId = parseInt(id);
    if (isNaN(contractId)) {
      return ApiResponseBuilder.error("Invalid contract ID", "BAD_REQUEST", 400);
    }

    // Check if contract exists
    const contract = await prisma.contract.findUnique({
      where: { id: contractId },
    });

    if (!contract) {
      return ApiResponseBuilder.error("Contract not found", "NOT_FOUND", 404);
    }

    // Get contract tenants
    const contractTenants = await prisma.contractTenant.findMany({
      where: { contract_id: contractId },
      include: {
        tenant: true,
      },
      orderBy: [
        { is_primary: "desc" },
        { created_at: "asc" },
      ],
    });

    return ApiResponseBuilder.success(contractTenants);
  } catch (error) {
    console.error("Error fetching contract tenants:", error);
    return ApiResponseBuilder.error("Failed to fetch contract tenants", "INTERNAL_ERROR", 500);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for contracts
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "contracts", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create contracts");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const contractId = parseInt(id);
    if (isNaN(contractId)) {
      return ApiResponseBuilder.error("Invalid contract ID", "BAD_REQUEST", 400);
    }

    const body = await request.json();
    const validatedData = contractTenantSchema.parse(body);

    // Check if contract exists
    const contract = await prisma.contract.findUnique({
      where: { id: contractId },
    });

    if (!contract) {
      return ApiResponseBuilder.error("Contract not found", "NOT_FOUND", 404);
    }

    // Check if contract is in a state that allows adding tenants
    if (["TERMINATED", "EXPIRED"].includes(contract.status)) {
      return ApiResponseBuilder.error("Cannot add tenants to terminated or expired contracts", "BAD_REQUEST", 400);
    }

    // Check if tenant exists
    const tenant = await prisma.tenant.findUnique({
      where: { id: validatedData.tenant_id },
    });

    if (!tenant) {
      return ApiResponseBuilder.error("Tenant not found", "NOT_FOUND", 404);
    }

    // Check if tenant is already in this contract
    const existingContractTenant = await prisma.contractTenant.findFirst({
      where: {
        contract_id: contractId,
        tenant_id: validatedData.tenant_id,
      },
    });

    if (existingContractTenant) {
      return ApiResponseBuilder.error("Tenant is already in this contract", "BAD_REQUEST", 400);
    }

    // If this is marked as primary, unset other primary tenants
    if (validatedData.is_primary) {
      await prisma.contractTenant.updateMany({
        where: {
          contract_id: contractId,
          is_primary: true,
        },
        data: {
          is_primary: false,
        },
      });
    }

    // Create contract tenant
    const contractTenant = await prisma.contractTenant.create({
      data: {
        contract_id: contractId,
        tenant_id: validatedData.tenant_id,
        is_primary: validatedData.is_primary,
      },
      include: {
        tenant: true,
      },
    });

    // Update contract to reflect changes
    await prisma.contract.update({
      where: { id: contractId },
      data: {
        updated_by: decoded.id,
      },
    });

    return ApiResponseBuilder.success(contractTenant, undefined, 201);
  } catch (error) {
    console.error("Error adding tenant to contract:", error);
    return ApiResponseBuilder.error("Failed to add tenant to contract", "INTERNAL_ERROR", 500);
  }
}