import { NextRequest, NextResponse } from "next/server";
import { verifyToken } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";
import { db } from "@/lib/db";
import { Decimal } from "@prisma/client/runtime/library";

// PUT - Update a payment record
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; paymentId: string; recordId: string }> }
) {
  try {
    const resolvedParams = await params;
    
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: "Invalid or expired token" },
        { status: 401 }
      );
    }

    // Check if user has UPDATE permission for owners-associations
    const canUpdate = await hasPermission(decoded.id, "owners-associations", "UPDATE");
    if (!canUpdate) {
      return NextResponse.json(
        { success: false, error: "You don't have permission to update payment records" },
        { status: 403 }
      );
    }
    const associationId = parseInt(resolvedParams.id);
    const paymentId = parseInt(resolvedParams.paymentId);
    const recordId = parseInt(resolvedParams.recordId);

    if (isNaN(associationId) || isNaN(paymentId) || isNaN(recordId)) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { amount, payment_date, payment_method, reference_number, notes } = body;

    // Validate required fields
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      return NextResponse.json(
        { success: false, error: "Valid amount is required" },
        { status: 400 }
      );
    }

    if (!payment_date) {
      return NextResponse.json(
        { success: false, error: "Payment date is required" },
        { status: 400 }
      );
    }

    if (!payment_method) {
      return NextResponse.json(
        { success: false, error: "Payment method is required" },
        { status: 400 }
      );
    }

    // Verify the transaction record exists and belongs to this association
    const existingTransaction = await db.associationTransaction.findFirst({
      where: {
        id: recordId,
        association_id: associationId
      }
    });

    if (!existingTransaction) {
      return NextResponse.json(
        { success: false, error: "Payment record not found" },
        { status: 404 }
      );
    }

    // Verify the payment belongs to this association
    const payment = await db.subscriptionPayment.findFirst({
      where: {
        id: paymentId,
        subscription: {
          association_id: associationId
        }
      },
      include: {
        member: true,
        subscription: true
      }
    });

    if (!payment) {
      return NextResponse.json(
        { success: false, error: "Payment not found" },
        { status: 404 }
      );
    }

    // Get all other transaction records for this payment
    const otherTransactions = await db.associationTransaction.findMany({
      where: {
        association_id: associationId,
        type: 'INCOME',
        category: 'SUBSCRIPTION_PAYMENT',
        member_id: payment.member_id,
        id: { not: recordId }, // Exclude the current record being updated
        description: {
          contains: payment.member.full_name
        }
      }
    });

    // Calculate total from other records
    const totalFromOthers = otherTransactions.reduce((sum, record) => {
      return sum.add(record.amount);
    }, new Decimal(0));

    // Calculate amount due
    const amountDue = payment.amount_due || payment.amount;
    const remainingAmount = new Decimal(amountDue).sub(totalFromOthers);

    // Validate new payment amount doesn't exceed remaining
    if (new Decimal(amount).gt(remainingAmount)) {
      return NextResponse.json(
        { success: false, error: `Payment amount exceeds remaining balance of ${remainingAmount.toFixed(3)}` },
        { status: 400 }
      );
    }

    // Update the transaction record
    const updatedTransaction = await db.associationTransaction.update({
      where: { id: recordId },
      data: {
        amount: new Decimal(amount),
        transaction_date: new Date(payment_date),
        payment_method,
        reference_number,
        description: notes || `${payment.subscription.name_en} - ${payment.member.full_name}`,
        updated_at: new Date()
      }
    });
    
    const updatedRecord = {
      id: recordId,
      payment_id: paymentId,
      amount: updatedTransaction.amount,
      payment_date: updatedTransaction.transaction_date,
      payment_method: updatedTransaction.payment_method,
      reference_number: updatedTransaction.reference_number,
      notes: updatedTransaction.description,
      created_by: updatedTransaction.created_by,
      created_at: updatedTransaction.created_at,
      updated_at: updatedTransaction.updated_at
    };

    // Recalculate total paid and update payment status
    const newTotalPaid = totalFromOthers.add(new Decimal(amount));
    const newStatus = newTotalPaid.gte(amountDue) 
      ? 'PAID' 
      : newTotalPaid.gt(0) 
        ? 'PARTIALLY_PAID' 
        : 'UNPAID';

    await db.subscriptionPayment.update({
      where: { id: paymentId },
      data: {
        amount_paid: newTotalPaid,
        status: newStatus
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedRecord
    });
  } catch (error) {
    console.error("Error updating payment record:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update payment record" },
      { status: 500 }
    );
  }
}

// DELETE - Delete a payment record
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; paymentId: string; recordId: string }> }
) {
  try {
    const resolvedParams = await params;
    
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: "Invalid or expired token" },
        { status: 401 }
      );
    }

    // Check if user has DELETE permission for owners-associations
    const canDelete = await hasPermission(decoded.id, "owners-associations", "DELETE");
    if (!canDelete) {
      return NextResponse.json(
        { success: false, error: "You don't have permission to delete payment records" },
        { status: 403 }
      );
    }
    const associationId = parseInt(resolvedParams.id);
    const paymentId = parseInt(resolvedParams.paymentId);
    const recordId = parseInt(resolvedParams.recordId);

    if (isNaN(associationId) || isNaN(paymentId) || isNaN(recordId)) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters" },
        { status: 400 }
      );
    }

    // Verify the transaction record exists and belongs to this payment
    const transaction = await db.associationTransaction.findFirst({
      where: {
        id: recordId,
        association_id: associationId
      }
    });

    if (!transaction) {
      return NextResponse.json(
        { success: false, error: "Payment record not found" },
        { status: 404 }
      );
    }

    // Verify the payment belongs to this association
    const payment = await db.subscriptionPayment.findFirst({
      where: {
        id: paymentId,
        subscription: {
          association_id: associationId
        }
      },
      include: {
        member: true,
        subscription: true
      }
    });

    if (!payment) {
      return NextResponse.json(
        { success: false, error: "Payment not found" },
        { status: 404 }
      );
    }

    // Delete the transaction record
    await db.associationTransaction.delete({
      where: { id: recordId }
    });

    // Get remaining transaction records for this payment
    const remainingTransactions = await db.associationTransaction.findMany({
      where: {
        association_id: associationId,
        type: 'INCOME',
        category: 'SUBSCRIPTION_PAYMENT',
        member_id: payment.member_id,
        description: {
          contains: payment.member.full_name // Match transactions for this member
        }
      }
    });

    // Calculate new total paid (excluding the deleted transaction)
    const newTotalPaid = remainingTransactions
      .filter(t => t.id !== recordId) // Exclude the deleted transaction
      .reduce((sum, rec) => {
        return sum.add(rec.amount);
      }, new Decimal(0));

    // Update payment status
    const amountDue = payment.amount_due || payment.amount;
    const newStatus = newTotalPaid.gte(amountDue) 
      ? 'PAID' 
      : newTotalPaid.gt(0) 
        ? 'PARTIALLY_PAID' 
        : 'UNPAID';

    await db.subscriptionPayment.update({
      where: { id: paymentId },
      data: {
        amount_paid: newTotalPaid,
        status: newStatus
      }
    });

    // If payment is no longer fully paid and has a transaction, consider removing it
    if (newStatus !== 'PAID' && payment.transaction_id) {
      await db.subscriptionPayment.update({
        where: { id: paymentId },
        data: {
          transaction_id: null
        }
      });

      // Optionally delete the transaction
      await db.associationTransaction.delete({
        where: { id: payment.transaction_id }
      }).catch(() => {
        // Transaction might be referenced elsewhere, ignore error
      });
    }

    return NextResponse.json({
      success: true,
      message: "Payment record deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting payment record:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete payment record" },
      { status: 500 }
    );
  }
}