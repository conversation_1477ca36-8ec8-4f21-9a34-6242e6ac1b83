import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ownersAssociationSchema, ownersAssociationFilterSchema } from "@/types/owners-association";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, checkUserPermission } from "@/lib/auth";

// GET /api/owners-associations - List all owners associations with filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await checkUserPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view owners associations");
    }

    const searchParams = request.nextUrl.searchParams;
    
    // Parse filters
    const filters = ownersAssociationFilterSchema.parse({
      search: searchParams.get("search") || undefined,
      property_id: searchParams.get("property_id") ? parseInt(searchParams.get("property_id")!) : undefined,
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
      pageSize: searchParams.get("pageSize") ? parseInt(searchParams.get("pageSize")!) : 10,
      sortBy: searchParams.get("sortBy") || "created_at",
      sortOrder: searchParams.get("sortOrder") || "desc",
    });

    // Build where clause
    const where: any = {};

    if (filters.search) {
      where.OR = [
        { name_en: { contains: filters.search, mode: 'insensitive' } },
        { name_ar: { contains: filters.search, mode: 'insensitive' } },
        { president_name: { contains: filters.search, mode: 'insensitive' } },
        { contact_email: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    if (filters.property_id) {
      where.property_id = filters.property_id;
    }

    // Calculate pagination
    const page = filters.page || 1;
    const pageSize = filters.pageSize || 10;
    const skip = (page - 1) * pageSize;

    // Fetch associations with relations
    const [associations, total] = await Promise.all([
      db.ownersAssociation.findMany({
        where,
        include: {
          property: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
              address_en: true,
              address_ar: true,
            },
          },
          _count: {
            select: {
              members: true,
              subscriptions: true,
              transactions: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || "created_at"]: filters.sortOrder || "desc",
        },
        skip,
        take: pageSize,
      }),
      db.ownersAssociation.count({ where }),
    ]);

    return ApiResponseBuilder.success(associations, {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error: any) {
    console.error("Error fetching owners associations:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }
    
    return ApiResponseBuilder.error("Failed to fetch owners associations", "INTERNAL_ERROR", 500);
  }
}

// POST /api/owners-associations - Create a new owners association
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for owners-associations
    const canCreate = await checkUserPermission(decoded.id, "owners-associations", "CREATE");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create owners associations");
    }

    const body = await request.json();
    const validatedData = ownersAssociationSchema.parse(body);

    // Check if property exists
    const property = await db.property.findUnique({
      where: { id: validatedData.property_id },
    });

    if (!property) {
      return ApiResponseBuilder.error("Property not found", "NOT_FOUND", 404);
    }

    // Check if an association already exists for this property
    const existingAssociation = await db.ownersAssociation.findFirst({
      where: { property_id: validatedData.property_id },
    });

    if (existingAssociation) {
      return ApiResponseBuilder.error(
        "An owners association already exists for this property",
        "ALREADY_EXISTS",
        400
      );
    }

    // Create the association
    const association = await db.ownersAssociation.create({
      data: {
        name_en: validatedData.name_en,
        name_ar: validatedData.name_ar,
        property_id: validatedData.property_id,
        establishment_date: new Date(validatedData.establishment_date),
        president_name: validatedData.president_name,
        management_term_duration: validatedData.management_term_duration,
        contact_email: validatedData.contact_email,
        contact_phone: validatedData.contact_phone,
        created_by: decoded.id,
        updated_by: decoded.id,
      },
      include: {
        property: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            address_en: true,
            address_ar: true,
          },
        },
      },
    });

    return ApiResponseBuilder.success(association);
  } catch (error: any) {
    console.error("Error creating owners association:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to create owners association", "INTERNAL_ERROR", 500);
  }
}