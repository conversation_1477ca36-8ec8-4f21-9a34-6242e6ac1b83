"use client";

import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon, Search, X, Filter } from "lucide-react";
import { EXPENSE_CATEGORIES, INCOME_CATEGORIES } from "@/types/owners-association";
import { useLocale } from "next-intl";
import { ar, enUS } from "date-fns/locale";

interface TransactionFiltersProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  typeFilter: string;
  setTypeFilter: (value: string) => void;
  categoryFilter: string;
  setCategoryFilter: (value: string) => void;
  memberFilter?: string;
  setMemberFilter?: (value: string) => void;
  dateFrom?: Date;
  setDateFrom?: (date: Date | undefined) => void;
  dateTo?: Date;
  setDateTo?: (date: Date | undefined) => void;
  members?: Array<{ id: number; full_name: string; unit_number: string }>;
  onReset: () => void;
}

export function TransactionFilters({
  searchTerm,
  setSearchTerm,
  typeFilter,
  setTypeFilter,
  categoryFilter,
  setCategoryFilter,
  memberFilter,
  setMemberFilter,
  dateFrom,
  setDateFrom,
  dateTo,
  setDateTo,
  members = [],
  onReset,
}: TransactionFiltersProps) {
  const t = useTranslations("ownersAssociations.transactions");
  const locale = useLocale();
  const dateLocale = locale === 'ar' ? ar : enUS;
  const isRTL = locale === 'ar';

  const categories = typeFilter === 'EXPENSE' ? EXPENSE_CATEGORIES : 
                    typeFilter === 'INCOME' ? INCOME_CATEGORIES : 
                    [...EXPENSE_CATEGORIES, ...INCOME_CATEGORIES];

  const hasActiveFilters = searchTerm || typeFilter !== 'all' || categoryFilter !== 'all' || 
                          memberFilter !== 'all' || dateFrom || dateTo;

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        {/* Search */}
        <div className="relative flex-1 min-w-[200px]">
          <Search className={cn(
            "absolute top-2.5 h-4 w-4 text-muted-foreground",
            isRTL ? "right-3" : "left-3"
          )} />
          <Input
            placeholder={t("searchTransactions")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={isRTL ? "pr-9" : "pl-9"}
          />
        </div>

        {/* Type Filter */}
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t("allTypes")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t("allTypes")}</SelectItem>
            <SelectItem value="INCOME">{t("type.income")}</SelectItem>
            <SelectItem value="EXPENSE">{t("type.expense")}</SelectItem>
          </SelectContent>
        </Select>

        {/* Category Filter */}
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t("allCategories")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t("allCategories")}</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category.value} value={category.value}>
                {locale === 'ar' ? category.label_ar : category.label_en}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Member Filter */}
        {setMemberFilter && members.length > 0 && (
          <Select value={memberFilter} onValueChange={setMemberFilter}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder={t("allMembers")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("allMembers")}</SelectItem>
              {members.map((member) => (
                <SelectItem key={member.id} value={member.id.toString()}>
                  {member.full_name} ({member.unit_number})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {/* Date From */}
        {setDateFrom && (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-[180px] justify-start text-left font-normal",
                  !dateFrom && "text-muted-foreground"
                )}
              >
                <CalendarIcon className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                {dateFrom ? format(dateFrom, "PPP", { locale: dateLocale }) : t("dateFrom")}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={dateFrom}
                onSelect={setDateFrom}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        )}

        {/* Date To */}
        {setDateTo && (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-[180px] justify-start text-left font-normal",
                  !dateTo && "text-muted-foreground"
                )}
              >
                <CalendarIcon className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                {dateTo ? format(dateTo, "PPP", { locale: dateLocale }) : t("dateTo")}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={dateTo}
                onSelect={setDateTo}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        )}

        {/* Reset Filters */}
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onReset}
            className="h-10 w-10"
            title={t("resetFilters")}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}