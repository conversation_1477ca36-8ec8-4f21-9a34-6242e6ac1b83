import { getTranslations } from "next-intl/server";
import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { Edit, ArrowLeft, Building2, User, FileText } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { db } from "@/lib/db";
import { PropertyUnitsTab } from "./_components/property-units-tab";
import { PropertyOwnerTab } from "./_components/property-owner-tab";
import { PropertyContractsTab } from "./_components/property-contracts-tab";

interface PropertyDetailPageProps {
  params: Promise<{
    locale: string;
    id: string;
  }>;
}

async function getProperty(id: number) {
  const property = await db.property.findUnique({
    where: { id },
    include: {
      property_type: true,
      owner: {
        select: {
          id: true,
          name_en: true,
          name_ar: true,
          phone: true,
          mobile: true,
          email: true,
          tax_id: true,
          bank_account_number: true,
          bank_name: true,
          bank_iban: true,
        },
      },
      amenities: {
        include: {
          amenity: true,
        },
      },
      units: {
        include: {
          amenities: {
            include: {
              amenity: true,
            },
          },
        },
      },
      creator: {
        select: {
          id: true,
          username: true,
          first_name: true,
          last_name: true,
        },
      },
    },
  });

  if (!property) {
    return null;
  }

  // Transform Decimal fields to strings for client components
  return {
    ...property,
    base_rent: property.base_rent.toString(),
    total_area: property.total_area?.toString() || null,
    units: property.units.map(unit => ({
      ...unit,
      rent_amount: unit.rent_amount.toString(),
      area: unit.area?.toString() || null,
      amenities: unit.amenities.map(ua => ua.amenity),
    })),
    amenities: property.amenities.map(pa => pa.amenity),
  };
}

export async function generateMetadata({
  params,
}: PropertyDetailPageProps): Promise<Metadata> {
  const { locale, id } = await params;
  const propertyId = parseInt(id);
  if (isNaN(propertyId)) {
    return { title: "Property Not Found" };
  }

  const property = await getProperty(propertyId);
  if (!property) {
    return { title: "Property Not Found" };
  }

  const t = await getTranslations({ locale, namespace: "properties" });
  const propertyName = locale === "ar" ? property.name_ar : property.name_en;

  return {
    title: `${propertyName} - ${t("title")}`,
    description: t("description"),
  };
}

export default async function PropertyDetailPage({
  params,
}: PropertyDetailPageProps) {
  const { locale, id } = await params;
  const propertyId = parseInt(id);
  if (isNaN(propertyId)) {
    notFound();
  }

  const property = await getProperty(propertyId);
  if (!property) {
    notFound();
  }

  const t = await getTranslations({ locale, namespace: "properties" });
  const tCommon = await getTranslations({ locale, namespace: "common" });
  const tStatus = await getTranslations({ locale, namespace: "properties.status" });

  const propertyName = locale === "ar" ? property.name_ar : property.name_en;
  const propertyAddress = locale === "ar" ? property.address_ar : property.address_en;
  const propertyTypeName = locale === "ar" ? property.property_type.name_ar : property.property_type.name_en;

  const statusVariant = 
    property.status === "AVAILABLE" ? "success" :
    property.status === "RENTED" ? "default" :
    property.status === "UNDER_MAINTENANCE" ? "warning" :
    property.status === "OUT_OF_SERVICE" ? "destructive" :
    "default";

  const statusText = 
    property.status === "AVAILABLE" ? tStatus("available") :
    property.status === "RENTED" ? tStatus("rented") :
    property.status === "UNDER_MAINTENANCE" ? tStatus("underMaintenance") :
    property.status === "OUT_OF_SERVICE" ? tStatus("outOfService") :
    property.status;

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/${locale}/dashboard/properties`}>
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h2 className="text-2xl font-bold tracking-tight">{propertyName}</h2>
            <Badge variant={statusVariant as any}>{statusText}</Badge>
          </div>
          <p className="text-muted-foreground">{propertyAddress}</p>
        </div>
        <Button asChild>
          <Link href={`/${locale}/dashboard/properties/${property.id}/edit`}>
            <Edit className="mr-2 h-4 w-4" />
            {t("editProperty")}
          </Link>
        </Button>
      </div>

      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${locale}/dashboard`}>
              {tCommon("navigation.dashboard")}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${locale}/dashboard/properties`}>
              {t("title")}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{propertyName}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Property Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("details.baseRent")}</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{parseFloat(property.base_rent).toFixed(3)} OMR</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("details.propertyType")}</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{propertyTypeName}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("details.totalUnits")}</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{property.units.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t("details.availableUnits")}</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {property.units.filter(unit => unit.status === "AVAILABLE").length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Property Details */}
      <Card>
        <CardHeader>
          <CardTitle>{t("details.propertyDetails")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {property.total_area && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("details.totalArea")}</label>
                <p className="text-sm">{parseFloat(property.total_area).toFixed(2)} m²</p>
              </div>
            )}
            {property.floors_count && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("details.numberOfFloors")}</label>
                <p className="text-sm">{property.floors_count}</p>
              </div>
            )}
            {property.parking_spaces && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("details.parkingSpaces")}</label>
                <p className="text-sm">{property.parking_spaces}</p>
              </div>
            )}
          </div>
          
          {property.amenities.length > 0 && (
            <>
              <Separator />
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("details.amenities")}</label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {property.amenities.map((amenity) => (
                    <Badge key={amenity.id} variant="outline">
                      {locale === "ar" ? amenity.name_ar : amenity.name_en}
                    </Badge>
                  ))}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Tabs for detailed information */}
      <Tabs defaultValue="units" className="space-y-4">
        <TabsList>
          <TabsTrigger value="units" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            {t("details.units")} ({property.units.length})
          </TabsTrigger>
          <TabsTrigger value="owner" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            {t("details.owner")}
          </TabsTrigger>
          <TabsTrigger value="contracts" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            {t("details.contracts")}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="units">
          <PropertyUnitsTab property={property} locale={locale} />
        </TabsContent>
        
        <TabsContent value="owner">
          <PropertyOwnerTab property={property} locale={locale} />
        </TabsContent>
        
        <TabsContent value="contracts">
          <PropertyContractsTab property={property} locale={locale} />
        </TabsContent>
      </Tabs>
    </div>
  );
}