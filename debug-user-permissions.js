const { PrismaClient } = require('./src/generated/prisma/client');
const prisma = new PrismaClient();

async function checkPermissions() {
  try {
    // Find user 'ali'
    const user = await prisma.user.findFirst({
      where: { username: 'ali' },
      include: {
        user_roles: {
          include: {
            role: {
              include: {
                role_permissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        }
      }
    });
    
    console.log('User ali found:', !!user);
    
    if (user) {
      console.log('User ID:', user.id);
      console.log('Username:', user.username);
      console.log('Status:', user.status);
      console.log('');
      
      console.log('User roles and permissions:');
      for (const userRole of user.user_roles) {
        console.log('Role:', userRole.role.name);
        console.log('Permissions:');
        for (const rolePermission of userRole.role.role_permissions) {
          console.log('  -', rolePermission.permission.module + ':' + rolePermission.permission.action);
        }
        console.log('');
      }
      
      // Also check what properties permissions look like
      const propertiesPermissions = user.user_roles.flatMap(ur => 
        ur.role.role_permissions.filter(rp => rp.permission.module === 'properties')
      );
      
      console.log('Properties-related permissions:');
      propertiesPermissions.forEach(rp => {
        console.log('  -', rp.permission.module + ':' + rp.permission.action);
      });
    } else {
      console.log('User ali not found');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkPermissions();