import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ApiResponseBuilder } from "@/lib/api-response";
import { z } from "zod";
import { generateContractNumber } from "@/types/contract";


const renewSchema = z.object({
  start_date: z.string().min(1, "Start date is required"),
  end_date: z.string().min(1, "End date is required"),
  monthly_rent: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format"),
  security_deposit: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format").optional(),
  insurance_amount: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format").optional().nullable(),
  insurance_due_date: z.string().optional().nullable(),
  notes: z.string().optional(),
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for contracts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "contracts", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create contracts");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const contractId = parseInt(id);
    if (isNaN(contractId)) {
      return ApiResponseBuilder.error("Invalid contract ID", "BAD_REQUEST", 400);
    }

    const body = await request.json();
    const validatedData = renewSchema.parse(body);

    // Check if contract exists
    const existingContract = await prisma.contract.findUnique({
      where: { id: contractId },
      include: {
        tenants: true,
        unit: true,
      },
    });

    if (!existingContract) {
      return ApiResponseBuilder.error("Contract not found", "NOT_FOUND", 404);
    }

    // Only active or expired contracts can be renewed
    if (!["ACTIVE", "EXPIRED"].includes(existingContract.status)) {
      return ApiResponseBuilder.error("Only active or expired contracts can be renewed", "BAD_REQUEST", 400);
    }

    const newStartDate = new Date(validatedData.start_date);
    const existingEndDate = new Date(existingContract.end_date);

    // New contract should start after the existing contract ends
    if (newStartDate <= existingEndDate) {
      return ApiResponseBuilder.error("Renewal start date must be after the current contract end date", "BAD_REQUEST", 400);
    }

    // Check if property_id and unit exist
    if (!existingContract.property_id) {
      return ApiResponseBuilder.error("Contract is missing property information", "BAD_REQUEST", 400);
    }

    if (!existingContract.unit) {
      return ApiResponseBuilder.error("Contract is missing unit information", "BAD_REQUEST", 400);
    }

    // Generate new contract number
    const newContractNumber = generateContractNumber(
      existingContract.property_id,
      existingContract.unit.unit_number,
      newStartDate.getFullYear()
    );

    // Create new contract as renewal
    const newContract = await prisma.contract.create({
      data: {
        contract_number: newContractNumber,
        property_id: existingContract.property_id,
        unit_id: existingContract.unit_id,
        start_date: newStartDate,
        end_date: new Date(validatedData.end_date),
        monthly_rent: validatedData.monthly_rent,
        payment_due_day: existingContract.payment_due_day,
        security_deposit: validatedData.security_deposit || existingContract.security_deposit,
        insurance_amount: validatedData.insurance_amount !== undefined ? validatedData.insurance_amount : existingContract.insurance_amount,
        insurance_due_date: validatedData.insurance_due_date ? new Date(validatedData.insurance_due_date) : existingContract.insurance_due_date,
        status: "DRAFT",
        notes: validatedData.notes || `Renewal of contract ${existingContract.contract_number}`,
        created_by: decoded.id,
        updated_by: decoded.id,
      },
      include: {
        property: true,
        unit: true,
        tenants: {
          include: {
            tenant: true,
          },
        },
        documents: true,
      },
    });

    // Copy tenants from existing contract
    if (existingContract.tenants.length > 0) {
      await prisma.contractTenant.createMany({
        data: existingContract.tenants.map(ct => ({
          contract_id: newContract.id,
          tenant_id: ct.tenant_id,
          is_primary: ct.is_primary,
        })),
      });

      // Fetch the new contract with tenants
      const contractWithTenants = await prisma.contract.findUnique({
        where: { id: newContract.id },
        include: {
          property: true,
          unit: true,
          tenants: {
            include: {
              tenant: true,
            },
          },
          documents: true,
        },
      });

      // Update existing contract status to renewed
      await prisma.contract.update({
        where: { id: contractId },
        data: {
          status: "RENEWED",
          notes: `${existingContract.notes ? existingContract.notes + "\n\n" : ""}Renewed with contract ${newContractNumber}`,
          updated_by: decoded.id,
        },
      });

      return ApiResponseBuilder.success({
        message: "Contract renewed successfully",
        newContract: contractWithTenants,
      }, undefined, 201);
    }

    return ApiResponseBuilder.success({
      message: "Contract renewed successfully",
      newContract: newContract,
    }, undefined, 201);
  } catch (error) {
    console.error("Error renewing contract:", error);
    return ApiResponseBuilder.error("Failed to renew contract", "INTERNAL_ERROR", 500);
  }
}