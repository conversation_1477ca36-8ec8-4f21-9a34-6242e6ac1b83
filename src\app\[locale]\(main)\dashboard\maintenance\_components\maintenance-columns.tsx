"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  Clock,
  Wrench,
  FileText
} from "lucide-react";
import { MaintenanceRequestWithRelations, getPriorityColor, getStatusColor } from "@/types/maintenance";
import { formatOMR } from "@/lib/format";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export const maintenanceColumns: ColumnDef<MaintenanceRequestWithRelations>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: "actions",
    cell: function ActionsCell({ row }) {
      const locale = useLocale();
      const t = useTranslations();
      const router = useRouter();
      const request = row.original;

      const handleDelete = async () => {
        if (!confirm(t("maintenance.confirmCancel"))) {
          return;
        }

        try {
          const response = await fetch(`/api/maintenance/${request.id}`, {
            method: "DELETE",
          });

          if (!response.ok) {
            throw new Error("Failed to cancel request");
          }

          toast.success(t("maintenance.messages.cancelSuccess"));
          router.refresh();
        } catch (error) {
          console.error("Error cancelling request:", error);
          toast.error(t("maintenance.messages.cancelError"));
        }
      };

      return (
        <div className="text-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">{t("common.openMenu")}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t("common.actions")}</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/${locale}/dashboard/maintenance/${request.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  {t("common.view")}
                </Link>
              </DropdownMenuItem>
              {request.status !== "COMPLETED" && request.status !== "CANCELLED" && (
                <>
                  <DropdownMenuItem asChild>
                    <Link href={`/${locale}/dashboard/maintenance/${request.id}/edit`}>
                      <Edit className="mr-2 h-4 w-4" />
                      {t("common.edit")}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleDelete}
                    className="text-destructive focus:text-destructive"
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    {t("maintenance.cancel")}
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
  {
    accessorKey: "id",
    header: ({ column }) => {
      const t = useTranslations();
      return (
        <DataTableColumnHeader column={column} title={t("common.id")} />
      );
    },
    cell: ({ row }) => <div className="text-center">{row.getValue("id")}</div>,
  },
  {
    accessorKey: "request_number",
    header: ({ column }) => {
      const t = useTranslations();
      return (
        <DataTableColumnHeader column={column} title={t("maintenance.requestNumber")} />
      );
    },
  },
  {
    accessorKey: "title",
    header: ({ column }) => {
      const t = useTranslations();
      return (
        <DataTableColumnHeader column={column} title={t("maintenance.title")} />
      );
    },
    cell: ({ row }) => {
      return (
        <div>
          <div className="font-medium">{row.original.title}</div>
          <div className="text-sm text-muted-foreground line-clamp-1">
            {row.original.description}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "property",
    header: ({ column }) => {
      const t = useTranslations();
      return (
        <DataTableColumnHeader column={column} title={t("common.property")} />
      );
    },
    cell: ({ row }) => {
      const locale = useLocale();
      const t = useTranslations();
      const property = row.original.property;
      const unit = row.original.unit;
      
      if (!property) return "—";
      
      const propertyName = locale === "ar" ? property.name_ar : property.name_en;
      
      return (
        <div>
          <div className="font-medium">{propertyName}</div>
          {unit && (
            <div className="text-sm text-muted-foreground">
              {t("common.unit")} {unit.unit_number}
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "category",
    header: ({ column }) => {
      const t = useTranslations();
      return (
        <DataTableColumnHeader column={column} title={t("maintenance.category")} />
      );
    },
    cell: ({ row }) => {
      const t = useTranslations();
      const category = row.getValue("category") as string;
      return t(`maintenance.categories.${category.toLowerCase()}`);
    },
  },
  {
    accessorKey: "priority",
    header: ({ column }) => {
      const t = useTranslations();
      return (
        <DataTableColumnHeader column={column} title={t("maintenance.priority")} />
      );
    },
    cell: ({ row }) => {
      const t = useTranslations();
      const priority = row.getValue("priority") as string;
      return (
        <Badge variant={getPriorityColor(priority as any) as any}>
          {t(`maintenance.priorities.${priority.toLowerCase()}`)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => {
      const t = useTranslations();
      return (
        <DataTableColumnHeader column={column} title={t("common.status")} />
      );
    },
    cell: ({ row }) => {
      const t = useTranslations();
      const status = row.getValue("status") as string;
      return (
        <Badge variant={getStatusColor(status as any) as any}>
          {t(`maintenance.statuses.${status.toLowerCase()}`)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "reported_date",
    header: ({ column }) => {
      const t = useTranslations();
      return (
        <DataTableColumnHeader column={column} title={t("maintenance.reportedDate")} />
      );
    },
    cell: ({ row }) => {
      const locale = useLocale();
      const date = row.getValue("reported_date") as Date;
      return new Date(date).toLocaleDateString(locale === "ar" ? "ar-OM" : "en-OM");
    },
  },
  {
    accessorKey: "assignee",
    header: ({ column }) => {
      const t = useTranslations();
      return (
        <DataTableColumnHeader column={column} title={t("maintenance.assignedTo")} />
      );
    },
    cell: ({ row }) => {
      const assignee = row.original.assignee;
      const t = useTranslations();
      if (!assignee) return <span className="text-muted-foreground">{t("maintenance.unassigned")}</span>;
      
      return `${assignee.first_name} ${assignee.last_name}`;
    },
  },
  {
    accessorKey: "estimated_cost",
    header: ({ column }) => {
      const t = useTranslations();
      return (
        <DataTableColumnHeader column={column} title={t("maintenance.estimatedCost")} />
      );
    },
    cell: ({ row }) => {
      const cost = row.getValue("estimated_cost") as number | null;
      if (!cost) return "—";
      return formatOMR(cost);
    },
  },
];