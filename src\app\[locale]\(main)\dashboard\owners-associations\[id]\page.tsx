import { Metadata } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { cookies, headers } from "next/headers";
import { ArrowLeft, Edit } from "lucide-react";
import { getTranslations } from "next-intl/server";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { AssociationOverview } from "./_components/association-overview";
import { AssociationMembersTable } from "../_components/association-members-table";
import { AssociationSubscriptionsTable } from "../_components/association-subscriptions-table";
import { AssociationTransactionsTable } from "../_components/association-transactions-table";
import { AssociationReports } from "../_components/association-reports";
import { AssociationPaymentsWrapper } from "../_components/association-payments-wrapper";

interface AssociationDetailPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

export async function generateMetadata({
  params,
}: AssociationDetailPageProps): Promise<Metadata> {
  const { locale, id } = await params;
  const t = await getTranslations({ locale, namespace: "ownersAssociations" });
  
  try {
    // Get dynamic host for multi-port development
    const headersList = await headers();
    const host = headersList.get('host') || 'localhost:3001';
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}`;
    
    // Get cookies for server-side authentication
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');
      
    const response = await fetch(
      `${baseUrl}/api/owners-associations/${id}`,
      { 
        cache: 'no-store',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': authToken ? `auth-token=${authToken.value}` : '',
        }
      }
    );
    
    if (!response.ok) {
      return {
        title: t("associationNotFound"),
      };
    }
    
    const { data: association } = await response.json();
    const name = locale === 'ar' ? association.name_ar : association.name_en;
    
    return {
      title: `${name} - ${t("title")}`,
      description: t("viewDescription", { name }),
    };
  } catch (error) {
    return {
      title: t("associationNotFound"),
    };
  }
}

async function getAssociation(id: string) {
  try {
    // Get dynamic host for multi-port development
    const headersList = await headers();
    const host = headersList.get('host') || 'localhost:3001';
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}`;
    
    // Get cookies for server-side authentication
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');
    
    const response = await fetch(
      `${baseUrl}/api/owners-associations/${id}`,
      { 
        cache: 'no-store',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': authToken ? `auth-token=${authToken.value}` : '',
        }
      }
    );

    if (!response.ok) {
      console.error(`Failed to fetch association: ${response.status} ${response.statusText}`);
      return null;
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error("Error fetching association:", error);
    return null;
  }
}

export default async function AssociationDetailPage({ params }: AssociationDetailPageProps) {
  const { id, locale } = await params;
  const association = await getAssociation(id);
  const t = await getTranslations({ locale, namespace: "ownersAssociations" });
  const tCommon = await getTranslations({ locale, namespace: "common" });

  if (!association) {
    notFound();
  }

  const name = locale === 'ar' ? association.name_ar : association.name_en;

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href={`/${locale}/dashboard/owners-associations`}>
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">{tCommon("back")}</span>
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{name}</h1>
            <p className="text-muted-foreground">{t("associationDetails")}</p>
          </div>
        </div>
        <Button asChild>
          <Link href={`/${locale}/dashboard/owners-associations/${association.id}/edit`}>
            <Edit className="mr-2 h-4 w-4" />
            {t("editAssociation")}
          </Link>
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-4" dir={locale === 'ar' ? 'rtl' : 'ltr'}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">{t("tabs.overview")}</TabsTrigger>
          <TabsTrigger value="members">{t("tabs.members")}</TabsTrigger>
          <TabsTrigger value="subscriptions">{t("tabs.subscriptions")}</TabsTrigger>
          <TabsTrigger value="payments">{t("tabs.payments")}</TabsTrigger>
          <TabsTrigger value="transactions">{t("tabs.transactions")}</TabsTrigger>
          <TabsTrigger value="reports">{t("tabs.reports")}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <AssociationOverview association={association} />
        </TabsContent>

        <TabsContent value="members" className="space-y-4">
          <AssociationMembersTable associationId={association.id} />
        </TabsContent>

        <TabsContent value="subscriptions" className="space-y-4">
          <AssociationSubscriptionsTable associationId={association.id} />
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <AssociationPaymentsWrapper associationId={association.id} />
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <AssociationTransactionsTable associationId={association.id} />
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <AssociationReports associationId={association.id} />
        </TabsContent>
      </Tabs>
    </div>
  );
}