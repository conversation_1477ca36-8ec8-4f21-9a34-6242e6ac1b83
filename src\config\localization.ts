/**
 * Localization configuration for the property management application
 * Configured for Oman (Asia/Muscat timezone, OMR currency, DD/MM/YYYY date format)
 */

export const LOCALIZATION_CONFIG = {
  // Timezone configuration
  timezone: "Asia/Muscat", // UTC+4
  
  // Currency configuration
  currency: {
    code: "OMR",
    symbol: "OMR",
    locale: "en-OM", // English locale for Oman
    decimals: 3, // Omani Rial uses 3 decimal places (1 OMR = 1000 baisa)
    format: "OMR 1,234.567", // Format example
  },
  
  // Date configuration
  date: {
    format: "DD/MM/YYYY",
    locale: "en-GB", // British English for DD/MM/YYYY format
    timezone: "Asia/Muscat",
  },
  
  // Number formatting
  number: {
    locale: "en-OM",
    thousandsSeparator: ",",
    decimalSeparator: ".",
  },
} as const;

// Type definitions for better TypeScript support
export type LocalizationConfig = typeof LOCALIZATION_CONFIG;
export type CurrencyConfig = typeof LOCALIZATION_CONFIG.currency;
export type DateConfig = typeof LOCALIZATION_CONFIG.date;
