import { PrismaClient } from '@prisma/client'
import { Decimal } from '@prisma/client/runtime/library'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting minimal database seeding...')

  try {
    // 1. Create basic roles
    const adminRole = await prisma.role.create({
      data: {
        name: 'Admin',
        description: 'System Administrator',
        is_system: true,
      },
    })

    const managerRole = await prisma.role.create({
      data: {
        name: 'Property Manager',
        description: 'Property Management Staff',
        is_system: false,
      },
    })

    // 2. Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 10)
    const adminUser = await prisma.user.create({
      data: {
        username: 'admin',
        email: '<EMAIL>',
        password_hash: hashedPassword,
        first_name: 'System',
        last_name: 'Administrator',
        phone: '+968 2123 4567',
        status: 'ACTIVE',
        email_verified: true,
      },
    })

    await prisma.userRole.create({
      data: {
        user_id: adminUser.id,
        role_id: adminRole.id,
      },
    })

    // 3. Create property types
    const villaType = await prisma.propertyType.create({
      data: {
        name_en: 'Villa',
        name_ar: 'فيلا',
        description_en: 'Standalone villa property',
        description_ar: 'عقار فيلا منفصلة',
      },
    })

    const apartmentType = await prisma.propertyType.create({
      data: {
        name_en: 'Apartment',
        name_ar: 'شقة',
        description_en: 'Apartment unit',
        description_ar: 'وحدة شقة',
      },
    })

    // 4. Create amenities
    const poolAmenity = await prisma.amenity.create({
      data: {
        name_en: 'Swimming Pool',
        name_ar: 'مسبح',
        icon: 'waves',
      },
    })

    const parkingAmenity = await prisma.amenity.create({
      data: {
        name_en: 'Parking',
        name_ar: 'موقف سيارات',
        icon: 'car',
      },
    })

    // 5. Create property owner
    const owner = await prisma.propertyOwner.create({
      data: {
        name_en: 'Ahmed Al-Rashid',
        name_ar: 'أحمد الراشد',
        email: '<EMAIL>',
        phone: '+968 2123 4567',
        mobile: '+968 9123 4567',
        address_en: 'Al-Khuwair, Muscat, Oman',
        address_ar: 'الخوير، مسقط، عمان',
        tax_id: 'TAX001',
        bank_name: 'Bank Muscat',
        bank_account_number: '**********',
        bank_iban: 'OM81BMAG**********123456',
        management_fee_percentage: new Decimal('10.00'),
        status: 'ACTIVE',
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })

    // 6. Create properties
    const villa = await prisma.property.create({
      data: {
        name_en: 'Luxury Villa in Al-Khuwair',
        name_ar: 'فيلا فاخرة في الخوير',
        address_en: 'Street 123, Al-Khuwair, Muscat, Oman',
        address_ar: 'شارع 123، الخوير، مسقط، عمان',
        property_type_id: villaType.id,
        owner_id: owner.id,
        base_rent: new Decimal('1200.000'),
        status: 'AVAILABLE',
        total_area: new Decimal('500.00'),
        floors_count: 2,
        parking_spaces: 3,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })

    const apartment = await prisma.property.create({
      data: {
        name_en: 'Modern Apartment Complex',
        name_ar: 'مجمع شقق حديث',
        address_en: 'Building 45, Ruwi, Muscat, Oman',
        address_ar: 'مبنى 45، روي، مسقط، عمان',
        property_type_id: apartmentType.id,
        owner_id: owner.id,
        base_rent: new Decimal('800.000'),
        status: 'RENTED',
        total_area: new Decimal('1200.00'),
        floors_count: 5,
        parking_spaces: 20,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })

    // 7. Add amenities to properties
    await prisma.propertyAmenity.createMany({
      data: [
        { property_id: villa.id, amenity_id: poolAmenity.id },
        { property_id: villa.id, amenity_id: parkingAmenity.id },
        { property_id: apartment.id, amenity_id: parkingAmenity.id },
      ],
    })

    // 8. Create units
    const villaUnit = await prisma.unit.create({
      data: {
        property_id: villa.id,
        unit_number: 'V01',
        unit_name_en: 'Main Villa Unit',
        unit_name_ar: 'وحدة الفيلا الرئيسية',
        floor_number: 1,
        rooms_count: 4,
        majalis_count: 2,
        bathrooms_count: 3,
        area: new Decimal('500.00'),
        rent_amount: new Decimal('1200.000'),
        status: 'AVAILABLE',
        description_en: 'Spacious 4-bedroom villa with modern amenities',
        description_ar: 'فيلا واسعة من 4 غرف نوم مع وسائل الراحة الحديثة',
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })

    const apartmentUnit = await prisma.unit.create({
      data: {
        property_id: apartment.id,
        unit_number: 'A101',
        unit_name_en: 'Apartment 101',
        unit_name_ar: 'شقة 101',
        floor_number: 1,
        rooms_count: 2,
        majalis_count: 1,
        bathrooms_count: 2,
        area: new Decimal('120.00'),
        rent_amount: new Decimal('600.000'),
        status: 'RENTED',
        description_en: 'Modern 2-bedroom apartment',
        description_ar: 'شقة حديثة من غرفتي نوم',
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })

    // 9. Create tenant
    const tenant = await prisma.tenant.create({
      data: {
        first_name: 'Omar',
        last_name: 'Al-Salam',
        email: '<EMAIL>',
        phone: '+968 9123 4567',
        national_id: '12345678',
        national_id_expiry: new Date('2030-12-31'),
        date_of_birth: new Date('1985-05-15'),
        nationality: 'Omani',
        occupation: 'Engineer',
        company_name: 'Petroleum Development Oman',
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })

    // 10. Create contract
    const contract = await prisma.contract.create({
      data: {
        contract_number: 'CON-0001',
        property_id: apartment.id,
        unit_id: apartmentUnit.id,
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-12-31'),
        monthly_rent: apartmentUnit.rent_amount,
        payment_due_day: 1,
        security_deposit: new Decimal('1200.000'),
        insurance_amount: new Decimal('100.000'),
        insurance_due_date: new Date('2024-06-01'),
        terms_and_conditions: 'Standard rental agreement terms and conditions apply.',
        status: 'ACTIVE',
        auto_renew: false,
        renewal_notice_days: 30,
        notes: 'Initial contract for apartment unit',
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })

    // Link tenant to contract
    await prisma.contractTenant.create({
      data: {
        contract_id: contract.id,
        tenant_id: tenant.id,
        is_primary: true,
      },
    })

    console.log('🎉 Minimal seeding completed successfully!')
    console.log('✅ Created basic data structure with sample records')
    console.log('📧 Admin login: <EMAIL> / admin123')

  } catch (error) {
    console.error('❌ Error during seeding:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
