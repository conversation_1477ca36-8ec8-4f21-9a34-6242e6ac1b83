import { getTranslations } from "next-intl/server";
import { Metada<PERSON> } from "next";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { UnitsDataTable } from "./_components/units-data-table";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "units" });

  return {
    title: t("title"),
    description: t("description"),
  };
}

export default async function UnitsPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "units" });
  const tCommon = await getTranslations({ locale, namespace: "common" });

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${locale}/dashboard`}>
              {tCommon("navigation.dashboard")}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t("title")}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
            {t("title")}
          </h1>
          <p className="text-muted-foreground">
            {t("description")}
          </p>
        </div>
        <Button asChild size="default" className="w-full md:w-auto">
          <Link href={`/${locale}/dashboard/units/create`}>
            <Plus className="h-4 w-4 ltr:mr-2 rtl:ml-2" />
            {t("addUnit")}
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("allUnits")}</CardTitle>
          <CardDescription>
            {t("viewManageUnits")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <UnitsDataTable />
        </CardContent>
      </Card>
    </div>
  );
}