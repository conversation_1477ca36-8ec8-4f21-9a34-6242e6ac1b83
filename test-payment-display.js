const { PrismaClient } = require('./src/generated/prisma');
const prisma = new PrismaClient();

async function testPaymentDisplay() {
  try {
    // Get a subscription payment record
    const payment = await prisma.subscriptionPayment.findFirst({
      where: {
        status: 'PARTIALLY_PAID'
      },
      include: {
        subscription: true,
        member: true
      }
    });
    
    if (!payment) {
      console.log('No partially paid payment found. Checking all payments:');
      const allPayments = await prisma.subscriptionPayment.findMany({
        select: {
          id: true,
          amount: true,
          status: true,
          payment_date: true,
          member: {
            select: {
              full_name: true
            }
          }
        },
        take: 5
      });
      
      console.log('\nFirst 5 payments:');
      allPayments.forEach(p => {
        console.log(`- ID: ${p.id}, Amount: ${p.amount}, Status: ${p.status}, Member: ${p.member.full_name}`);
      });
      
      // Check table structure
      const columns = await prisma.$queryRaw`
        SHOW COLUMNS FROM subscription_payments
      `;
      
      console.log('\nTable columns:');
      columns.forEach(col => {
        if (col.Field.includes('amount') || col.Field.includes('paid')) {
          console.log(`- ${col.Field}: ${col.Type} ${col.Default ? `(default: ${col.Default})` : ''}`);
        }
      });
    } else {
      console.log('Found partially paid payment:');
      console.log(`- ID: ${payment.id}`);
      console.log(`- Amount: ${payment.amount}`);
      console.log(`- Status: ${payment.status}`);
      console.log(`- Member: ${payment.member.full_name}`);
      console.log(`- Payment Date: ${payment.payment_date}`);
      
      // Check for enhanced columns
      console.log('\nChecking for enhanced columns:');
      console.log(`- amount_due: ${payment.amount_due || 'undefined'}`);
      console.log(`- amount_paid: ${payment.amount_paid || 'undefined'}`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPaymentDisplay();