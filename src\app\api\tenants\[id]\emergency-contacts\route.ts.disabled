import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";

import { emergencyContactSchema } from "@/types/tenant";



import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
// GET /api/tenants/[id]/emergency-contacts - Get all emergency contacts for a tenant
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // No authentication required for GET requests in this context

    const tenantId = parseInt(params.id);
    if (isNaN(tenantId)) {
      return ApiResponseBuilder.error(
        "Invalid tenant ID",
        "INVALID_ID",
        400
      );
    }

    // Check if tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
    });

    if (!tenant) {
      return ApiResponseBuilder.error(
        "Tenant not found",
        "NOT_FOUND",
        404
      );
    }

    // Get all emergency contacts for the tenant
    const contacts = await db.emergencyContact.findMany({
      where: { tenant_id: tenantId },
      orderBy: [
        { is_primary: "desc" },
        { created_at: "desc" },
      ],
    });

    return ApiResponseBuilder.success(contacts);
  } catch (error) {
    console.error("Error fetching emergency contacts:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch emergency contacts",
      "FETCH_ERROR",
      500,
      error
    );
  }
}

// POST /api/tenants/[id]/emergency-contacts - Create a new emergency contact
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for tenants
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "tenants", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create tenants");
    }

    // Verify authentication
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for tenants
    const canRead = hasPermission(userPermissions, "tenants", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to read tenants");
    }
    const decoded = verifyToken(token);
    if (!decoded) {
      return ApiResponseBuilder.unauthorized("Invalid authentication token", request.url);
    }

    const tenantId = parseInt(params.id);
    if (isNaN(tenantId)) {
      return ApiResponseBuilder.error(
        "Invalid tenant ID",
        "INVALID_ID",
        400
      );
    }

    // Check if tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
    });

    if (!tenant) {
      return ApiResponseBuilder.error(
        "Tenant not found",
        "NOT_FOUND",
        404
      );
    }

    // Parse request body
    const body = await request.json();
    const validation = emergencyContactSchema.safeParse(body);

    if (!validation.success) {
      return ApiResponseBuilder.error(
        "Invalid request data",
        "VALIDATION_ERROR",
        400,
        validation.error.errors
      );
    }

    const validatedData = validation.data;

    // If this is set as primary, unset other primary contacts
    if (validatedData.is_primary) {
      await db.emergencyContact.updateMany({
        where: { 
          tenant_id: tenantId,
          is_primary: true,
        },
        data: { is_primary: false },
      });
    }

    // Create the emergency contact
    const contact = await db.emergencyContact.create({
      data: {
        tenant_id: tenantId,
        ...validatedData,
      },
    });

    return ApiResponseBuilder.success(contact);
  } catch (error) {
    console.error("Error creating emergency contact:", error);
    return ApiResponseBuilder.error(
      "Failed to create emergency contact",
      "CREATE_ERROR",
      500,
      error
    );
  }
}

// PUT /api/tenants/[id]/emergency-contacts/[contactId] - Update an emergency contact
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; contactId: string } }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for tenants
    const canUpdate = hasPermission(userPermissions, "tenants", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update tenants");
    }

    // No authentication required for GET requests in this context

    const tenantId = parseInt(params.id);
    const contactId = parseInt(params.contactId);
    
    if (isNaN(tenantId) || isNaN(contactId)) {
      return ApiResponseBuilder.error(
        "Invalid ID",
        "INVALID_ID",
        400
      );
    }

    // Check if contact exists and belongs to the tenant
    const existingContact = await db.emergencyContact.findFirst({
      where: { 
        id: contactId,
        tenant_id: tenantId,
      },
    });

    if (!existingContact) {
      return ApiResponseBuilder.error(
        "Emergency contact not found",
        "NOT_FOUND",
        404
      );
    }

    // Parse request body
    const body = await request.json();
    const validation = emergencyContactSchema.safeParse(body);

    if (!validation.success) {
      return ApiResponseBuilder.error(
        "Invalid request data",
        "VALIDATION_ERROR",
        400,
        validation.error.errors
      );
    }

    const validatedData = validation.data;

    // If this is set as primary, unset other primary contacts
    if (validatedData.is_primary && !existingContact.is_primary) {
      await db.emergencyContact.updateMany({
        where: { 
          tenant_id: tenantId,
          is_primary: true,
          id: { not: contactId },
        },
        data: { is_primary: false },
      });
    }

    // Update the emergency contact
    const contact = await db.emergencyContact.update({
      where: { id: contactId },
      data: validatedData,
    });

    return ApiResponseBuilder.success(contact);
  } catch (error) {
    console.error("Error updating emergency contact:", error);
    return ApiResponseBuilder.error(
      "Failed to update emergency contact",
      "UPDATE_ERROR",
      500,
      error
    );
  }
}

// DELETE /api/tenants/[id]/emergency-contacts/[contactId] - Delete an emergency contact
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; contactId: string } }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for tenants
    const canDelete = hasPermission(userPermissions, "tenants", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete tenants");
    }

    // No authentication required for GET requests in this context

    const tenantId = parseInt(params.id);
    const contactId = parseInt(params.contactId);
    
    if (isNaN(tenantId) || isNaN(contactId)) {
      return ApiResponseBuilder.error(
        "Invalid ID",
        "INVALID_ID",
        400
      );
    }

    // Check if contact exists and belongs to the tenant
    const existingContact = await db.emergencyContact.findFirst({
      where: { 
        id: contactId,
        tenant_id: tenantId,
      },
    });

    if (!existingContact) {
      return ApiResponseBuilder.error(
        "Emergency contact not found",
        "NOT_FOUND",
        404
      );
    }

    // Delete the emergency contact
    await db.emergencyContact.delete({
      where: { id: contactId },
    });

    return ApiResponseBuilder.success({ message: "Emergency contact deleted successfully" });
  } catch (error) {
    console.error("Error deleting emergency contact:", error);
    return ApiResponseBuilder.error(
      "Failed to delete emergency contact",
      "DELETE_ERROR",
      500,
      error
    );
  }
}