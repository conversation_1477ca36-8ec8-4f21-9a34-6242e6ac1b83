"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar, DollarSign } from "lucide-react";
import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import { useLocale } from "next-intl";

interface Payment {
  id: string;
  tenant: string;
  property: string;
  unit: string;
  amount: number;
  dueDate: Date;
  status: "upcoming" | "overdue" | "paid";
}

export function UpcomingPayments() {
  const t = useTranslations();
  const locale = useLocale();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data - in real app, fetch from API
    const mockPayments: Payment[] = [
      {
        id: "1",
        tenant: "<PERSON>",
        property: "Sunset Apartments",
        unit: "A-101",
        amount: 1200,
        dueDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2), // 2 days from now
        status: "upcoming",
      },
      {
        id: "2",
        tenant: "<PERSON>",
        property: "Garden Villas",
        unit: "B-205",
        amount: 1500,
        dueDate: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3), // 3 days ago
        status: "overdue",
      },
      {
        id: "3",
        tenant: "Mohammed Al-Said",
        property: "Downtown Tower",
        unit: "C-302",
        amount: 2000,
        dueDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5), // 5 days from now
        status: "upcoming",
      },
      {
        id: "4",
        tenant: "Fatima Hassan",
        property: "Riverside Complex",
        unit: "D-104",
        amount: 1800,
        dueDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // 7 days from now
        status: "upcoming",
      },
    ];

    setPayments(mockPayments);
    setLoading(false);
  }, []);

  const getStatusBadge = (status: Payment["status"]) => {
    switch (status) {
      case "upcoming":
        return <Badge variant="secondary">{t("dashboard.upcoming")}</Badge>;
      case "overdue":
        return <Badge variant="destructive">{t("dashboard.overdue")}</Badge>;
      case "paid":
        return <Badge variant="default">{t("dashboard.paid")}</Badge>;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("dashboard.upcomingPayments")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="p-4 border rounded-lg">
                <div className="space-y-2">
                  <div className="h-4 bg-muted animate-pulse rounded w-1/2" />
                  <div className="h-3 bg-muted animate-pulse rounded w-3/4" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>{t("dashboard.upcomingPayments")}</CardTitle>
        <Button variant="outline" size="sm">
          {t("dashboard.viewAll")}
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {payments.map((payment) => (
            <div
              key={payment.id}
              className={`p-4 border rounded-lg space-y-3 ${
                payment.status === "overdue" ? "border-red-200 bg-red-50" : ""
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">{payment.tenant}</p>
                  <p className="text-xs text-muted-foreground">
                    {payment.property} - {payment.unit}
                  </p>
                </div>
                {getStatusBadge(payment.status)}
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {format(payment.dueDate, "PPP", {
                      locale: locale === "ar" ? ar : enUS,
                    })}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm font-medium">
                  <DollarSign className="h-4 w-4" />
                  <span>OMR {payment.amount.toLocaleString()}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}