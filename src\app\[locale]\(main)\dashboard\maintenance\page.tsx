import { Suspense } from "react";
import { Metada<PERSON> } from "next";
import { getTranslations } from "next-intl/server";
import { Plus } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { MaintenanceDataTable } from "./_components/maintenance-data-table";
import { maintenanceColumns } from "./_components/maintenance-columns";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Maintenance Requests",
  description: "Manage property maintenance requests",
};

export default async function MaintenancePage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale });

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t("maintenance.title")}
          </h1>
          <p className="text-muted-foreground">
            {t("maintenance.description")}
          </p>
        </div>
        <Button asChild>
          <Link href={`/${locale}/dashboard/maintenance/new`}>
            <Plus className="mr-2 h-4 w-4" />
            {t("maintenance.createRequest")}
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("maintenance.allRequests")}</CardTitle>
          <CardDescription>
            {t("maintenance.viewManageRequests")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div>Loading...</div>}>
            <MaintenanceDataTable columns={maintenanceColumns} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}