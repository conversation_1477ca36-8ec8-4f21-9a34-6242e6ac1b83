import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { authenticateUser, generateTokenAsync } from "@/lib/auth";
import { loginSchema } from "@/types/user";

// POST /api/auth/login - Authenticate user
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = loginSchema.parse(body);
    
    // Authenticate user
    const user = await authenticateUser(validatedData.username, validatedData.password);
    
    if (!user) {
      return NextResponse.json(
        { error: "Invalid username or password" },
        { status: 401 }
      );
    }
    
    // Generate JWT token
    const token = await generateTokenAsync(user);
    
    // Create response with token in httpOnly cookie
    const response = NextResponse.json({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        status: user.status,
        permissions: user.permissions,
      },
      message: "Login successful",
    });

    // Set httpOnly cookie with token
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax" as const,
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: "/",
    };

    response.cookies.set("auth-token", token, cookieOptions);

    return response;
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }
    
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
