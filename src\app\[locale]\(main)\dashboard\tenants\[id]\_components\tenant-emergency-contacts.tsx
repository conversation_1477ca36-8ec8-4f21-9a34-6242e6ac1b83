"use client";

import { useState, useEffect } from "react";
import { authenticatedFetch } from "@/lib/api-client";
import { useTranslations } from "next-intl";
import { Phone, User, Plus, Edit2, Trash2, Star } from "lucide-react";
import { toast } from "sonner";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";

interface TenantEmergencyContactsProps {
  tenantId: string;
}

interface EmergencyContact {
  id?: string;
  name: string;
  relationship: string;
  phone: string;
  email?: string;
  is_primary: boolean;
}

export function TenantEmergencyContacts({ tenantId }: TenantEmergencyContactsProps) {
  const t = useTranslations("tenants");
  const tCommon = useTranslations("common");
  
  const [contacts, setContacts] = useState<EmergencyContact[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [editingContact, setEditingContact] = useState<EmergencyContact | null>(null);
  const [formData, setFormData] = useState<EmergencyContact>({
    name: "",
    relationship: "",
    phone: "",
    email: "",
    is_primary: false,
  });

  useEffect(() => {
    fetchContacts();
  }, [tenantId]);

  const fetchContacts = async () => {
    try {
      setLoading(true);
      const result = await authenticatedFetch(`/api/tenants/${tenantId}/emergency-contacts`);
      if (result.success) {
        setContacts(result.data);
      }
    } catch (error) {
      console.error("Error fetching emergency contacts:", error);
      toast.error(t("emergencyContacts.fetchError"));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      const url = editingContact
        ? `/api/tenants/${tenantId}/emergency-contacts/${editingContact.id}`
        : `/api/tenants/${tenantId}/emergency-contacts`;
      
      console.log('Submitting emergency contact:', {
        url,
        method: editingContact ? "PUT" : "POST",
        formData
      });
      
      await authenticatedFetch(url, {
        method: editingContact ? "PUT" : "POST",
        body: JSON.stringify(formData),
      });

      toast.success(
        editingContact
          ? t("emergencyContacts.updateSuccess")
          : t("emergencyContacts.createSuccess")
      );
      
      setDialogOpen(false);
      setEditingContact(null);
      setFormData({
        name: "",
        relationship: "",
        phone: "",
        email: "",
        is_primary: false,
      });
      fetchContacts();
    } catch (error) {
      console.error("Error saving emergency contact:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      toast.error(`${t("emergencyContacts.saveError")}: ${errorMessage}`);
    }
  };

  const handleDelete = async () => {
    if (!deleteId) return;

    try {
      await authenticatedFetch(
        `/api/tenants/${tenantId}/emergency-contacts/${deleteId}`,
        {
          method: "DELETE",
        }
      );

      toast.success(t("emergencyContacts.deleteSuccess"));
      setDeleteId(null);
      fetchContacts();
    } catch (error) {
      console.error("Error deleting emergency contact:", error);
      toast.error(t("emergencyContacts.deleteError"));
    }
  };

  const handleEdit = (contact: EmergencyContact) => {
    setEditingContact(contact);
    setFormData(contact);
    setDialogOpen(true);
  };

  const handleAdd = () => {
    setEditingContact(null);
    setFormData({
      name: "",
      relationship: "",
      phone: "",
      email: "",
      is_primary: false,
    });
    setDialogOpen(true);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("emergencyContacts.title")}</CardTitle>
          <CardDescription>{t("emergencyContacts.description")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{t("emergencyContacts.title")}</CardTitle>
              <CardDescription>{t("emergencyContacts.description")}</CardDescription>
            </div>
            <Button onClick={handleAdd}>
              <Plus className="mr-2 h-4 w-4" />
              {t("emergencyContacts.addContact")}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {contacts.length === 0 ? (
            <div className="text-center py-8">
              <User className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground">{t("emergencyContacts.noContacts")}</p>
              <Button onClick={handleAdd} variant="outline" className="mt-4">
                <Plus className="mr-2 h-4 w-4" />
                {t("emergencyContacts.addFirst")}
              </Button>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2">
              {contacts.map((contact) => (
                <Card key={contact.id}>
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-semibold">{contact.name}</h4>
                          {contact.is_primary && (
                            <Badge variant="default" className="gap-1">
                              <Star className="h-3 w-3" />
                              {t("emergencyContacts.primary")}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {contact.relationship}
                        </p>
                        <div className="flex items-center gap-2 text-sm">
                          <Phone className="h-3 w-3" />
                          {contact.phone}
                        </div>
                        {contact.email && (
                          <p className="text-sm text-muted-foreground">{contact.email}</p>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleEdit(contact)}
                        >
                          <Edit2 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => setDeleteId(contact.id!)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingContact
                ? t("emergencyContacts.editContact")
                : t("emergencyContacts.addContact")}
            </DialogTitle>
            <DialogDescription>
              {t("emergencyContacts.contactFormDescription")}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t("emergencyContacts.form.name")}</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder={t("emergencyContacts.form.namePlaceholder")}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="relationship">
                {t("emergencyContacts.form.relationship")}
              </Label>
              <Input
                id="relationship"
                value={formData.relationship}
                onChange={(e) =>
                  setFormData({ ...formData, relationship: e.target.value })
                }
                placeholder={t("emergencyContacts.form.relationshipPlaceholder")}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">{t("emergencyContacts.form.phone")}</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                placeholder={t("emergencyContacts.form.phonePlaceholder")}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">{t("emergencyContacts.form.email")}</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder={t("emergencyContacts.form.emailPlaceholder")}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_primary"
                checked={formData.is_primary}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, is_primary: checked as boolean })
                }
              />
              <Label htmlFor="is_primary">
                {t("emergencyContacts.form.isPrimary")}
              </Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              {tCommon("cancel")}
            </Button>
            <Button onClick={handleSubmit}>
              {editingContact ? tCommon("update") : tCommon("create")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <AlertDialog open={!!deleteId} onOpenChange={() => setDeleteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("emergencyContacts.deleteTitle")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("emergencyContacts.deleteDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{tCommon("cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>
              {tCommon("delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}