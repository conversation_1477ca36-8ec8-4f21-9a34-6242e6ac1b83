const fs = require('fs');
const path = require('path');

// Sub-routes that need permissions
const subRoutes = [
  // Contracts
  { path: 'contracts/[id]/documents/route.ts', module: 'contracts', actions: ['GET', 'POST'] },
  { path: 'contracts/[id]/documents/[documentId]/download/route.ts', module: 'contracts', actions: ['GET'] },
  { path: 'contracts/[id]/documents/[documentId]/route.ts', module: 'contracts', actions: ['DELETE'] },
  { path: 'contracts/[id]/renew/route.ts', module: 'contracts', actions: ['POST'] },
  { path: 'contracts/[id]/terminate/route.ts', module: 'contracts', actions: ['POST'] },
  { path: 'contracts/[id]/tenants/route.ts', module: 'contracts', actions: ['GET', 'POST'] },
  { path: 'contracts/[id]/tenants/[tenantId]/route.ts', module: 'contracts', actions: ['PUT', 'DELETE'] },
  
  // Maintenance
  { path: 'maintenance/[id]/attachments/route.ts', module: 'maintenance', actions: ['GET', 'POST'] },
  { path: 'maintenance/[id]/attachments/[attachmentId]/route.ts', module: 'maintenance', actions: ['DELETE'] },
  { path: 'maintenance/[id]/status/route.ts', module: 'maintenance', actions: ['PUT'] },
  
  // Tenants
  { path: 'tenants/[id]/documents/route.ts', module: 'tenants', actions: ['GET', 'POST'] },
  { path: 'tenants/[id]/documents/[documentId]/route.ts', module: 'tenants', actions: ['DELETE'] },
  { path: 'tenants/[id]/emergency-contacts/route.ts', module: 'tenants', actions: ['GET', 'POST', 'PUT', 'DELETE'] },
  { path: 'tenants/[id]/financial-summary/route.ts', module: 'tenants', actions: ['GET'] },
  
  // Invoices
  { path: 'invoices/[id]/cancel/route.ts', module: 'invoices', actions: ['POST'] },
  { path: 'invoices/generate-from-contracts/route.ts', module: 'invoices', actions: ['POST'] },
  
  // Payments
  { path: 'payments/[id]/cancel/route.ts', module: 'payments', actions: ['POST'] },
  { path: 'payments/[id]/refund/route.ts', module: 'payments', actions: ['POST'] },
  
  // Owner Payouts
  { path: 'owner-payouts/[id]/approve/route.ts', module: 'owner-payouts', actions: ['POST'] },
  { path: 'owner-payouts/[id]/pay/route.ts', module: 'owner-payouts', actions: ['POST'] },
  
  // Expenses
  { path: 'expenses/[id]/approve/route.ts', module: 'expenses', actions: ['GET', 'POST'] },
];

function updateSubRoute(filePath, module, actions) {
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  // Check if already has permission imports
  if (content.includes('getUserFromRequest') && content.includes('hasPermission')) {
    console.log(`✓ ${path.basename(path.dirname(filePath))}/${path.basename(filePath)} already has permissions`);
    return;
  }
  
  // Add import if not present
  if (!content.includes('getUserFromRequest')) {
    // Find the last import line
    const importMatch = content.match(/import[\s\S]*?from\s+["'][^"']+["'];/g);
    if (importMatch) {
      const lastImport = importMatch[importMatch.length - 1];
      const lastImportIndex = content.lastIndexOf(lastImport);
      const insertPosition = lastImportIndex + lastImport.length;
      
      const newImport = '\nimport { getUserFromRequest, hasPermission } from "@/lib/permissions";';
      content = content.slice(0, insertPosition) + newImport + content.slice(insertPosition);
    }
  }
  
  // Update methods based on actions
  actions.forEach(action => {
    const methodName = action;
    const permissionAction = action === 'GET' ? 'READ' : action === 'POST' ? 'CREATE' : action === 'PUT' ? 'UPDATE' : 'DELETE';
    
    if (content.includes(`export async function ${methodName}`)) {
      const methodRegex = new RegExp(`export async function ${methodName}\\([^)]*\\)[^{]*{\\s*try\\s*{`);
      const permissionCheck = `
    // Check authentication and permissions
    const user = await getUserFromRequest(request);
    if (!user || !user.userId) {
      return ApiResponseBuilder.unauthorized();
    }

    // Check if user has ${permissionAction} permission for ${module}
    const can${permissionAction.charAt(0) + permissionAction.slice(1).toLowerCase()} = await hasPermission(user.userId, "${module}", "${permissionAction}");
    if (!can${permissionAction.charAt(0) + permissionAction.slice(1).toLowerCase()}) {
      return ApiResponseBuilder.forbidden("You don't have permission to ${permissionAction.toLowerCase()} ${module.replace('-', ' ')}");
    }
`;
      
      // Remove old auth code if exists
      const authPatterns = [
        /\/\/ Verify authentication[\s\S]*?\/\/ TODO:.*?\n/,
        /const token = request\.cookies\.get\("auth-token"\)[\s\S]*?}\s*\n/,
      ];
      
      let replaced = false;
      for (const pattern of authPatterns) {
        if (pattern.test(content)) {
          content = content.replace(pattern, permissionCheck.trim() + '\n');
          replaced = true;
          break;
        }
      }
      
      if (!replaced) {
        content = content.replace(methodRegex, (match) => {
          return match + permissionCheck;
        });
      }
    }
  });
  
  // Save if modified
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Updated ${path.basename(path.dirname(filePath))}/${path.basename(filePath)}`);
  }
}

// Process each sub-route
subRoutes.forEach(route => {
  const fullPath = path.join(__dirname, '..', 'src', 'app', 'api', route.path);
  if (fs.existsSync(fullPath)) {
    console.log(`\nProcessing ${route.path}...`);
    updateSubRoute(fullPath, route.module, route.actions);
  } else {
    console.log(`\n❌ File not found: ${route.path}`);
  }
});

console.log('\n✅ Sub-route permission updates complete!');