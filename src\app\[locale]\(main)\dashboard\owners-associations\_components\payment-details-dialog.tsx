"use client";

import { useTranslations, useLocale } from "next-intl";
import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Calendar,
  User,
  Home,
  FileText,
  Banknote,
  CreditCard,
  CheckCircle,
  AlertCircle,
  Clock,
  TrendingDown
} from "lucide-react";
import { cn } from "@/lib/utils";
import { formatCurrency } from "@/lib/utils";
import { type SubscriptionPaymentWithRelations } from "@/types/owners-association";
import { useRTL } from "@/hooks/use-rtl";

interface PaymentDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  payment: SubscriptionPaymentWithRelations;
  associationId: number;
}

export function PaymentDetailsDialog({
  open,
  onOpenChange,
  payment,
}: PaymentDetailsDialogProps) {
  const locale = useLocale();
  const { isRTL } = useRTL();
  const t = useTranslations("ownersAssociations.payments.details");
  const dateLocale = locale === 'ar' ? ar : enUS;

  const amountDue = parseFloat(payment.amount_due?.toString() || payment.amount.toString());
  const amountPaid = parseFloat(payment.amount_paid?.toString() || "0");
  const remainingBalance = amountDue - amountPaid;
  const paymentPercentage = amountDue > 0 ? (amountPaid / amountDue) * 100 : 0;

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PAID":
        return (
          <Badge variant="default" className="gap-1">
            <CheckCircle className="h-3 w-3" />
            {t("status.paid")}
          </Badge>
        );
      case "PARTIALLY_PAID":
        return (
          <Badge variant="secondary" className="gap-1">
            <Clock className="h-3 w-3" />
            {t("status.partiallyPaid")}
          </Badge>
        );
      case "OVERDUE":
        return (
          <Badge variant="destructive" className="gap-1">
            <AlertCircle className="h-3 w-3" />
            {t("status.overdue")}
          </Badge>
        );
      case "UNPAID":
        return (
          <Badge variant="outline" className="gap-1">
            <TrendingDown className="h-3 w-3" />
            {t("status.unpaid")}
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>
            {t("description")}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Member Information */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-muted-foreground">{t("memberInfo")}</h3>
            <div className="rounded-lg border p-4 space-y-2">
              <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">{payment.member.full_name}</span>
              </div>
              <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <Home className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{t("unit")}: {payment.member.unit_number}</span>
              </div>
              {payment.member.email && (
                <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                  <span className="text-sm text-muted-foreground">{payment.member.email}</span>
                </div>
              )}
            </div>
          </div>

          {/* Subscription Information */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-muted-foreground">{t("subscriptionInfo")}</h3>
            <div className="rounded-lg border p-4 space-y-2">
              <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">
                  {locale === 'ar' ? payment.subscription.name_ar : payment.subscription.name_en}
                </span>
              </div>
              <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <Banknote className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  {formatCurrency(payment.subscription.amount.toString())} / {t(`frequency.${payment.subscription.frequency.toLowerCase()}`)}
                </span>
              </div>
            </div>
          </div>

          {/* Payment Summary */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-muted-foreground">{t("paymentSummary")}</h3>
            <div className="rounded-lg border p-4 space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">{t("dueDate")}:</span>
                <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">
                    {format(new Date(payment.due_date), "dd/MM/yyyy", { locale: dateLocale })}
                  </span>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{t("amountDue")}:</span>
                  <span className="font-medium">{formatCurrency(amountDue)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>{t("amountPaid")}:</span>
                  <span className="font-medium text-green-600">{formatCurrency(amountPaid)}</span>
                </div>
                <div className="flex justify-between text-sm font-medium">
                  <span>{t("remainingBalance")}:</span>
                  <span className={remainingBalance > 0 ? "text-red-600" : "text-green-600"}>
                    {formatCurrency(remainingBalance)}
                  </span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">{t("paymentProgress")}:</span>
                  <span className="text-sm font-medium">{paymentPercentage.toFixed(1)}%</span>
                </div>
                <Progress value={paymentPercentage} className="h-2" />
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm">{t("currentStatus")}:</span>
                {getStatusBadge(payment.status)}
              </div>
              
              {payment.installment && (
                <div className="flex justify-between items-center">
                  <span className="text-sm">{t("installmentNumber")}:</span>
                  <Badge variant="outline" className="font-medium">
                    {t("installment")} #{payment.installment}
                  </Badge>
                </div>
              )}
            </div>
          </div>

          {/* Payment History */}
          {/* Payment History - Currently installments not implemented in schema */}
          {false && (
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-muted-foreground">{t("paymentHistory")}</h3>
              <div className="rounded-lg border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("date")}</TableHead>
                      <TableHead>{t("amount")}</TableHead>
                      <TableHead>{t("method")}</TableHead>
                      <TableHead>{t("reference")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {/* Placeholder for installments when implemented */}
                    {[].map((installment: any) => (
                      <TableRow key={installment.id}>
                        <TableCell>
                          {format(new Date(installment.payment_date), "dd/MM/yyyy", { locale: dateLocale })}
                        </TableCell>
                        <TableCell className="font-medium">
                          {formatCurrency(installment.amount)}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            <CreditCard className="h-3 w-3 mr-1" />
                            {t(`methods.${installment.payment_method.toLowerCase()}`)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {installment.reference_number || "-"}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}

          {/* Full Payment Transaction */}
          {payment.transaction && (
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-muted-foreground">{t("transactionInfo")}</h3>
              <div className="rounded-lg border p-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{t("transactionDate")}:</span>
                  <span className="font-medium">
                    {format(new Date(payment.transaction.transaction_date), "dd/MM/yyyy", { locale: dateLocale })}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>{t("transactionAmount")}:</span>
                  <span className="font-medium text-green-600">
                    {formatCurrency(payment.transaction.amount.toString())}
                  </span>
                </div>
                {/* Reference number field not in current transaction schema */}
                {payment.transaction.notes && (
                  <div className="flex justify-between text-sm">
                    <span>{t("notes")}:</span>
                    <span className="text-xs">{payment.transaction.notes}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Notes */}
          {payment.notes && (
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-muted-foreground">{t("notes")}</h3>
              <div className="rounded-lg border p-4">
                <p className="text-sm text-muted-foreground">{payment.notes}</p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}