const fetch = require('node-fetch');

async function testFinalAuth() {
  try {
    console.log('=== Final Authentication Test ===\n');
    
    // Step 1: Test if we get redirected to login when not authenticated
    console.log('1. Testing unauthenticated access to dashboard...');
    const unauthResponse = await fetch('http://localhost:3000/en/dashboard/properties', {
      redirect: 'manual'
    });
    
    console.log('Unauthenticated status:', unauthResponse.status);
    if (unauthResponse.status >= 300 && unauthResponse.status < 400) {
      const location = unauthResponse.headers.get('location');
      console.log('✓ Correctly redirected to:', location);
    }
    
    // Step 2: Login
    console.log('\n2. Attempting login...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: '<EMAIL>',
        password: '123456'
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('Login status:', loginResponse.status);
    
    if (loginResponse.status !== 200) {
      console.error('✗ Login failed:', loginData);
      return;
    }
    
    // Extract token
    const cookies = loginResponse.headers.get('set-cookie');
    const tokenMatch = cookies.match(/auth-token=([^;]+)/);
    const token = tokenMatch ? tokenMatch[1] : null;
    
    if (!token) {
      console.error('✗ No token found in login response');
      return;
    }
    
    console.log('✓ Login successful');
    
    // Step 3: Test authenticated access to dashboard
    console.log('\n3. Testing authenticated access to dashboard...');
    const authResponse = await fetch('http://localhost:3000/en/dashboard/properties', {
      headers: {
        'Cookie': `auth-token=${token}`
      },
      redirect: 'manual'
    });
    
    console.log('Authenticated status:', authResponse.status);
    
    if (authResponse.status === 200) {
      console.log('✓ Dashboard accessible with authentication');
    } else if (authResponse.status >= 300 && authResponse.status < 400) {
      console.log('- Dashboard redirected to:', authResponse.headers.get('location'));
    } else {
      console.log('✗ Dashboard access failed');
    }
    
    // Step 4: Test /api/auth/me
    console.log('\n4. Testing /api/auth/me...');
    const meResponse = await fetch('http://localhost:3000/api/auth/me', {
      headers: {
        'Cookie': `auth-token=${token}`
      }
    });
    
    console.log('Me endpoint status:', meResponse.status);
    
    if (meResponse.status === 200) {
      const meData = await meResponse.json();
      console.log('✓ User authenticated as:', meData.user.username);
      console.log('✓ Properties permission:', !!meData.user.permissions.properties?.read);
    }
    
    console.log('\n=== Test Complete ===');
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

testFinalAuth();