"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useTranslations, useLocale } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { format, addMonths } from "date-fns";
import { CalendarIcon, Loader2, Plus, Trash } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import {
  Pop<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { invoiceSchema, type InvoiceInput, type InvoiceItemInput } from "@/types/invoice";
import type { TenantWithRelations } from "@/types/tenant";
import type { PropertyWithRelations } from "@/types/property";
import type { UnitWithRelations } from "@/types/unit";
import type { ContractWithRelations } from "@/types/contract";

export function InvoiceForm() {
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations("invoices");
  const tCommon = useTranslations("common");
  
  const [loading, setLoading] = useState(false);
  const [tenants, setTenants] = useState<TenantWithRelations[]>([]);
  const [properties, setProperties] = useState<PropertyWithRelations[]>([]);
  const [units, setUnits] = useState<UnitWithRelations[]>([]);
  const [contracts, setContracts] = useState<ContractWithRelations[]>([]);
  const [loadingTenants, setLoadingTenants] = useState(true);
  const [loadingProperties, setLoadingProperties] = useState(true);
  const [loadingUnits, setLoadingUnits] = useState(false);
  const [loadingContracts, setLoadingContracts] = useState(false);

  const form = useForm<InvoiceInput>({
    resolver: zodResolver(invoiceSchema),
    defaultValues: {
      contract_id: undefined,
      tenant_id: undefined,
      property_id: undefined,
      unit_id: undefined,
      invoice_date: new Date().toISOString().split('T')[0],
      due_date: addMonths(new Date(), 1).toISOString().split('T')[0],
      items: [
        {
          description_en: "Monthly Rent",
          description_ar: "الإيجار الشهري",
          quantity: 1,
          unit_price: "",
        },
      ],
      notes: "",
    },
  });

  // Fetch tenants
  useEffect(() => {
    const fetchTenants = async () => {
      try {
        const response = await fetch("/api/tenants");
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setTenants(result.data);
          }
        }
      } catch (error) {
        console.error("Error fetching tenants:", error);
        toast.error("Failed to load tenants");
      } finally {
        setLoadingTenants(false);
      }
    };

    fetchTenants();
  }, []);

  // Fetch properties
  useEffect(() => {
    const fetchProperties = async () => {
      try {
        const response = await fetch("/api/properties");
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setProperties(result.data);
          }
        }
      } catch (error) {
        console.error("Error fetching properties:", error);
        toast.error("Failed to load properties");
      } finally {
        setLoadingProperties(false);
      }
    };

    fetchProperties();
  }, []);

  // Fetch units when property is selected
  const handlePropertyChange = async (propertyId: string) => {
    form.setValue("property_id", parseInt(propertyId));
    // Reset unit_id to force reselection
    if (form.getValues("unit_id")) {
      form.setValue("unit_id", 0 as any);
    }
    
    setLoadingUnits(true);
    try {
      const response = await fetch(`/api/units?property_id=${propertyId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setUnits(result.data);
        }
      }
    } catch (error) {
      console.error("Error fetching units:", error);
      toast.error("Failed to load units");
    } finally {
      setLoadingUnits(false);
    }
  };

  // Fetch contracts when tenant is selected
  const handleTenantChange = async (tenantId: string) => {
    form.setValue("tenant_id", parseInt(tenantId));
    
    setLoadingContracts(true);
    try {
      const response = await fetch(`/api/contracts?tenant_id=${tenantId}&status=ACTIVE`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setContracts(result.data);
        }
      }
    } catch (error) {
      console.error("Error fetching contracts:", error);
      toast.error("Failed to load contracts");
    } finally {
      setLoadingContracts(false);
    }
  };

  // Handle contract selection
  const handleContractChange = (contractId: string) => {
    const contract = contracts.find(c => c.id === parseInt(contractId));
    if (contract) {
      form.setValue("contract_id", contract.id);
      // Set tenant from the first tenant in the contract
      if (contract.tenants && contract.tenants.length > 0) {
        form.setValue("tenant_id", contract.tenants[0].tenant.id);
      }
      form.setValue("property_id", contract.property?.id || 0);
      form.setValue("unit_id", contract.unit?.id || 0);
      
      // Set the rent amount in the first item
      const items = form.getValues("items");
      if (items.length > 0) {
        items[0].unit_price = contract.monthly_rent.toString();
        form.setValue("items", items);
      }
    }
  };

  // Add invoice item
  const addItem = () => {
    const items = form.getValues("items");
    form.setValue("items", [
      ...items,
      {
        description_en: "",
        description_ar: "",
        quantity: 1,
        unit_price: "",
      },
    ]);
  };

  // Remove invoice item
  const removeItem = (index: number) => {
    const items = form.getValues("items");
    if (items.length > 1) {
      form.setValue(
        "items",
        items.filter((_, i) => i !== index)
      );
    }
  };

  // Calculate item amount - amount is derived from quantity * unit_price
  const calculateItemAmount = (index: number, quantity: number, rate: string) => {
    // Update the unit_price when rate changes
    const items = form.getValues("items");
    items[index].unit_price = rate;
    form.setValue("items", items);
  };

  const onSubmit = async (data: InvoiceInput) => {
    try {
      setLoading(true);

      const response = await fetch("/api/invoices", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error?.message || "Failed to create invoice");
      }

      const result = await response.json();
      
      toast.success("Invoice created successfully");
      router.push(`/${locale}/dashboard/invoices`);
    } catch (error) {
      console.error("Error creating invoice:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create invoice");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{t("actions.create")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Tenant Selection */}
            <FormField
              control={form.control}
              name="tenant_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("table.tenant")}</FormLabel>
                  <Select
                    onValueChange={handleTenantChange}
                    value={field.value?.toString()}
                    disabled={loadingTenants}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={tCommon("placeholders.select")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {tenants.map((tenant) => (
                        <SelectItem key={tenant.id} value={tenant.id.toString()}>
                          {`${tenant.first_name} ${tenant.last_name}`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Contract Selection (optional) */}
            {contracts.length > 0 && (
              <FormField
                control={form.control}
                name="contract_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("contract")} ({tCommon("optional")})</FormLabel>
                    <Select
                      onValueChange={handleContractChange}
                      value={field.value?.toString()}
                      disabled={loadingContracts}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={tCommon("placeholders.select")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {contracts.map((contract) => (
                          <SelectItem key={contract.id} value={contract.id.toString()}>
                            {contract.contract_number} - {contract.property && (locale === "ar" ? contract.property.name_ar : contract.property.name_en)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      {t("contractDescription")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Property Selection */}
            <FormField
              control={form.control}
              name="property_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("table.property")}</FormLabel>
                  <Select
                    onValueChange={handlePropertyChange}
                    value={field.value?.toString()}
                    disabled={loadingProperties}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={tCommon("placeholders.select")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {properties.map((property) => (
                        <SelectItem key={property.id} value={property.id.toString()}>
                          {locale === "ar" ? property.name_ar : property.name_en}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Unit Selection */}
            {units.length > 0 && (
              <FormField
                control={form.control}
                name="unit_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("table.unit")}</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      value={field.value?.toString()}
                      disabled={loadingUnits}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={tCommon("placeholders.select")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {units.map((unit) => (
                          <SelectItem key={unit.id} value={unit.id.toString()}>
                            {unit.unit_number}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="grid grid-cols-2 gap-4">
              {/* Invoice Date */}
              <FormField
                control={form.control}
                name="invoice_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>{t("table.invoiceDate")}</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(new Date(field.value), "PPP")
                            ) : (
                              <span>{tCommon("placeholders.pickDate")}</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value ? new Date(field.value) : undefined}
                          onSelect={(date) => field.onChange(date?.toISOString().split('T')[0])}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Due Date */}
              <FormField
                control={form.control}
                name="due_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>{t("table.dueDate")}</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(new Date(field.value), "PPP")
                            ) : (
                              <span>{tCommon("placeholders.pickDate")}</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value ? new Date(field.value) : undefined}
                          onSelect={(date) => field.onChange(date?.toISOString().split('T')[0])}
                          disabled={(date) =>
                            date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Invoice Items */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <FormLabel>{t("items")}</FormLabel>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addItem}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  {t("addItem")}
                </Button>
              </div>

              {form.watch("items").map((_, index) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="grid grid-cols-12 gap-4">
                      <div className="col-span-5">
                        <FormField
                          control={form.control}
                          name={`items.${index}.description_en`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{t("itemDescription")}</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="col-span-2">
                        <FormField
                          control={form.control}
                          name={`items.${index}.quantity`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{t("quantity")}</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  type="number"
                                  min="1"
                                  onChange={(e) => {
                                    const quantity = parseInt(e.target.value) || 1;
                                    field.onChange(quantity);
                                    const rate = form.getValues(`items.${index}.unit_price`);
                                    calculateItemAmount(index, quantity, rate);
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="col-span-2">
                        <FormField
                          control={form.control}
                          name={`items.${index}.unit_price`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{t("rate")} (OMR)</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  type="number"
                                  step="0.001"
                                  onChange={(e) => {
                                    field.onChange(e.target.value);
                                    const quantity = form.getValues(`items.${index}.quantity`);
                                    calculateItemAmount(index, quantity, e.target.value);
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="col-span-2">
                        <div className="space-y-2">
                          <FormLabel>{t("table.amount")} (OMR)</FormLabel>
                          <div className="h-10 px-3 py-2 bg-muted rounded-md flex items-center">
                            {(parseFloat(form.watch(`items.${index}.unit_price`) || "0") * form.watch(`items.${index}.quantity`)).toFixed(3)}
                          </div>
                        </div>
                      </div>
                      <div className="col-span-1 flex items-end">
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => removeItem(index)}
                          disabled={form.watch("items").length === 1}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("notes")} ({tCommon("optional")})</FormLabel>
                  <FormControl>
                    <Textarea {...field} value={field.value ?? ""} rows={2} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={loading}
          >
            {tCommon("actions.cancel")}
          </Button>
          <Button type="submit" disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {tCommon("actions.create")}
          </Button>
        </div>
      </form>
    </Form>
  );
}