const mysql = require('mysql2/promise');

async function testConnection() {
  try {
    console.log('🔍 Testing MySQL connection...');
    
    // Test connection with no password
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '', // No password
      port: 3306
    });
    
    console.log('✅ Successfully connected to MySQL!');
    
    // Test if database exists
    const [databases] = await connection.execute('SHOW DATABASES');
    console.log('📊 Available databases:');
    databases.forEach(db => console.log(`   - ${db.Database}`));
    
    // Check if property_management database exists
    const dbExists = databases.some(db => db.Database === 'property_management');
    if (dbExists) {
      console.log('✅ property_management database exists');
    } else {
      console.log('⚠️  property_management database does not exist');
      console.log('Creating property_management database...');
      await connection.execute('CREATE DATABASE property_management');
      console.log('✅ property_management database created');
    }
    
    await connection.end();
    console.log('🎉 MySQL connection test completed successfully!');
    
  } catch (error) {
    console.error('❌ MySQL connection failed:');
    console.error('Error:', error.message);
    console.error('Code:', error.code);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Troubleshooting tips:');
      console.log('1. Make sure MySQL is running in XAMPP Control Panel');
      console.log('2. Check if port 3306 is being used by another service');
      console.log('3. Try restarting XAMPP');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 Access denied - try these steps:');
      console.log('1. Reset MySQL root password using the methods above');
      console.log('2. Make sure you\'re using the correct username/password');
    }
  }
}

testConnection();
