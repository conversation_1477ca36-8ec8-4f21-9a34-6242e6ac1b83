"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, Edit, Trash2, Home } from "lucide-react";
import { AmenityWithRelations } from "@/types/amenity";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import * as Icons from "lucide-react";
import { useState } from "react";
import { DeleteAmenityDialog } from "./delete-amenity-dialog";

// Helper function to create translated column headers
const createHeader = (translationKey: string) => {
  return () => {
    const t = useTranslations();
    return t(translationKey);
  };
};

export const amenityColumns: ColumnDef<AmenityWithRelations>[] = [
  {
    accessorKey: "icon",
    header: createHeader("amenities.icon"),
    cell: ({ row }) => {
      const iconName = row.original.icon || "Home";
      const Icon = Icons[iconName as keyof typeof Icons] as any || Icons.Home;
      
      return (
        <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-muted">
          <Icon className="h-5 w-5" />
        </div>
      );
    },
  },
  {
    accessorKey: "name_en",
    header: createHeader("common.name"),
    cell: ({ row }) => {
      const locale = useLocale();
      const amenity = row.original;
      const name = locale === "ar" ? amenity.name_ar : amenity.name_en;
      
      return (
        <div className="font-medium">
          {name}
        </div>
      );
    },
  },
  {
    accessorKey: "name_ar",
    header: createHeader("amenities.nameAr"),
    cell: ({ row }) => {
      return (
        <div className="font-medium" dir="rtl">
          {row.original.name_ar}
        </div>
      );
    },
  },
  {
    accessorKey: "_count.property_amenities",
    header: createHeader("navigation.properties"),
    cell: ({ row }) => {
      const count = row.original._count?.property_amenities || 0;
      const t = useTranslations();
      return (
        <Badge variant={count > 0 ? "default" : "secondary"}>
          {count} {t(count === 1 ? "common.property" : "navigation.properties").toLowerCase()}
        </Badge>
      );
    },
  },
  {
    accessorKey: "created_at",
    header: createHeader("common.createdAt"),
    cell: ({ row }) => {
      const date = new Date(row.original.created_at);
      return date.toLocaleDateString();
    },
  },
  {
    id: "actions",
    header: () => {
      const t = useTranslations();
      return <div className="text-center">{t("common.actions")}</div>;
    },
    cell: ({ row }) => {
      const amenity = row.original;
      const t = useTranslations();
      const [showDeleteDialog, setShowDeleteDialog] = useState(false);

      return (
        <div className="text-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t("common.actions")}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/amenities/${amenity.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  {t("common.view")}
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/amenities/${amenity.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  {t("common.edit")}
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className="text-destructive"
                onClick={() => setShowDeleteDialog(true)}
                disabled={(amenity._count?.property_amenities ?? 0) > 0}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {t("common.delete")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DeleteAmenityDialog
            amenity={amenity}
            open={showDeleteDialog}
            onOpenChange={setShowDeleteDialog}
          />
        </div>
      );
    },
  },
];