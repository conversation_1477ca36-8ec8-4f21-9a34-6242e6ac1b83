import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for payments
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "payments", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view payments");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const paymentId = parseInt(id);
    if (isNaN(paymentId)) {
      return ApiResponseBuilder.error("Invalid payment ID", "BAD_REQUEST", 400);
    }

    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        invoice: true,
        tenant: true,
        allocations: {
          include: {
            invoice: {
              include: {
                property: true,
                unit: true,
              },
            },
          },
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    if (!payment) {
      return ApiResponseBuilder.error("Payment not found", "NOT_FOUND", 404);
    }

    return ApiResponseBuilder.success(payment);
  } catch (error) {
    console.error("Error fetching payment:", error);
    return ApiResponseBuilder.error("Failed to fetch payment", "INTERNAL_ERROR", 500);
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for payments
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "payments", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete payments");
    }

    // User is already authenticated via token above

    const { id } = await params;


    const paymentId = parseInt(id);
    if (isNaN(paymentId)) {
      return ApiResponseBuilder.error("Invalid payment ID", "BAD_REQUEST", 400);
    }

    // Check if payment exists
    const payment = await prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        allocations: true,
      },
    });

    if (!payment) {
      return ApiResponseBuilder.error("Payment not found", "NOT_FOUND", 404);
    }

    // Only allow deletion of pending or cancelled payments
    if (!['PENDING', 'CANCELLED'].includes(payment.status)) {
      return ApiResponseBuilder.error("Only pending or cancelled payments can be deleted", "BAD_REQUEST", 400);
    }

    // Delete payment in a transaction (will cascade delete allocations)
    await prisma.$transaction(async (tx) => {
      // If payment had allocations, revert invoice amounts
      for (const allocation of payment.allocations) {
        const invoice = await tx.invoice.findUnique({
          where: { id: allocation.invoice_id },
        });

        if (invoice && payment.status === 'COMPLETED') {
          const newPaidAmount = parseFloat(invoice.paid_amount.toString()) - parseFloat(allocation.allocated_amount.toString());
          const newBalance = parseFloat(invoice.total_amount.toString()) - newPaidAmount;
          
          let newStatus = invoice.status;
          if (newPaidAmount <= 0) {
            newStatus = new Date(invoice.due_date) < new Date() ? 'OVERDUE' : 'PENDING';
          } else {
            newStatus = 'PARTIALLY_PAID';
          }

          await tx.invoice.update({
            where: { id: allocation.invoice_id },
            data: {
              paid_amount: newPaidAmount,
              balance_amount: newBalance,
              status: newStatus,
              updated_by: decoded.id,
            },
          });
        }
      }

      // Delete payment
      await tx.payment.delete({
        where: { id: paymentId },
      });
    });

    return ApiResponseBuilder.success({ message: "Payment deleted successfully" });
  } catch (error) {
    console.error("Error deleting payment:", error);
    return ApiResponseBuilder.error("Failed to delete payment", "INTERNAL_ERROR", 500);
  }
}