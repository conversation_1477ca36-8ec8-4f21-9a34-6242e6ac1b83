"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations, useLocale } from "next-intl";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { apiClient } from "@/lib/api-client";
import { associationSubscriptionUpdateSchema, type AssociationSubscriptionUpdateInput, type AssociationSubscriptionWithRelations } from "@/types/owners-association";
import { useRTL } from "@/hooks/use-rtl";

interface EditSubscriptionFormProps {
  associationId: number;
  subscription: AssociationSubscriptionWithRelations;
  onSuccess: () => void;
  onCancel: () => void;
}

export function EditSubscriptionForm({ associationId, subscription, onSuccess, onCancel }: EditSubscriptionFormProps) {
  const t = useTranslations("ownersAssociations.subscriptions");
  const locale = useLocale();
  const { isRTL } = useRTL();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<AssociationSubscriptionUpdateInput>({
    resolver: zodResolver(associationSubscriptionUpdateSchema),
    defaultValues: {
      name_en: subscription.name_en,
      name_ar: subscription.name_ar,
      amount: subscription.amount.toString(),
      frequency: subscription.frequency,
      is_active: subscription.is_active,
    },
  });

  const onSubmit = async (data: AssociationSubscriptionUpdateInput) => {
    try {
      setIsLoading(true);
      
      // Only send changed fields
      const changedFields: Partial<AssociationSubscriptionUpdateInput> = {};
      let hasChanges = false;

      if (data.name_en !== subscription.name_en) {
        changedFields.name_en = data.name_en;
        hasChanges = true;
      }
      if (data.name_ar !== subscription.name_ar) {
        changedFields.name_ar = data.name_ar;
        hasChanges = true;
      }
      if (data.amount !== subscription.amount.toString()) {
        changedFields.amount = data.amount;
        hasChanges = true;
      }
      if (data.frequency !== subscription.frequency) {
        changedFields.frequency = data.frequency;
        hasChanges = true;
      }
      if (data.is_active !== subscription.is_active) {
        changedFields.is_active = data.is_active;
        hasChanges = true;
      }

      if (!hasChanges) {
        toast.info(t("noChanges"));
        return;
      }
      
      // Check if we have a PUT or PATCH endpoint
      const response = await apiClient.put(
        `/api/owners-associations/${associationId}/subscriptions/${subscription.id}`,
        changedFields
      );

      if (response.success) {
        toast.success(t("editSuccess"));
        onSuccess();
      }
    } catch (error: any) {
      console.error("Error updating subscription:", error);
      toast.error(t("editError"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name_en"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("nameEn")}</FormLabel>
              <FormControl>
                <Input {...field} placeholder={t("nameEnPlaceholder")} />
              </FormControl>
              <FormDescription>{t("nameEnDescription")}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="name_ar"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("nameAr")}</FormLabel>
              <FormControl>
                <Input {...field} placeholder={t("nameArPlaceholder")} dir="rtl" />
              </FormControl>
              <FormDescription>{t("nameArDescription")}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("amount")}</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="0.000" />
                </FormControl>
                <FormDescription>{t("amountDescription")}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="frequency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("frequencyLabel")}</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectFrequency")} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="MONTHLY">{t("frequency.monthly")}</SelectItem>
                    <SelectItem value="QUARTERLY">{t("frequency.quarterly")}</SelectItem>
                    <SelectItem value="YEARLY">{t("frequency.yearly")}</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>{t("frequencyDescription")}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="is_active"
          render={({ field }) => (
            <FormItem className={cn("flex flex-row items-start space-x-3 space-y-0", isRTL && "flex-row-reverse space-x-reverse")}>
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>{t("isActive")}</FormLabel>
                <FormDescription>
                  {t("isActiveDescription")}
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        <div className={cn("flex gap-3", isRTL && "flex-row-reverse")}>
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isLoading}
          >
            {t("cancel")}
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className={cn("h-4 w-4 animate-spin", isRTL ? "ml-2" : "mr-2")} />}
            {t("saveChanges")}
          </Button>
        </div>
      </form>
    </Form>
  );
}