const { PrismaClient } = require('./src/generated/prisma');
const prisma = new PrismaClient();

async function checkTable() {
  try {
    // Try to query the subscription_payments table
    const count = await prisma.subscriptionPayment.count();
    console.log('subscription_payments table exists, count:', count);
    
    // Check the schema
    const sample = await prisma.subscriptionPayment.findFirst();
    console.log('Sample record:', sample);
    
  } catch (error) {
    console.error('Error accessing subscription_payments table:', error.message);
    
    if (error.code === 'P2021') {
      console.log('\nThe subscription_payments table does not exist in the database.');
      console.log('You need to run the migration first:');
      console.log('1. mysql -u root -p property_management < prisma/migrations/full_payment_tracking_migration.sql');
      console.log('2. npx prisma generate');
    }
  } finally {
    await prisma.$disconnect();
  }
}

checkTable();