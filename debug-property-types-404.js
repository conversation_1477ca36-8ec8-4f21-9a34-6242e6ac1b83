const fetch = require('node-fetch');

// Test authentication and property-types access
async function debugPropertyTypes404() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🔍 Debugging Property Types 404 Error...\n');

  try {
    // Step 1: Test login
    console.log('1. Testing login...');
    const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: '123456'
      })
    });

    console.log(`Login Status: ${loginResponse.status}`);
    
    if (!loginResponse.ok) {
      const errorText = await loginResponse.text();
      console.log(`Login Error: ${errorText}`);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('Login Success:', loginData.success);

    // Get cookies from login response
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('Cookies received:', cookies);

    // Step 2: Test direct page access with cookies
    console.log('\n2. Testing property-types page access...');
    
    const pageTests = [
      '/property-types',
      '/en/property-types', 
      '/dashboard/property-types',
      '/en/dashboard/property-types'
    ];

    for (const path of pageTests) {
      console.log(`\nTesting: ${baseUrl}${path}`);
      
      const pageResponse = await fetch(`${baseUrl}${path}`, {
        headers: {
          'Cookie': cookies || '',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        redirect: 'manual'
      });
      
      console.log(`  Status: ${pageResponse.status}`);
      console.log(`  Status Text: ${pageResponse.statusText}`);
      
      if (pageResponse.status >= 300 && pageResponse.status < 400) {
        const location = pageResponse.headers.get('location');
        console.log(`  Redirect to: ${location}`);
      }
      
      if (pageResponse.status === 404) {
        console.log('  ❌ 404 - Page not found');
      } else if (pageResponse.status === 200) {
        console.log('  ✅ 200 - Page found');
      }
    }

    // Step 3: Test API endpoints
    console.log('\n3. Testing property-types API...');
    const apiResponse = await fetch(`${baseUrl}/api/property-types`, {
      headers: {
        'Cookie': cookies || '',
      }
    });
    
    console.log(`API Status: ${apiResponse.status}`);
    if (apiResponse.ok) {
      const apiData = await apiResponse.json();
      console.log('API Response:', apiData);
    } else {
      const apiError = await apiResponse.text();
      console.log('API Error:', apiError);
    }

    // Step 4: Test /me endpoint to verify auth state
    console.log('\n4. Testing /me endpoint...');
    const meResponse = await fetch(`${baseUrl}/api/auth/me`, {
      headers: {
        'Cookie': cookies || '',
      }
    });
    
    console.log(`/me Status: ${meResponse.status}`);
    if (meResponse.ok) {
      const meData = await meResponse.json();
      console.log('User data:', meData);
    }

  } catch (error) {
    console.error('Debug error:', error.message);
  }
}

// Run the debug
debugPropertyTypes404().catch(console.error);