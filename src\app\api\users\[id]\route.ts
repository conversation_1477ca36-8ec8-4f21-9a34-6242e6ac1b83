import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";

import { userFormSchema } from "@/types/user";



import { hashPassword, verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
// GET /api/users/[id] - Get a specific user
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return NextResponse.json({ error: "Invalid or expired token" }, { status: 401 });
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has READ permission for users
    const canRead = hasPermission(userPermissions, "users", "read");
    if (!canRead) {
      return NextResponse.json({ error: "You don't have permission to view users" }, { status: 403 });
    }
    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid user ID" },
        { status: 400 }
      );
    }

    const user = await db.user.findUnique({
      where: { id },
      select: {
        id: true,
        username: true,
        email: true,
        first_name: true,
        last_name: true,
        phone: true,
        status: true,
        last_login_at: true,
        email_verified: true,
        created_at: true,
        updated_at: true,
        user_roles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error("Error fetching user:", error);
    console.error("Stack trace:", error instanceof Error ? error.stack : "No stack");
    
    if (error instanceof Error) {
      // Handle specific Prisma/database errors
      if (error.message.includes("Invalid `prisma.user.findUnique()` invocation")) {
        return NextResponse.json(
          { error: "Database query error", details: error.message },
          { status: 500 }
        );
      }
    }
    
    return NextResponse.json(
      { error: "Failed to fetch user", details: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}

// PUT /api/users/[id] - Update a specific user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return NextResponse.json({ error: "Invalid or expired token" }, { status: 401 });
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has UPDATE permission for users
    const canUpdate = hasPermission(userPermissions, "users", "update");
    if (!canUpdate) {
      return NextResponse.json({ error: "You don't have permission to update users" }, { status: 403 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid user ID" },
        { status: 400 }
      );
    }

    const body = await request.json();
    
    // Validate the request body - use partial schema for updates
    const updateSchema = userFormSchema.partial();
    const validatedData = updateSchema.parse(body);

    // Check if user exists
    const existingUser = await db.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check for unique constraints if updating username or email
    if (validatedData.username && validatedData.username !== existingUser.username) {
      const usernameExists = await db.user.findUnique({
        where: { username: validatedData.username },
      });

      if (usernameExists) {
        return NextResponse.json(
          { error: "A user with this username already exists" },
          { status: 400 }
        );
      }
    }

    if (validatedData.email && validatedData.email !== existingUser.email) {
      const emailExists = await db.user.findUnique({
        where: { email: validatedData.email },
      });

      if (emailExists) {
        return NextResponse.json(
          { error: "A user with this email already exists" },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};
    
    if (validatedData.username !== undefined) updateData.username = validatedData.username;
    if (validatedData.email !== undefined) updateData.email = validatedData.email;
    if (validatedData.first_name !== undefined) updateData.first_name = validatedData.first_name;
    if (validatedData.last_name !== undefined) updateData.last_name = validatedData.last_name;
    if (validatedData.phone !== undefined) updateData.phone = validatedData.phone;
    if (validatedData.status !== undefined) updateData.status = validatedData.status;
    
    // Handle password update if provided
    if (validatedData.password && validatedData.password.trim() !== '') {
      updateData.password_hash = await hashPassword(validatedData.password);
    }

    // Check if there's actually data to update
    if (Object.keys(updateData).length === 0) {
      // Just return the existing user if no fields are being updated
      const user = await db.user.findUnique({
        where: { id },
        select: {
          id: true,
          username: true,
          email: true,
          first_name: true,
          last_name: true,
          phone: true,
          status: true,
          last_login_at: true,
          email_verified: true,
          created_at: true,
          updated_at: true,
          user_roles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
            },
          },
        },
      });
      return NextResponse.json(user);
    }

    // Use a transaction to ensure atomicity
    const updatedUser = await db.$transaction(async (tx) => {
      // Update the user
      const user = await tx.user.update({
        where: { id },
        data: updateData,
      });

      // Update roles if provided
      if (validatedData.role_ids !== undefined) {
        // Remove existing roles
        await tx.userRole.deleteMany({
          where: { user_id: id },
        });

        // Add new roles
        if (validatedData.role_ids.length > 0) {
          await tx.userRole.createMany({
            data: validatedData.role_ids.map(roleId => ({
              user_id: id,
              role_id: roleId,
            })),
          });
        }
      }

      // Fetch the updated user with roles
      const completeUser = await tx.user.findUnique({
        where: { id },
        select: {
          id: true,
          username: true,
          email: true,
          first_name: true,
          last_name: true,
          phone: true,
          status: true,
          last_login_at: true,
          email_verified: true,
          created_at: true,
          updated_at: true,
          user_roles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
            },
          },
        },
      });

      return completeUser;
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating user:", error);
    console.error("Error stack:", error instanceof Error ? error.stack : "No stack trace");
    
    // Provide more specific error messages
    if (error instanceof Error) {
      // Prisma unique constraint error
      if (error.message.includes('Unique constraint failed') || error.message.includes('Unique constraint')) {
        return NextResponse.json(
          { error: "Username or email already exists" },
          { status: 400 }
        );
      }
      // MySQL specific duplicate entry error
      if (error.message.includes('Duplicate entry')) {
        if (error.message.includes('email')) {
          return NextResponse.json(
            { error: "A user with this email already exists" },
            { status: 400 }
          );
        }
        if (error.message.includes('username')) {
          return NextResponse.json(
            { error: "A user with this username already exists" },
            { status: 400 }
          );
        }
      }
      if (error.message.includes('Foreign key constraint failed')) {
        return NextResponse.json(
          { error: "Invalid role ID provided" },
          { status: 400 }
        );
      }
    }
    
    return NextResponse.json(
      { error: "Failed to update user", details: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}

// DELETE /api/users/[id] - Delete a specific user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return NextResponse.json({ error: "Invalid or expired token" }, { status: 401 });
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has DELETE permission for users
    const canDelete = hasPermission(userPermissions, "users", "delete");
    if (!canDelete) {
      return NextResponse.json({ error: "You don't have permission to delete users" }, { status: 403 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid user ID" },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await db.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Delete the user (cascade will handle related records)
    await db.user.delete({
      where: { id },
    });

    return NextResponse.json({
      message: "User deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json(
      { error: "Failed to delete user" },
      { status: 500 }
    );
  }
}
