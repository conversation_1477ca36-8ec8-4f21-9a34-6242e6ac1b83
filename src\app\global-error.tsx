'use client';

import { useEffect } from 'react';

interface GlobalErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function GlobalError({ error, reset }: GlobalErrorProps) {
  useEffect(() => {
    console.error('Global error:', error);
  }, [error]);

  return (
    <html>
      <body>
        <div style={{
          display: 'flex',
          minHeight: '100vh',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '20px',
          fontFamily: 'system-ui, sans-serif',
        }}>
          <div style={{
            maxWidth: '400px',
            padding: '20px',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            textAlign: 'center',
          }}>
            <h1 style={{ margin: '0 0 10px 0', color: '#dc2626' }}>
              Critical Error
            </h1>
            <p style={{ margin: '0 0 20px 0', color: '#64748b' }}>
              Something went wrong with the application.
            </p>
            {error.message && (
              <div style={{
                padding: '10px',
                backgroundColor: '#f8fafc',
                border: '1px solid #e2e8f0',
                borderRadius: '4px',
                marginBottom: '20px',
                fontSize: '14px',
                color: '#64748b',
              }}>
                {error.message}
              </div>
            )}
            <button
              onClick={reset}
              style={{
                padding: '10px 20px',
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                marginRight: '10px',
              }}
            >
              Try again
            </button>
            <a
              href="/en/dashboard/default"
              style={{
                padding: '10px 20px',
                backgroundColor: '#6b7280',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '4px',
                display: 'inline-block',
              }}
            >
              Go Home
            </a>
          </div>
        </div>
      </body>
    </html>
  );
} 