import { notFound } from "next/navigation";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { headers, cookies } from "next/headers";

import { Button } from "@/components/ui/button";
import { UserForm } from "../../_components/user-form";
import type { UserWithRoles } from "@/types/user";
import { getTranslations } from "next-intl/server";

interface EditUserPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

async function getUser(id: number): Promise<UserWithRoles | null> {
  try {
    // Get dynamic host for multi-port development
    const headersList = await headers();
    const host = headersList.get('host') || 'localhost:3001';
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}`;
    
    // Get cookies for server-side authentication
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth-token');
    
    const response = await fetch(`${baseUrl}/api/users/${id}`, {
      cache: 'no-store',
      headers: {
        'Cookie': authToken ? `auth-token=${authToken.value}` : '',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to fetch user:', response.status, response.statusText, errorText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching user:', error);
    return null;
  }
}

export default async function EditUserPage({ params }: EditUserPageProps) {
  const { id, locale } = await params;
  const userId = parseInt(id);
  const t = await getTranslations('users');

  if (isNaN(userId)) {
    notFound();
  }

  const user = await getUser(userId);

  if (!user) {
    notFound();
  }

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${locale}/dashboard/users/${user.id}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('backToUser')}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('editUser')}</h1>
          <p className="text-muted-foreground">{t('editDescription')}</p>
        </div>
      </div>

      <UserForm user={user} mode="edit" />
    </div>
  );
}
