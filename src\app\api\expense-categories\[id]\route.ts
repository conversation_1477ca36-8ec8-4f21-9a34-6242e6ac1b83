import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { expenseCategoryFormSchema } from "@/types/expense";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { ApiResponseBuilder } from "@/lib/api-response";

const prisma = new PrismaClient();

// GET /api/expense-categories/[id] - Get expense category by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for expense-categories
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "expense-categories", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view expense categories");
    }

    const { id } = await params;
    console.log(`Expense Category API: GET request received for ID: ${id}`);

    // For development, skip authentication
    if (process.env.NODE_ENV === "development") {
      console.log("Expense Category API: Development mode - skipping authentication");
    }

    const categoryId = parseInt(id, 10);
    if (isNaN(categoryId)) {
      return NextResponse.json(
        { error: "Invalid category ID" },
        { status: 400 }
      );
    }

    const category = await prisma.expenseCategory.findUnique({
      where: { id: categoryId },
      include: {
        _count: {
          select: {
            expenses: true
          }
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });

    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    console.log(`Expense Category API: Category retrieved: ${category.name_en}`);
    return NextResponse.json(category);

  } catch (error) {
    console.error("Expense Category API: Error fetching category:", error);
    return NextResponse.json(
      { error: "Failed to fetch expense category" },
      { status: 500 }
    );
  }
}

// PUT /api/expense-categories/[id] - Update expense category
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for expense-categories
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "expense-categories", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update expense categories");
    }

    const { id } = await params;
    console.log(`Expense Category API: PUT request received for ID: ${id}`);

    // For development, skip authentication
    if (process.env.NODE_ENV === "development") {
      console.log("Expense Category API: Development mode - skipping authentication");
    }

    const categoryId = parseInt(id, 10);
    if (isNaN(categoryId)) {
      return NextResponse.json(
        { error: "Invalid category ID" },
        { status: 400 }
      );
    }

    const body = await request.json();
    console.log("Expense Category API: Request body:", body);

    // Validate the request body
    const validationResult = expenseCategoryFormSchema.safeParse(body);
    if (!validationResult.success) {
      console.log("Expense Category API: Validation failed:", validationResult.error);
      return NextResponse.json(
        { 
          error: "Validation failed", 
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Check if category exists
    const existingCategory = await prisma.expenseCategory.findUnique({
      where: { id: categoryId }
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Check if another category with same name exists
    const duplicateCategory = await prisma.expenseCategory.findFirst({
      where: {
        AND: [
          { id: { not: categoryId } },
          {
            OR: [
              { name_en: data.name_en },
              { name_ar: data.name_ar }
            ]
          }
        ]
      }
    });

    if (duplicateCategory) {
      return NextResponse.json(
        { error: "Category with this name already exists" },
        { status: 409 }
      );
    }

    // Update the category
    const updatedCategory = await prisma.expenseCategory.update({
      where: { id: categoryId },
      data: {
        name_en: data.name_en,
        name_ar: data.name_ar,
        description: data.description || null,
        is_active: data.is_active,
        sort_order: data.sort_order,
        updated_by: 1, // TODO: Get from authenticated user
      },
      include: {
        _count: {
          select: {
            expenses: true
          }
        },
        creator: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        },
        updater: {
          select: {
            id: true,
            first_name: true,
            last_name: true
          }
        }
      }
    });

    console.log(`Expense Category API: Category updated: ${updatedCategory.id}`);
    return NextResponse.json(updatedCategory);

  } catch (error) {
    console.error("Expense Category API: Error updating category:", error);
    return NextResponse.json(
      { error: "Failed to update expense category" },
      { status: 500 }
    );
  }
}

// DELETE /api/expense-categories/[id] - Delete expense category
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for expense-categories
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "expense-categories", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete expense categories");
    }

    const { id } = await params;
    console.log(`Expense Category API: DELETE request received for ID: ${id}`);

    // For development, skip authentication
    if (process.env.NODE_ENV === "development") {
      console.log("Expense Category API: Development mode - skipping authentication");
    }

    const categoryId = parseInt(id, 10);
    if (isNaN(categoryId)) {
      return NextResponse.json(
        { error: "Invalid category ID" },
        { status: 400 }
      );
    }

    // Check if category exists
    const existingCategory = await prisma.expenseCategory.findUnique({
      where: { id: categoryId },
      include: {
        _count: {
          select: {
            expenses: true
          }
        }
      }
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Check if category has associated expenses
    if (existingCategory._count.expenses > 0) {
      return NextResponse.json(
        { error: "Cannot delete category with associated expenses" },
        { status: 409 }
      );
    }

    // Delete the category
    await prisma.expenseCategory.delete({
      where: { id: categoryId }
    });

    console.log(`Expense Category API: Category deleted: ${categoryId}`);
    return NextResponse.json({ message: "Category deleted successfully" });

  } catch (error) {
    console.error("Expense Category API: Error deleting category:", error);
    return NextResponse.json(
      { error: "Failed to delete expense category" },
      { status: 500 }
    );
  }
}
