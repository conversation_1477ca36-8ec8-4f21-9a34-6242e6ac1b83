import { <PERSON>ada<PERSON> } from "next";
import { getTranslations } from "next-intl/server";
import { OwnerPayoutForm } from "../_components/owner-payout-form";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

interface NewOwnerPayoutPageProps {
  params: Promise<{
    locale: string;
  }>;
}

export async function generateMetadata({ params }: NewOwnerPayoutPageProps): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "ownerPayouts" });
  
  return {
    title: t("createTitle"),
    description: t("createDescription"),
  };
}

export default async function NewOwnerPayoutPage({ params }: NewOwnerPayoutPageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale });
  
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href={`/${locale}/dashboard/owner-payouts`}>
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("ownerPayouts.createTitle")}</h1>
          <p className="text-muted-foreground">{t("ownerPayouts.createDescription")}</p>
        </div>
      </div>
      
      <OwnerPayoutForm mode="create" />
    </div>
  );
}