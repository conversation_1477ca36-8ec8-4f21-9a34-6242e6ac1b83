import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { associationMemberSchema, memberFilterSchema, calculateTotalOwnership } from "@/types/owners-association";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, checkUserPermission } from "@/lib/auth";
import { Decimal } from "@prisma/client/runtime/library";

// GET /api/owners-associations/[id]/members - List all members of an association
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await checkUserPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view association members");
    }

    const { id: idParam } = await params;
    const associationId = parseInt(idParam);

    if (isNaN(associationId)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    // Check if association exists
    const association = await db.ownersAssociation.findUnique({
      where: { id: associationId },
    });

    if (!association) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    const searchParams = request.nextUrl.searchParams;
    
    // Parse filters
    const filters = memberFilterSchema.parse({
      search: searchParams.get("search") || undefined,
      is_board_member: searchParams.get("is_board_member") === "true" ? true : 
                       searchParams.get("is_board_member") === "false" ? false : undefined,
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
      pageSize: searchParams.get("pageSize") ? parseInt(searchParams.get("pageSize")!) : 10,
      sortBy: searchParams.get("sortBy") || "full_name",
      sortOrder: searchParams.get("sortOrder") || "asc",
    });

    // Build where clause
    const where: any = {
      association_id: associationId,
    };

    if (filters.search) {
      where.OR = [
        { full_name: { contains: filters.search, mode: 'insensitive' } },
        { unit_number: { contains: filters.search, mode: 'insensitive' } },
        { email: { contains: filters.search, mode: 'insensitive' } },
        { phone: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    if (filters.is_board_member !== undefined) {
      where.is_board_member = filters.is_board_member;
    }

    // Calculate pagination
    const page = filters.page || 1;
    const pageSize = filters.pageSize || 10;
    const skip = (page - 1) * pageSize;

    // Fetch members
    const [members, total] = await Promise.all([
      db.associationMember.findMany({
        where,
        include: {
          _count: {
            select: {
              subscription_payments: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || "full_name"]: filters.sortOrder || "asc",
        },
        skip,
        take: pageSize,
      }),
      db.associationMember.count({ where }),
    ]);

    // Transform members to handle Decimal serialization
    const transformedMembers = members.map(member => ({
      ...member,
      ownership_percentage: member.ownership_percentage?.toString() || "0",
    }));

    // Calculate total ownership
    const allMembers = await db.associationMember.findMany({
      where: { association_id: associationId },
    });
    const totalOwnership = calculateTotalOwnership(allMembers);

    return ApiResponseBuilder.success({
      members: transformedMembers,
      totalOwnership: totalOwnership.toFixed(2),
    }, {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error: any) {
    console.error("Error fetching association members:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }
    
    return ApiResponseBuilder.error("Failed to fetch association members", "INTERNAL_ERROR", 500);
  }
}

// POST /api/owners-associations/[id]/members - Add a new member to an association
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for owners-associations
    const canCreate = await checkUserPermission(decoded.id, "owners-associations", "CREATE");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to add association members");
    }

    const { id: idParam } = await params;
    const associationId = parseInt(idParam);

    if (isNaN(associationId)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    // Check if association exists
    const association = await db.ownersAssociation.findUnique({
      where: { id: associationId },
    });

    if (!association) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    const body = await request.json();
    const validatedData = associationMemberSchema.parse({
      ...body,
      association_id: associationId,
    });

    // Check if member with same unit number already exists
    const existingMember = await db.associationMember.findFirst({
      where: {
        association_id: associationId,
        unit_number: validatedData.unit_number,
      },
    });

    if (existingMember) {
      return ApiResponseBuilder.error(
        "A member with this unit number already exists in the association",
        "ALREADY_EXISTS",
        400
      );
    }

    // Get all current members to check total ownership
    const currentMembers = await db.associationMember.findMany({
      where: { association_id: associationId },
    });

    const currentTotal = calculateTotalOwnership(currentMembers);
    const newOwnership = parseFloat(validatedData.ownership_percentage);
    const newTotal = currentTotal + newOwnership;

    if (newTotal > 100.01) { // Allow for small floating point errors
      return ApiResponseBuilder.error(
        `Total ownership would exceed 100%. Current total: ${currentTotal.toFixed(2)}%, trying to add: ${newOwnership}%`,
        "INVALID_OWNERSHIP",
        400
      );
    }

    // Create the member
    const member = await db.associationMember.create({
      data: {
        association_id: associationId,
        full_name: validatedData.full_name,
        unit_number: validatedData.unit_number,
        ownership_percentage: new Decimal(validatedData.ownership_percentage),
        phone: validatedData.phone,
        email: validatedData.email,
        join_date: new Date(validatedData.join_date),
        is_board_member: validatedData.is_board_member,
      },
    });

    // Update association member count
    await db.ownersAssociation.update({
      where: { id: associationId },
      data: {
        total_members: {
          increment: 1,
        },
        updated_by: decoded.id,
      },
    });

    // Transform member to handle Decimal serialization
    const transformedMember = {
      ...member,
      ownership_percentage: member.ownership_percentage?.toString() || "0",
    };

    return ApiResponseBuilder.success(transformedMember);
  } catch (error: any) {
    console.error("Error creating association member:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to create association member", "INTERNAL_ERROR", 500);
  }
}