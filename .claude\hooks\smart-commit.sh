#!/bin/bash

# Smart auto-commit hook for Claude Code
# Only commits if files pass basic validation

# Configuration
SKIP_PATTERNS=("*.log" "*.tmp" "node_modules/*" ".env")
MIN_CHANGES=1  # Minimum number of changed lines to trigger commit

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "Not in a git repository. Initializing git..."
    git init
    git add .
    git commit -m "Initial commit by <PERSON> auto-commit hook"
    exit 0
fi

# Get changed files count
CHANGED_COUNT=$(git status --porcelain | wc -l)

if [ $CHANGED_COUNT -eq 0 ]; then
    echo "No changes detected."
    exit 0
fi

# Check for syntax errors in common file types
ERROR_FOUND=0

# Check TypeScript/JavaScript files
for file in $(git diff --name-only --cached --diff-filter=ACM | grep -E '\.(ts|tsx|js|jsx)$'); do
    if [ -f "$file" ]; then
        # Basic syntax check (you can enhance this)
        if ! node -c "$file" 2>/dev/null; then
            echo "Syntax error in $file. Skipping commit."
            ERROR_FOUND=1
        fi
    fi
done

# Check JSON files
for file in $(git diff --name-only --cached --diff-filter=ACM | grep '\.json$'); do
    if [ -f "$file" ]; then
        if ! jq empty "$file" 2>/dev/null; then
            echo "Invalid JSON in $file. Skipping commit."
            ERROR_FOUND=1
        fi
    fi
done

if [ $ERROR_FOUND -eq 1 ]; then
    echo "Errors found. Auto-commit skipped."
    exit 1
fi

# Generate detailed commit message
COMMIT_MSG="Auto-commit: "

# Count changes by file type
TS_CHANGES=$(git diff --name-only | grep -E '\.(ts|tsx)$' | wc -l)
JS_CHANGES=$(git diff --name-only | grep -E '\.(js|jsx)$' | wc -l)
CSS_CHANGES=$(git diff --name-only | grep -E '\.(css|scss)$' | wc -l)
MD_CHANGES=$(git diff --name-only | grep '\.md$' | wc -l)

CHANGES_DESC=""
[ $TS_CHANGES -gt 0 ] && CHANGES_DESC="${CHANGES_DESC}TypeScript($TS_CHANGES) "
[ $JS_CHANGES -gt 0 ] && CHANGES_DESC="${CHANGES_DESC}JavaScript($JS_CHANGES) "
[ $CSS_CHANGES -gt 0 ] && CHANGES_DESC="${CHANGES_DESC}Styles($CSS_CHANGES) "
[ $MD_CHANGES -gt 0 ] && CHANGES_DESC="${CHANGES_DESC}Docs($MD_CHANGES) "

if [ -n "$CHANGES_DESC" ]; then
    COMMIT_MSG="${COMMIT_MSG}Updated ${CHANGES_DESC}"
else
    COMMIT_MSG="${COMMIT_MSG}Updated $(git diff --name-only | head -3 | tr '\n' ' ')"
fi

# Stage all changes
git add -A

# Commit
if git commit -m "$COMMIT_MSG" --author="Claude <<EMAIL>>"; then
    echo "✓ Successfully committed: $COMMIT_MSG"
    
    # Show commit details
    git log -1 --oneline
else
    echo "Commit failed or no changes to commit."
fi