import { setRequestLocale } from 'next-intl/server';
import { redirect } from 'next/navigation';
import { cookies } from 'next/headers';
import { routing } from '@/i18n/routing';
import { verifyToken } from '@/lib/auth';

interface LocalePageProps {
  params: Promise<{ locale: string }>;
}

// Generate static params for all supported locales
export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

export default async function LocalePage({ params }: LocalePageProps) {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  // Check if user is authenticated
  const cookieStore = await cookies();
  const authToken = cookieStore.get('auth-token');
  
  if (!authToken) {
    // No auth token, redirect to login
    redirect(`/${locale}/login`);
  }

  try {
    // Verify the token is valid
    const decoded = verifyToken(authToken.value);
    
    if (!decoded) {
      // Invalid token, redirect to login
      redirect(`/${locale}/login`);
    }

    // User is authenticated, redirect to dashboard
    redirect(`/${locale}/dashboard/default`);
  } catch (error) {
    // Token verification failed, redirect to login
    redirect(`/${locale}/login`);
  }
}
