import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { db } from "@/lib/db";
import { PropertyOwnerForm } from "../../_components/property-owner-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

interface EditPropertyOwnerPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

async function getPropertyOwner(id: string) {
  try {
    const owner = await db.propertyOwner.findUnique({
      where: { id: parseInt(id, 10) },
    });
    
    return owner;
  } catch (error) {
    console.error("Error fetching property owner:", error);
    return null;
  }
}

export default async function EditPropertyOwnerPage({ params }: EditPropertyOwnerPageProps) {
  const { id, locale } = await params;
  const t = await getTranslations('propertyOwners');

  const owner = await getPropertyOwner(id);

  if (!owner) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href={`/${locale}/dashboard/property-owners`}>
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('editTitle')}</h1>
          <p className="text-muted-foreground">{t('editDescription')}</p>
        </div>
      </div>

      <PropertyOwnerForm owner={owner} isEdit />
    </div>
  );
}