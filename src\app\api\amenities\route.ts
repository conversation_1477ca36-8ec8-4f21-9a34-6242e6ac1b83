import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { z } from "zod";

// Validation schema for amenity
const amenitySchema = z.object({
  name_en: z.string().min(1, "Name in English is required").max(100),
  name_ar: z.string().min(1, "Name in Arabic is required").max(100),
  icon: z.string().max(50).optional().nullable(),
});

// GET /api/amenities - Get all amenities
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for amenities
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "amenities", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view amenities");
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "name_en";
    const order = searchParams.get("order") || "asc";

    const whereConditions = search
      ? {
          OR: [
            { name_en: { contains: search } },
            { name_ar: { contains: search } },
          ],
        }
      : {};

    const [amenities, total] = await Promise.all([
      db.amenity.findMany({
        where: whereConditions,
        orderBy: {
          [sortBy]: order,
        },
      }),
      db.amenity.count({
        where: whereConditions,
      }),
    ]);

    return ApiResponseBuilder.success(amenities, {
      total,
    });
  } catch (error) {
    console.error("Error fetching amenities:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch amenities",
      "FETCH_ERROR",
      500,
      error
    );
  }
}

// POST /api/amenities - Create a new amenity
export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for amenities
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "amenities", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create amenities");
    }

    const body = await request.json();
    const validatedData = amenitySchema.parse(body);

    // Check if amenity with same name already exists
    const existingAmenity = await db.amenity.findFirst({
      where: {
        OR: [
          { name_en: validatedData.name_en },
          { name_ar: validatedData.name_ar },
        ],
      },
    });

    if (existingAmenity) {
      return ApiResponseBuilder.error(
        "Amenity with this name already exists",
        "DUPLICATE_ENTRY",
        400
      );
    }

    const amenity = await db.amenity.create({
      data: validatedData,
    });

    return ApiResponseBuilder.success(amenity);
  } catch (error: any) {
    console.error("Error creating amenity:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error(
      "Failed to create amenity",
      "CREATE_ERROR",
      500,
      error
    );
  }
}