import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { calculateLateFee, getDaysOverdue } from "@/types/invoice";
import { ApiResponseBuilder } from "@/lib/api-response";

// This endpoint should be called by a cron job daily
// You can use services like Vercel Cron, GitHub Actions, or external cron services
export async function GET(request: NextRequest) {
  try {
    // Optional: Add authentication for cron job
    const authHeader = request.headers.get("authorization");
    const cronSecret = process.env.CRON_SECRET;
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return ApiResponseBuilder.error("Unauthorized", "UNAUTHORIZED", 401);
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Find all pending invoices that are overdue
    const overdueInvoices = await prisma.invoice.findMany({
      where: {
        status: 'PENDING',
        due_date: {
          lt: today,
        },
      },
      include: {
        tenant: true,
        property: true,
        unit: true,
      },
    });

    const updatedInvoices = [];
    const errors = [];

    for (const invoice of overdueInvoices) {
      try {
        const daysOverdue = getDaysOverdue(invoice.due_date);
        
        // Calculate late fee (5% of original amount)
        const lateFee = calculateLateFee(invoice.original_amount.toString(), daysOverdue, 5);
        
        // Only update if late fee has changed
        if (parseFloat(invoice.late_fee.toString()) !== lateFee) {
          const newTotalAmount = parseFloat(invoice.original_amount.toString()) + lateFee;
          const newBalanceAmount = newTotalAmount - parseFloat(invoice.paid_amount.toString());

          const updated = await prisma.invoice.update({
            where: { id: invoice.id },
            data: {
              status: 'OVERDUE',
              late_fee: lateFee,
              total_amount: newTotalAmount,
              balance_amount: newBalanceAmount,
            },
          });

          updatedInvoices.push({
            id: updated.id,
            invoice_number: updated.invoice_number,
            tenant: `${invoice.tenant.first_name} ${invoice.tenant.last_name}`,
            days_overdue: daysOverdue,
            late_fee: lateFee,
            new_total: newTotalAmount,
          });
        }
      } catch (error) {
        console.error(`Error updating invoice ${invoice.id}:`, error);
        errors.push({
          invoice_id: invoice.id,
          invoice_number: invoice.invoice_number,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    // Also check if any overdue invoices have been paid and should be updated
    const paidOverdueInvoices = await prisma.invoice.findMany({
      where: {
        status: 'OVERDUE',
        balance_amount: 0,
      },
    });

    for (const invoice of paidOverdueInvoices) {
      try {
        await prisma.invoice.update({
          where: { id: invoice.id },
          data: {
            status: 'PAID',
          },
        });
      } catch (error) {
        console.error(`Error updating paid invoice ${invoice.id}:`, error);
      }
    }

    // Check partially paid invoices that are no longer overdue
    const partiallyPaidInvoices = await prisma.invoice.findMany({
      where: {
        status: 'OVERDUE',
        paid_amount: { gt: 0 },
        balance_amount: { gt: 0 },
        due_date: { gte: today },
      },
    });

    for (const invoice of partiallyPaidInvoices) {
      try {
        await prisma.invoice.update({
          where: { id: invoice.id },
          data: {
            status: 'PARTIALLY_PAID',
          },
        });
      } catch (error) {
        console.error(`Error updating partially paid invoice ${invoice.id}:`, error);
      }
    }

    return ApiResponseBuilder.success({
      message: `Processed ${overdueInvoices.length} overdue invoices`,
      updated: updatedInvoices.length,
      errors: errors.length,
      details: {
        updated_invoices: updatedInvoices,
        error_details: errors.length > 0 ? errors : undefined,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error checking overdue invoices:", error);
    return ApiResponseBuilder.error("Failed to check overdue invoices", "INTERNAL_ERROR", 500);
  }
}

// POST endpoint for manual trigger
export async function POST(request: NextRequest) {
  return GET(request);
}