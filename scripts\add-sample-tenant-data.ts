import { PrismaClient } from "@prisma/client";
import { hash } from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  try {
    // Get a tenant to add sample data to
    const tenant = await prisma.tenant.findFirst({});

    if (!tenant) {
      console.log("No active tenant found. Please create a tenant first.");
      return;
    }

    console.log(`Adding sample data for tenant: ${tenant.first_name} ${tenant.last_name}`);

    // Get a property and unit
    const unit = await prisma.unit.findFirst({
      where: {
        status: "AVAILABLE"
      },
      include: {
        property: true
      }
    });

    if (!unit) {
      console.log("No available unit found. Please create a property with units first.");
      return;
    }

    // Create a contract
    const contract = await prisma.contract.create({
      data: {
        contract_number: `CNT-${Date.now()}`,
        property_id: unit.property_id,
        unit_id: unit.id,
        start_date: new Date(),
        end_date: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
        monthly_rent: 500,
        security_deposit: 1000,
        status: "ACTIVE",
        tenants: {
          create: {
            tenant_id: tenant.id,
            is_primary: true
          }
        }
      }
    });

    console.log("Created contract:", contract.contract_number);

    // Create invoices
    const invoices = [];
    for (let i = 0; i < 6; i++) {
      const invoiceDate = new Date();
      invoiceDate.setMonth(invoiceDate.getMonth() - i);
      
      const dueDate = new Date(invoiceDate);
      dueDate.setDate(dueDate.getDate() + 7);

      const status = i < 2 ? "PAID" : i === 2 ? "PARTIALLY_PAID" : i === 3 ? "PENDING" : "PENDING";
      const isOverdue = status === "PENDING" && dueDate < new Date();

      const invoice = await prisma.invoice.create({
        data: {
          invoice_number: `INV-${Date.now()}-${i}`,
          contract_id: contract.id,
          tenant_id: tenant.id,
          property_id: unit.property_id,
          unit_id: unit.id,
          invoice_date: invoiceDate,
          due_date: dueDate,
          original_amount: 500,
          late_fee: isOverdue ? 50 : 0,
          total_amount: isOverdue ? 550 : 500,
          paid_amount: status === "PAID" ? 500 : status === "PARTIALLY_PAID" ? 250 : 0,
          balance_amount: status === "PAID" ? 0 : status === "PARTIALLY_PAID" ? 250 : (isOverdue ? 550 : 500),
          status: status as any,
          items: {
            create: {
              description_en: "Monthly Rent",
              description_ar: "الإيجار الشهري",
              quantity: 1,
              unit_price: 500,
              amount: 500
            }
          }
        }
      });
      
      invoices.push(invoice);
      console.log(`Created invoice: ${invoice.invoice_number} - Status: ${status}`);
    }

    // Create payments for paid invoices
    for (const invoice of invoices.filter(inv => inv.status === "PAID")) {
      const payment = await prisma.payment.create({
        data: {
          payment_number: `PAY-${Date.now()}-${invoice.id}`,
          invoice_id: invoice.id,
          tenant_id: tenant.id,
          property_id: unit.property_id,
          unit_id: unit.id,
          amount: invoice.total_amount,
          payment_method: "BANK_TRANSFER",
          payment_date: invoice.invoice_date,
          status: "COMPLETED",
          allocations: {
            create: {
              invoice_id: invoice.id,
              allocated_amount: invoice.total_amount
            }
          }
        }
      });
      console.log(`Created payment: ${payment.payment_number}`);
    }

    // Create partial payment
    const partialInvoice = invoices.find(inv => inv.status === "PARTIALLY_PAID");
    if (partialInvoice) {
      const payment = await prisma.payment.create({
        data: {
          payment_number: `PAY-${Date.now()}-PARTIAL`,
          invoice_id: partialInvoice.id,
          tenant_id: tenant.id,
          property_id: unit.property_id,
          unit_id: unit.id,
          amount: 250,
          payment_method: "CASH",
          payment_date: partialInvoice.invoice_date,
          status: "COMPLETED",
          allocations: {
            create: {
              invoice_id: partialInvoice.id,
              allocated_amount: 250
            }
          }
        }
      });
      console.log(`Created partial payment: ${payment.payment_number}`);
    }

    // Add sample documents
    const documentTypes = ["NATIONAL_ID", "PASSPORT", "EMPLOYMENT_CONTRACT", "BANK_STATEMENT"];
    
    for (const type of documentTypes) {
      await prisma.tenantDocument.create({
        data: {
          tenant_id: tenant.id,
          document_type: type as any,
          document_number: `DOC-${type}-${Date.now()}`,
          file_name: `${type.toLowerCase()}_sample.pdf`,
          file_path: `/uploads/tenant-documents/${tenant.id}/sample.pdf`,
          file_size: 1024 * 100, // 100KB
          mime_type: "application/pdf",
          issue_date: new Date(),
          expiry_date: type === "NATIONAL_ID" || type === "PASSPORT" ? new Date(new Date().setFullYear(new Date().getFullYear() + 5)) : null
        }
      });
      console.log(`Created document: ${type}`);
    }

    // Add emergency contacts
    await prisma.emergencyContact.create({
      data: {
        tenant_id: tenant.id,
        name: "John Emergency",
        relationship: "Brother",
        phone: "+968 9123 4567",
        email: "<EMAIL>",
        is_primary: true
      }
    });

    await prisma.emergencyContact.create({
      data: {
        tenant_id: tenant.id,
        name: "Jane Emergency",
        relationship: "Sister",
        phone: "+968 9234 5678",
        is_primary: false
      }
    });

    console.log("Created emergency contacts");

    console.log("\nSample data added successfully!");

  } catch (error) {
    console.error("Error adding sample data:", error);
  } finally {
    await prisma.$disconnect();
  }
}

main();