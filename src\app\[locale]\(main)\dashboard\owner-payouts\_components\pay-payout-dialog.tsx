"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  OwnerPayoutWithRelations, 
  OwnerPayoutPaymentInput,
  ownerPayoutPaymentSchema 
} from "@/types/owner-payout";
import { formatOMR } from "@/lib/format";

interface PayPayoutDialogProps {
  payout: OwnerPayoutWithRelations;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PayPayoutDialog({
  payout,
  open,
  onOpenChange,
}: PayPayoutDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const t = useTranslations();

  const form = useForm<OwnerPayoutPaymentInput>({
    resolver: zodResolver(ownerPayoutPaymentSchema),
    defaultValues: {
      payment_method: "BANK_TRANSFER",
      reference_number: "",
      bank_transfer_ref: "",
      notes: "",
    },
  });

  const handleSubmit = async (data: OwnerPayoutPaymentInput) => {
    try {
      setIsLoading(true);

      const response = await fetch(`/api/owner-payouts/${payout.id}/pay`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(t("ownerPayouts.paymentSuccess"));

        router.refresh();
        onOpenChange(false);
        form.reset();
      } else {
        toast.error(result.message || t("common.unexpectedError"));
      }
    } catch (error) {
      console.error("Error processing payout payment:", error);
      toast.error(t("common.unexpectedError"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t("ownerPayouts.paymentTitle")}</DialogTitle>
          <DialogDescription>
            {t("ownerPayouts.paymentDescription", {
              payoutNumber: payout.payout_number,
              amount: formatOMR(payout.net_amount.toString()),
              owner: payout.owner?.name_en || "",
            })}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="payment_method"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("ownerPayouts.paymentMethod")} *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="CASH">{t("common.paymentMethods.cash")}</SelectItem>
                      <SelectItem value="BANK_TRANSFER">{t("common.paymentMethods.bankTransfer")}</SelectItem>
                      <SelectItem value="CHECK">{t("common.paymentMethods.check")}</SelectItem>
                      <SelectItem value="CARD">{t("common.paymentMethods.card")}</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="reference_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("ownerPayouts.referenceNumber")}</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value || ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bank_transfer_ref"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("ownerPayouts.bankTransferRef")}</FormLabel>
                  <FormControl>
                    <Input {...field} value={field.value || ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("ownerPayouts.paymentNotes")}</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      value={field.value || ""}
                      placeholder={t("ownerPayouts.paymentNotesPlaceholder")}
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                {t("common.cancel")}
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t("ownerPayouts.confirmPayment")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}