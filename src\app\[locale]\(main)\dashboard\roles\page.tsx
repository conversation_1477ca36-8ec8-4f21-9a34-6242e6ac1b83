import { Suspense } from "react";
import Link from "next/link";
import { Plus } from "lucide-react";
import { getTranslations } from 'next-intl/server';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ErrorBoundary } from "@/components/error-boundary";

import { RoleDataTable } from "./_components/role-data-table";

interface RolesPageProps {
  params: Promise<{
    locale: string;
  }>;
}

export default async function RolesPage({ params }: RolesPageProps) {
  const { locale } = await params;
  const t = await getTranslations('roles');
  const tCommon = await getTranslations('common');

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
          <p className="text-muted-foreground">{t('description')}</p>
        </div>
        <Button asChild>
          <Link href={`/${locale}/dashboard/roles/create`}>
            <Plus className="mr-2 h-4 w-4" />
            {t('addRole')}
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('allRoles')}</CardTitle>
          <CardDescription>
            {t('viewManage')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ErrorBoundary>
            <Suspense fallback={<div>{tCommon('loading')}</div>}>
              <RoleDataTable />
            </Suspense>
          </ErrorBoundary>
        </CardContent>
      </Card>
    </div>
  );
}
