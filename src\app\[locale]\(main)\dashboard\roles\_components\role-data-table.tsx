"use client";

import { useState, useEffect } from "react";
import { useDataTableInstance } from "@/hooks/use-data-table-instance";
import { DataTable } from "@/components/data-table/data-table";
import { DataTablePagination } from "@/components/data-table/data-table-pagination";
import { DataTableViewOptions } from "@/components/data-table/data-table-view-options";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Search, Filter, Loader2 } from "lucide-react";
import { toast } from "sonner";
import type { RoleWithPermissions, RoleFilters } from "@/types/user";

import { roleColumns } from "./role-columns";

export function RoleDataTable() {
  const [roles, setRoles] = useState<(RoleWithPermissions & { _count: { user_roles: number } })[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters state
  const [filters, setFilters] = useState<RoleFilters>({
    search: "",
    is_system: undefined,
    page: 1,
    pageSize: 10,
    sortBy: "created_at",
    sortOrder: "desc",
  });

  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  // Initialize data table
  const table = useDataTableInstance({
    data: roles,
    columns: roleColumns,
    defaultPageIndex: pagination.page - 1,
    defaultPageSize: pagination.pageSize,
  });

  // Fetch roles
  const fetchRoles = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const searchParams = new URLSearchParams();
      if (filters.search) searchParams.set("search", filters.search);
      if (filters.is_system !== undefined) searchParams.set("is_system", filters.is_system.toString());
      searchParams.set("page", filters.page.toString());
      searchParams.set("pageSize", filters.pageSize.toString());
      searchParams.set("sortBy", filters.sortBy);
      searchParams.set("sortOrder", filters.sortOrder);

      const response = await fetch(`/api/roles?${searchParams}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch roles");
      }

      const data = await response.json();
      setRoles(data.roles);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching roles:", error);
      setError("Failed to load roles. Please try again.");
      toast.error("Failed to load roles");
    } finally {
      setIsLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchRoles();
  }, []);

  // Refetch when filters change (but not on initial mount)
  useEffect(() => {
    if (!isLoading) {
      fetchRoles();
    }
  }, [filters.search, filters.is_system, filters.page, filters.pageSize, filters.sortBy, filters.sortOrder]);

  // Handle search
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }));
  };

  // Handle system filter
  const handleSystemFilter = (value: string) => {
    setFilters(prev => ({ 
      ...prev, 
      is_system: value === "all" ? undefined : value === "true",
      page: 1 
    }));
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setFilters({
      search: "",
      is_system: undefined,
      page: 1,
      pageSize: 10,
      sortBy: "created_at",
      sortOrder: "desc",
    });
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={fetchRoles}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search roles..."
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-9"
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearFilters}
            className="shrink-0"
          >
            <Filter className="mr-2 h-4 w-4" />
            Clear
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <Select value={filters.is_system?.toString() || "all"} onValueChange={handleSystemFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="All Types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="true">System Roles</SelectItem>
              <SelectItem value="false">Custom Roles</SelectItem>
            </SelectContent>
          </Select>

          <DataTableViewOptions table={table} />
        </div>
      </div>

      {/* Data Table */}
      <div className="rounded-md border overflow-hidden">
        <div className="w-full overflow-x-auto [&_[data-slot=table-container]]:overflow-x-visible">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading roles...</span>
            </div>
          ) : (
            <DataTable table={table} columns={roleColumns} />
          )}
        </div>
      </div>

      {/* Pagination */}
      {!isLoading && <DataTablePagination table={table} />}
    </div>
  );
}
