"use client";

import { useState, useEffect } from "react";
import { useTranslations, useLocale } from "next-intl";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ArrowLeft,
  ArrowRight,
  Building,
  Calendar,
  Crown,
  DollarSign,
  Edit,
  FileText,
  Home,
  Mail,
  Percent,
  Phone,
  Receipt,
  User,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useRTL } from "@/hooks/use-rtl";
import { apiClient } from "@/lib/api-client";
import { MemberPaymentsTable } from "./member-payments-table";
import { MemberTransactionsTable } from "./member-transactions-table";

interface MemberDetailProps {
  associationId: number;
  memberId: number;
  association: any;
  member: any;
}

export function MemberDetail({ associationId, memberId, association, member: initialMember }: MemberDetailProps) {
  const locale = useLocale();
  const { isRTL } = useRTL();
  const router = useRouter();
  const t = useTranslations("ownersAssociations.memberDetail");
  const tCommon = useTranslations("common");
  const dateLocale = locale === 'ar' ? ar : enUS;

  const [member, setMember] = useState(initialMember);
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchMemberStats();
  }, [memberId]);

  const fetchMemberStats = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(
        `/api/owners-associations/${associationId}/members/${memberId}/stats`
      );
      
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error("Error fetching member stats:", error);
      toast.error(t("loadError"));
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.push(`/${locale}/dashboard/owners-associations/${associationId}`);
  };

  const handleEdit = () => {
    // This would open an edit dialog or navigate to edit page
    toast.info(t("editNotImplemented"));
  };

  const BackIcon = isRTL ? ArrowRight : ArrowLeft;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={cn("flex items-center justify-between", isRTL && "flex-row-reverse")}>
        <div className={cn("flex items-center gap-4", isRTL && "flex-row-reverse")}>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBack}
            className={cn("gap-2", isRTL && "flex-row-reverse")}
          >
            <BackIcon className="h-4 w-4" />
            {tCommon("back")}
          </Button>
          <div>
            <h1 className={cn("text-2xl font-bold", isRTL && "text-right")}>
              {member.full_name}
            </h1>
            <p className={cn("text-sm text-muted-foreground", isRTL && "text-right")}>
              {association.name_en} / {association.name_ar}
            </p>
          </div>
        </div>
        <Button onClick={handleEdit} size="sm">
          <Edit className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
          {tCommon("edit")}
        </Button>
      </div>

      {/* Member Info Card */}
      <Card>
        <CardHeader>
          <CardTitle className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
            <User className="h-5 w-5" />
            {t("memberInfo")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            {/* Basic Info */}
            <div className="space-y-4">
              <div className={cn("flex items-start gap-3", isRTL && "flex-row-reverse")}>
                <User className="h-4 w-4 text-muted-foreground mt-1" />
                <div className={cn("space-y-1", isRTL && "text-right")}>
                  <p className="text-sm font-medium">{t("fullName")}</p>
                  <p className="text-sm text-muted-foreground">
                    {member.full_name}
                    {member.is_board_member && (
                      <Badge variant="default" className="ml-2">
                        <Crown className="h-3 w-3 mr-1" />
                        {t("boardMember")}
                      </Badge>
                    )}
                  </p>
                </div>
              </div>

              <div className={cn("flex items-start gap-3", isRTL && "flex-row-reverse")}>
                <Phone className="h-4 w-4 text-muted-foreground mt-1" />
                <div className={cn("space-y-1", isRTL && "text-right")}>
                  <p className="text-sm font-medium">{t("phone")}</p>
                  <p className="text-sm text-muted-foreground">
                    {member.phone || t("notProvided")}
                  </p>
                </div>
              </div>

              <div className={cn("flex items-start gap-3", isRTL && "flex-row-reverse")}>
                <Mail className="h-4 w-4 text-muted-foreground mt-1" />
                <div className={cn("space-y-1", isRTL && "text-right")}>
                  <p className="text-sm font-medium">{t("email")}</p>
                  <p className="text-sm text-muted-foreground">
                    {member.email || t("notProvided")}
                  </p>
                </div>
              </div>

              <div className={cn("flex items-start gap-3", isRTL && "flex-row-reverse")}>
                <Calendar className="h-4 w-4 text-muted-foreground mt-1" />
                <div className={cn("space-y-1", isRTL && "text-right")}>
                  <p className="text-sm font-medium">{t("joinDate")}</p>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(member.join_date), "dd/MM/yyyy", { locale: dateLocale })}
                  </p>
                </div>
              </div>
            </div>

            {/* Property Info */}
            <div className="space-y-4">
              <div className={cn("flex items-start gap-3", isRTL && "flex-row-reverse")}>
                <Building className="h-4 w-4 text-muted-foreground mt-1" />
                <div className={cn("space-y-1", isRTL && "text-right")}>
                  <p className="text-sm font-medium">{t("property")}</p>
                  <p className="text-sm text-muted-foreground">
                    {member.property?.name || t("notLinked")}
                  </p>
                </div>
              </div>

              <div className={cn("flex items-start gap-3", isRTL && "flex-row-reverse")}>
                <Home className="h-4 w-4 text-muted-foreground mt-1" />
                <div className={cn("space-y-1", isRTL && "text-right")}>
                  <p className="text-sm font-medium">{t("unit")}</p>
                  <p className="text-sm text-muted-foreground">
                    {member.unit_number}
                  </p>
                </div>
              </div>

              <div className={cn("flex items-start gap-3", isRTL && "flex-row-reverse")}>
                <Percent className="h-4 w-4 text-muted-foreground mt-1" />
                <div className={cn("space-y-1", isRTL && "text-right")}>
                  <p className="text-sm font-medium">{t("ownershipPercentage")}</p>
                  <p className="text-sm text-muted-foreground">
                    {parseFloat(member.ownership_percentage.toString()).toFixed(2)}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      {loading ? (
        <div className="grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-3">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : stats ? (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-3">
              <CardDescription className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <DollarSign className="h-4 w-4" />
                {t("totalPaid")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn("text-2xl font-bold", isRTL && "text-right")}>
                {stats.totalPaid?.toFixed(3) || "0.000"} {tCommon("currency")}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardDescription className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <Receipt className="h-4 w-4" />
                {t("totalDue")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn("text-2xl font-bold", isRTL && "text-right")}>
                {stats.totalDue?.toFixed(3) || "0.000"} {tCommon("currency")}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardDescription className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <FileText className="h-4 w-4" />
                {t("paymentsCount")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn("text-2xl font-bold", isRTL && "text-right")}>
                {stats.paymentsCount || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardDescription className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
                <Percent className="h-4 w-4" />
                {t("complianceRate")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn("text-2xl font-bold", isRTL && "text-right")}>
                {stats.complianceRate?.toFixed(1) || "0"}%
              </div>
            </CardContent>
          </Card>
        </div>
      ) : null}

      {/* Tabs for Payments and Transactions */}
      <Card>
        <CardContent className="pt-6">
          <Tabs defaultValue="payments" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="payments">{t("paymentsTab")}</TabsTrigger>
              <TabsTrigger value="transactions">{t("transactionsTab")}</TabsTrigger>
            </TabsList>
            <TabsContent value="payments" className="space-y-4">
              <MemberPaymentsTable 
                associationId={associationId} 
                memberId={memberId}
              />
            </TabsContent>
            <TabsContent value="transactions" className="space-y-4">
              <MemberTransactionsTable 
                associationId={associationId}
                memberId={memberId}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}