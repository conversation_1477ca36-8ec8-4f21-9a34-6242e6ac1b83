"use client";

import Link from "next/link";
import { Plus, FileText, Calendar, User, CreditCard } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useTranslations } from "next-intl";

interface PropertyContractsTabProps {
  property: any;
  locale: string;
}

export function PropertyContractsTab({ property, locale }: PropertyContractsTabProps) {
  const t = useTranslations("properties.tabs.contracts");
  const tStatus = useTranslations("contracts.status");
  
  // For now, we'll show a placeholder since contracts aren't implemented yet
  const contracts: any[] = []; // This would come from the database in the future

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "success";
      case "EXPIRED":
        return "destructive"; 
      case "TERMINATED":
        return "secondary";
      case "DRAFT":
        return "outline";
      default:
        return "default";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return tStatus("active");
      case "EXPIRED":
        return tStatus("expired");
      case "TERMINATED":
        return tStatus("terminated");
      case "DRAFT":
        return tStatus("draft");
      default:
        return status;
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {t("title")} ({contracts.length})
          </CardTitle>
          <CardDescription>
            {t("description")}
          </CardDescription>
        </div>
        <Button asChild>
          <Link href={`/${locale}/dashboard/contracts/create?property=${property.id}`}>
            <Plus className="mr-2 h-4 w-4" />
            {t("createContract")}
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        {contracts.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-semibold">{t("noContracts")}</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              {t("noContractsDescription")}
            </p>
            <Button asChild className="mt-4">
              <Link href={`/${locale}/dashboard/contracts/create?property=${property.id}`}>
                <Plus className="mr-2 h-4 w-4" />
                {t("createFirstContract")}
              </Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("contractId")}</TableHead>
                  <TableHead>{t("tenant")}</TableHead>
                  <TableHead>{t("unit")}</TableHead>
                  <TableHead className="text-center">{t("startDate")}</TableHead>
                  <TableHead className="text-center">{t("endDate")}</TableHead>
                  <TableHead className="text-center">{t("monthlyRent")}</TableHead>
                  <TableHead className="text-center">{t("securityDeposit")}</TableHead>
                  <TableHead className="text-center">{t("status")}</TableHead>
                  <TableHead>{t("actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {contracts.map((contract: any) => (
                  <TableRow key={contract.id}>
                    <TableCell className="font-medium">
                      <Link 
                        href={`/${locale}/dashboard/contracts/${contract.id}`}
                        className="text-blue-600 hover:underline"
                      >
                        #{contract.id}
                      </Link>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{contract.tenant_name}</p>
                          <p className="text-sm text-muted-foreground">{contract.tenant_phone}</p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{contract.unit_number || t("entireProperty")}</TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center gap-1 justify-center">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        {contract.start_date}
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center gap-1 justify-center">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        {contract.end_date}
                      </div>
                    </TableCell>
                    <TableCell className="text-center font-medium">
                      <div className="flex items-center gap-1 justify-center">
                        <CreditCard className="h-4 w-4 text-muted-foreground" />
                        {contract.monthly_rent} OMR
                      </div>
                    </TableCell>
                    <TableCell className="text-center font-medium">
                      {contract.security_deposit} OMR
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant={getStatusVariant(contract.status) as any}>
                        {getStatusText(contract.status)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/${locale}/dashboard/contracts/${contract.id}`}>
                          {t("view")}
                        </Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {/* Contract Summary */}
        <div className="mt-6 grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="pt-4">
              <div className="text-2xl font-bold">0</div>
              <p className="text-sm text-muted-foreground">{t("activeContracts")}</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-4">
              <div className="text-2xl font-bold">0</div>
              <p className="text-sm text-muted-foreground">{t("expiringSoon")}</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-4">
              <div className="text-2xl font-bold text-green-600">0.000 OMR</div>
              <p className="text-sm text-muted-foreground">{t("monthlyIncome")}</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-4">
              <div className="text-2xl font-bold text-blue-600">0.000 OMR</div>
              <p className="text-sm text-muted-foreground">{t("securityDeposits")}</p>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  );
}