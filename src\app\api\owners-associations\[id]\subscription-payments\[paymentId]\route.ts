import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { recordPaymentSchema } from "@/types/owners-association";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";
import { Decimal } from "@prisma/client/runtime/library";
import { z } from "zod";

// Schema for recording payment - without subscription_payment_id since it's in the URL
const recordPaymentBodySchema = z.object({
  amount: z.string().regex(/^\d+(\.\d{1,3})?$/, "Invalid amount format"),
  payment_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
  payment_method: z.enum(["CASH", "BANK_TRANSFER", "CREDIT_CARD", "DEBIT_CARD", "CHECK", "OTHER"]),
  installment: z.number().optional().nullable(),
  notes: z.string().optional().nullable(),
  create_transaction: z.boolean().default(true),
});

// GET /api/owners-associations/[id]/subscription-payments/[paymentId] - Get payment details with installments
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; paymentId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await hasPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view payment details");
    }

    const { id: idParam, paymentId: paymentIdParam } = await params;
    const associationId = parseInt(idParam);
    const paymentId = parseInt(paymentIdParam);

    if (isNaN(associationId) || isNaN(paymentId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    // Fetch payment with all relations - handle both schemas gracefully
    let payment;
    try {
      payment = await db.subscriptionPayment.findFirst({
        where: {
          id: paymentId,
          subscription: {
            association_id: associationId,
          },
        },
        include: {
          subscription: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
              amount: true,
              frequency: true,
            },
          },
          member: {
            select: {
              id: true,
              full_name: true,
              unit_number: true,
              email: true,
              phone: true,
            },
          },
          creator: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
            },
          },
        },
      });
    } catch (error: any) {
      console.warn('Enhanced schema query failed, using basic schema:', error.message);
      payment = await db.subscriptionPayment.findFirst({
        where: {
          id: paymentId,
          subscription: {
            association_id: associationId,
          },
        },
        include: {
          subscription: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
              amount: true,
              frequency: true,
            },
          },
          member: {
            select: {
              id: true,
              full_name: true,
              unit_number: true,
              email: true,
              phone: true,
            },
          },
          creator: {
            select: {
              id: true,
              first_name: true,
              last_name: true,
            },
          },
        },
      });
    }

    if (!payment) {
      return ApiResponseBuilder.notFound("Payment not found");
    }

    // Transform payment to handle Decimal serialization
    const hasEnhancedSchema = 'amount_due' in payment;
    const amountDue = hasEnhancedSchema 
      ? new Decimal((payment as any).amount_due?.toString() || payment.amount.toString())
      : new Decimal(payment.amount.toString());
    const amountPaid = hasEnhancedSchema
      ? new Decimal((payment as any).amount_paid?.toString() || "0")
      : payment.status === 'PAID' ? new Decimal(payment.amount.toString()) : new Decimal("0");
    
    const transformedPayment = {
      ...payment,
      amount: payment.amount.toString(),
      amount_due: amountDue.toString(),
      amount_paid: amountPaid.toString(),
      remaining_balance: amountDue.minus(amountPaid).toString(),
      subscription: {
        ...payment.subscription,
        amount: payment.subscription.amount.toString(),
      },
      transaction: (payment as any).transaction ? {
        ...(payment as any).transaction,
        amount: (payment as any).transaction.amount.toString(),
      } : null,
      installments: (payment as any).installments ? (payment as any).installments.map((inst: any) => ({
        ...inst,
        amount: inst.amount.toString(),
        transaction: {
          ...inst.transaction,
          amount: inst.transaction.amount.toString(),
        },
      })) : [],
      enhanced_schema: hasEnhancedSchema,
    };

    return ApiResponseBuilder.success(transformedPayment);
  } catch (error) {
    console.error("Error fetching payment details:", error);
    return ApiResponseBuilder.error("Failed to fetch payment details", "INTERNAL_ERROR", 500);
  }
}

// POST /api/owners-associations/[id]/subscription-payments/[paymentId] - Record payment or partial payment
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; paymentId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for owners-associations
    const canCreate = await hasPermission(decoded.id, "owners-associations", "CREATE");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to record payments");
    }

    const { id: idParam, paymentId: paymentIdParam } = await params;
    const associationId = parseInt(idParam);
    const paymentId = parseInt(paymentIdParam);

    if (isNaN(associationId) || isNaN(paymentId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    const body = await request.json();
    const validatedData = recordPaymentBodySchema.parse(body);

    // Start transaction
    const result = await db.$transaction(async (tx) => {
      // Fetch the subscription payment
      const subscriptionPayment = await tx.subscriptionPayment.findFirst({
        where: {
          id: paymentId,
          subscription: {
            association_id: associationId,
          },
        },
        include: {
          subscription: true,
          member: true,
        },
      });

      if (!subscriptionPayment) {
        throw new Error("Payment not found");
      }

      if (subscriptionPayment.status === "PAID") {
        throw new Error("Payment is already fully paid");
      }

      const paymentAmount = new Decimal(validatedData.amount);
      const paymentHasEnhancedSchema = 'amount_due' in subscriptionPayment;
      const amountDue = paymentHasEnhancedSchema
        ? new Decimal((subscriptionPayment as any).amount_due?.toString() || (subscriptionPayment as any).amount.toString())
        : new Decimal((subscriptionPayment as any).amount.toString());
      const amountPaid = paymentHasEnhancedSchema
        ? new Decimal((subscriptionPayment as any).amount_paid?.toString() || "0")
        : (subscriptionPayment as any).status === 'PAID' ? new Decimal((subscriptionPayment as any).amount.toString()) : new Decimal("0");
      const remainingBalance = amountDue.minus(amountPaid);

      // Validate payment amount
      if (paymentAmount.greaterThan(remainingBalance)) {
        throw new Error(`Payment amount exceeds remaining balance of ${remainingBalance.toString()} OMR`);
      }

      const newAmountPaid = amountPaid.plus(paymentAmount);
      const isFullPayment = newAmountPaid.greaterThanOrEqualTo(amountDue);

      // Create income transaction if requested
      let transaction = null;
      if (validatedData.create_transaction !== false) {
        // Build transaction data - only include fields that exist in the schema
        const transactionData: any = {
          association_id: associationId,
          transaction_date: new Date(validatedData.payment_date),
          type: "INCOME",
          category: "subscription",
          description: `${subscriptionPayment.subscription.name_en} payment from ${subscriptionPayment.member.full_name} (Unit: ${subscriptionPayment.member.unit_number})`,
          amount: paymentAmount,
          payment_method: validatedData.payment_method,
          notes: validatedData.notes,
          created_by: decoded.id,
        };

        // Note: reference_number is stored in subscription_payment, not in transaction

        // Try to create with enhanced schema fields first
        try {
          transaction = await tx.associationTransaction.create({
            data: {
              ...transactionData,
              member_id: subscriptionPayment.member_id,
              subscription_payment_id: isFullPayment ? paymentId : null,
            },
          });
        } catch (error: any) {
          // If enhanced fields don't exist, create without them
          console.warn('Creating transaction without enhanced fields:', error.message);
          transaction = await tx.associationTransaction.create({
            data: transactionData,
          });
        }
      }

      // Update subscription payment - handle both schemas
      let updateData: any = {
        payment_date: new Date(validatedData.payment_date),
        status: isFullPayment ? "PAID" : "PARTIALLY_PAID",
        payment_method: validatedData.payment_method,
        installment: validatedData.installment,
        notes: validatedData.notes,
      };
      
      // Only include enhanced schema fields if they exist
      const hasEnhancedSchema = 'amount_due' in subscriptionPayment;
      if (hasEnhancedSchema) {
        updateData.amount_paid = newAmountPaid;
        if (isFullPayment && transaction) {
          updateData.transaction_id = transaction.id;
        }
      }
      
      const updatedPayment = await tx.subscriptionPayment.update({
        where: { id: paymentId },
        data: updateData,
      });

      // Create payment installment record - currently not implemented in schema
      // TODO: Add subscriptionPaymentInstallment table to schema if needed
      if (false && transaction && !isFullPayment && hasEnhancedSchema) {
        try {
          // TODO: Implement installment creation when schema supports it
          /* 
          const installment = await tx.subscriptionPaymentInstallment.create({
            data: {
              subscription_payment_id: paymentId,
              transaction_id: transaction.id,
              amount: paymentAmount,
              payment_date: new Date(validatedData.payment_date),
              payment_method: validatedData.payment_method,
              notes: validatedData.notes,
              created_by: decoded.id,
            },
          });
          */
        } catch (installmentError) {
          console.warn('Installment creation failed - enhanced schema not available:', installmentError);
          // Continue without creating installment for backward compatibility
        }
      }

      // Fetch updated payment with relations
      let paymentWithRelations;
      try {
        paymentWithRelations = await tx.subscriptionPayment.findUnique({
          where: { id: paymentId },
          include: {
            subscription: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
                amount: true,
              },
            },
            member: {
              select: {
                id: true,
                full_name: true,
                unit_number: true,
              },
            },
          },
        });
      } catch (error) {
        // Fallback to basic query
        paymentWithRelations = await tx.subscriptionPayment.findUnique({
          where: { id: paymentId },
          include: {
            subscription: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
                amount: true,
              },
            },
            member: {
              select: {
                id: true,
                full_name: true,
                unit_number: true,
              },
            },
          },
        });
      }

      return { payment: paymentWithRelations, transaction };
    });

    // Transform result to handle Decimal serialization
    const payment = result.payment!;
    const hasEnhancedSchema = 'amount_due' in payment;
    const amountDue = hasEnhancedSchema 
      ? new Decimal((payment as any).amount_due?.toString() || (payment as any).amount.toString())
      : new Decimal((payment as any).amount.toString());
    const amountPaid = hasEnhancedSchema
      ? new Decimal((payment as any).amount_paid?.toString() || "0")
      : (payment as any).status === 'PAID' ? new Decimal((payment as any).amount.toString()) : new Decimal("0");
    
    const transformedResult = {
      payment: {
        ...payment,
        amount: payment.amount.toString(),
        amount_due: amountDue.toString(),
        amount_paid: amountPaid.toString(),
        remaining_balance: amountDue.minus(amountPaid).toString(),
        subscription: {
          ...payment.subscription,
          amount: payment.subscription.amount.toString(),
        },
        transaction: (payment as any).transaction ? {
          ...(payment as any).transaction,
          amount: (payment as any).transaction.amount.toString(),
        } : null,
        installments: (payment as any).installments ? (payment as any).installments.map((inst: any) => ({
          ...inst,
          amount: inst.amount.toString(),
          transaction: {
            ...inst.transaction,
            amount: inst.transaction.amount.toString(),
          },
        })) : [],
        enhanced_schema: hasEnhancedSchema,
      },
      transaction: result.transaction ? {
        ...result.transaction,
        amount: result.transaction.amount.toString(),
      } : null,
    };

    return ApiResponseBuilder.success(transformedResult);
  } catch (error: any) {
    console.error("Error recording payment:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    if (error.message) {
      return ApiResponseBuilder.error(error.message, "BUSINESS_ERROR", 400);
    }

    return ApiResponseBuilder.error("Failed to record payment", "INTERNAL_ERROR", 500);
  }
}