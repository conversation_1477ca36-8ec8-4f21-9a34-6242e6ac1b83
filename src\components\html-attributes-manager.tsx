'use client';

import { useEffect } from 'react';
import { useLocale } from 'next-intl';
import { getLangDir } from 'rtl-detect';

/**
 * Client component that manages HTML attributes for locale-specific changes
 * This ensures proper RTL/LTR direction and language attributes without hydration issues
 */
export function HTMLAttributesManager() {
  const locale = useLocale();

  useEffect(() => {
    const direction = getLangDir(locale);

    // Update HTML attributes
    document.documentElement.lang = locale;
    document.documentElement.dir = direction;

    // Add/remove RTL class for CSS targeting
    if (direction === 'rtl') {
      document.documentElement.classList.add('rtl');
      document.documentElement.classList.remove('ltr');

      // Force Tajawal font for Arabic content
      const tajawalFont = "'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif";
      document.documentElement.style.fontFamily = tajawalFont;
      document.body.style.fontFamily = tajawalFont;

      // Apply font to all elements with a slight delay to ensure DOM is ready
      setTimeout(() => {
        const allElements = document.querySelectorAll('*');
        allElements.forEach((element) => {
          if (element instanceof HTMLElement) {
            element.style.fontFamily = tajawalFont;
          }
        });
      }, 100);
    } else {
      document.documentElement.classList.add('ltr');
      document.documentElement.classList.remove('rtl');

      // Use Inter font for LTR content
      document.documentElement.style.fontFamily = "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif";
      document.body.style.fontFamily = "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif";
    }
  }, [locale]);

  return null; // This component doesn't render anything
}
