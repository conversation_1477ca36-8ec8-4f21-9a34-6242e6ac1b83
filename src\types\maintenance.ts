import { z } from "zod";
import type {
  MaintenanceRequest,
  MaintenanceAttachment,
  MaintenanceStatusHistory,
  Property,
  Unit,
  Tenant,
  User,
  MaintenancePriority,
  MaintenanceCategory,
  MaintenanceStatus,
} from "@/generated/prisma";

// Re-export enums for convenience
export { MaintenanceStatus, MaintenancePriority, MaintenanceCategory };

// Maintenance Types
export interface MaintenanceRequestWithRelations extends MaintenanceRequest {
  property?: Property;
  unit?: Unit | null;
  tenant?: Tenant | null;
  reporter?: User | null;
  assignee?: User | null;
  creator?: User | null;
  updater?: User | null;
  attachments?: MaintenanceAttachment[];
  status_history?: MaintenanceStatusHistory[];
}

export interface MaintenanceAttachmentWithRelations extends MaintenanceAttachment {
  request?: MaintenanceRequest;
  uploader?: User | null;
}

export interface MaintenanceStatusHistoryWithRelations extends MaintenanceStatusHistory {
  request?: MaintenanceRequest;
  changer?: User | null;
}

// Maintenance Schemas
export const maintenanceRequestSchema = z.object({
  property_id: z.number().int().positive(),
  unit_id: z.number().int().positive().optional().nullable(),
  tenant_id: z.number().int().positive().optional().nullable(),
  title: z.string().min(1, "Title is required").max(200),
  description: z.string().min(1, "Description is required"),
  priority: z.enum(["LOW", "MEDIUM", "HIGH", "EMERGENCY"]),
  category: z.enum([
    "ELECTRICAL",
    "PLUMBING",
    "HVAC",
    "STRUCTURAL",
    "APPLIANCES",
    "PAINTING",
    "CLEANING",
    "LANDSCAPING",
    "SECURITY",
    "OTHER",
  ]),
  scheduled_date: z.string().optional().nullable(),
  estimated_cost: z.coerce.number().min(0).optional().nullable(),
  contractor_name: z.string().max(100).optional().nullable(),
  contractor_phone: z.string().max(20).optional().nullable(),
  internal_notes: z.string().optional().nullable(),
});

export const maintenanceUpdateSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  description: z.string().min(1).optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH", "EMERGENCY"]).optional(),
  category: z.enum([
    "ELECTRICAL",
    "PLUMBING",
    "HVAC",
    "STRUCTURAL",
    "APPLIANCES",
    "PAINTING",
    "CLEANING",
    "LANDSCAPING",
    "SECURITY",
    "OTHER",
  ]).optional(),
  status: z.enum(["REPORTED", "ACKNOWLEDGED", "IN_PROGRESS", "ON_HOLD", "COMPLETED", "CANCELLED"]).optional(),
  assigned_to: z.number().int().positive().optional().nullable(),
  scheduled_date: z.string().optional().nullable(),
  estimated_cost: z.coerce.number().min(0).optional().nullable(),
  actual_cost: z.coerce.number().min(0).optional().nullable(),
  contractor_name: z.string().max(100).optional().nullable(),
  contractor_phone: z.string().max(20).optional().nullable(),
  internal_notes: z.string().optional().nullable(),
  resolution_notes: z.string().optional().nullable(),
});

export const maintenanceStatusUpdateSchema = z.object({
  status: z.enum(["REPORTED", "ACKNOWLEDGED", "IN_PROGRESS", "ON_HOLD", "COMPLETED", "CANCELLED"]),
  notes: z.string().optional().nullable(),
  actual_cost: z.coerce.number().min(0).optional().nullable(),
  resolution_notes: z.string().optional().nullable(),
});

// Maintenance Filter Schema
export const maintenanceFilterSchema = z.object({
  property_id: z.number().int().positive().optional(),
  unit_id: z.number().int().positive().optional(),
  tenant_id: z.number().int().positive().optional(),
  status: z.enum(["REPORTED", "ACKNOWLEDGED", "IN_PROGRESS", "ON_HOLD", "COMPLETED", "CANCELLED"]).optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH", "EMERGENCY"]).optional(),
  category: z.enum([
    "ELECTRICAL",
    "PLUMBING",
    "HVAC",
    "STRUCTURAL",
    "APPLIANCES",
    "PAINTING",
    "CLEANING",
    "LANDSCAPING",
    "SECURITY",
    "OTHER",
  ]).optional(),
  assigned_to: z.number().int().positive().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  search: z.string().optional(),
  page: z.number().int().positive().default(1),
  pageSize: z.number().int().positive().max(100).default(10),
  sortBy: z.string().default("created_at"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// Type exports
export type MaintenanceRequestInput = z.infer<typeof maintenanceRequestSchema>;
export type MaintenanceUpdateInput = z.infer<typeof maintenanceUpdateSchema>;
export type MaintenanceStatusUpdateInput = z.infer<typeof maintenanceStatusUpdateSchema>;
export type MaintenanceFilters = z.infer<typeof maintenanceFilterSchema>;

// Helper functions
export function generateRequestNumber(): string {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, "0");
  return `MNT-${year}${month}-${random}`;
}

export function getPriorityColor(priority: MaintenancePriority): string {
  switch (priority) {
    case "LOW":
      return "default";
    case "MEDIUM":
      return "secondary";
    case "HIGH":
      return "destructive";
    case "EMERGENCY":
      return "destructive";
    default:
      return "default";
  }
}

export function getStatusColor(status: MaintenanceStatus): string {
  switch (status) {
    case "REPORTED":
      return "secondary";
    case "ACKNOWLEDGED":
      return "default";
    case "IN_PROGRESS":
      return "secondary";
    case "ON_HOLD":
      return "secondary";
    case "COMPLETED":
      return "default";
    case "CANCELLED":
      return "outline";
    default:
      return "default";
  }
}

export function canEditRequest(status: MaintenanceStatus): boolean {
  return status !== "COMPLETED" && status !== "CANCELLED";
}

export function canCompleteRequest(status: MaintenanceStatus): boolean {
  return status === "IN_PROGRESS";
}

export function canCancelRequest(status: MaintenanceStatus): boolean {
  return status !== "COMPLETED" && status !== "CANCELLED";
}

export function calculateResponseTime(reportedDate: Date | string, acknowledgedDate?: Date | string | null): number | null {
  if (!acknowledgedDate) return null;
  
  const reported = new Date(reportedDate);
  const acknowledged = new Date(acknowledgedDate);
  
  return Math.floor((acknowledged.getTime() - reported.getTime()) / (1000 * 60 * 60)); // Hours
}

export function calculateCompletionTime(reportedDate: Date | string, completedDate?: Date | string | null): number | null {
  if (!completedDate) return null;
  
  const reported = new Date(reportedDate);
  const completed = new Date(completedDate);
  
  return Math.floor((completed.getTime() - reported.getTime()) / (1000 * 60 * 60 * 24)); // Days
}

export function calculateMaintenanceTimeMetrics(maintenance: MaintenanceRequestWithRelations) {
  return {
    responseTime: calculateResponseTime(maintenance.reported_date, maintenance.assigned_date),
    resolutionTime: calculateCompletionTime(maintenance.reported_date, maintenance.completed_date),
    totalDuration: calculateCompletionTime(maintenance.reported_date, maintenance.completed_date || new Date()),
  };
}