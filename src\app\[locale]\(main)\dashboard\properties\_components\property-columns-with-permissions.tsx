"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Edit, Trash, Eye } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import { apiClient } from "@/lib/api-client";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertD<PERSON>ogFooter,
  <PERSON><PERSON><PERSON>ial<PERSON><PERSON>eader,
  AlertDialog<PERSON>itle,
} from "@/components/ui/alert-dialog";
import type { PropertyWithRelations } from "@/types/property";
import { useState } from "react";
import { usePermission } from "@/hooks/use-permissions";

interface ColumnTranslations {
  id: string;
  name: string;
  address: string;
  propertyType: string;
  baseRent: string;
  status: string;
  owner: string;
  totalArea: string;
  floorsCount: string;
  parkingSpaces: string;
  amenities: string;
  amenity: string;
  units?: string;
  createdAt: string;
  actions: string;
  selectAll: string;
  selectRow: string;
  openMenu: string;
  viewDetails: string;
  editProperty: string;
  deleteProperty: string;
  statusAvailable: string;
  statusRented: string;
  statusUnderMaintenance: string;
  statusOutOfService: string;
}

function formatCurrency(amount: number | string): string {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  return new Intl.NumberFormat('en-OM', {
    minimumFractionDigits: 3,
    maximumFractionDigits: 3,
  }).format(numAmount);
}

function getStatusVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
  switch (status) {
    case "AVAILABLE":
      return "default";
    case "RENTED":
      return "secondary";
    case "UNDER_MAINTENANCE":
      return "outline";
    case "OUT_OF_SERVICE":
      return "destructive";
    default:
      return "default";
  }
}

function ActionCell({ 
  property, 
  locale, 
  t, 
  onRefresh 
}: { 
  property: PropertyWithRelations;
  locale: string;
  t: ColumnTranslations;
  onRefresh?: () => void;
}) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const tDelete = useTranslations('properties.delete');
  
  // Check permissions
  const { hasPermission: canUpdate } = usePermission("properties", "update");
  const { hasPermission: canDelete } = usePermission("properties", "delete");

  const handleDelete = async () => {
    if (!canDelete) return;
    
    try {
      setIsDeleting(true);
      const response = await apiClient.delete(`/api/properties/${property.id}`);

      if (response.ok) {
        toast.success(tDelete('success'));
        setShowDeleteDialog(false);
        onRefresh?.();
      } else {
        toast.error(tDelete('error'));
      }
    } catch (error) {
      toast.error(tDelete('error'));
    } finally {
      setIsDeleting(false);
    }
  };

  // If user has no update or delete permissions, don't show actions menu
  if (!canUpdate && !canDelete) {
    return (
      <Button variant="ghost" size="sm" asChild>
        <Link href={`/${locale}/dashboard/properties/${property.id}`}>
          <Eye className="h-4 w-4" />
        </Link>
      </Button>
    );
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">{t.openMenu}</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>{t.actions}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link href={`/${locale}/dashboard/properties/${property.id}`}>
              <Eye className="mr-2 h-4 w-4" />
              {t.viewDetails}
            </Link>
          </DropdownMenuItem>
          {canUpdate && (
            <DropdownMenuItem asChild>
              <Link href={`/${locale}/dashboard/properties/${property.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                {t.editProperty}
              </Link>
            </DropdownMenuItem>
          )}
          {canDelete && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => setShowDeleteDialog(true)}
                className="text-destructive"
                disabled={property.status === "RENTED"}
              >
                <Trash className="mr-2 h-4 w-4" />
                {t.deleteProperty}
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{tDelete('title')}</AlertDialogTitle>
            <AlertDialogDescription>
              {tDelete('description', { name: locale === "ar" ? property.name_ar : property.name_en })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>{tDelete('cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? tDelete('deleting') : tDelete('confirm')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

export function getPropertyColumnsWithPermissions(
  t: ColumnTranslations,
  locale: string,
  onRefresh?: () => void
): ColumnDef<PropertyWithRelations>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label={t.selectAll}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={t.selectRow}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.name} />
      ),
      cell: ({ row }) => {
        const property = row.original;
        return (
          <div className="flex items-center gap-2">
            <div className="font-medium">
              {locale === "ar" ? property.name_ar : property.name_en}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "property_type",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.propertyType} />
      ),
      cell: ({ row }) => {
        const propertyType = row.original.property_type;
        if (!propertyType) return "-";
        return locale === "ar" ? propertyType.name_ar : propertyType.name_en;
      },
    },
    {
      accessorKey: "base_rent_amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.baseRent} />
      ),
      cell: ({ row }) => {
        return (
          <div className="text-right font-medium">
            {formatCurrency(row.getValue("base_rent_amount"))} OMR
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.status} />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        const statusKey = `status${status.charAt(0) + status.slice(1).toLowerCase().replace(/_/g, '')}`;
        return (
          <Badge variant={getStatusVariant(status)}>
            {t[statusKey as keyof ColumnTranslations] || status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "owner",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t.owner} />
      ),
      cell: ({ row }) => {
        const owner = row.original.owner;
        if (!owner) return "-";
        return locale === "ar" ? owner.name_ar : owner.name_en;
      },
    },
    {
      id: "actions",
      header: () => <div className="text-right">{t.actions}</div>,
      cell: ({ row }) => (
        <ActionCell 
          property={row.original} 
          locale={locale} 
          t={t} 
          onRefresh={onRefresh} 
        />
      ),
    },
  ];
}