---
allowed-tools: <PERSON><PERSON>(*), Read(*), Write(*), Grep(*), Glob(*), Task(*), MultiEdit(*)
description: Automatically analyze code changes and update all documentation
model: claude-3-5-sonnet-20241022
---

## Context

### Check Git Status
!`git status --porcelain 2>/dev/null || echo "Not a git repository"`

### Recent Changes Summary
!`git diff --stat HEAD~1..HEAD 2>/dev/null || echo "No recent commits"`

### Changed Files List
!`git diff --name-only HEAD~1..HEAD 2>/dev/null || find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | head -20`

## Your Task

Perform a comprehensive documentation update based on code changes:

1. **Analyze Code Changes**:
   - Check for new modules, components, or features
   - Identify API changes or new endpoints
   - Note configuration changes
   - Track dependency updates

2. **Update Core Documentation**:
   - **README.md**: Project overview, setup, features
   - **CLAUDE.md**: Implementation guidelines and architecture
   - Module-specific documentation files

3. **Auto-generate Documentation Sections**:
   - API endpoint documentation from route files
   - Component documentation from TSX/JSX files
   - Database schema documentation from Prisma schema
   - Configuration documentation from config files

4. **Create New Documentation** (if needed):
   - New feature guides
   - Migration guides for breaking changes
   - Architecture decision records

### Documentation Update Checklist:

- [ ] Update feature list in README
- [ ] Update installation/setup instructions
- [ ] Update API documentation
- [ ] Update component documentation
- [ ] Update configuration documentation
- [ ] Update CLAUDE.md with new patterns/guidelines
- [ ] Add migration notes for breaking changes
- [ ] Update examples and code snippets
- [ ] Update dependency list
- [ ] Update troubleshooting section

### Output Format:

After analyzing changes, update all relevant documentation files and provide a summary of:
- Files updated
- Major changes documented
- New documentation created
- Any documentation that needs manual review