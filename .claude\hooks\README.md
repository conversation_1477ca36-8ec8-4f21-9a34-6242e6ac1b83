# Claude Code Hooks

This directory contains hooks for Claude Code. These are scripts that run automatically after certain Claude Code operations.

## Auto-commit Hook

The `auto-commit.bat` file is a Windows-compatible version of the auto-commit hook. It:

1. Checks if you're in a git repository
2. Checks if there are any changes to commit
3. Automatically stages and commits all changes with a descriptive message

## Fixing the Hook Error

If you're getting errors about `bash` not being recognized on Windows, you have several options:

### Option 1: Use the Windows batch file
Claude Code should automatically use `auto-commit.bat` on Windows.

### Option 2: Disable the hook
You can disable the auto-commit hook in your Claude Code settings.

### Option 3: Install Git Bash
If you have Git for Windows installed, you can use Git Bash which provides a bash environment.

### Option 4: Use WSL (Windows Subsystem for Linux)
WSL provides a full Linux environment on Windows where bash commands will work.

## Note

These hooks are specific to Claude Code and are not part of your project's git hooks. They help automate common tasks when using Claude Code for development.