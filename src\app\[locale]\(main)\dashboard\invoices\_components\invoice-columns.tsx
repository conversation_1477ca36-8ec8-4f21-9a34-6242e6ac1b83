"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Eye, Edit, Trash, CreditCard } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useState } from "react";
import { InvoiceWithRelations } from "@/types/invoice";
import { formatOMR } from "@/lib/format";
import { cn } from "@/lib/utils";

export function getInvoiceColumns(
  translations: any,
  locale: string,
  onRefresh?: () => void
): ColumnDef<InvoiceWithRelations>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label={translations.selectAll}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={translations.selectRow}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "invoiceNumber",
      accessorKey: "invoice_number",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.invoiceNumber} />
      ),
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {row.original.invoice_number}
          </div>
        );
      },
    },
    {
      id: "tenant",
      accessorKey: "tenant",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.tenant} />
      ),
      cell: ({ row }) => {
        const tenant = row.original.tenant;
        if (!tenant) return "-";
        const name = `${tenant.first_name} ${tenant.last_name}`;
        return <div>{name}</div>;
      },
    },
    {
      id: "property",
      accessorKey: "property",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.property} />
      ),
      cell: ({ row }) => {
        const property = row.original.property;
        if (!property) return "-";
        const name = locale === "ar" ? property.name_ar : property.name_en;
        return <div>{name}</div>;
      },
    },
    {
      id: "unit",
      accessorKey: "unit",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.unit} />
      ),
      cell: ({ row }) => {
        const unit = row.original.unit;
        if (!unit) return "-";
        return <div>{unit.unit_number}</div>;
      },
    },
    {
      id: "invoiceDate",
      accessorKey: "invoice_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.invoiceDate} />
      ),
      cell: ({ row }) => {
        return (
          <div className="text-sm">
            {format(new Date(row.original.invoice_date), "dd/MM/yyyy")}
          </div>
        );
      },
    },
    {
      id: "dueDate",
      accessorKey: "due_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.dueDate} />
      ),
      cell: ({ row }) => {
        const dueDate = new Date(row.original.due_date);
        const isOverdue = dueDate < new Date() && row.original.status !== "PAID";
        
        return (
          <div className={cn("text-sm", isOverdue && "text-destructive font-medium")}>
            {format(dueDate, "dd/MM/yyyy")}
          </div>
        );
      },
    },
    {
      id: "totalAmount",
      accessorKey: "total_amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.totalAmount} />
      ),
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {formatOMR(row.original.total_amount.toString())}
          </div>
        );
      },
    },
    {
      id: "paidAmount",
      accessorKey: "paid_amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.paidAmount} />
      ),
      cell: ({ row }) => {
        return (
          <div className="text-sm">
            {formatOMR(row.original.paid_amount.toString())}
          </div>
        );
      },
    },
    {
      id: "balanceAmount",
      accessorKey: "balance_amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.balanceAmount} />
      ),
      cell: ({ row }) => {
        const balance = parseFloat(row.original.balance_amount.toString());
        return (
          <div className={cn("font-medium", balance > 0 && "text-destructive")}>
            {formatOMR(row.original.balance_amount.toString())}
          </div>
        );
      },
    },
    {
      id: "status",
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={translations.status} />
      ),
      cell: ({ row }) => {
        const status = row.original.status;
        
        const statusConfig = {
          DRAFT: { label: translations.statusDraft || "Draft", variant: "outline" as const },
          PENDING: { label: translations.statusPending, variant: "secondary" as const },
          PARTIALLY_PAID: { label: translations.statusPartiallyPaid, variant: "default" as const },
          PAID: { label: translations.statusPaid, variant: "default" as const },
          OVERDUE: { label: translations.statusOverdue, variant: "destructive" as const },
          CANCELLED: { label: translations.statusCancelled, variant: "outline" as const },
        };

        const config = statusConfig[status];

        return (
          <Badge variant={config.variant}>
            {config.label}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "actions",
      header: () => <div className="text-center">{translations.actions}</div>,
      cell: ({ row }) => {
        const invoice = row.original;
        const [showDeleteDialog, setShowDeleteDialog] = useState(false);
        const [isDeleting, setIsDeleting] = useState(false);

        const handleDelete = async () => {
          try {
            setIsDeleting(true);
            const response = await fetch(`/api/invoices/${invoice.id}`, {
              method: "DELETE",
            });

            if (!response.ok) {
              const error = await response.json();
              throw new Error(error.error?.message || "Failed to delete invoice");
            }

            toast.success("Invoice deleted successfully");
            setShowDeleteDialog(false);
            if (onRefresh) {
              onRefresh();
            }
          } catch (error) {
            console.error("Delete error:", error);
            toast.error(error instanceof Error ? error.message : "Failed to delete invoice");
          } finally {
            setIsDeleting(false);
          }
        };

        return (
          <div className="text-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  aria-label={translations.openMenu}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>{translations.actions}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href={`/dashboard/invoices/${invoice.id}`}>
                    <Eye className="mr-2 h-4 w-4" />
                    {translations.viewDetails}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/dashboard/invoices/${invoice.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    {translations.editInvoice}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem 
                  asChild
                  disabled={invoice.status === "PAID" || invoice.status === "CANCELLED"}
                >
                  <Link href={`/dashboard/invoices/${invoice.id}/payment`}>
                    <CreditCard className="mr-2 h-4 w-4" />
                    {translations.recordPayment}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-destructive"
                  disabled={invoice.status === "PAID"}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {translations.deleteInvoice}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete invoice
                    "{invoice.invoice_number}".
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {isDeleting ? "Deleting..." : "Delete"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        );
      },
    },
  ];
}