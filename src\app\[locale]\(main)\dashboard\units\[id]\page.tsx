import { getTranslations } from "next-intl/server";
import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { Edit, ArrowLeft, Building2, MapPin, Home, Bath, Users } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { db } from "@/lib/db";

interface UnitDetailPageProps {
  params: Promise<{
    locale: string;
    id: string;
  }>;
}

async function getUnit(id: number) {
  const unit = await db.unit.findUnique({
    where: { id },
    include: {
      property: {
        include: {
          property_type: true,
        },
      },
      amenities: {
        include: {
          amenity: true,
        },
      },
      creator: {
        select: {
          id: true,
          username: true,
          first_name: true,
          last_name: true,
        },
      },
      updater: {
        select: {
          id: true,
          username: true,
          first_name: true,
          last_name: true,
        },
      },
    },
  });

  if (!unit) {
    return null;
  }

  // Transform Decimal fields to strings for client components
  return {
    ...unit,
    area: unit.area?.toString() || null,
    rent_amount: unit.rent_amount.toString(),
    property: {
      ...unit.property,
      base_rent: unit.property.base_rent.toString(),
      total_area: unit.property.total_area?.toString() || null,
    },
    amenities: unit.amenities.map(ua => ua.amenity),
  };
}

export async function generateMetadata({
  params,
}: UnitDetailPageProps): Promise<Metadata> {
  const { locale, id } = await params;
  const t = await getTranslations({ locale, namespace: "units" });
  const unitId = parseInt(id);
  if (isNaN(unitId)) {
    return { title: t("unitNotFound") };
  }

  const unit = await getUnit(unitId);
  if (!unit) {
    return { title: t("unitNotFound") };
  }

  const unitName = locale === "ar" ? 
    unit.unit_name_ar || unit.unit_number : 
    unit.unit_name_en || unit.unit_number;

  return {
    title: `${unitName} - ${t("title")}`,
    description: t("description"),
  };
}

export default async function UnitDetailPage({
  params,
}: UnitDetailPageProps) {
  const { locale, id } = await params;
  const unitId = parseInt(id);
  if (isNaN(unitId)) {
    notFound();
  }

  const unit = await getUnit(unitId);
  if (!unit) {
    notFound();
  }

  const t = await getTranslations({ locale, namespace: "units" });
  const tCommon = await getTranslations({ locale, namespace: "common" });
  const tStatus = await getTranslations({ locale, namespace: "units.status" });
  const tDetails = await getTranslations({ locale, namespace: "units.details" });

  const unitName = locale === "ar" ? 
    unit.unit_name_ar || unit.unit_number : 
    unit.unit_name_en || unit.unit_number;
  
  const propertyName = locale === "ar" ? 
    unit.property.name_ar : 
    unit.property.name_en;
    
  const propertyTypeName = locale === "ar" ? 
    unit.property.property_type.name_ar : 
    unit.property.property_type.name_en;

  const statusVariant = 
    unit.status === "AVAILABLE" ? "success" :
    unit.status === "RENTED" ? "default" :
    unit.status === "UNDER_MAINTENANCE" ? "warning" :
    "default";

  const statusText = 
    unit.status === "AVAILABLE" ? tStatus("available") :
    unit.status === "RENTED" ? tStatus("rented") :
    unit.status === "UNDER_MAINTENANCE" ? tStatus("underMaintenance") :
    unit.status;

  const creatorName = unit.creator ? 
    `${unit.creator.first_name} ${unit.creator.last_name}` : 
    "System";
    
  const updaterName = unit.updater ? 
    `${unit.updater.first_name} ${unit.updater.last_name}` : 
    "-";

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/${locale}/dashboard/units`}>
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h2 className="text-2xl font-bold tracking-tight">{unitName}</h2>
            <Badge variant={statusVariant as any}>{statusText}</Badge>
          </div>
          <p className="text-muted-foreground">{propertyName} - {propertyTypeName}</p>
        </div>
        <Button asChild>
          <Link href={`/${locale}/dashboard/units/${unit.id}/edit`}>
            <Edit className="mr-2 h-4 w-4" />
            {t("editUnit")}
          </Link>
        </Button>
      </div>

      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${locale}/dashboard`}>
              {tCommon("navigation.dashboard")}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${locale}/dashboard/units`}>
              {t("title")}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{unitName}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Unit Overview Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{tDetails("monthlyRent")}</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{parseFloat(unit.rent_amount).toFixed(3)} OMR</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{tDetails("floor")}</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{unit.floor_number || tDetails('ground')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{tDetails("rooms")}</CardTitle>
            <Home className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{unit.rooms_count || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{tDetails("bathrooms")}</CardTitle>
            <Bath className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{unit.bathrooms_count || 0}</div>
          </CardContent>
        </Card>
      </div>

      {/* Unit Details */}
      <Card>
        <CardHeader>
          <CardTitle>{t("unitDetails")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div>
              <label className="text-sm font-medium text-muted-foreground">{tDetails("unitNumber")}</label>
              <p className="text-sm">{unit.unit_number}</p>
            </div>
            {unit.area && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{tDetails("totalArea")}</label>
                <p className="text-sm">{parseFloat(unit.area).toFixed(2)} m²</p>
              </div>
            )}
            {unit.majalis_count !== null && unit.majalis_count > 0 && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{tDetails("majalis")}</label>
                <p className="text-sm">{unit.majalis_count}</p>
              </div>
            )}
          </div>
          
          {(unit.description_en || unit.description_ar) && (
            <>
              <Separator />
              <div>
                <label className="text-sm font-medium text-muted-foreground">{tDetails("description")}</label>
                <p className="text-sm mt-2 whitespace-pre-wrap">
                  {locale === "ar" ? unit.description_ar || unit.description_en : unit.description_en || unit.description_ar}
                </p>
              </div>
            </>
          )}
          
          {unit.amenities.length > 0 && (
            <>
              <Separator />
              <div>
                <label className="text-sm font-medium text-muted-foreground">{tDetails("amenities")}</label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {unit.amenities.map((amenity) => (
                    <Badge key={amenity.id} variant="outline">
                      {locale === "ar" ? amenity.name_ar : amenity.name_en}
                    </Badge>
                  ))}
                </div>
              </div>
            </>
          )}
          
          <Separator />
          
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-muted-foreground">{tDetails("createdBy")}</label>
              <p className="text-sm">{creatorName}</p>
              <p className="text-xs text-muted-foreground">
                {new Date(unit.created_at).toLocaleDateString()}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">{tDetails("lastUpdatedBy")}</label>
              <p className="text-sm">{updaterName}</p>
              <p className="text-xs text-muted-foreground">
                {new Date(unit.updated_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Property Information */}
      <Card>
        <CardHeader>
          <CardTitle>{tDetails("propertyInformation")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-muted-foreground">{tDetails("propertyName")}</label>
              <p className="text-sm">{propertyName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">{tDetails("propertyType")}</label>
              <p className="text-sm">{propertyTypeName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">{tDetails("propertyAddress")}</label>
              <p className="text-sm">
                {locale === "ar" ? unit.property.address_ar : unit.property.address_en}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">{tDetails("propertyBaseRent")}</label>
              <p className="text-sm">{parseFloat(unit.property.base_rent).toFixed(3)} OMR</p>
            </div>
          </div>
          <div className="pt-2">
            <Link href={`/${locale}/dashboard/properties/${unit.property.id}`}>
              <Button variant="outline" size="sm">
                {tDetails("viewPropertyDetails")}
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}