"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { format } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { apiClient } from "@/lib/api-client";
import { associationMemberUpdateSchema, type AssociationMemberUpdateInput, type AssociationMemberWithRelations } from "@/types/owners-association";
import { useRTL } from "@/hooks/use-rtl";

interface EditMemberFormProps {
  associationId: number;
  member: AssociationMemberWithRelations;
  onSuccess: () => void;
  onCancel: () => void;
}

export function EditMemberForm({ associationId, member, onSuccess, onCancel }: EditMemberFormProps) {
  const t = useTranslations("ownersAssociations.members");
  const { isRTL } = useRTL();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<AssociationMemberUpdateInput>({
    resolver: zodResolver(associationMemberUpdateSchema),
    defaultValues: {
      full_name: member.full_name,
      unit_number: member.unit_number,
      ownership_percentage: member.ownership_percentage?.toString() || "0",
      phone: member.phone || "",
      email: member.email || "",
      join_date: member.join_date ? format(new Date(member.join_date), "yyyy-MM-dd") : "",
      is_board_member: member.is_board_member,
    },
  });

  const onSubmit = async (data: AssociationMemberUpdateInput) => {
    try {
      setIsLoading(true);
      
      // Only send changed fields
      const changedFields: Partial<AssociationMemberUpdateInput> = {};
      let hasChanges = false;

      if (data.full_name !== member.full_name) {
        changedFields.full_name = data.full_name;
        hasChanges = true;
      }
      if (data.unit_number !== member.unit_number) {
        changedFields.unit_number = data.unit_number;
        hasChanges = true;
      }
      if (data.ownership_percentage !== (member.ownership_percentage?.toString() || "0")) {
        changedFields.ownership_percentage = data.ownership_percentage;
        hasChanges = true;
      }
      if (data.phone !== (member.phone || "")) {
        changedFields.phone = data.phone || null;
        hasChanges = true;
      }
      if (data.email !== (member.email || "")) {
        changedFields.email = data.email || null;
        hasChanges = true;
      }
      const memberJoinDate = member.join_date ? format(new Date(member.join_date), "yyyy-MM-dd") : "";
      if (data.join_date !== memberJoinDate) {
        changedFields.join_date = data.join_date;
        hasChanges = true;
      }
      if (data.is_board_member !== member.is_board_member) {
        changedFields.is_board_member = data.is_board_member;
        hasChanges = true;
      }

      if (!hasChanges) {
        toast.info(t("noChanges"));
        return;
      }
      
      const response = await apiClient.patch(
        `/api/owners-associations/${associationId}/members/${member.id}`,
        changedFields
      );

      if (response.success) {
        toast.success(t("editSuccess"));
        onSuccess();
      }
    } catch (error: any) {
      console.error("Error updating member:", error);
      
      if (error.response?.data?.code === "ALREADY_EXISTS") {
        toast.error(t("unitAlreadyExists"));
      } else if (error.response?.data?.code === "INVALID_OWNERSHIP") {
        toast.error(error.response.data.error);
      } else {
        toast.error(t("editError"));
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="full_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("fullName")}</FormLabel>
              <FormControl>
                <Input {...field} placeholder={t("fullNamePlaceholder")} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="unit_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("unitNumber")}</FormLabel>
                <FormControl>
                  <Input {...field} placeholder={t("unitNumberPlaceholder")} />
                </FormControl>
                <FormDescription>{t("unitNumberDescription")}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="ownership_percentage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("ownershipPercentage")}</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="0.00" />
                </FormControl>
                <FormDescription>{t("ownershipPercentageDescription")}</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("phone")}</FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    value={field.value || ""} 
                    placeholder={t("phonePlaceholder")} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("email")}</FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    value={field.value || ""} 
                    type="email" 
                    placeholder={t("emailPlaceholder")} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="join_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>{t("joinDate")}</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !field.value && "text-muted-foreground",
                        isRTL && "flex-row-reverse"
                      )}
                    >
                      <CalendarIcon className={cn("h-4 w-4", isRTL ? "ml-2" : "mr-2")} />
                      {field.value ? (
                        format(new Date(field.value), "PPP")
                      ) : (
                        <span>{t("selectDate")}</span>
                      )}
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value ? new Date(field.value) : undefined}
                    onSelect={(date) => {
                      field.onChange(date ? format(date, "yyyy-MM-dd") : "");
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="is_board_member"
          render={({ field }) => (
            <FormItem className={cn("flex flex-row items-start space-x-3 space-y-0", isRTL && "flex-row-reverse space-x-reverse")}>
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>{t("isBoardMember")}</FormLabel>
                <FormDescription>
                  {t("isBoardMemberDescription")}
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        <div className={cn("flex gap-3", isRTL && "flex-row-reverse")}>
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isLoading}
          >
            {t("cancel")}
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className={cn("h-4 w-4 animate-spin", isRTL ? "ml-2" : "mr-2")} />}
            {t("saveChanges")}
          </Button>
        </div>
      </form>
    </Form>
  );
}