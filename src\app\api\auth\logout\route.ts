import { NextRequest, NextResponse } from "next/server";

// POST /api/auth/logout - Logout user
export async function POST(request: NextRequest) {
  try {
    // Create response
    const response = NextResponse.json({
      message: "Logout successful",
    });
    
    // Clear the auth token cookie
    response.cookies.set("auth-token", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 0, // Expire immediately
      path: "/",
    });
    
    return response;
  } catch (error) {
    console.error("Logout error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
