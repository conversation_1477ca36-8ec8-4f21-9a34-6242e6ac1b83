"use client";

import * as React from "react";
import { useState, useEffect, useCallback } from "react";
import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { useDataTableInstance } from "@/hooks/use-data-table-instance";
import { DataTable } from "@/components/data-table/data-table";
import { DataTablePagination } from "@/components/data-table/data-table-pagination";
import { PropertiesViewOptions } from "./properties-view-options";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Loader2, RefreshCw, X, Building2 } from "lucide-react";
import { apiClient } from "@/lib/api-client";
import { ErrorState } from "@/components/error-state";
import type { PropertyWithRelations, PropertyFilters, PropertyTypeWithCount } from "@/types/property";

import { getPropertyColumnsWithPermissions } from "./property-columns-with-permissions";

interface PropertyListResponse {
  success: boolean;
  data: PropertyWithRelations[];
  meta: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

function formatCurrency(amount: number | string): string {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  return new Intl.NumberFormat('en-OM', {
    minimumFractionDigits: 3,
    maximumFractionDigits: 3,
  }).format(numAmount);
}

export function PropertiesDataTable() {
  const locale = useLocale();
  const router = useRouter();
  const t = useTranslations('properties');
  const tTable = useTranslations('properties.table');
  const tCommon = useTranslations('common');
  const tStatus = useTranslations('properties.status');

  const [data, setData] = useState<PropertyWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [retrying, setRetrying] = useState(false);
  const [propertyTypes, setPropertyTypes] = useState<PropertyTypeWithCount[]>([]);
  const [filters, setFilters] = useState<PropertyFilters>({
    page: 1,
    pageSize: 10,
    sortBy: "created_at",
    sortOrder: "desc",
  });
  const [searchInput, setSearchInput] = useState("");
  const [totalPages, setTotalPages] = useState(0);
  const [total, setTotal] = useState(0);

  const columnTranslations = {
    id: tTable('id'),
    name: tTable('name'),
    address: tTable('address'),
    propertyType: tTable('propertyType'),
    baseRent: tTable('baseRent'),
    status: tTable('status'),
    owner: tTable('owner'),
    totalArea: tTable('totalArea'),
    floorsCount: tTable('floorsCount'),
    parkingSpaces: tTable('parkingSpaces'),
    amenities: tTable('amenities'),
    amenity: tTable('amenity'),
    units: tTable('units'),
    createdAt: tTable('createdAt'),
    actions: tTable('actions'),
    selectAll: tTable('selectAll'),
    selectRow: tTable('selectRow'),
    openMenu: tTable('openMenu'),
    viewDetails: tTable('viewDetails'),
    editProperty: tTable('editProperty'),
    deleteProperty: tTable('deleteProperty'),
    statusAvailable: tStatus('available'),
    statusRented: tStatus('rented'),
    statusUnderMaintenance: tStatus('underMaintenance'),
    statusOutOfService: tStatus('outOfService'),
    // Column accessor keys for view options
    'name_en': tTable('name'),
    'name_ar': tTable('name'),
    'address_en': tTable('address'),
    'address_ar': tTable('address'),
    'base_rent': tTable('baseRent'),
    'total_area': tTable('totalArea'),
    'created_at': tTable('createdAt'),
  };


  // Fetch property types for filter
  const fetchPropertyTypes = async () => {
    try {
      const result = await apiClient.get("/api/property-types");
      if (result.success) {
        setPropertyTypes(result.data);
      }
    } catch (error) {
      console.error("Error fetching property types:", error);
    }
  };

  // Fetch properties
  const fetchProperties = useCallback(async (currentFilters: PropertyFilters, isRetry = false) => {
    try {
      if (isRetry) {
        setRetrying(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const params = new URLSearchParams();

      if (currentFilters.search) params.append("search", currentFilters.search);
      if (currentFilters.property_type_id) params.append("property_type_id", currentFilters.property_type_id.toString());
      if (currentFilters.status) params.append("status", currentFilters.status);
      if (currentFilters.min_rent) params.append("min_rent", currentFilters.min_rent.toString());
      if (currentFilters.max_rent) params.append("max_rent", currentFilters.max_rent.toString());
      if (currentFilters.page) params.append("page", currentFilters.page.toString());
      if (currentFilters.pageSize) params.append("pageSize", currentFilters.pageSize.toString());
      if (currentFilters.sortBy) params.append("sortBy", currentFilters.sortBy);
      if (currentFilters.sortOrder) params.append("sortOrder", currentFilters.sortOrder);

      const result: PropertyListResponse = await apiClient.get(`/api/properties?${params.toString()}`);
      
      if (result.success) {
        setData(result.data);
        setTotal(result.meta.total);
        setTotalPages(result.meta.totalPages);
        setError(null);
      } else {
        throw new Error(tTable('errorLoading'));
      }
    } catch (error) {
      console.error("Error fetching properties:", error);
      const errorObj = error instanceof Error ? error : new Error(tTable('unknownError'));
      setError(errorObj);

      if (!isRetry) {
        toast.error(errorObj.message);
      }
    } finally {
      setLoading(false);
      setRetrying(false);
    }
  }, [tTable]);

  // Create a refresh function that fetches data again
  const handleRefresh = useCallback(() => {
    fetchProperties(filters);
  }, [filters, fetchProperties]);

  const table = useDataTableInstance({
    data,
    columns: getPropertyColumnsWithPermissions(columnTranslations, locale, handleRefresh),
    enableRowSelection: true,
    defaultPageSize: filters.pageSize,
    getRowId: (row) => row.id.toString(),
  });

  // Update table pagination when filters change
  React.useEffect(() => {
    if (table && filters.page) {
      table.setPageIndex((filters.page || 1) - 1);
    }
  }, [filters.page, table]);

  React.useEffect(() => {
    if (table && filters.pageSize) {
      table.setPageSize(filters.pageSize);
    }
  }, [filters.pageSize, table]);

  // Initial data fetch
  useEffect(() => {
    fetchPropertyTypes();
    fetchProperties(filters);
  }, []);

  // Handle search
  const handleSearch = () => {
    const newFilters = { ...filters, search: searchInput, page: 1 };
    setFilters(newFilters);
    fetchProperties(newFilters);
  };

  // Handle clear search
  const handleClearSearch = () => {
    setSearchInput("");
    const newFilters = { ...filters, search: undefined, page: 1 };
    setFilters(newFilters);
    fetchProperties(newFilters);
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof PropertyFilters, value: any) => {
    const newFilters = { ...filters, [key]: value, page: 1 };
    setFilters(newFilters);
    fetchProperties(newFilters);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    fetchProperties(newFilters);
  };

  // Handle page size change
  const handlePageSizeChange = (pageSize: number) => {
    const newFilters = { ...filters, pageSize, page: 1 };
    setFilters(newFilters);
    fetchProperties(newFilters);
  };

  // Handle retry
  const handleRetry = () => {
    fetchProperties(filters, true);
  };

  // Show main error state if initial load failed
  if (error && !data.length && !loading) {
    return (
      <ErrorState
        error={error}
        onRetry={handleRetry}
        className="mx-auto max-w-md"
      />
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex flex-1 items-center space-x-2">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={tTable('searchPlaceholder')}
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                className="pl-8 pr-8"
                disabled={loading || retrying}
              />
              {searchInput && (
                <Button
                  onClick={handleClearSearch}
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1 h-7 w-7 p-0"
                  disabled={loading || retrying}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            <Button
              onClick={handleSearch}
              variant="outline"
              size="sm"
              disabled={loading || retrying}
            >
              {(loading || retrying) ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
            </Button>
            <Button
              onClick={handleRetry}
              variant="outline"
              size="icon"
              disabled={loading || retrying}
            >
              <RefreshCw className={`h-4 w-4 ${retrying ? 'animate-spin' : ''}`} />
            </Button>
          </div>
          
          <PropertiesViewOptions table={table} columnTranslations={columnTranslations} />
        </div>

        <div className="flex flex-wrap gap-2">
          <Select
            value={filters.property_type_id?.toString() || "all"}
            onValueChange={(value) => handleFilterChange("property_type_id", value === "all" ? undefined : parseInt(value))}
            disabled={loading || retrying}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder={tTable('filterByType')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{tTable('allTypes')}</SelectItem>
              {propertyTypes.map((type) => (
                <SelectItem key={type.id} value={type.id.toString()}>
                  {locale === "ar" ? type.name_ar : type.name_en}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={filters.status || "all"}
            onValueChange={(value) => handleFilterChange("status", value === "all" ? undefined : value)}
            disabled={loading || retrying}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder={tTable('filterByStatus')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{tTable('allStatuses')}</SelectItem>
              <SelectItem value="AVAILABLE">{tStatus('available')}</SelectItem>
              <SelectItem value="RENTED">{tStatus('rented')}</SelectItem>
              <SelectItem value="UNDER_MAINTENANCE">{tStatus('underMaintenance')}</SelectItem>
              <SelectItem value="OUT_OF_SERVICE">{tStatus('outOfService')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Data Table */}
      <div className="rounded-md border overflow-hidden">
        <div className="w-full overflow-x-auto [&_[data-slot=table-container]]:overflow-x-visible">
          {loading && !data.length ? (
            <div className="flex h-64 items-center justify-center">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="text-muted-foreground">{tTable('loading')}</span>
              </div>
            </div>
          ) : (
            <DataTable 
              table={table} 
            columns={getPropertyColumnsWithPermissions(columnTranslations, locale, handleRefresh)}
            renderSubComponent={({ row }) => {
              const property = row.original as PropertyWithRelations;
              const units = property.units || [];
              
              if (units.length === 0) return <div />;
              
              return (
                <div className="p-4">
                  <h4 className="text-sm font-semibold mb-2">Units ({units.length})</h4>
                  <div className="grid gap-2">
                    {units.map((unit) => {
                      const unitName = locale === "ar" 
                        ? unit.unit_name_ar || unit.unit_number
                        : unit.unit_name_en || unit.unit_number;
                      
                      const statusColor = 
                        unit.status === "AVAILABLE" ? "text-green-600" :
                        unit.status === "RENTED" ? "text-blue-600" :
                        "text-orange-600";
                      
                      return (
                        <div key={unit.id} className="flex items-center justify-between p-2 bg-muted/50 rounded-lg">
                          <div className="flex items-center gap-2">
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{unitName}</span>
                            {unit.floor_number !== null && (
                              <span className="text-sm text-muted-foreground">
                                (Floor {unit.floor_number})
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-4">
                            {unit.rooms_count !== null && (
                              <span className="text-sm text-muted-foreground">
                                {unit.rooms_count} rooms
                              </span>
                            )}
                            {unit.bathrooms_count !== null && (
                              <span className="text-sm text-muted-foreground">
                                {unit.bathrooms_count} baths
                              </span>
                            )}
                            <span className="font-medium">
                              {formatCurrency(unit.rent_amount)} OMR
                            </span>
                            <span className={`text-sm font-medium ${statusColor}`}>
                              {unit.status}
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            }}
              />
        )}
        </div>
      </div>

      {/* Pagination */}
      <DataTablePagination table={table} />
    </div>
  );
}