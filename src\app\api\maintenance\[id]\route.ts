import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { maintenanceUpdateSchema, canEditRequest } from "@/types/maintenance";
import { ApiResponseBuilder } from "@/lib/api-response";




import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
// GET /api/maintenance/[id] - Get a single maintenance request
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for maintenance
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "maintenance", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view maintenance");
    }

    const { id: idParam } = await params;


    const id = parseInt(idParam);
    
    const maintenanceRequest = await db.maintenanceRequest.findUnique({
      where: { id },
      include: {
        property: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
          },
        },
        assignee: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    if (!maintenanceRequest) {
      return ApiResponseBuilder.notFound("Maintenance request not found");
    }

    return ApiResponseBuilder.success(maintenanceRequest);
  } catch (error: any) {
    console.error("Error fetching maintenance request:", error);
    return ApiResponseBuilder.error("Failed to fetch maintenance request", "INTERNAL_ERROR", 500);
  }
}

// PATCH /api/maintenance/[id] - Update a maintenance request
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;

    const id = parseInt(idParam);
    const body = await request.json();
    
    // Validate the input data
    const validatedData = maintenanceUpdateSchema.parse(body);
    
    // Get current request to check authorization
    const currentRequest = await db.maintenanceRequest.findUnique({
      where: { id },
      include: {
        property: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
          },
        },
      },
    });

    if (!currentRequest) {
      return ApiResponseBuilder.notFound("Maintenance request not found");
    }

    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);
    
    // Check if user has UPDATE permission for maintenance
    const canUpdate = hasPermission(userPermissions, "maintenance", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update maintenance");
    }

    // Prepare update data
    const updateData: any = { ...validatedData };

    // Handle status change
    let statusChanged = false;
    if (validatedData.status && validatedData.status !== currentRequest.status) {
      statusChanged = true;
      
      // Update status-specific fields
      if (validatedData.status === "ACKNOWLEDGED" && !currentRequest.assigned_date) {
        updateData.assigned_date = new Date();
      }
      
      if (validatedData.status === "COMPLETED") {
        updateData.completed_date = new Date();
      }
    }

    // Handle assignment change
    if (validatedData.assigned_to !== undefined && validatedData.assigned_to !== currentRequest.assigned_to) {
      if (validatedData.assigned_to && !currentRequest.assigned_date) {
        updateData.assigned_date = new Date();
      }
    }

    const updatedRequest = await db.maintenanceRequest.update({
      where: { id },
      data: updateData,
      include: {
        property: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            address_en: true,
            address_ar: true,
          },
        },
        unit: {
          select: {
            id: true,
            unit_number: true,
            floor_number: true,
          },
        },
        tenant: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            phone: true,
          },
        },
        assignee: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
      },
    });

    // Create status history entry if status changed
    if (statusChanged) {
      await db.maintenanceStatusHistory.create({
        data: {
          request_id: id,
          from_status: currentRequest.status,
          to_status: validatedData.status!,
          notes: `Status changed from ${currentRequest.status} to ${validatedData.status}`,
          changed_by: undefined, // TODO: Get from auth
        },
      });
    }

    return ApiResponseBuilder.success(updatedRequest);
  } catch (error: any) {
    console.error("Error updating maintenance request:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to update maintenance request", "INTERNAL_ERROR", 500);
  }
}

// DELETE /api/maintenance/[id] - Cancel a maintenance request
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has DELETE permission for maintenance
    const canDelete = hasPermission(userPermissions, "maintenance", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete maintenance");
    }

    const { id: idParam } = await params;


    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.error("Invalid maintenance request ID", "BAD_REQUEST", 400);
    }

    const maintenanceRequest = await db.maintenanceRequest.findUnique({
      where: { id },
    });

    if (!maintenanceRequest) {
      return ApiResponseBuilder.error("Maintenance request not found", "NOT_FOUND", 404);
    }

    if (maintenanceRequest.status === "COMPLETED") {
      return ApiResponseBuilder.error("Cannot cancel a completed maintenance request", "BAD_REQUEST", 400);
    }

    if (maintenanceRequest.status === "CANCELLED") {
      return ApiResponseBuilder.error("Maintenance request is already cancelled", "BAD_REQUEST", 400);
    }

    const cancelledRequest = await db.maintenanceRequest.update({
      where: { id },
      data: {
        status: "CANCELLED",
        updated_by: undefined, // TODO: Get from auth
      },
    });

    // Create status history entry
    await db.maintenanceStatusHistory.create({
      data: {
        request_id: id,
        from_status: maintenanceRequest.status,
        to_status: "CANCELLED",
        notes: "Maintenance request cancelled",
        changed_by: undefined, // TODO: Get from auth
      },
    });

    return ApiResponseBuilder.success(cancelledRequest);
  } catch (error) {
    console.error("Error cancelling maintenance request:", error);
    return ApiResponseBuilder.error("Failed to cancel maintenance request", "INTERNAL_ERROR", 500);
  }
}