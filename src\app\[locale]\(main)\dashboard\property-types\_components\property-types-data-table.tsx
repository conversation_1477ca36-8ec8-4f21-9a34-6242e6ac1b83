"use client";

import * as React from "react";
import { useState, useEffect, useCallback } from "react";
import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { apiClient } from "@/lib/api-client";

import { useDataTableInstance } from "@/hooks/use-data-table-instance";
import { DataTable } from "@/components/data-table/data-table";
import { DataTablePagination } from "@/components/data-table/data-table-pagination";
import { PropertyTypesViewOptions } from "./property-types-view-options";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Loader2, RefreshCw, X } from "lucide-react";
import { ErrorState } from "@/components/error-state";
import type { PropertyTypeWithCount, PropertyTypeFilters } from "@/types/property";

import { getPropertyTypeColumns } from "./property-type-columns";

interface PropertyTypeListResponse {
  success: boolean;
  data: PropertyTypeWithCount[];
  meta: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

export function PropertyTypesDataTable() {
  const locale = useLocale();
  const router = useRouter();
  const t = useTranslations('properties.propertyTypes');
  const tTable = useTranslations('properties.propertyTypes.table');
  const tCommon = useTranslations('common');

  const [data, setData] = useState<PropertyTypeWithCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [retrying, setRetrying] = useState(false);
  const [filters, setFilters] = useState<PropertyTypeFilters>({
    page: 1,
    pageSize: 10,
    sortBy: "name_en",
    sortOrder: "asc",
  });
  const [searchInput, setSearchInput] = useState("");
  const [totalPages, setTotalPages] = useState(0);
  const [total, setTotal] = useState(0);

  // Fetch property types
  const fetchPropertyTypes = useCallback(async (currentFilters: PropertyTypeFilters, isRetry = false) => {
    try {
      if (isRetry) {
        setRetrying(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const params = new URLSearchParams();

      if (currentFilters.search) params.append("search", currentFilters.search);
      if (currentFilters.page) params.append("page", currentFilters.page.toString());
      if (currentFilters.pageSize) params.append("pageSize", currentFilters.pageSize.toString());
      if (currentFilters.sortBy) params.append("sortBy", currentFilters.sortBy);
      if (currentFilters.sortOrder) params.append("sortOrder", currentFilters.sortOrder);

      const result: PropertyTypeListResponse = await apiClient.get(`/api/property-types?${params.toString()}`);
      
      console.log("Property Types Data Table: API Response", result);
      
      if (result.success) {
        setData(result.data);
        setTotal(result.meta.total);
        setTotalPages(result.meta.totalPages);
        setError(null);
      } else {
        throw new Error(tTable('errorLoading'));
      }
    } catch (error) {
      console.error("Error fetching property types:", error);
      const errorObj = error instanceof Error ? error : new Error(tTable('unknownError'));
      setError(errorObj);

      if (!isRetry) {
        toast.error(errorObj.message);
      }
    } finally {
      setLoading(false);
      setRetrying(false);
    }
  }, [tTable]);

  // Create a refresh function that fetches data again
  const handleRefresh = useCallback(() => {
    fetchPropertyTypes(filters);
  }, [filters, fetchPropertyTypes]);

  const columnTranslations = {
    id: tTable('id'),
    nameEn: tTable('nameEn'),
    nameAr: tTable('nameAr'),
    descriptionEn: tTable('descriptionEn'),
    descriptionAr: tTable('descriptionAr'),
    propertiesCount: tTable('propertiesCount'),
    createdAt: tTable('createdAt'),
    actions: tTable('actions'),
    selectAll: tTable('selectAll'),
    selectRow: tTable('selectRow'),
    openMenu: tTable('openMenu'),
    viewDetails: tTable('viewDetails'),
    editPropertyType: tTable('editPropertyType'),
    deletePropertyType: tTable('deletePropertyType'),
    // Column accessor keys for view options
    'name_en': tTable('nameEn'),
    'name_ar': tTable('nameAr'),
    'description_en': tTable('descriptionEn'),
    'description_ar': tTable('descriptionAr'),
    '_count.properties': tTable('propertiesCount'),
    'created_at': tTable('createdAt'),
  };

  const table = useDataTableInstance({
    data,
    columns: getPropertyTypeColumns(columnTranslations, locale, handleRefresh),
    enableRowSelection: true,
    defaultPageSize: filters.pageSize,
    getRowId: (row) => row.id.toString(),
  });

  // Update table pagination when filters change
  React.useEffect(() => {
    if (table && filters.page) {
      table.setPageIndex((filters.page || 1) - 1);
    }
  }, [filters.page, table]);

  React.useEffect(() => {
    if (table && filters.pageSize) {
      table.setPageSize(filters.pageSize);
    }
  }, [filters.pageSize, table]);

  // Initial data fetch
  useEffect(() => {
    fetchPropertyTypes(filters);
  }, []);

  // Handle search
  const handleSearch = () => {
    const newFilters = { ...filters, search: searchInput, page: 1 };
    setFilters(newFilters);
    fetchPropertyTypes(newFilters);
  };

  // Handle clear search
  const handleClearSearch = () => {
    setSearchInput("");
    const newFilters = { ...filters, search: undefined, page: 1 };
    setFilters(newFilters);
    fetchPropertyTypes(newFilters);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    fetchPropertyTypes(newFilters);
  };

  // Handle page size change
  const handlePageSizeChange = (pageSize: number) => {
    const newFilters = { ...filters, pageSize, page: 1 };
    setFilters(newFilters);
    fetchPropertyTypes(newFilters);
  };

  // Handle retry
  const handleRetry = () => {
    fetchPropertyTypes(filters, true);
  };

  // Show main error state if initial load failed
  if (error && !data.length && !loading) {
    return (
      <ErrorState
        error={error}
        onRetry={handleRetry}
        className="mx-auto max-w-md"
      />
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={tTable('searchPlaceholder')}
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              className="pl-8 pr-8"
              disabled={loading || retrying}
            />
            {searchInput && (
              <Button
                onClick={handleClearSearch}
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1 h-7 w-7 p-0"
                disabled={loading || retrying}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <Button
            onClick={handleSearch}
            variant="outline"
            size="sm"
            disabled={loading || retrying}
          >
            {(loading || retrying) ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Search className="h-4 w-4" />
            )}
          </Button>
          <Button
            onClick={handleRetry}
            variant="outline"
            size="icon"
            disabled={loading || retrying}
            title={tCommon('retry')}
          >
            <RefreshCw className={`h-4 w-4 ${retrying ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        
        <PropertyTypesViewOptions table={table} columnTranslations={columnTranslations} />
      </div>

      {/* Data Table */}
      <div className="rounded-md border overflow-hidden">
        <div className="w-full overflow-x-auto [&_[data-slot=table-container]]:overflow-x-visible">
          {loading && !data.length ? (
            <div className="flex h-64 items-center justify-center">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="text-muted-foreground">{tTable('loading')}</span>
              </div>
            </div>
          ) : (
            <DataTable table={table} columns={getPropertyTypeColumns(columnTranslations, locale, router.refresh)} />
          )}
        </div>
      </div>

      {/* Pagination */}
      <DataTablePagination table={table} />
    </div>
  );
}