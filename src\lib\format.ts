/**
 * Format a number as Omani Rial (OMR) currency
 * OMR uses 3 decimal places
 */
export function formatOMR(amount: string | number | null | undefined): string {
  if (amount === null || amount === undefined) {
    return "OMR 0.000";
  }

  const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
  
  if (isNaN(numAmount)) {
    return "OMR 0.000";
  }

  // Format with 3 decimal places and thousands separator
  const formatted = new Intl.NumberFormat("en-US", {
    minimumFractionDigits: 3,
    maximumFractionDigits: 3,
  }).format(numAmount);

  return `OMR ${formatted}`;
}

/**
 * Format a number as a percentage
 */
export function formatPercentage(value: number, decimals: number = 0): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * Format a date in DD/MM/YYYY format
 */
export function formatDate(date: Date | string): string {
  const d = typeof date === "string" ? new Date(date) : date;
  
  const day = d.getDate().toString().padStart(2, "0");
  const month = (d.getMonth() + 1).toString().padStart(2, "0");
  const year = d.getFullYear();
  
  return `${day}/${month}/${year}`;
}

/**
 * Format a date in DD/MM/YYYY HH:mm format
 */
export function formatDateTime(date: Date | string): string {
  const d = typeof date === "string" ? new Date(date) : date;
  
  const day = d.getDate().toString().padStart(2, "0");
  const month = (d.getMonth() + 1).toString().padStart(2, "0");
  const year = d.getFullYear();
  const hours = d.getHours().toString().padStart(2, "0");
  const minutes = d.getMinutes().toString().padStart(2, "0");
  
  return `${day}/${month}/${year} ${hours}:${minutes}`;
}

/**
 * Format a number with thousands separator
 */
export function formatNumber(value: number, decimals: number = 0): string {
  return new Intl.NumberFormat("en-US", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
}

/**
 * Parse OMR string to number
 */
export function parseOMR(value: string): number {
  // Remove "OMR" prefix and any spaces
  const cleanValue = value.replace(/OMR\s*/i, "").trim();
  // Remove thousands separator
  const numValue = cleanValue.replace(/,/g, "");
  return parseFloat(numValue) || 0;
}