import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ownerPayoutApprovalSchema, canApprovePayout } from "@/types/owner-payout";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

// POST /api/owner-payouts/[id]/approve - Approve or reject an owner payout
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for owner-payouts
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "owner-payouts", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create owner payouts");
    }

    const { id: idParam } = await params;


    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.error("Invalid payout ID", "BAD_REQUEST", 400);
    }

    const body = await request.json();
    const validatedData = ownerPayoutApprovalSchema.parse(body);

    const payout = await db.ownerPayout.findUnique({
      where: { id },
      include: {
        owner: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            email: true,
          },
        },
      },
    });

    if (!payout) {
      return ApiResponseBuilder.error("Owner payout not found", "NOT_FOUND", 404);
    }

    if (!canApprovePayout(payout.status)) {
      return ApiResponseBuilder.error(
        `Cannot ${validatedData.action.toLowerCase()} payout with status ${payout.status}`,
        "BAD_REQUEST",
        400
      );
    }

    const newStatus = validatedData.action === "APPROVE" ? "APPROVED" : "CANCELLED";
    const updateData: any = {
      status: newStatus,
    };

    if (validatedData.action === "APPROVE") {
      updateData.approved_by = undefined; // TODO: Get from auth
      updateData.approved_at = new Date();
    }

    // Add notes if provided
    if (validatedData.notes) {
      updateData.notes = payout.notes 
        ? `${payout.notes}\n\n${validatedData.action} Notes: ${validatedData.notes}`
        : `${validatedData.action} Notes: ${validatedData.notes}`;
    }

    const updatedPayout = await db.ownerPayout.update({
      where: { id },
      data: updateData,
      include: {
        owner: {
          select: {
            id: true,
            name_en: true,
            name_ar: true,
            email: true,
          },
        },
        approver: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
          },
        },
        payout_details: {
          include: {
            property: {
              select: {
                id: true,
                name_en: true,
                name_ar: true,
              },
            },
          },
        },
      },
    });

    // Transform payout to handle Decimal serialization
    const transformedPayout = {
      ...updatedPayout,
      total_rent_collected: updatedPayout.total_rent_collected.toString(),
      management_fee: updatedPayout.management_fee.toString(),
      other_deductions: updatedPayout.other_deductions.toString(),
      net_amount: updatedPayout.net_amount.toString(),
      payout_details: updatedPayout.payout_details.map(detail => ({
        ...detail,
        rent_collected: detail.rent_collected.toString(),
        management_fee: detail.management_fee.toString(),
        net_amount: detail.net_amount.toString(),
      })),
    };

    return ApiResponseBuilder.success(transformedPayout);
  } catch (error: any) {
    console.error("Error processing payout approval:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error);
    }

    return ApiResponseBuilder.error("Failed to process payout approval", "INTERNAL_ERROR", 500);
  }
}