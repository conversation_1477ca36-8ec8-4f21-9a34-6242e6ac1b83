import { NextRequest, NextResponse } from "next/server";
import { verifyToken } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";
import { db } from "@/lib/db";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; memberId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: "Invalid or expired token" },
        { status: 401 }
      );
    }

    // Check if user has READ permission for owners-associations
    const canRead = await hasPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return NextResponse.json(
        { success: false, error: "You don't have permission to view member transactions" },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const associationId = parseInt(resolvedParams.id);
    const memberId = parseInt(resolvedParams.memberId);

    if (isNaN(associationId) || isNaN(memberId)) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters" },
        { status: 400 }
      );
    }

    // Get member's transactions
    const transactions = await db.associationTransaction.findMany({
      where: {
        association_id: associationId,
        member_id: memberId
      },
      include: {
        member: true
      },
      orderBy: {
        transaction_date: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        transactions
      }
    });
  } catch (error) {
    console.error("Error fetching member transactions:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch member transactions" },
      { status: 500 }
    );
  }
}