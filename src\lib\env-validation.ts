import { z } from "zod";

// Define the environment variables schema
const envSchema = z.object({
  // Database
  DATABASE_URL: z.string().url().startsWith("mysql://", {
    message: "DATABASE_URL must be a valid MySQL connection string",
  }),
  
  // Authentication
  JWT_SECRET: z.string().min(32, {
    message: "JWT_SECRET must be at least 32 characters long for security",
  }),
  JWT_EXPIRES_IN: z.string().regex(/^\d+[dwmy]$/, {
    message: "JWT_EXPIRES_IN must be in format like '7d', '30d', '1w', '1m', '1y'",
  }).optional().default("7d"),
  
  // Node Environment
  NODE_ENV: z.enum(["development", "production", "test"]).optional().default("development"),
  
  // Next.js Public Variables (optional)
  NEXT_PUBLIC_APP_NAME: z.string().optional().default("Property Management System"),
  NEXT_PUBLIC_APP_URL: z.string().url().optional(),
  
  // File Upload (optional)
  MAX_FILE_SIZE: z.string().regex(/^\d+$/).optional().default("5242880"), // 5MB default
  ALLOWED_FILE_TYPES: z.string().optional().default("image/jpeg,image/png,image/gif,application/pdf"),
  
  // Email Configuration (optional for future use)
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().regex(/^\d+$/).optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  SMTP_FROM: z.string().email().optional(),
  
  // SMS Configuration (optional for future use)
  SMS_API_KEY: z.string().optional(),
  SMS_API_URL: z.string().url().optional(),
  SMS_SENDER_ID: z.string().optional(),
  
  // Redis Configuration (optional for future caching)
  REDIS_URL: z.string().url().optional(),
  
  // Rate Limiting (optional)
  RATE_LIMIT_WINDOW: z.string().regex(/^\d+$/).optional().default("900000"), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().regex(/^\d+$/).optional().default("100"),
});

// Type for the validated environment variables
export type Env = z.infer<typeof envSchema>;

// Validate environment variables
let env: Env;

try {
  env = envSchema.parse(process.env);
} catch (error) {
  if (error instanceof z.ZodError) {
    const missingVars = error.errors
      .filter(err => err.message.includes("Required"))
      .map(err => err.path.join("."));
    
    const invalidVars = error.errors
      .filter(err => !err.message.includes("Required"))
      .map(err => `${err.path.join(".")}: ${err.message}`);
    
    console.error("❌ Environment validation failed:");
    
    if (missingVars.length > 0) {
      console.error("\nMissing required environment variables:");
      missingVars.forEach(varName => console.error(`  - ${varName}`));
    }
    
    if (invalidVars.length > 0) {
      console.error("\nInvalid environment variables:");
      invalidVars.forEach(varError => console.error(`  - ${varError}`));
    }
    
    console.error("\nPlease check your .env file and ensure all required variables are set correctly.");
    
    // Exit the process in production, but allow development to continue with warnings
    if (process.env.NODE_ENV === "production") {
      process.exit(1);
    }
  }
  throw error;
}

// Export validated environment variables
export { env };

// Helper function to check if a feature is enabled based on environment variables
export function isFeatureEnabled(feature: string): boolean {
  switch (feature) {
    case "email":
      return !!(env.SMTP_HOST && env.SMTP_PORT && env.SMTP_USER && env.SMTP_PASS);
    case "sms":
      return !!(env.SMS_API_KEY && env.SMS_API_URL);
    case "redis":
      return !!env.REDIS_URL;
    case "rateLimiting":
      return true; // Always enabled with default values
    default:
      return false;
  }
}

// Helper function to get configuration for features
export function getFeatureConfig(feature: string) {
  switch (feature) {
    case "email":
      return {
        host: env.SMTP_HOST,
        port: env.SMTP_PORT ? parseInt(env.SMTP_PORT) : undefined,
        user: env.SMTP_USER,
        pass: env.SMTP_PASS,
        from: env.SMTP_FROM,
      };
    case "sms":
      return {
        apiKey: env.SMS_API_KEY,
        apiUrl: env.SMS_API_URL,
        senderId: env.SMS_SENDER_ID,
      };
    case "redis":
      return {
        url: env.REDIS_URL,
      };
    case "rateLimiting":
      return {
        windowMs: parseInt(env.RATE_LIMIT_WINDOW),
        maxRequests: parseInt(env.RATE_LIMIT_MAX_REQUESTS),
      };
    case "fileUpload":
      return {
        maxSize: parseInt(env.MAX_FILE_SIZE),
        allowedTypes: env.ALLOWED_FILE_TYPES.split(","),
      };
    default:
      return null;
  }
}