{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(npx prisma migrate dev:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx prisma migrate:*)", "Bash(npx prisma generate:*)", "Bash(npm run:*)", "Bash(ls:*)", "Bash(node:*)", "Bash(npx prisma db push:*)", "<PERSON><PERSON>(mysql:*)", "Bash(rm:*)", "<PERSON><PERSON>(taskkill:*)", "<PERSON><PERSON>(curl:*)", "Bash(find:*)", "Bash(npm install:*)", "Bash(npx prisma:*)", "Bash(grep:*)", "Bash(del \"C:\\xampp\\htdocs\\claude_property\\prisma\\migrations\\20240115_create_units\\migration.sql\")", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(php:*)", "Bash(npx tsx:*)", "<PERSON><PERSON>(powershell:*)", "Bash(^C)", "<PERSON><PERSON>(tail:*)", "<PERSON><PERSON>(jq:*)", "Bash(copy messagesar.json messagesar.json.backup)", "Bash(copy messagesen.json messagesen.json.backup)", "Bash(cp:*)", "Bash(del fix-translations.js)", "WebFetch(domain:docs.anthropic.com)", "Bash(git:*)", "Bash(git init:*)", "<PERSON><PERSON>(chmod:*)", "Bash(bash:*)", "Bash(git config:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(echo $NEXT_PUBLIC_APP_URL)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(move:*)", "Bash(move srcmiddleware.disabled.ts srcmiddleware.ts)", "Bash(del \"C:\\xampp\\htdocs\\property_claude\\src\\generated\\prisma\\query_engine-windows.dll.node\")", "Bash(del fix-auth-imports.js)", "<PERSON><PERSON>(python:*)", "Bash(npx next:*)", "Bash(del fix-nullable-inputs.js)", "Bash(del fix-api-response-errors.js)", "Bash(npx tsc:*)", "<PERSON><PERSON>(tasklist)", "Bash(npx ts-node:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(dir:*)", "Bash(del /F /Q \"C:\\xampp\\htdocs\\property_claude\\prisma\\migrations\\20250116_enhance_subscription_payments\\migration.sql\")", "<PERSON><PERSON>(cat:*)", "Bash(npm cache clean:*)", "<PERSON><PERSON>(killall:*)", "Bash(npx jsonlint:*)", "Bash(del /F /Q \"C:\\xampp\\htdocs\\property_claude\\src\\generated\\prisma\\query_engine-windows.dll.node\")", "Bash(del test-translations.js)", "Bash(set NODE_OPTIONS=--max-old-space-size=4096)", "Bash(set NEXT_PUBLIC_SKIP_ESLINT=true)"], "deny": []}, "hooks": {"PostToolUse": [{"matcher": "Write|Edit|MultiEdit", "hooks": [{"type": "command", "command": "bash .claude/hooks/auto-commit.sh"}]}]}, "disableAllHooks": true}