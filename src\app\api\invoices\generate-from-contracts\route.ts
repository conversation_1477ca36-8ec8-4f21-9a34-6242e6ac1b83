import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ApiResponseBuilder } from "@/lib/api-response";
import { generateInvoiceNumber } from "@/types/invoice";
import { z } from "zod";


const generateSchema = z.object({
  month: z.number().min(1).max(12),
  year: z.number().min(2020).max(2100),
  contract_ids: z.array(z.number()).optional(), // If not provided, generate for all active contracts
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for invoices
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "invoices", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create invoices");
    }

    // User is already authenticated via token above

    const body = await request.json();
    const validatedData = generateSchema.parse(body);

    // Get the invoice date and due date
    const invoiceDate = new Date(validatedData.year, validatedData.month - 1, 1);
    const dueDate = new Date(validatedData.year, validatedData.month - 1, 10); // Default to 10th of the month

    // Build where clause for contracts
    const contractWhere: any = {
      status: 'ACTIVE',
      start_date: { lte: invoiceDate },
      end_date: { gte: invoiceDate },
    };

    if (validatedData.contract_ids && validatedData.contract_ids.length > 0) {
      contractWhere.id = { in: validatedData.contract_ids };
    }

    // Get active contracts
    const contracts = await prisma.contract.findMany({
      where: contractWhere,
      include: {
        tenants: {
          where: { is_primary: true },
          include: { tenant: true },
        },
        property: true,
        unit: true,
      },
    });

    if (contracts.length === 0) {
      return ApiResponseBuilder.error("No active contracts found for the specified period", "NOT_FOUND", 404);
    }

    const generatedInvoices = [];
    const errors = [];

    for (const contract of contracts) {
      try {
        // Check if invoice already exists for this contract and period
        const existingInvoice = await prisma.invoice.findFirst({
          where: {
            contract_id: contract.id,
            invoice_date: {
              gte: new Date(validatedData.year, validatedData.month - 1, 1),
              lt: new Date(validatedData.year, validatedData.month, 1),
            },
            status: { not: 'CANCELLED' },
          },
        });

        if (existingInvoice) {
          errors.push({
            contract_id: contract.id,
            contract_number: contract.contract_number,
            error: "Invoice already exists for this period",
          });
          continue;
        }

        // Get primary tenant
        const primaryTenant = contract.tenants.find(ct => ct.is_primary)?.tenant;
        if (!primaryTenant) {
          errors.push({
            contract_id: contract.id,
            contract_number: contract.contract_number,
            error: "No primary tenant found",
          });
          continue;
        }

        // Generate invoice number
        const latestInvoice = await prisma.invoice.findFirst({
          where: {
            invoice_number: {
              startsWith: `INV-${validatedData.year}-${validatedData.month.toString().padStart(2, '0')}`,
            },
          },
          orderBy: {
            invoice_number: 'desc',
          },
        });

        let sequence = 1;
        if (latestInvoice) {
          const parts = latestInvoice.invoice_number.split('-');
          sequence = parseInt(parts[3]) + 1;
        }

        const invoiceNumber = generateInvoiceNumber(validatedData.year, validatedData.month, sequence);

        // Set due date based on contract payment_due_day
        const actualDueDate = new Date(validatedData.year, validatedData.month - 1, contract.payment_due_day);

        // Skip if contract doesn't have property_id or unit_id (required fields)
        if (!contract.property_id || !contract.unit_id) {
          errors.push({
            contract_id: contract.id,
            contract_number: contract.contract_number,
            error: "Contract is missing property or unit information",
          });
          continue;
        }

        // Create invoice
        const invoice = await prisma.invoice.create({
          data: {
            invoice_number: invoiceNumber,
            contract_id: contract.id,
            tenant_id: primaryTenant.id,
            property_id: contract.property_id,
            unit_id: contract.unit_id,
            invoice_date: invoiceDate,
            due_date: actualDueDate,
            original_amount: contract.monthly_rent,
            late_fee: 0,
            total_amount: contract.monthly_rent,
            paid_amount: 0,
            balance_amount: contract.monthly_rent,
            status: 'PENDING',
            notes: `Monthly rent for ${validatedData.month}/${validatedData.year}`,
            created_by: decoded.id,
            updated_by: decoded.id,
            items: {
              create: {
                description_en: `Monthly Rent - ${contract.unit?.unit_number || 'Unit'}`,
                description_ar: `الإيجار الشهري - ${contract.unit?.unit_number || 'الوحدة'}`,
                quantity: 1,
                unit_price: contract.monthly_rent,
                amount: contract.monthly_rent,
              },
            },
          },
          include: {
            contract: true,
            tenant: true,
            property: true,
            unit: true,
            items: true,
          },
        });

        generatedInvoices.push(invoice);
      } catch (error) {
        console.error(`Error generating invoice for contract ${contract.id}:`, error);
        errors.push({
          contract_id: contract.id,
          contract_number: contract.contract_number,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return ApiResponseBuilder.success({
      message: `Generated ${generatedInvoices.length} invoices`,
      generated: generatedInvoices.length,
      errors: errors.length,
      invoices: generatedInvoices,
      errorDetails: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    console.error("Error generating invoices:", error);
    return ApiResponseBuilder.error("Failed to generate invoices", "INTERNAL_ERROR", 500);
  }
}