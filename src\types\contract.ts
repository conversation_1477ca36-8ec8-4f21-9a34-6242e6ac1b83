import { z } from "zod";
import type { 
  Contract, 
  ContractStatus, 
  ContractTenant, 
  ContractDocument,
  ContractDocumentType,
  Property,
  Unit,
  Tenant,
  User
} from "@/generated/prisma";

// Contract Schema
export const contractSchema = z.object({
  contract_number: z.string().min(1, "Contract number is required"),
  property_id: z.number().optional().nullable(),
  unit_id: z.number().optional().nullable(),
  start_date: z.string().min(1, "Start date is required"),
  end_date: z.string().min(1, "End date is required"),
  monthly_rent: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format"),
  payment_due_day: z.number().min(1).max(31, "Payment due day must be between 1 and 31"),
  security_deposit: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format"),
  insurance_amount: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format").optional().nullable(),
  insurance_due_date: z.string().optional().nullable(),
  terms_and_conditions: z.string().optional().nullable(),
  status: z.nativeEnum({
    DRAFT: "DRAFT",
    ACTIVE: "ACTIVE",
    EXPIRED: "EXPIRED",
    TERMINATED: "TERMINATED",
    RENEWED: "RENEWED"
  } as const),
  auto_renew: z.boolean().default(false),
  renewal_notice_days: z.number().min(0).default(30),
  notes: z.string().optional().nullable(),
  tenant_ids: z.array(z.number()).min(1, "At least one tenant is required"),
}).refine((data) => {
  // Must have either property_id or unit_id
  return data.property_id || data.unit_id;
}, {
  message: "Either property or unit must be selected",
  path: ["property_id"]
}).refine((data) => {
  // End date must be after start date
  return new Date(data.end_date) > new Date(data.start_date);
}, {
  message: "End date must be after start date",
  path: ["end_date"]
});

// Contract Tenant Schema
export const contractTenantSchema = z.object({
  tenant_id: z.number().min(1, "Tenant is required"),
  is_primary: z.boolean().default(false),
});

// Contract Document Schema
export const contractDocumentSchema = z.object({
  document_type: z.nativeEnum({
    CONTRACT: "CONTRACT",
    ADDENDUM: "ADDENDUM",
    TERMINATION_LETTER: "TERMINATION_LETTER",
    RENEWAL_AGREEMENT: "RENEWAL_AGREEMENT",
    PAYMENT_RECEIPT: "PAYMENT_RECEIPT",
    OTHER: "OTHER"
  } as const),
  document_name: z.string().min(1, "Document name is required"),
  file: z.any().optional(), // For file uploads
});

// Contract Filter Schema
export const contractFilterSchema = z.object({
  property_id: z.number().optional(),
  unit_id: z.number().optional(),
  tenant_id: z.number().optional(),
  status: z.nativeEnum({
    DRAFT: "DRAFT",
    ACTIVE: "ACTIVE",
    EXPIRED: "EXPIRED",
    TERMINATED: "TERMINATED",
    RENEWED: "RENEWED"
  } as const).optional(),
  start_date_from: z.string().optional(),
  start_date_to: z.string().optional(),
  end_date_from: z.string().optional(),
  end_date_to: z.string().optional(),
  search: z.string().optional(),
});

// Types for API responses
export type ContractInput = z.infer<typeof contractSchema>;
export type ContractTenantInput = z.infer<typeof contractTenantSchema>;
export type ContractDocumentInput = z.infer<typeof contractDocumentSchema>;
export type ContractFilter = z.infer<typeof contractFilterSchema>;

// Extended types with relations
export interface ContractWithRelations extends Contract {
  property?: Property | null;
  unit?: Unit | null;
  tenants: (ContractTenant & {
    tenant: Tenant;
  })[];
  documents: ContractDocument[];
  creator?: User | null;
  updater?: User | null;
  _count?: {
    invoices: number;
    documents: number;
    renewals: number;
  };
}

export interface ContractTenantWithTenant extends ContractTenant {
  tenant: Tenant;
}

export interface ContractDocumentWithUploader extends ContractDocument {
  uploader?: User | null;
}

// Helper function to format contract number
export function generateContractNumber(propertyId: number, unitNumber: string, year: number): string {
  const paddedPropertyId = propertyId.toString().padStart(3, '0');
  const cleanUnitNumber = unitNumber.replace(/[^A-Za-z0-9]/g, '');
  return `CNT-${year}-${paddedPropertyId}-${cleanUnitNumber}`;
}

// Helper function to check if contract is active
export function isContractActive(contract: Contract): boolean {
  const now = new Date();
  const startDate = new Date(contract.start_date);
  const endDate = new Date(contract.end_date);
  
  return contract.status === 'ACTIVE' && 
         startDate <= now && 
         endDate >= now;
}

// Helper function to check if contract is expiring soon (within 30 days)
export function isContractExpiringSoon(contract: Contract): boolean {
  const now = new Date();
  const endDate = new Date(contract.end_date);
  const daysUntilExpiry = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  
  return contract.status === 'ACTIVE' && daysUntilExpiry > 0 && daysUntilExpiry <= 30;
}

// formatCurrency function moved to @/lib/localization.ts for consistency
// Use: import { formatCurrency } from "@/lib/utils";

// Contract renewal schema
export const contractRenewalSchema = z.object({
  new_end_date: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "Invalid end date",
  }),
  new_monthly_rent: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format"),
  new_security_deposit: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format").optional(),
  new_insurance_amount: z.string().regex(/^\d+(\.\d{0,3})?$/, "Invalid amount format").optional().nullable(),
  new_insurance_due_date: z.string().optional().nullable(),
  renewal_notes: z.string().optional().nullable(),
});

export type ContractRenewalInput = z.infer<typeof contractRenewalSchema>;

// Helper function to calculate contract value
export function calculateContractValue(contract: Contract): number {
  const monthlyRent = typeof contract.monthly_rent === "string" 
    ? parseFloat(contract.monthly_rent) 
    : typeof contract.monthly_rent === "object" && contract.monthly_rent !== null
    ? parseFloat(contract.monthly_rent.toString())
    : Number(contract.monthly_rent);
  
  const startDate = new Date(contract.start_date);
  const endDate = new Date(contract.end_date);
  const months = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30));
  
  return monthlyRent * months;
}

// Helper function to check if contract should send renewal notice
export function shouldSendRenewalNotice(contract: Contract & { auto_renew: boolean; renewal_notice_days: number }): boolean {
  if (!contract.auto_renew || contract.status !== "ACTIVE") {
    return false;
  }
  
  const now = new Date();
  const endDate = new Date(contract.end_date);
  const daysUntilExpiry = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  
  return daysUntilExpiry <= contract.renewal_notice_days && daysUntilExpiry > 0;
}