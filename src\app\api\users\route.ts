import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";

import { userCreateApiSchema, type UserFilters } from "@/types/user";



import { hashPassword, verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
// GET /api/users - List users with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return NextResponse.json({ error: "Invalid or expired token" }, { status: 401 });
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has READ permission for users
    const canRead = hasPermission(userPermissions, "users", "read");
    if (!canRead) {
      return NextResponse.json({ error: "You don't have permission to view users" }, { status: 403 });
    }
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const filters: UserFilters = {
      search: searchParams.get("search") || undefined,
      status: searchParams.get("status") as any || undefined,
      role_id: searchParams.get("role_id") ? parseInt(searchParams.get("role_id")!) : undefined,
      page: parseInt(searchParams.get("page") || "1"),
      pageSize: parseInt(searchParams.get("pageSize") || "10"),
      sortBy: searchParams.get("sortBy") || "created_at",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
    };

    // Build where clause
    const where: any = {};
    
    if (filters.search) {
      where.OR = [
        { first_name: { contains: filters.search } },
        { last_name: { contains: filters.search } },
        { email: { contains: filters.search } },
        { username: { contains: filters.search } },
      ];
    }
    
    if (filters.status) {
      where.status = filters.status;
    }
    
    if (filters.role_id) {
      where.user_roles = {
        some: {
          role_id: filters.role_id,
        },
      };
    }

    // Calculate pagination
    const skip = (filters.page - 1) * filters.pageSize;
    const take = filters.pageSize;

    // Build orderBy
    const orderBy: any = {};
    orderBy[filters.sortBy] = filters.sortOrder;

    // Get users with roles
    const [users, totalCount] = await Promise.all([
      db.user.findMany({
        where,
        skip,
        take,
        orderBy,
        select: {
          id: true,
          username: true,
          email: true,
          first_name: true,
          last_name: true,
          phone: true,
          status: true,
          last_login_at: true,
          email_verified: true,
          created_at: true,
          updated_at: true,
          user_roles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
            },
          },
        },
      }),
      db.user.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / filters.pageSize);

    return NextResponse.json({
      users,
      pagination: {
        page: filters.page,
        pageSize: filters.pageSize,
        totalCount,
        totalPages,
        hasNext: filters.page < totalPages,
        hasPrev: filters.page > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 }
    );
  }
}

// POST /api/users - Create a new user
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return NextResponse.json({ error: "Invalid or expired token" }, { status: 401 });
    }

    // Get user permissions
    const userPermissions = await getUserPermissions(decoded.id);

    // Check if user has CREATE permission for users
    const canCreate = hasPermission(userPermissions, "users", "create");
    if (!canCreate) {
      return NextResponse.json({ error: "You don't have permission to create users" }, { status: 403 });
    }

    const body = await request.json();
    
    // Validate the request body
    const validatedData = userCreateApiSchema.parse(body);

    // Check if username already exists
    const existingUsername = await db.user.findUnique({
      where: { username: validatedData.username },
    });

    if (existingUsername) {
      return NextResponse.json(
        { error: "A user with this username already exists" },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingEmail = await db.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingEmail) {
      return NextResponse.json(
        { error: "A user with this email already exists" },
        { status: 400 }
      );
    }

    // Hash password
    const passwordHash = await hashPassword(validatedData.password);

    // Create the user
    const user = await db.user.create({
      data: {
        username: validatedData.username,
        email: validatedData.email,
        password_hash: passwordHash,
        first_name: validatedData.first_name,
        last_name: validatedData.last_name,
        phone: validatedData.phone || null,
        status: validatedData.status,
      },
      select: {
        id: true,
        username: true,
        email: true,
        first_name: true,
        last_name: true,
        phone: true,
        status: true,
        email_verified: true,
        created_at: true,
        updated_at: true,
      },
    });

    // Assign roles if provided
    if (validatedData.role_ids && validatedData.role_ids.length > 0) {
      await db.userRole.createMany({
        data: validatedData.role_ids.map(roleId => ({
          user_id: user.id,
          role_id: roleId,
        })),
      });
    }

    return NextResponse.json(user, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 }
    );
  }
}
