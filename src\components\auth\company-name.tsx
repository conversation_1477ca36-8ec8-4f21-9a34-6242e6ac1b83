"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { APP_CONFIG } from "@/config/app-config";

interface CompanySettings {
  id: number;
  company_name: string;
  company_name_ar?: string;
  logo_url: string | null;
  created_at: string;
  updated_at: string;
}

interface CompanyNameProps {
  locale: string;
  className?: string;
}

export function CompanyName({ locale, className = "" }: CompanyNameProps) {
  const [settings, setSettings] = useState<CompanySettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const t = useTranslations("common");

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setIsLoading(true);
        const response = await fetch("/api/company-settings", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setSettings(result.data);
          }
        }
      } catch (error) {
        console.log("Could not fetch company settings:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  if (isLoading) {
    return (
      <div className={`h-8 w-48 bg-muted animate-pulse rounded mx-auto ${className}`} />
    );
  }

  // Use Arabic name if locale is Arabic and it exists, otherwise fallback to default name
  const companyName = locale === 'ar' && settings?.company_name_ar 
    ? settings.company_name_ar 
    : (settings?.company_name || APP_CONFIG.name);

  return (
    <h2 className={`text-2xl font-semibold text-foreground ${className}`}>
      {companyName}
    </h2>
  );
}