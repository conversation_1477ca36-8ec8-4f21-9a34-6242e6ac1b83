#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// The replacement patterns
const OLD_IMPORTS = `import { getUserFromRequest, hasPermission } from "@/lib/permissions";`;
const NEW_IMPORTS = `import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";`;

const OLD_PATTERN_GET_USER = /const user = await getUserFromRequest\(request\);[\s\S]*?if \(!user \|\| !user\.userId\) \{[\s\S]*?return ApiResponseBuilder\.unauthorized\(\);[\s\S]*?\}/g;

const NEW_PATTERN_GET_USER = `// Check authentication
    const authHeader = request.headers.get("authorization");
    const token = authHeader?.replace("Bearer ", "");
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }`;

const OLD_PERMISSION_CHECK = /const (can\w+) = await hasPermission\(user\.userId, "([\w-]+)", "(\w+)"\);/g;

function replacePermissionCheck(match, varName, module, action) {
  const actionLower = action.toLowerCase();
  return `// Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const ${varName} = hasPermission(userPermissions, "${module}", "${actionLower}");`;
}

// Find all API route files
const apiDir = path.join(__dirname, '..', 'src', 'app', 'api');
const routeFiles = glob.sync('**/route.ts', { cwd: apiDir });

console.log(`Found ${routeFiles.length} API route files`);

let fixedFiles = 0;
let skippedFiles = 0;

routeFiles.forEach(file => {
  const filePath = path.join(apiDir, file);
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Skip if already uses the correct auth import
  if (content.includes('import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth"')) {
    console.log(`✓ Already fixed: ${file}`);
    skippedFiles++;
    return;
  }

  // Skip if it doesn't use the problematic permissions import
  if (!content.includes('import { getUserFromRequest, hasPermission } from "@/lib/permissions"')) {
    skippedFiles++;
    return;
  }

  console.log(`Fixing: ${file}`);

  // Replace imports
  if (content.includes(OLD_IMPORTS)) {
    content = content.replace(OLD_IMPORTS, NEW_IMPORTS);
    modified = true;
  }

  // Replace getUserFromRequest pattern
  if (OLD_PATTERN_GET_USER.test(content)) {
    content = content.replace(OLD_PATTERN_GET_USER, NEW_PATTERN_GET_USER);
    modified = true;
  }

  // Replace permission checks
  let permissionReplaced = false;
  content = content.replace(OLD_PERMISSION_CHECK, (match, varName, module, action) => {
    if (!permissionReplaced) {
      permissionReplaced = true;
      return replacePermissionCheck(match, varName, module, action);
    } else {
      // For subsequent permission checks, just update the call
      const actionLower = action.toLowerCase();
      return `const ${varName} = hasPermission(userPermissions, "${module}", "${actionLower}");`;
    }
  });

  if (permissionReplaced) {
    modified = true;
  }

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Fixed: ${file}`);
    fixedFiles++;
  } else {
    console.log(`⏭️  Skipped: ${file} (no changes needed)`);
    skippedFiles++;
  }
});

console.log(`\n✅ Fixed ${fixedFiles} files`);
console.log(`⏭️  Skipped ${skippedFiles} files`);
console.log(`\nPermission system update complete!`);