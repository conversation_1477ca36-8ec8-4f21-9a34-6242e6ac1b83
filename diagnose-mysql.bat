@echo off
echo ========================================
echo MySQL Diagnostic Script
echo ========================================
echo.

echo 1. Checking if MySQL process is running...
tasklist | findstr mysqld
if %errorlevel% == 0 (
    echo ✅ MySQL process found
) else (
    echo ❌ MySQL process not running
)
echo.

echo 2. Checking port 3306...
netstat -an | findstr :3306
if %errorlevel% == 0 (
    echo ✅ Port 3306 is in use
) else (
    echo ❌ Port 3306 is free
)
echo.

echo 3. Checking MySQL service...
sc query mysql 2>nul
if %errorlevel% == 0 (
    echo ✅ MySQL service exists
) else (
    echo ❌ MySQL service not found
)
echo.

echo 4. Checking XAMPP installation...
if exist "C:\xampp\mysql\bin\mysqld.exe" (
    echo ✅ XAMPP MySQL found at C:\xampp\mysql\bin\mysqld.exe
) else (
    echo ❌ XAMPP MySQL not found at expected location
)
echo.

echo 5. Checking MySQL data directory...
if exist "C:\xampp\mysql\data" (
    echo ✅ MySQL data directory exists
    dir "C:\xampp\mysql\data" | findstr "property_management"
    if %errorlevel% == 0 (
        echo ✅ property_management database folder found
    ) else (
        echo ⚠️  property_management database folder not found
    )
) else (
    echo ❌ MySQL data directory not found
)
echo.

echo 6. Trying to connect to MySQL...
"C:\xampp\mysql\bin\mysql.exe" -u root -e "SELECT 'Connection successful' as status;" 2>nul
if %errorlevel% == 0 (
    echo ✅ MySQL connection successful
) else (
    echo ❌ MySQL connection failed
)
echo.

echo ========================================
echo Diagnostic complete
echo ========================================
pause
