/**
 * <PERSON><PERSON><PERSON> Font Configuration
 * 
 * Tajawal font is loaded locally from /public/fonts/tajawal/
 * This improves performance and ensures the font is always available
 * without requiring an internet connection.
 * 
 * Font weights available:
 * - 200: Extra Light
 * - 300: Light
 * - 400: Regular
 * - 500: Medium
 * - 700: Bold
 * - 800: Extra Bold
 * - 900: Black
 */

/* Local Tajawal font face definitions */
@font-face {
  font-family: 'Tajawal';
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url('/fonts/tajawal/tajawal-extralight.ttf') format('truetype');
}

@font-face {
  font-family: 'Tajawal';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('/fonts/tajawal/tajawal-light.ttf') format('truetype');
}

@font-face {
  font-family: 'Tajawal';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/fonts/tajawal/tajawal-regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Tajawal';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('/fonts/tajawal/tajawal-medium.ttf') format('truetype');
}

@font-face {
  font-family: 'Tajawal';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('/fonts/tajawal/tajawal-bold.ttf') format('truetype');
}

@font-face {
  font-family: 'Tajawal';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url('/fonts/tajawal/tajawal-extrabold.ttf') format('truetype');
}

@font-face {
  font-family: 'Tajawal';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url('/fonts/tajawal/tajawal-black.ttf') format('truetype');
}

/* Font family definitions */
:root {
  --font-inter: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-tajawal: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Default font for LTR languages */
html[dir="ltr"] {
  font-family: var(--font-inter);
}

/* Arabic font for RTL languages */
html[dir="rtl"] {
  font-family: var(--font-tajawal) !important;
}

/* Specific font classes */
.font-inter {
  font-family: var(--font-inter);
}

.font-tajawal {
  font-family: var(--font-tajawal);
}

/* Arabic text styling improvements - Higher specificity */
html[dir="rtl"] *,
html[dir="rtl"] body,
html[dir="rtl"] body *,
html[dir="rtl"] [data-slot="sidebar"],
html[dir="rtl"] [data-slot="sidebar"] *,
html[dir="rtl"] main,
html[dir="rtl"] main * {
  font-family: var(--font-tajawal) !important;
}

/* Ensure proper Arabic text rendering */
html[dir="rtl"],
html[dir="rtl"] *,
html[dir="rtl"] body,
html[dir="rtl"] body * {
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Arabic number formatting */
html[dir="rtl"] .arabic-numbers {
  font-variant-numeric: tabular-nums;
  direction: ltr;
  unicode-bidi: embed;
}

/* Font weight classes for Arabic */
html[dir="rtl"] .font-extralight {
  font-weight: 200;
}

html[dir="rtl"] .font-light {
  font-weight: 300;
}

html[dir="rtl"] .font-normal {
  font-weight: 400;
}

html[dir="rtl"] .font-medium {
  font-weight: 500;
}

html[dir="rtl"] .font-semibold {
  font-weight: 600;
}

html[dir="rtl"] .font-bold {
  font-weight: 700;
}

html[dir="rtl"] .font-extrabold {
  font-weight: 800;
}

html[dir="rtl"] .font-black {
  font-weight: 900;
}

/* Force Tajawal font for all Arabic content with maximum specificity */
html[dir="rtl"] *,
html[dir="rtl"] body,
html[dir="rtl"] body *,
html[dir="rtl"] [data-slot="sidebar"],
html[dir="rtl"] [data-slot="sidebar"] *,
html[dir="rtl"] main,
html[dir="rtl"] main *,
html[dir="rtl"] button,
html[dir="rtl"] button *,
html[dir="rtl"] input,
html[dir="rtl"] textarea,
html[dir="rtl"] select,
html[dir="rtl"] table,
html[dir="rtl"] table *,
html[dir="rtl"] nav,
html[dir="rtl"] nav *,
html[dir="rtl"] .card,
html[dir="rtl"] .card *,
html[dir="rtl"] div,
html[dir="rtl"] span,
html[dir="rtl"] p,
html[dir="rtl"] h1,
html[dir="rtl"] h2,
html[dir="rtl"] h3,
html[dir="rtl"] h4,
html[dir="rtl"] h5,
html[dir="rtl"] h6,
html[dir="rtl"] a,
html[dir="rtl"] label,
html[dir="rtl"] li,
html[dir="rtl"] ul,
html[dir="rtl"] ol,
html[dir="rtl"] section,
html[dir="rtl"] article,
html[dir="rtl"] header,
html[dir="rtl"] footer,
html[dir="rtl"] aside {
  font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Override any Inter font declarations for RTL */
html[dir="rtl"] .font-inter,
html[dir="rtl"] [class*="inter"],
html[dir="rtl"] [style*="Inter"] {
  font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}
