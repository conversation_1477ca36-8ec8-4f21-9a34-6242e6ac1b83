"use client";

import { useTranslations, useLocale } from "next-intl";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";
import { AlertCircle, CheckCircle2, Loader2, Shield } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { roleFormSchema, APPLICATION_MODULES, type RoleWithPermissions, type Permission } from "@/types/user";
import { ErrorState, InlineErrorState, getErrorVariant } from "@/components/error-state";
import { Alert, AlertDescription } from "@/components/ui/alert";

type FormData = z.infer<typeof roleFormSchema>;

interface RoleFormProps {
  role?: RoleWithPermissions;
  mode: "create" | "edit";
}

interface ModulePermissions {
  [moduleId: string]: {
    create: boolean;
    read: boolean;
    update: boolean;
    delete: boolean;
  };
}

export function RoleForm({ role, mode }: RoleFormProps) {
  const router = useRouter();
  const t = useTranslations("roles");
  const tCommon = useTranslations("common");
  const locale = useLocale();
  const [isLoading, setIsLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [permissionsError, setPermissionsError] = useState<string | null>(null);
  const [modulePermissions, setModulePermissions] = useState<ModulePermissions>({});

  // Initialize module permissions from existing role
  useEffect(() => {
    if (role) {
      const initialPermissions: ModulePermissions = {};
      
      // Initialize all modules with false
      APPLICATION_MODULES.forEach(module => {
        initialPermissions[module.id] = {
          create: false,
          read: false,
          update: false,
          delete: false,
        };
      });

      // Set existing permissions to true
      role.role_permissions.forEach(rp => {
        const module = rp.permission.module;
        const action = rp.permission.action.toLowerCase() as keyof ModulePermissions[string];
        
        if (initialPermissions[module] && action in initialPermissions[module]) {
          initialPermissions[module][action] = true;
        }
      });

      setModulePermissions(initialPermissions);
    } else {
      // Initialize empty permissions for create mode
      const initialPermissions: ModulePermissions = {};
      APPLICATION_MODULES.forEach(module => {
        initialPermissions[module.id] = {
          create: false,
          read: false,
          update: false,
          delete: false,
        };
      });
      setModulePermissions(initialPermissions);
    }
  }, [role]);

  const form = useForm<FormData>({
    resolver: zodResolver(roleFormSchema),
    mode: "onChange",
    defaultValues: {
      name: role?.name || "",
      description: role?.description || "",
      permissions: [],
    },
  });

  // Watch form state for real-time validation feedback
  const formState = form.formState;
  const { errors, isValid, isDirty } = formState;

  // Fetch permissions for reference
  useEffect(() => {
    const fetchPermissions = async () => {
      try {
        setPermissionsError(null);
        const response = await fetch("/api/permissions");

        if (!response.ok) {
          throw new Error("Failed to fetch permissions");
        }

        const data = await response.json();
        setPermissions(data.all_permissions);
      } catch (error) {
        console.error("Error fetching permissions:", error);
        setPermissionsError(t("messages.fetchError"));
      }
    };

    fetchPermissions();
  }, []);

  // Clear submit error when form is modified
  useEffect(() => {
    if (submitError && isDirty) {
      setSubmitError(null);
    }
  }, [isDirty, submitError]);

  // Handle permission toggle
  const handlePermissionToggle = (moduleId: string, action: keyof ModulePermissions[string]) => {
    setModulePermissions(prev => ({
      ...prev,
      [moduleId]: {
        ...prev[moduleId],
        [action]: !prev[moduleId]?.[action],
      },
    }));
  };

  // Handle full access toggle for a module
  const handleFullAccessToggle = (moduleId: string) => {
    const currentModule = modulePermissions[moduleId];
    const hasFullAccess = currentModule?.create && currentModule?.read && currentModule?.update && currentModule?.delete;
    
    setModulePermissions(prev => ({
      ...prev,
      [moduleId]: {
        create: !hasFullAccess,
        read: !hasFullAccess,
        update: !hasFullAccess,
        delete: !hasFullAccess,
      },
    }));
  };

  const onSubmit = async (data: FormData) => {
    try {
      setIsLoading(true);
      setSubmitError(null);
      setValidationErrors({});

      // Convert modulePermissions to the format expected by the API
      const permissionsArray = Object.entries(modulePermissions)
        .map(([moduleId, perms]) => ({
          module: moduleId,
          actions: Object.entries(perms)
            .filter(([_, enabled]) => enabled)
            .map(([action]) => action.toUpperCase()),
        }))
        .filter(modulePerms => modulePerms.actions.length > 0);

      const submitData = {
        ...data,
        permissions: permissionsArray,
      };

      const url = mode === "create" ? "/api/roles" : `/api/roles/${role?.id}`;
      const method = mode === "create" ? "POST" : "PUT";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Handle validation errors
        if (response.status === 400 && errorData.details) {
          const fieldErrors: Record<string, string> = {};
          errorData.details.forEach((detail: any) => {
            if (detail.path && detail.path.length > 0) {
              fieldErrors[detail.path[0]] = detail.message;
            }
          });
          setValidationErrors(fieldErrors);

          // Create a more detailed error message
          const errorMessages = errorData.details.map((detail: any) => detail.message);
          throw new Error(`Validation failed: ${errorMessages.join(", ")}`);
        }

        throw new Error(errorData.error || `Failed to ${mode} role`);
      }

      const result = await response.json();
      
      toast.success(
        mode === "create" 
          ? t("messages.createSuccess") 
          : t("messages.updateSuccess")
      );

      // Redirect to role detail page
      router.push(`/${locale}/dashboard/roles/${result.id}`);
    } catch (error) {
      console.error(`Error ${mode === "create" ? "creating" : "updating"} role:`, error);
      setSubmitError(error instanceof Error ? error.message : `Failed to ${mode} role`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="mx-auto max-w-4xl">
      <CardHeader>
        <CardTitle>
          {mode === "create" ? t("createNewRole") : `${t("editRole")}: ${role?.name}`}
        </CardTitle>
        <CardDescription>
          {mode === "create" 
            ? t("createDescription")
            : t("description")
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        {submitError && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        {permissionsError && (
          <InlineErrorState 
            error={permissionsError}
            variant="server"
            className="mb-6"
          />
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">{t("basicInformation")}</h3>
              
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("roleName")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("roleNamePlaceholder")} {...field} />
                    </FormControl>
                    <FormMessage />
                    {validationErrors.name && (
                      <p className="text-sm text-destructive">{validationErrors.name}</p>
                    )}
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("descriptionOptional")}</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder={t("descriptionPlaceholder")} 
                        className="resize-none"
                        rows={3}
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                    {validationErrors.description && (
                      <p className="text-sm text-destructive">{validationErrors.description}</p>
                    )}
                  </FormItem>
                )}
              />
            </div>

            {/* Permissions */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">{t("modulePermissions")}</h3>
              <p className="text-sm text-muted-foreground">
                {t("modulePermissionsDescription")}
              </p>
              
              <div className="space-y-6">
                {APPLICATION_MODULES.map((module) => {
                  const modulePerms = modulePermissions[module.id] || {
                    create: false,
                    read: false,
                    update: false,
                    delete: false,
                  };
                  
                  const hasFullAccess = modulePerms.create && modulePerms.read && modulePerms.update && modulePerms.delete;
                  const hasAnyAccess = modulePerms.create || modulePerms.read || modulePerms.update || modulePerms.delete;

                  return (
                    <Card key={module.id} className="p-4">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Shield className="h-5 w-5 text-primary" />
                            <div>
                              <h4 className="font-medium">{t(`modules.${module.id}` as any)}</h4>
                              <p className="text-sm text-muted-foreground">{t(`modules.${module.id}Description` as any)}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {hasAnyAccess && (
                              <Badge variant={hasFullAccess ? "default" : "secondary"}>
                                {hasFullAccess ? t("fullAccess") : t("partialAccess")}
                              </Badge>
                            )}
                            <Button
                              type="button"
                              variant={hasFullAccess ? "default" : "outline"}
                              size="sm"
                              onClick={() => handleFullAccessToggle(module.id)}
                            >
                              {hasFullAccess ? t("removeFullAccess") : t("grantFullAccess")}
                            </Button>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
                          {[
                            { key: "create" as const, label: t("create"), description: t("createDescription") },
                            { key: "read" as const, label: t("view"), description: t("viewDescription") },
                            { key: "update" as const, label: t("edit"), description: t("editDescription") },
                            { key: "delete" as const, label: t("delete"), description: t("deleteDescription") },
                          ].map((permission) => (
                            <div key={permission.key} className="flex items-start space-x-3">
                              <Checkbox
                                checked={modulePerms[permission.key]}
                                onCheckedChange={() => handlePermissionToggle(module.id, permission.key)}
                              />
                              <div className="space-y-1 leading-none">
                                <label className="text-sm font-medium cursor-pointer">
                                  {permission.label}
                                </label>
                                <p className="text-xs text-muted-foreground">
                                  {permission.description}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isLoading}
              >
                {tCommon("cancel")}
              </Button>
              <Button type="submit" disabled={isLoading || !isValid}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {mode === "create" ? t("creating") : t("updating")}
                  </>
                ) : (
                  <>
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                    {mode === "create" ? t("createRole") : t("updateRole")}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
