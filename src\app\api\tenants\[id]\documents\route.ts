import { NextRequest } from "next/server";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { z } from "zod";
import { writeFile, mkdir } from "fs/promises";
import path from "path";

const UPLOAD_DIR = path.join(process.cwd(), "public", "uploads", "tenant-documents");

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/tenants/:id/documents - Get all documents for a tenant
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "tenants", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view tenants");
    }

    const { id } = await params;
    const tenantId = parseInt(id);
    
    if (isNaN(tenantId)) {
      return ApiResponseBuilder.badRequest("Invalid tenant ID");
    }

    const documents = await db.tenantDocument.findMany({
      where: { tenant_id: tenantId },
      include: {
        uploader: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
      },
      orderBy: { created_at: "desc" },
    });

    return ApiResponseBuilder.success(documents);
  } catch (error) {
    console.error("Get Tenant Documents Error:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch documents",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// POST /api/tenants/:id/documents - Upload a new document
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "tenants", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update tenants");
    }

    const { id } = await params;
    const tenantId = parseInt(id);
    
    if (isNaN(tenantId)) {
      return ApiResponseBuilder.badRequest("Invalid tenant ID");
    }

    // Check if tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
    });

    if (!tenant) {
      return ApiResponseBuilder.notFound("Tenant");
    }

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const documentType = formData.get("document_type") as string || "OTHER";

    if (!file) {
      return ApiResponseBuilder.badRequest("No file provided");
    }

    // Validate file type (only allow common document formats)
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (!allowedTypes.includes(file.type)) {
      return ApiResponseBuilder.badRequest(
        "Invalid file type. Only PDF, JPG, PNG, DOC, and DOCX files are allowed",
        undefined,
        request.url
      );
    }

    // Create upload directory if it doesn't exist
    await mkdir(UPLOAD_DIR, { recursive: true });

    // Generate unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(file.name);
    const fileName = `tenant_${tenantId}_${timestamp}_${randomString}${fileExtension}`;
    const filePath = path.join(UPLOAD_DIR, fileName);

    // Write file to disk
    const buffer = Buffer.from(await file.arrayBuffer());
    await writeFile(filePath, buffer);

    // Save document record to database
    const document = await db.tenantDocument.create({
      data: {
        tenant_id: tenantId,
        document_type: documentType as any,
        file_name: file.name,
        file_path: `/uploads/tenant-documents/${fileName}`,
        file_size: file.size,
        mime_type: file.type,
        uploaded_by: decoded.id,
      },
      include: {
        uploader: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    return ApiResponseBuilder.success(document);
  } catch (error) {
    console.error("Upload Tenant Document Error:", error);
    return ApiResponseBuilder.error(
      "Failed to upload document",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}