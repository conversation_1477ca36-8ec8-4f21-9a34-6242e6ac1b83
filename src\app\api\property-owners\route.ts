import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { propertyOwnerSchema, propertyOwnerFilterSchema } from "@/types/property-owner";
import { ApiResponseBuilder } from "@/lib/api-response";




import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
// GET /api/property-owners - List all property owners with filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for property-owners
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "property-owners", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view property owners");
    }

    const searchParams = request.nextUrl.searchParams;
    
    // Parse filters
    const filters = propertyOwnerFilterSchema.parse({
      search: searchParams.get("search") || undefined,
      status: searchParams.get("status") || undefined,
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
      pageSize: searchParams.get("pageSize") ? parseInt(searchParams.get("pageSize")!) : 10,
      sortBy: searchParams.get("sortBy") || "created_at",
      sortOrder: searchParams.get("sortOrder") || "desc",
    });

    // Build where clause
    const where: any = {};

    if (filters.search) {
      where.OR = [
        { name_en: { contains: filters.search } },
        { name_ar: { contains: filters.search } },
        { email: { contains: filters.search } },
        { phone: { contains: filters.search } },
        { mobile: { contains: filters.search } },
        { tax_id: { contains: filters.search } },
      ];
    }

    if (filters.status) {
      where.status = filters.status;
    }

    // Calculate pagination
    const page = filters.page || 1;
    const pageSize = filters.pageSize || 10;
    const skip = (page - 1) * pageSize;

    // Fetch owners with relations
    const [owners, total] = await Promise.all([
      db.propertyOwner.findMany({
        where,
        include: {
          primary_properties: {
            select: {
              id: true,
              name_en: true,
              name_ar: true,
              address_en: true,
              address_ar: true,
              base_rent: true,
              status: true,
            },
          },
          _count: {
            select: {
              payouts: true,
              primary_properties: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || "created_at"]: filters.sortOrder || "desc",
        },
        skip,
        take: pageSize,
      }),
      db.propertyOwner.count({ where }),
    ]);

    // Transform owners to ensure all Decimal values are serialized
    const transformedOwners = owners.map(owner => ({
      ...owner,
      management_fee_percentage: owner.management_fee_percentage?.toString() || null,
      primary_properties: owner.primary_properties.map(property => ({
        ...property,
        base_rent: property.base_rent.toString()
      }))
    }));

    return ApiResponseBuilder.success(transformedOwners, {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    });
  } catch (error) {
    console.error("Error fetching property owners:", error);
    return ApiResponseBuilder.error("Failed to fetch property owners", "INTERNAL_ERROR", 500);
  }
}

// POST /api/property-owners - Create a new property owner
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
        // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has CREATE permission for property-owners
    const userPermissions = await getUserPermissions(decoded.id);
    const canCreate = hasPermission(userPermissions, "property-owners", "create");
    if (!canCreate) {
      return ApiResponseBuilder.forbidden("You don't have permission to create property owners");
    }

    const body = await request.json();
    const validatedData = propertyOwnerSchema.parse(body);

    const owner = await db.propertyOwner.create({
      data: {
        ...validatedData,
        created_by: decoded.id,
        updated_by: decoded.id,
      },
      include: {
        primary_properties: true,
        _count: {
          select: {
            payouts: true,
            primary_properties: true,
          },
        },
      },
    });

    // Transform owner to ensure all Decimal values are serialized
    const transformedOwner = {
      ...owner,
      management_fee_percentage: owner.management_fee_percentage?.toString() || null,
      primary_properties: owner.primary_properties.map(property => ({
        ...property,
        base_rent: property.base_rent.toString(),
        total_area: property.total_area?.toString() || null
      }))
    };

    return ApiResponseBuilder.success(transformedOwner);
  } catch (error: any) {
    console.error("Error creating property owner:", error);
    
    if (error.name === "ZodError") {
      return ApiResponseBuilder.validationError(error, request.url);
    }

    return ApiResponseBuilder.error("Failed to create property owner", "INTERNAL_ERROR", 500);
  }
}