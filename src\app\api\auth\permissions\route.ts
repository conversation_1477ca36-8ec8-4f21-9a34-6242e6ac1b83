import { NextRequest, NextResponse } from "next/server";
import { getUserFromRequest, getUserPermissions } from "@/lib/permissions";

export async function GET(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request);
    
    if (!user || !user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const permissions = await getUserPermissions(user.id);

    return NextResponse.json({
      permissions,
    });
  } catch (error) {
    console.error("Error fetching permissions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}