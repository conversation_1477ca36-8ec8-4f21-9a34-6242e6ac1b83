import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { db } from "@/lib/db";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";

interface PropertyTypeDetailsPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

async function getPropertyType(id: string) {
  try {
    const propertyType = await db.propertyType.findUnique({
      where: { id: parseInt(id, 10) },
      include: {
        _count: {
          select: { properties: true },
        },
      },
    });
    return propertyType;
  } catch (error) {
    console.error("Error fetching property type:", error);
    return null;
  }
}

export default async function PropertyTypeDetailsPage({ params }: PropertyTypeDetailsPageProps) {
  const { id, locale } = await params;
  const t = await getTranslations('properties.propertyTypes');
  const tDetails = await getTranslations('properties.propertyTypes.details');

  const propertyType = await getPropertyType(id);

  if (!propertyType) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href={`/${locale}/dashboard/property-types`}>
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
            <p className="text-muted-foreground">
              {locale === "ar" ? propertyType.name_ar : propertyType.name_en}
            </p>
          </div>
        </div>
        <Link href={`/${locale}/dashboard/property-types/${id}/edit`}>
          <Button>
            <Edit className="mr-2 h-4 w-4" />
            {t('editPropertyType')}
          </Button>
        </Link>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>{t('table.nameEn')} / {t('table.nameAr')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="text-sm text-muted-foreground">{t('table.nameEn')}</p>
              <p className="font-medium">{propertyType.name_en}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{t('table.nameAr')}</p>
              <p className="font-medium">{propertyType.name_ar}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('table.descriptionEn')} / {t('table.descriptionAr')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="text-sm text-muted-foreground">{t('table.descriptionEn')}</p>
              <p className="font-medium">{propertyType.description_en || '-'}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{t('table.descriptionAr')}</p>
              <p className="font-medium">{propertyType.description_ar || '-'}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('table.propertiesCount')}</CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={propertyType._count?.properties ? "default" : "secondary"} className="text-lg px-3 py-1">
              {propertyType._count?.properties || 0}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{tDetails('systemInformation')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="text-sm text-muted-foreground">{t('table.createdAt')}</p>
              <p className="font-medium">{format(new Date(propertyType.created_at), "dd/MM/yyyy HH:mm")}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{tDetails('updatedAt')}</p>
              <p className="font-medium">{format(new Date(propertyType.updated_at), "dd/MM/yyyy HH:mm")}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}