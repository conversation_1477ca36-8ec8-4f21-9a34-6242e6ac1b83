const fetch = require('node-fetch');

async function testLogin() {
  try {
    console.log('Testing login API...');
    
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: '<EMAIL>',
        password: '123456'
      })
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const data = await response.json();
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    // Check if auth-token cookie was set
    const cookies = response.headers.get('set-cookie');
    console.log('Set-Cookie header:', cookies);
    
  } catch (error) {
    console.error('Login test error:', error);
  }
}

testLogin();