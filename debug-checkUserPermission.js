const { PrismaClient } = require('./src/generated/prisma');
const prisma = new PrismaClient();

// Re-implementing the getPermissionsFromDB function
async function getPermissionsFromDB(userId) {
  const userWithRoles = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      user_roles: {
        include: {
          role: {
            include: {
              role_permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      },
    },
  });

  if (!userWithRoles) {
    return [];
  }

  // Check if user has admin role
  const isAdmin = userWithRoles.user_roles.some(ur => {
    const roleName = ur.role.name.toLowerCase();
    return roleName.includes('admin') || 
           roleName.includes('superuser') || 
           roleName === 'administrator' ||
           roleName === 'super admin';
  });

  if (isAdmin) {
    // Return all modules with all permissions
    const allModules = [
      'dashboard', 'properties', 'property-types', 'property-owners', 'units',
      'tenants', 'contracts', 'invoices', 'payments', 'owners-associations', 'owner-payouts',
      'maintenance', 'expenses', 'expense-categories', 'amenities', 'users', 'roles',
      'reports', 'settings'
    ];
    
    return allModules.map(module => ({
      module,
      actions: ['READ', 'CREATE', 'UPDATE', 'DELETE']
    }));
  }

  // For non-admin users, collect permissions from roles
  const permissionMap = new Map();
  
  userWithRoles.user_roles.forEach(userRole => {
    userRole.role.role_permissions.forEach(rolePermission => {
      const { module, action } = rolePermission.permission;
      
      if (!permissionMap.has(module)) {
        permissionMap.set(module, new Set());
      }
      
      permissionMap.get(module).add(action);
    });
  });

  // Convert map to array
  return Array.from(permissionMap.entries()).map(([module, actions]) => ({
    module,
    actions: Array.from(actions),
  }));
}

async function testPermission() {
  try {
    // Find the admin user
    const adminUser = await prisma.user.findFirst({
      where: { username: 'admin' }
    });
    
    if (!adminUser) {
      console.log('No admin user found!');
      return;
    }
    
    const userId = adminUser.id;
    
    // First check the user data
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        user_roles: {
          include: {
            role: true
          }
        }
      }
    });
    
    console.log('User:', user?.username);
    console.log('Roles:');
    user?.user_roles.forEach(ur => {
      console.log(`  - ${ur.role.name}`);
    });
    
    const permissions = await getPermissionsFromDB(userId);
    
    console.log('User permissions:');
    permissions.forEach(p => {
      console.log(`  ${p.module}: ${p.actions.join(', ')}`);
    });
    
    // Check specific permission
    const module = 'owners-associations';
    const action = 'READ';
    
    const modulePermission = permissions.find(p => p.module === module);
    const hasPermission = modulePermission ? modulePermission.actions.includes(action) : false;
    
    console.log(`\nChecking ${module}:${action} = ${hasPermission}`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPermission();