"use client";

import { useState, useEffect, useCallback } from "react";
import { useTranslations, useLocale } from "next-intl";
import { toast } from "sonner";
import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
  SortingState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { 
  Banknote,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  TrendingDown,
  FileText,
  Download,
  Eye,
  CreditCard
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useRTL } from "@/hooks/use-rtl";
import { apiClient } from "@/lib/api-client";
import { formatCurrency } from "@/lib/utils";
import type { SubscriptionPaymentWithRelations } from "@/types/owners-association";
import { PaymentRecordsForm } from "./payment-records-form";
import { PaymentDetailsDialog } from "./payment-details-dialog";

interface MemberPaymentsTableProps {
  associationId: number;
  memberId?: number;
}

export function MemberPaymentsTable({ associationId, memberId }: MemberPaymentsTableProps) {
  const locale = useLocale();
  const { isRTL } = useRTL();
  const t = useTranslations("ownersAssociations.payments");
  const tCommon = useTranslations("common");
  const dateLocale = locale === 'ar' ? ar : enUS;
  
  const [payments, setPayments] = useState<SubscriptionPaymentWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [selectedPayment, setSelectedPayment] = useState<SubscriptionPaymentWithRelations | null>(null);

  const [showPaymentDetails, setShowPaymentDetails] = useState(false);
  const [stats, setStats] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'table' | 'payment-records'>('table');

  const fetchPayments = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (memberId) {
        params.append("member_id", memberId.toString());
      }

      const response = await apiClient.get(
        `/api/owners-associations/${associationId}/subscription-payments?${params}`
      );
      
      if (response.success) {
        setPayments(response.data.payments || []);
        setStats(response.data.stats || null);
      }
    } catch (error) {
      console.error("Error fetching payments:", error);
      toast.error(t("fetchError"));
    } finally {
      setLoading(false);
    }
  }, [associationId, memberId, t]);

  useEffect(() => {
    fetchPayments();
  }, [fetchPayments]);

  const handleRecordPayment = (payment: SubscriptionPaymentWithRelations) => {
    setSelectedPayment(payment);
    setViewMode('payment-records');
  };

  const handleViewDetails = (payment: SubscriptionPaymentWithRelations) => {
    setSelectedPayment(payment);
    setShowPaymentDetails(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PAID":
        return (
          <Badge variant="default" className="gap-1">
            <CheckCircle className="h-3 w-3" />
            {t("status.paid")}
          </Badge>
        );
      case "PARTIALLY_PAID":
        return (
          <Badge variant="secondary" className="gap-1">
            <Clock className="h-3 w-3" />
            {t("status.partiallyPaid")}
          </Badge>
        );
      case "OVERDUE":
        return (
          <Badge variant="destructive" className="gap-1">
            <AlertCircle className="h-3 w-3" />
            {t("status.overdue")}
          </Badge>
        );
      case "UNPAID":
        return (
          <Badge variant="outline" className="gap-1">
            <TrendingDown className="h-3 w-3" />
            {t("status.unpaid")}
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const columns: ColumnDef<SubscriptionPaymentWithRelations>[] = [
    {
      accessorKey: "member",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("member")} />
      ),
      cell: ({ row }) => {
        const member = row.original.member;
        return (
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="font-medium">{member.full_name}</div>
            <div className="text-sm text-muted-foreground">
              {t("unit")}: {member.unit_number}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "subscription",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("subscription")} />
      ),
      cell: ({ row }) => {
        const subscription = row.original.subscription;
        const name = locale === 'ar' ? subscription.name_ar : subscription.name_en;
        return (
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className="font-medium">{name}</div>
            <div className="text-sm text-muted-foreground">
              {formatCurrency(subscription.amount.toString())} / {t(`frequency.${subscription.frequency.toLowerCase()}`)}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "due_date",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("dueDate")} />
      ),
      cell: ({ row }) => {
        const date = new Date(row.original.due_date);
        const today = new Date();
        const isOverdue = date < today && row.original.status !== "PAID";
        
        return (
          <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className={cn(isOverdue && "text-destructive font-medium")}>
              {format(date, "dd/MM/yyyy", { locale: dateLocale })}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "amount_due",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("amountDue")} />
      ),
      cell: ({ row }) => {
        const amountDue = parseFloat(row.original.amount_due?.toString() || row.original.amount.toString());
        const amountPaid = parseFloat(row.original.amount_paid?.toString() || "0");
        // Calculate percentage if both values are available
        const percentage = amountDue > 0 ? Math.round((amountPaid / amountDue) * 100) : 0;
        
        return (
          <div className="space-y-2">
            <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
              <Banknote className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">{formatCurrency(amountDue)}</span>
            </div>
            {amountPaid > 0 && (
              <div className="space-y-1">
                <Progress value={percentage} className="h-2" />
                <div className="text-xs text-muted-foreground">
                  {formatCurrency(amountPaid)} {t("paid")} ({percentage}%)
                </div>
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "amount_paid",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("amountPaid")} />
      ),
      cell: ({ row }) => {
        const amountPaid = parseFloat(row.original.amount_paid?.toString() || "0");
        const amountDue = parseFloat(row.original.amount_due?.toString() || row.original.amount.toString());
        
        return (
          <div className={cn("space-y-1", isRTL && "text-right")}>
            <div className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">{formatCurrency(amountPaid)}</span>
            </div>
            {amountPaid < amountDue && (
              <div className="text-xs text-muted-foreground">
                {t("remaining")}: {formatCurrency(amountDue - amountPaid)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("statusLabel")} className="justify-center" />
      ),
      cell: ({ row }) => {
        return (
          <div className="flex justify-center">
            {getStatusBadge(row.original.status)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className={cn("text-right", isRTL && "text-left")}>{tCommon("actions")}</div>,
      cell: ({ row }) => {
        const payment = row.original;
        const canPay = payment.status !== "PAID";

        return (
          <div className={cn("flex items-center gap-2 justify-end", isRTL && "flex-row-reverse justify-start")}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleViewDetails(payment)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleRecordPayment(payment)}
              title={t("managePaymentRecords")}
            >
              <CreditCard className="h-4 w-4" />
            </Button>
            {payment.status === "PAID" && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // TODO: Generate receipt
                  toast.info(t("receiptComingSoon"));
                }}
              >
                <Download className="h-4 w-4" />
              </Button>
            )}
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: payments,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  });

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-96" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[400px] w-full" />
        </CardContent>
      </Card>
    );
  }

  // If in payment records view, show the form
  if (viewMode === 'payment-records' && selectedPayment) {
    return (
      <PaymentRecordsForm
        payment={selectedPayment}
        associationId={associationId}
        onBack={() => {
          setViewMode('table');
          setSelectedPayment(null);
        }}
        onSuccess={() => {
          fetchPayments();
        }}
      />
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className={cn("flex items-center gap-2", isRTL && "flex-row-reverse")}>
            <Banknote className="h-5 w-5" />
            {t("title")}
          </CardTitle>
          <CardDescription>
            {t("description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Summary Stats */}
          {stats && (
            <div className="grid gap-4 md:grid-cols-4 mb-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t("totalDue")}</CardTitle>
                  <Banknote className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(stats.total_due)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t("totalPaid")}</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {formatCurrency(stats.total_paid)}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t("outstanding")}</CardTitle>
                  <AlertCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">
                    {formatCurrency(stats.total_remaining)}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t("collectionRate")}</CardTitle>
                  <TrendingDown className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.collection_rate}%</div>
                  <Progress value={stats.collection_rate} className="mt-2" />
                </CardContent>
              </Card>
            </div>
          )}

          {/* Payments Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      {t("noPayments")}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>



      {/* Payment Details Dialog */}
      {selectedPayment && (
        <PaymentDetailsDialog
          open={showPaymentDetails}
          onOpenChange={setShowPaymentDetails}
          payment={selectedPayment}
          associationId={associationId}
        />
      )}
    </>
  );
}