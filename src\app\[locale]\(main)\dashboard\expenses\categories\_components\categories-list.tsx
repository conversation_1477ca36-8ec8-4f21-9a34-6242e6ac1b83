"use client";

import { useState, useMemo } from "react";
import { useTranslations } from "next-intl";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Edit, Trash2, Search } from "lucide-react";
import Link from "next/link";
import { DeleteCategoryDialog } from "./delete-category-dialog";
import { useParams } from "next/navigation";

interface CategoryWithCount {
  id: number;
  name_en: string;
  name_ar: string | null;
  description: string | null;
  is_active: boolean;
  sort_order: number;
  created_at: Date;
  _count: {
    expenses: number;
  };
}

interface CategoriesListProps {
  categories: CategoryWithCount[];
}

export function CategoriesList({ categories }: CategoriesListProps) {
  const t = useTranslations("expenses");
  const params = useParams();
  const locale = params.locale as string;
  const [searchQuery, setSearchQuery] = useState("");

  // Filter categories based on search query
  const filteredCategories = useMemo(() => {
    if (!searchQuery.trim()) return categories;

    const query = searchQuery.toLowerCase();
    return categories.filter((category) => {
      const nameEn = category.name_en?.toLowerCase() || "";
      const nameAr = category.name_ar?.toLowerCase() || "";
      const description = category.description?.toLowerCase() || "";
      
      return (
        nameEn.includes(query) ||
        nameAr.includes(query) ||
        description.includes(query)
      );
    });
  }, [categories, searchQuery]);

  return (
    <div className="space-y-6">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder={t("categories.searchPlaceholder")}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-8"
        />
      </div>

      {/* Categories Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredCategories.map((category) => (
          <Card key={category.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">
                  {locale === "ar" ? category.name_ar || category.name_en : category.name_en}
                </CardTitle>
                <Badge variant="secondary">
                  {category.is_active ? t("categories.active") : t("categories.inactive")}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                {category.description || t("categories.noDescription")}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  <span>{t("categories.expenseCount")}: {category._count.expenses}</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/${locale}/dashboard/expenses/categories/${category.id}/edit`}>
                      <Edit className="h-3 w-3" />
                    </Link>
                  </Button>
                  <DeleteCategoryDialog category={category as any}>
                    <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </DeleteCategoryDialog>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State for Search */}
      {filteredCategories.length === 0 && searchQuery && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">
                {t("categories.noSearchResults")}
              </h3>
              <p className="text-muted-foreground mb-4">
                {t("categories.tryDifferentSearch")}
              </p>
              <Button
                variant="outline"
                onClick={() => setSearchQuery("")}
              >
                {t("categories.clearFilters")}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State for No Categories */}
      {categories.length === 0 && !searchQuery && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">
                {t("categories.noCategories")}
              </h3>
              <p className="text-muted-foreground mb-4">
                {t("categories.noCategoriesDescription")}
              </p>
              <Button asChild>
                <Link href={`/${locale}/dashboard/expenses/categories/create`}>
                  <Plus className="mr-2 h-4 w-4" />
                  {t("categories.addFirstCategory")}
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}