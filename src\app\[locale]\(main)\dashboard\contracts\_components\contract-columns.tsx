"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { MoreH<PERSON><PERSON><PERSON>, Edit, Trash, Eye, FileText, Users, RefreshCw, Ban } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import type { ContractWithRelations } from "@/types/contract";
import { formatCurrency } from "@/lib/utils";
import { isContractExpiringSoon } from "@/types/contract";

interface ColumnTranslations {
  contractNumber: string;
  property: string;
  unit: string;
  tenants: string;
  period: string;
  monthlyRent: string;
  status: string;
  createdAt: string;
  actions: string;
  selectAll: string;
  selectRow: string;
  openMenu: string;
  viewDetails: string;
  editContract: string;
  terminateContract: string;
  renewContract: string;
  deleteContract: string;
  deleteConfirmation: string;
  deleteWarning: string;
  deleteError: string;
  deleting: string;
  confirmDelete: string;
  to: string;
  expiringSoon: string;
  statusBadges: {
    DRAFT: string;
    ACTIVE: string;
    EXPIRED: string;
    TERMINATED: string;
    RENEWED: string;
  };
}

interface CommonTranslations {
  cancel: string;
  delete: string;
}

export function getContractColumns(
  locale: string,
  t: any,
  tCommon: any,
  onRefresh: () => void
): ColumnDef<ContractWithRelations>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label={t("selectAll")}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={t("selectRow")}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "actions",
      header: () => <div className="text-center">{t("actions")}</div>,
      cell: ({ row }) => {
        const contract = row.original;
        const [showDeleteDialog, setShowDeleteDialog] = useState(false);
        const [isDeleting, setIsDeleting] = useState(false);

        const handleDelete = async () => {
          try {
            setIsDeleting(true);
            const response = await fetch(`/api/contracts/${contract.id}`, {
              method: "DELETE",
            });

            if (!response.ok) {
              const result = await response.json();
              throw new Error(result.error?.message || t("deleteError"));
            }

            toast.success("Contract deleted successfully");
            setShowDeleteDialog(false);
            onRefresh();
          } catch (error) {
            console.error("Delete error:", error);
            toast.error(
              error instanceof Error ? error.message : t("deleteError")
            );
          } finally {
            setIsDeleting(false);
          }
        };

        return (
          <div className="text-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">{t("openMenu")}</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>{t("actions")}</DropdownMenuLabel>
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/contracts/${contract.id}`}>
                    <Eye className="mr-2 h-4 w-4" />
                    {t("viewDetails")}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/${locale}/dashboard/contracts/${contract.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    {t("editContract")}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                {contract.status === "ACTIVE" && (
                  <>
                    <DropdownMenuItem asChild>
                      <Link href={`/${locale}/dashboard/contracts/${contract.id}/terminate`}>
                        <Ban className="mr-2 h-4 w-4" />
                        {t("terminateContract")}
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href={`/${locale}/dashboard/contracts/${contract.id}/renew`}>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        {t("renewContract")}
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                  </>
                )}
                {contract.status !== "ACTIVE" && (
                  <DropdownMenuItem
                    onClick={() => setShowDeleteDialog(true)}
                    className="text-destructive"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    {t("deleteContract")}
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{t("deleteContract")}</AlertDialogTitle>
                  <AlertDialogDescription>
                    {t("deleteConfirmation")} <strong>{contract.contract_number}</strong>?
                    <br />
                    <br />
                    {t("deleteWarning")}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>
                    {tCommon("cancel")}
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {isDeleting ? t("deleting") : t("confirmDelete")}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        );
      },
    },
    {
      accessorKey: "contract_number",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("contractNumber")} />
      ),
      cell: ({ row }) => {
        const contract = row.original;
        const isExpiring = isContractExpiringSoon(contract);
        
        return (
          <div className="flex items-center gap-2">
            <span className="font-medium">{contract.contract_number}</span>
            {isExpiring && (
              <Badge variant="outline" className="text-amber-600 border-amber-600">
                {t("expiringSoon")}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "property",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("property")} />
      ),
      cell: ({ row }) => {
        const property = row.original.property;
        if (!property) {
          return <span className="text-muted-foreground">-</span>;
        }
        return (
          <span className="font-medium">{locale === "ar" ? property.name_ar : property.name_en}</span>
        );
      },
    },
    {
      accessorKey: "unit",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("unit")} />
      ),
      cell: ({ row }) => {
        const unit = row.original.unit;
        if (!unit) {
          return <span className="text-muted-foreground">-</span>;
        }
        return <span className="font-medium">{unit.unit_number}</span>;
      },
    },
    {
      id: "tenants",
      header: t("tenants"),
      cell: ({ row }) => {
        const contract = row.original;
        const primaryTenant = contract.tenants?.find(ct => ct.is_primary)?.tenant;
        const totalTenants = contract.tenants?.length || 0;
        
        if (totalTenants === 0) {
          return <span className="text-muted-foreground">-</span>;
        }
        
        return (
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <div className="space-y-1">
              {primaryTenant && (
                <span className="text-sm">{primaryTenant.first_name} {primaryTenant.last_name}</span>
              )}
              {totalTenants > 1 && (
                <span className="text-xs text-muted-foreground">+{totalTenants - 1} more</span>
              )}
            </div>
          </div>
        );
      },
    },
    {
      id: "period",
      header: t("period"),
      cell: ({ row }) => {
        const contract = row.original;
        return (
          <div className="text-sm">
            <div className="font-medium">{format(new Date(contract.start_date), "dd/MM/yyyy")}</div>
            <div className="text-sm text-muted-foreground">{t("to")} {format(new Date(contract.end_date), "dd/MM/yyyy")}</div>
          </div>
        );
      },
    },
    {
      accessorKey: "monthly_rent",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("monthlyRent")} />
      ),
      cell: ({ row }) => {
        const rent = row.getValue("monthly_rent") as string;
        return <span className="font-medium">{formatCurrency(rent)}</span>;
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("status")} />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        const variant = {
          DRAFT: "secondary",
          ACTIVE: "success",
          EXPIRED: "destructive",
          TERMINATED: "destructive",
          RENEWED: "outline",
        }[status] as any;
        
        return (
          <Badge variant={variant}>
            {t(`statusBadges.${status}`)}
          </Badge>
        );
      },
    },
    {
      accessorKey: "created_at",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title={t("createdAt")} />
      ),
      cell: ({ row }) => {
        const date = row.getValue("created_at") as string;
        return <span className="text-foreground">{format(new Date(date), "dd/MM/yyyy")}</span>;
      },
    },
  ];
}