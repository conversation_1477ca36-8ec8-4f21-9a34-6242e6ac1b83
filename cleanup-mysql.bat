@echo off
echo ========================================
echo MySQL Cleanup Script
echo ========================================
echo.

echo 1. Backing up current data directory...
if not exist "C:\xampp\mysql\data_backup" (
    mkdir "C:\xampp\mysql\data_backup"
)
xcopy "C:\xampp\mysql\data\property_management" "C:\xampp\mysql\data_backup\property_management\" /E /I /Y 2>nul
if %errorlevel% == 0 (
    echo ✅ Database backed up to data_backup folder
) else (
    echo ⚠️  No property_management database found to backup
)
echo.

echo 2. Removing corrupted master info files...
cd "C:\xampp\mysql\data"
del master-*.info 2>nul
del relay-log-*.info 2>nul
del master.info 2>nul
del relay-log.info 2>nul
echo ✅ Corrupted master info files removed
echo.

echo 3. Removing binary logs...
del mysql-bin.* 2>nul
del ib_logfile* 2>nul
echo ✅ Binary logs removed
echo.

echo 4. Checking for auto.cnf file...
if exist "auto.cnf" (
    del auto.cnf
    echo ✅ auto.cnf removed
) else (
    echo ⚠️  auto.cnf not found
)
echo.

echo 5. Resetting MySQL configuration...
echo [mysqld] > temp_my.ini
echo port=3306 >> temp_my.ini
echo socket=mysql >> temp_my.ini
echo key_buffer_size=16M >> temp_my.ini
echo max_allowed_packet=1M >> temp_my.ini
echo table_open_cache=64 >> temp_my.ini
echo sort_buffer_size=512K >> temp_my.ini
echo net_buffer_length=8K >> temp_my.ini
echo read_buffer_size=256K >> temp_my.ini
echo read_rnd_buffer_size=512K >> temp_my.ini
echo myisam_sort_buffer_size=8M >> temp_my.ini
echo basedir=C:/xampp/mysql >> temp_my.ini
echo datadir=C:/xampp/mysql/data >> temp_my.ini
echo skip-external-locking >> temp_my.ini
echo skip-slave-start >> temp_my.ini
echo.
echo ✅ Temporary MySQL config created
echo.

echo ========================================
echo Cleanup complete!
echo Now try starting MySQL in XAMPP
echo ========================================
pause
