// Test the complete permission flow step by step
async function testPermissionFlow() {
  try {
    console.log('=== STEP 1: Login and get token ===');
    
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'ali', password: 'password123' }),
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed:', await loginResponse.text());
      return;
    }

    console.log('✅ Login successful');
    
    // Extract token
    const setCookieHeader = loginResponse.headers.get('Set-Cookie');
    const tokenMatch = setCookieHeader?.match(/auth-token=([^;]+)/);
    if (!tokenMatch) {
      console.log('❌ No auth token in response');
      return;
    }
    
    const authToken = tokenMatch[1];
    console.log('✅ Auth token extracted:', authToken.substring(0, 30) + '...');

    console.log('\n=== STEP 2: Test /api/auth/me endpoint ===');
    
    const meResponse = await fetch('http://localhost:3000/api/auth/me', {
      headers: { 'Cookie': `auth-token=${authToken}` },
    });

    if (!meResponse.ok) {
      console.log('❌ /api/auth/me failed:', await meResponse.text());
      return;
    }

    const meData = await meResponse.json();
    console.log('✅ /api/auth/me successful');
    console.log('User:', meData.user.username);
    console.log('Permissions keys:', Object.keys(meData.user.permissions));
    
    const propertiesPerms = meData.user.permissions.properties;
    if (propertiesPerms) {
      console.log('Properties permissions:');
      console.log('  - Create:', propertiesPerms.create);
      console.log('  - Read:', propertiesPerms.read);
      console.log('  - Update:', propertiesPerms.update);
      console.log('  - Delete:', propertiesPerms.delete);
      
      const hasModuleAccess = propertiesPerms.create || propertiesPerms.read || propertiesPerms.update || propertiesPerms.delete;
      console.log('  - Has module access:', hasModuleAccess);
    } else {
      console.log('❌ No properties permissions found in response');
    }

    console.log('\n=== STEP 3: Test debug permissions endpoint ===');
    
    const debugResponse = await fetch('http://localhost:3000/api/debug/permissions', {
      headers: { 'Cookie': `auth-token=${authToken}` },
    });

    if (debugResponse.ok) {
      const debugData = await debugResponse.json();
      console.log('✅ Debug endpoint successful');
      console.log('Has properties access:', debugData.hasPropertiesAccess);
      console.log('Permission modules:', debugData.permissionModules);
    } else {
      console.log('❌ Debug endpoint failed:', await debugResponse.text());
    }

    console.log('\n=== STEP 4: Test properties API endpoint ===');
    
    const propertiesResponse = await fetch('http://localhost:3000/api/properties', {
      headers: { 'Cookie': `auth-token=${authToken}` },
    });

    console.log('Properties API status:', propertiesResponse.status);
    if (!propertiesResponse.ok) {
      const errorText = await propertiesResponse.text();
      console.log('Properties API error:', errorText);
    } else {
      console.log('✅ Properties API accessible');
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testPermissionFlow();