import { notFound } from "next/navigation";
import { <PERSON>adata } from "next";
import { getTranslations } from "next-intl/server";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  ArrowLeft, 
  CheckCircle, 
  DollarSign, 
  FileText,
  Ban,
  Calendar,
  Building,
  User
} from "lucide-react";
import { formatOMR } from "@/lib/format";

async function getOwnerPayout(id: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_APP_URL}/api/owner-payouts/${id}`,
      {
        cache: "no-store",
      }
    );

    if (!response.ok) {
      return null;
    }

    const result = await response.json();
    return result.success ? result.data : null;
  } catch (error) {
    console.error("Error fetching owner payout:", error);
    return null;
  }
}

interface OwnerPayoutViewPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

export async function generateMetadata({
  params,
}: OwnerPayoutViewPageProps): Promise<Metadata> {
  const { id, locale } = await params;
  const payout = await getOwnerPayout(id);
  const t = await getTranslations({ locale, namespace: "ownerPayouts" });
  
  return {
    title: payout ? `${t("payoutNumber")} - ${payout.payout_number}` : t("notFound"),
    description: payout
      ? t("viewDescription")
      : t("notFound"),
  };
}

export default async function OwnerPayoutViewPage({
  params,
}: OwnerPayoutViewPageProps) {
  const { id, locale } = await params;
  const payout = await getOwnerPayout(id);
  const t = await getTranslations({ locale });

  if (!payout) {
    notFound();
  }

  return <ViewOwnerPayoutContent payout={payout} locale={locale} t={t} />;
}

function ViewOwnerPayoutContent({ payout, locale, t }: { payout: any; locale: string; t: any }) {

  const statusConfig = {
    PENDING: { variant: "secondary" as const, icon: FileText },
    APPROVED: { variant: "default" as const, icon: CheckCircle },
    PAID: { variant: "default" as const, icon: DollarSign },
    CANCELLED: { variant: "destructive" as const, icon: Ban },
  };

  const config = statusConfig[payout.status as keyof typeof statusConfig] || statusConfig.PENDING;
  const StatusIcon = config.icon;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            asChild
          >
            <Link href={`/${locale}/dashboard/owner-payouts`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("common.back")}
            </Link>
          </Button>
        </div>
        <Badge variant={config.variant} className="gap-1">
          <StatusIcon className="h-3 w-3" />
          {t(`ownerPayouts.status.${payout.status.toLowerCase()}`)}
        </Badge>
      </div>

      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {payout.payout_number}
        </h1>
        <p className="text-muted-foreground">
          {t("ownerPayouts.viewDescription")}
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Owner Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {t("ownerPayouts.ownerInformation")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("ownerPayouts.ownerName")}
              </label>
              <p className="mt-1 font-medium">
                {payout.owner?.name_en} - {payout.owner?.name_ar}
              </p>
            </div>
            {payout.owner?.email && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t("ownerPayouts.email")}
                </label>
                <p className="mt-1">{payout.owner.email}</p>
              </div>
            )}
            {payout.owner?.phone && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t("ownerPayouts.phone")}
                </label>
                <p className="mt-1">{payout.owner.phone}</p>
              </div>
            )}
            {payout.owner?.bank_name && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t("ownerPayouts.bankDetails")}
                </label>
                <p className="mt-1">
                  {payout.owner.bank_name}
                  {payout.owner.bank_account_number && (
                    <span className="block text-sm text-muted-foreground">
                      {payout.owner.bank_account_number}
                    </span>
                  )}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Payout Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {t("ownerPayouts.payoutDetails")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("ownerPayouts.payoutDate")}
              </label>
              <p className="mt-1">
                {new Date(payout.payout_date).toLocaleDateString()}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("ownerPayouts.period")}
              </label>
              <p className="mt-1">
                {new Date(payout.period_start).toLocaleDateString()} - {new Date(payout.period_end).toLocaleDateString()}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("ownerPayouts.paymentMethod")}
              </label>
              <p className="mt-1">
                {t(`common.paymentMethods.${payout.payment_method.toLowerCase()}`)}
              </p>
            </div>
            {payout.reference_number && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t("ownerPayouts.referenceNumber")}
                </label>
                <p className="mt-1">{payout.reference_number}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Property Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            {t("ownerPayouts.propertyBreakdown")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("ownerPayouts.property")}</TableHead>
                <TableHead className="text-right">{t("ownerPayouts.rentCollected")}</TableHead>
                <TableHead className="text-right">{t("ownerPayouts.managementFee")}</TableHead>
                <TableHead className="text-right">{t("ownerPayouts.netAmount")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payout.payout_details?.map((detail: any) => (
                <TableRow key={detail.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{detail.property?.name_en}</div>
                      <div className="text-sm text-muted-foreground">{detail.property?.address_en}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    {formatOMR(detail.rent_collected)}
                  </TableCell>
                  <TableCell className="text-right text-muted-foreground">
                    -{formatOMR(detail.management_fee)}
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {formatOMR(detail.net_amount)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Financial Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            {t("ownerPayouts.financialSummary")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("ownerPayouts.totalRentCollected")}
              </label>
              <p className="mt-1 text-lg font-medium">
                {formatOMR(payout.total_rent_collected)}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("ownerPayouts.totalManagementFee")}
              </label>
              <p className="mt-1 text-lg font-medium text-muted-foreground">
                -{formatOMR(payout.management_fee)}
              </p>
            </div>
          </div>
          
          {payout.other_deductions > 0 && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {t("ownerPayouts.otherDeductions")}
              </label>
              <p className="mt-1 text-lg font-medium text-muted-foreground">
                -{formatOMR(payout.other_deductions)}
              </p>
            </div>
          )}

          <Separator />

          <div>
            <label className="text-sm font-medium text-muted-foreground">
              {t("ownerPayouts.netPayoutAmount")}
            </label>
            <p className="mt-1 text-2xl font-bold text-primary">
              {formatOMR(payout.net_amount)}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Status History */}
      {(payout.approved_at || payout.paid_at) && (
        <Card>
          <CardHeader>
            <CardTitle>{t("ownerPayouts.statusHistory")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full" />
                <div className="flex-1">
                  <p className="text-sm font-medium">{t("ownerPayouts.created")}</p>
                  <p className="text-xs text-muted-foreground">
                    {new Date(payout.created_at).toLocaleString()} 
                    {payout.creator && ` by ${payout.creator.first_name} ${payout.creator.last_name}`}
                  </p>
                </div>
              </div>

              {payout.approved_at && (
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">{t("ownerPayouts.approved")}</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(payout.approved_at).toLocaleString()} 
                      {payout.approver && ` by ${payout.approver.first_name} ${payout.approver.last_name}`}
                    </p>
                  </div>
                </div>
              )}

              {payout.paid_at && (
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">{t("ownerPayouts.paid")}</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(payout.paid_at).toLocaleString()} 
                      {payout.payer && ` by ${payout.payer.first_name} ${payout.payer.last_name}`}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notes */}
      {payout.notes && (
        <Card>
          <CardHeader>
            <CardTitle>{t("ownerPayouts.notes")}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="whitespace-pre-wrap">{payout.notes}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}