import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { getTranslations } from "next-intl/server";

import { Button } from "@/components/ui/button";
import { RoleForm } from "../_components/role-form";

interface CreateRolePageProps {
  params: Promise<{
    locale: string;
  }>;
}

export default async function CreateRolePage({ params }: CreateRolePageProps) {
  const { locale } = await params;
  const t = await getTranslations("roles");
  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${locale}/dashboard/roles`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("backToRoles")}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("createRole")}</h1>
          <p className="text-muted-foreground">{t("createDescription")}</p>
        </div>
      </div>

      <RoleForm mode="create" />
    </div>
  );
}
