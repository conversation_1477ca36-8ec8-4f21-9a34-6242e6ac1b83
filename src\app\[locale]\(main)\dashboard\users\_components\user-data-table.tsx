"use client";

import { useState, useEffect } from "react";
import { useDataTableInstance } from "@/hooks/use-data-table-instance";
import { DataTable } from "@/components/data-table/data-table";
import { DataTablePagination } from "@/components/data-table/data-table-pagination";
import { DataTableViewOptions } from "@/components/data-table/data-table-view-options";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Search, Filter, Loader2 } from "lucide-react";
import { toast } from "sonner";
import type { UserWithRoles, UserFilters, Role } from "@/types/user";
import { USER_STATUS_VALUES } from "@/types/user";

import { userColumns } from "./user-columns";

export function UserDataTable() {
  const [users, setUsers] = useState<UserWithRoles[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters state
  const [filters, setFilters] = useState<UserFilters>({
    search: "",
    status: undefined,
    role_id: undefined,
    page: 1,
    pageSize: 10,
    sortBy: "created_at",
    sortOrder: "desc",
  });

  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  // Initialize data table
  const table = useDataTableInstance({
    data: users,
    columns: userColumns,
    defaultPageIndex: pagination.page - 1,
    defaultPageSize: pagination.pageSize,
  });

  // Fetch users
  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const searchParams = new URLSearchParams();
      if (filters.search) searchParams.set("search", filters.search);
      if (filters.status) searchParams.set("status", filters.status);
      if (filters.role_id) searchParams.set("role_id", filters.role_id.toString());
      searchParams.set("page", filters.page.toString());
      searchParams.set("pageSize", filters.pageSize.toString());
      searchParams.set("sortBy", filters.sortBy);
      searchParams.set("sortOrder", filters.sortOrder);

      const response = await fetch(`/api/users?${searchParams}`);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: "Failed to fetch users" }));
        console.error("Users API error:", response.status, errorData);
        throw new Error(errorData.error || "Failed to fetch users");
      }

      const data = await response.json();
      setUsers(data.users);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching users:", error);
      setError("Failed to load users. Please try again.");
      toast.error("Failed to load users");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch roles for filter dropdown
  const fetchRoles = async () => {
    try {
      const response = await fetch("/api/roles?pageSize=100");
      if (response.ok) {
        const data = await response.json();
        setRoles(data.roles);
      }
    } catch (error) {
      console.error("Error fetching roles:", error);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, []);

  // Refetch when filters change (but not on initial mount)
  useEffect(() => {
    if (!isLoading) {
      fetchUsers();
    }
  }, [filters.search, filters.status, filters.role_id, filters.page, filters.pageSize, filters.sortBy, filters.sortOrder]);

  // Handle search
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }));
  };

  // Handle status filter
  const handleStatusFilter = (value: string) => {
    setFilters(prev => ({ 
      ...prev, 
      status: value === "all" ? undefined : value as any,
      page: 1 
    }));
  };

  // Handle role filter
  const handleRoleFilter = (value: string) => {
    setFilters(prev => ({ 
      ...prev, 
      role_id: value === "all" ? undefined : parseInt(value),
      page: 1 
    }));
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setFilters({
      search: "",
      status: undefined,
      role_id: undefined,
      page: 1,
      pageSize: 10,
      sortBy: "created_at",
      sortOrder: "desc",
    });
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={fetchUsers}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-9"
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearFilters}
            className="shrink-0"
          >
            <Filter className="mr-2 h-4 w-4" />
            Clear
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <Select value={filters.status || "all"} onValueChange={handleStatusFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              {USER_STATUS_VALUES.map((status) => (
                <SelectItem key={status} value={status}>
                  {status.toLowerCase().replace("_", " ")}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.role_id?.toString() || "all"} onValueChange={handleRoleFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="All Roles" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              {roles.map((role) => (
                <SelectItem key={role.id} value={role.id.toString()}>
                  {role.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <DataTableViewOptions table={table} />
        </div>
      </div>

      {/* Data Table */}
      <div className="rounded-md border overflow-hidden">
        <div className="w-full overflow-x-auto [&_[data-slot=table-container]]:overflow-x-visible">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading users...</span>
            </div>
          ) : (
            <DataTable table={table} columns={userColumns} />
          )}
        </div>
      </div>

      {/* Pagination */}
      {!isLoading && <DataTablePagination table={table} />}
    </div>
  );
}
