import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";
import { Decimal } from "@prisma/client/runtime/library";

// GET /api/owners-associations/[id]/reports - Generate reports for an association
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await hasPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view association reports");
    }

    const { id: idParam } = await params;
    const associationId = parseInt(idParam);

    if (isNaN(associationId)) {
      return ApiResponseBuilder.error("Invalid association ID", "INVALID_ID", 400);
    }

    // Check if association exists
    const association = await db.ownersAssociation.findUnique({
      where: { id: associationId },
      include: {
        property: {
          select: {
            name_en: true,
            name_ar: true,
            address_en: true,
            address_ar: true,
          },
        },
      },
    });

    if (!association) {
      return ApiResponseBuilder.notFound("Owners association not found");
    }

    const searchParams = request.nextUrl.searchParams;
    const reportType = searchParams.get("type") || "financial_summary";
    const dateFrom = searchParams.get("date_from");
    const dateTo = searchParams.get("date_to");

    // Set default date range if not provided
    const endDate = dateTo ? new Date(dateTo) : new Date();
    const startDate = dateFrom ? new Date(dateFrom) : new Date(endDate.getFullYear(), endDate.getMonth() - 3, 1);

    let reportData: any = {
      association,
      generated_at: new Date().toISOString(),
      period: {
        from: startDate.toISOString(),
        to: endDate.toISOString(),
      },
    };

    switch (reportType) {
      case "financial_summary":
        // Get financial summary
        const [expenses, income, payments] = await Promise.all([
          db.associationTransaction.aggregate({
            where: {
              association_id: associationId,
              type: "EXPENSE",
              transaction_date: {
                gte: startDate,
                lte: endDate,
              },
            },
            _sum: { amount: true },
            _count: { _all: true },
          }),
          db.associationTransaction.aggregate({
            where: {
              association_id: associationId,
              type: "INCOME",
              transaction_date: {
                gte: startDate,
                lte: endDate,
              },
            },
            _sum: { amount: true },
            _count: { _all: true },
          }),
          db.subscriptionPayment.aggregate({
            where: {
              subscription: {
                association_id: associationId,
              },
              payment_date: {
                gte: startDate,
                lte: endDate,
              },
              status: "PAID",
            },
            _sum: { amount: true },
            _count: { _all: true },
          }),
        ]);

        const totalExpenses = expenses._sum.amount || new Decimal(0);
        const totalIncome = income._sum.amount || new Decimal(0);
        const totalPayments = payments._sum.amount || new Decimal(0);
        const netBalance = new Decimal(totalIncome.toString()).plus(totalPayments.toString()).minus(totalExpenses.toString());

        reportData.financial_summary = {
          total_expenses: totalExpenses.toString(),
          expense_count: expenses._count._all,
          total_income: totalIncome.toString(),
          income_count: income._count._all,
          total_subscription_payments: totalPayments.toString(),
          payment_count: payments._count._all,
          net_balance: netBalance.toString(),
        };

        // Get expense breakdown by category
        const expensesByCategory = await db.associationTransaction.groupBy({
          by: ["category"],
          where: {
            association_id: associationId,
            type: "EXPENSE",
            transaction_date: {
              gte: startDate,
              lte: endDate,
            },
          },
          _sum: { amount: true },
          _count: { _all: true },
        });

        reportData.expense_breakdown = expensesByCategory.map(item => ({
          category: item.category,
          amount: item._sum.amount?.toString() || "0",
          count: item._count._all,
        }));

        break;

      case "member_payments":
        // Get member payment status
        const members = await db.associationMember.findMany({
          where: { association_id: associationId },
          include: {
            subscription_payments: {
              where: {
                due_date: {
                  gte: startDate,
                  lte: endDate,
                },
              },
              include: {
                subscription: {
                  select: {
                    name_en: true,
                    name_ar: true,
                  },
                },
              },
            },
          },
        });

        reportData.members = members.map(member => {
          const totalDue = member.subscription_payments.reduce((sum, payment) => {
            return sum + parseFloat(payment.amount.toString());
          }, 0);

          const totalPaid = member.subscription_payments
            .filter(p => p.status === "PAID" || p.status === "PARTIALLY_PAID")
            .reduce((sum, payment) => {
              return sum + parseFloat(payment.amount.toString());
            }, 0);

          const overdue = member.subscription_payments.filter(p => p.status === "OVERDUE").length;

          return {
            id: member.id,
            full_name: member.full_name,
            unit_number: member.unit_number,
            ownership_percentage: member.ownership_percentage?.toString() || "0",
            total_due: totalDue.toFixed(3),
            total_paid: totalPaid.toFixed(3),
            outstanding: (totalDue - totalPaid).toFixed(3),
            overdue_count: overdue,
            payment_count: member.subscription_payments.length,
          };
        });

        break;

      case "transaction_details":
        // Get detailed transaction list
        const transactions = await db.associationTransaction.findMany({
          where: {
            association_id: associationId,
            transaction_date: {
              gte: startDate,
              lte: endDate,
            },
          },
          orderBy: {
            transaction_date: "desc",
          },
          include: {
            creator: {
              select: {
                first_name: true,
                last_name: true,
              },
            },
          },
        });

        reportData.transactions = transactions.map(transaction => ({
          id: transaction.id,
          date: transaction.transaction_date,
          type: transaction.type,
          category: transaction.category,
          description: transaction.description,
          amount: transaction.amount.toString(),
          payment_method: transaction.payment_method,
          created_by: transaction.creator ? `${transaction.creator.first_name} ${transaction.creator.last_name}` : null,
        }));

        break;

      default:
        return ApiResponseBuilder.error("Invalid report type", "INVALID_REPORT_TYPE", 400);
    }

    return ApiResponseBuilder.success(reportData);
  } catch (error: any) {
    console.error("Error generating report:", error);
    return ApiResponseBuilder.error("Failed to generate report", "INTERNAL_ERROR", 500);
  }
}