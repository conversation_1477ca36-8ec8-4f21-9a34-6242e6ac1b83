import { NextRequest, NextResponse } from "next/server";
import { writeFile, mkdir } from "fs/promises";
import { join } from "path";
import { verifyToken } from "@/lib/auth";

// POST /api/upload/logo - Upload company logo
export async function POST(request: NextRequest) {
  try {
    console.log("Logo Upload API: POST request received");

    // Get token from cookie
    const token = request.cookies.get("auth-token")?.value;

    if (!token) {
      console.log("Logo Upload API: No token found in cookies");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify token
    const decoded = verifyToken(token);
    if (!decoded) {
      console.log("Logo Upload API: Token verification failed");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("Logo Upload API: User authenticated:", decoded.username);

    // Parse form data
    const formData = await request.formData();
    const file = formData.get("logo") as File;

    if (!file) {
      return NextResponse.json(
        { error: "No file uploaded" },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: "Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed." },
        { status: 400 }
      );
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File size too large. Maximum size is 5MB." },
        { status: 400 }
      );
    }

    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), "public", "uploads", "logos");
    try {
      await mkdir(uploadsDir, { recursive: true });
    } catch (error) {
      console.log("Directory already exists or created");
    }

    // Generate unique filename
    const timestamp = Date.now();
    const fileExtension = file.name.split(".").pop();
    const fileName = `logo-${timestamp}.${fileExtension}`;
    const filePath = join(uploadsDir, fileName);

    // Convert file to buffer and save
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Return the public URL
    const logoUrl = `/uploads/logos/${fileName}`;

    console.log("Logo Upload API: File uploaded successfully:", logoUrl);

    return NextResponse.json({
      success: true,
      data: {
        logo_url: logoUrl,
        filename: fileName,
      },
      message: "Logo uploaded successfully",
    });
  } catch (error) {
    console.error("Logo Upload API Error:", error);
    return NextResponse.json(
      { error: "Failed to upload logo" },
      { status: 500 }
    );
  }
}
