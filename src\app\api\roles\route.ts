import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { roleFormSchema, type RoleFilters } from "@/types/user";

// GET /api/roles - List roles with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const filters: RoleFilters = {
      search: searchParams.get("search") || undefined,
      is_system: searchParams.get("is_system") ? searchParams.get("is_system") === "true" : undefined,
      page: parseInt(searchParams.get("page") || "1"),
      pageSize: parseInt(searchParams.get("pageSize") || "10"),
      sortBy: searchParams.get("sortBy") || "created_at",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
    };

    // Build where clause
    const where: any = {};
    
    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search } },
        { description: { contains: filters.search } },
      ];
    }
    
    if (filters.is_system !== undefined) {
      where.is_system = filters.is_system;
    }

    // Calculate pagination
    const skip = (filters.page - 1) * filters.pageSize;
    const take = filters.pageSize;

    // Build orderBy
    const orderBy: any = {};
    orderBy[filters.sortBy] = filters.sortOrder;

    // Get roles with permissions and user count
    const [roles, totalCount] = await Promise.all([
      db.role.findMany({
        where,
        skip,
        take,
        orderBy,
        include: {
          role_permissions: {
            include: {
              permission: true,
            },
          },
          _count: {
            select: {
              user_roles: true,
            },
          },
        },
      }),
      db.role.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / filters.pageSize);

    return NextResponse.json({
      roles,
      pagination: {
        page: filters.page,
        pageSize: filters.pageSize,
        totalCount,
        totalPages,
        hasNext: filters.page < totalPages,
        hasPrev: filters.page > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching roles:", error);
    return NextResponse.json(
      { error: "Failed to fetch roles" },
      { status: 500 }
    );
  }
}

// POST /api/roles - Create a new role
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validatedData = roleFormSchema.parse(body);

    // Check if role name already exists
    const existingRole = await db.role.findUnique({
      where: { name: validatedData.name },
    });

    if (existingRole) {
      return NextResponse.json(
        { error: "A role with this name already exists" },
        { status: 400 }
      );
    }

    // Create the role
    const role = await db.role.create({
      data: {
        name: validatedData.name,
        description: validatedData.description || null,
        is_system: false, // User-created roles are never system roles
      },
      include: {
        role_permissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    // Create permissions for the role
    if (validatedData.permissions && validatedData.permissions.length > 0) {
      for (const modulePermission of validatedData.permissions) {
        for (const action of modulePermission.actions) {
          // Find or create permission
          const permission = await db.permission.upsert({
            where: {
              module_action: {
                module: modulePermission.module,
                action: action,
              },
            },
            update: {},
            create: {
              module: modulePermission.module,
              action: action,
              description: `${action.toLowerCase()} access for ${modulePermission.module}`,
            },
          });

          // Create role permission
          await db.rolePermission.create({
            data: {
              role_id: role.id,
              permission_id: permission.id,
            },
          });
        }
      }
    }

    // Fetch the complete role with permissions
    const completeRole = await db.role.findUnique({
      where: { id: role.id },
      include: {
        role_permissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    return NextResponse.json(completeRole, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating role:", error);
    return NextResponse.json(
      { error: "Failed to create role" },
      { status: 500 }
    );
  }
}
