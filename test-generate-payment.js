const { PrismaClient } = require('./src/generated/prisma');
const prisma = new PrismaClient();
const fetch = require('node-fetch');
const jwt = require('jsonwebtoken');

async function testGeneratePayment() {
  try {
    // Get admin user
    const adminUser = await prisma.user.findFirst({
      where: { username: 'admin' }
    });
    
    if (!adminUser) {
      console.log('Admin user not found!');
      return;
    }
    
    // Create JWT token
    const token = jwt.sign(
      { id: adminUser.id, username: adminUser.username, email: adminUser.email },
      process.env.JWT_SECRET || 'your-secret-key-change-in-production',
      { expiresIn: '7d' }
    );
    
    // Get first association with subscription and member
    const association = await prisma.ownersAssociation.findFirst({
      include: {
        subscriptions: { where: { is_active: true } },
        members: true
      }
    });
    
    if (!association || association.subscriptions.length === 0 || association.members.length === 0) {
      console.log('No suitable association found with active subscriptions and members');
      await prisma.$disconnect();
      return;
    }
    
    const subscription = association.subscriptions[0];
    const member = association.members[0];
    
    console.log('Association:', association.name_en);
    console.log('Subscription:', subscription.name_en, '- Amount:', subscription.amount);
    console.log('Member:', member.full_name, '- Unit:', member.unit_number);
    
    // Test creating a subscription payment
    const payload = {
      subscription_id: subscription.id,
      member_id: member.id,
      amount_due: subscription.amount.toString(),
      amount_paid: "0",
      due_date: "2025-01-05",
      status: "UNPAID",
      payment_method: null,
      payment_date: "2025-01-05",
      notes: "Test payment generation",
      reference_number: null
    };
    
    console.log('\nPayload:', JSON.stringify(payload, null, 2));
    
    const response = await fetch(`http://localhost:3000/api/owners-associations/${association.id}/subscription-payments`, {
      method: 'POST',
      headers: {
        'Cookie': `auth-token=${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(payload)
    });
    
    console.log('\nResponse Status:', response.status);
    
    const result = await response.json();
    console.log('Response:', JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testGeneratePayment();