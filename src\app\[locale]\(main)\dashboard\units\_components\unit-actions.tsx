"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { More<PERSON><PERSON><PERSON><PERSON>, Edit, Trash, Eye } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { apiClient } from "@/lib/api-client";
import type { UnitWithRelations } from "@/types/unit";

interface UnitActionsProps {
  unit: UnitWithRelations & { rent_amount: string; area: string | null };
  locale: string;
  onRefresh?: () => void;
  translations: {
    openMenu: string;
    viewUnit: string;
    editUnit: string;
    deleteUnit: string;
  };
}

export function UnitActions({ unit, locale, onRefresh, translations }: UnitActionsProps) {
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleView = () => {
    router.push(`/${locale}/dashboard/units/${unit.id}`);
  };

  const handleEdit = () => {
    router.push(`/${locale}/dashboard/units/${unit.id}/edit`);
  };

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await apiClient.delete(`/api/units/${unit.id}`);
      
      toast.success("Unit deleted successfully");
      setShowDeleteDialog(false);
      
      if (onRefresh) {
        onRefresh();
      }
    } catch (error: any) {
      console.error("Delete error:", error);
      toast.error(error.message || "Failed to delete unit");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">{translations.openMenu}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem onClick={handleView}>
            <Eye className="mr-2 h-4 w-4" />
            {translations.viewUnit}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" />
            {translations.editUnit}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => setShowDeleteDialog(true)}
            className="text-destructive"
            disabled={unit.status === "RENTED"}
          >
            <Trash className="mr-2 h-4 w-4" />
            {translations.deleteUnit}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Unit</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete unit <strong>{unit.unit_number}</strong>?
              {unit.status === "RENTED" && (
                <>
                  <br />
                  <br />
                  <span className="text-destructive">
                    This unit is currently rented and cannot be deleted.
                  </span>
                </>
              )}
              {unit.status !== "RENTED" && (
                <>
                  <br />
                  <br />
                  This action cannot be undone.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting || unit.status === "RENTED"}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}