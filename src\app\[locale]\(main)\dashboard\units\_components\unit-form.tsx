"use client";

import { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useTranslations, useLocale } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { Loader2, ChevronDown, ChevronUp } from "lucide-react";
import { apiClient } from "@/lib/api-client";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { unitSchema, type UnitInput, type UnitWithRelations } from "@/types/unit";
import { AmenitySelector } from "../../properties/_components/amenity-selector";
import type { PropertyWithRelations } from "@/types/property";

interface UnitFormProps {
  unit?: UnitWithRelations;
  properties?: PropertyWithRelations[];
  amenities?: any[];
  isEdit?: boolean;
  propertyId?: number;
}

export function UnitForm({ unit, properties: propProperties, amenities: propAmenities, isEdit = false, propertyId }: UnitFormProps) {
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale();
  const t = useTranslations("units.form");
  const tButtons = useTranslations("units.form.buttons");
  const tValidation = useTranslations("units.form.validation");
  const tStatus = useTranslations("units.status");
  const tMessages = useTranslations("units.messages");
  const tSections = useTranslations("units.form.sections");
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [properties, setProperties] = useState<PropertyWithRelations[]>(propProperties || []);
  const [loadingProperties, setLoadingProperties] = useState(!propProperties);
  const [selectedAmenities, setSelectedAmenities] = useState<number[]>(
    unit?.amenities?.map(a => a.id) || []
  );
  const [isBasicInfoOpen, setIsBasicInfoOpen] = useState(!propertyId);

  const form = useForm<UnitInput>({
    resolver: zodResolver(unitSchema),
    defaultValues: {
      property_id: unit?.property_id || propertyId || 0,
      unit_number: unit?.unit_number || "",
      unit_name_en: unit?.unit_name_en || "",
      unit_name_ar: unit?.unit_name_ar || "",
      floor_number: unit?.floor_number ?? 0,
      rooms_count: unit?.rooms_count ?? 0,
      majalis_count: unit?.majalis_count ?? 0,
      bathrooms_count: unit?.bathrooms_count ?? 0,
      area: unit?.area?.toString() || "",
      rent_amount: unit?.rent_amount?.toString() || "",
      status: unit?.status || "AVAILABLE",
      description_en: unit?.description_en || "",
      description_ar: unit?.description_ar || "",
    },
  });

  // Fetch properties
  useEffect(() => {
    if (propProperties) return; // Skip if already provided
    
    const fetchProperties = async () => {
      try {
        setLoadingProperties(true);
        const result = await apiClient.get("/api/properties");
        
        if (result.success) {
          setProperties(result.data);
        }
      } catch (error) {
        console.error("Error fetching properties:", error);
        toast.error(tMessages('loadPropertiesError'));
      } finally {
        setLoadingProperties(false);
      }
    };

    fetchProperties();
  }, [propProperties]);

  const onSubmit = async (data: UnitInput) => {
    try {
      setIsSubmitting(true);

      const url = isEdit
        ? `/api/units/${unit?.id}`
        : "/api/units";
      
      // Add amenity_ids to the request
      const requestData = {
        ...data,
        amenity_ids: selectedAmenities,
      };
      
      console.log("Submitting unit data:", { url, requestData, isEdit });
      
      const result = isEdit
        ? await apiClient.put(url, requestData)
        : await apiClient.post(url, requestData);
        
      console.log("Unit submit result:", result);

      toast.success(
        isEdit
          ? tMessages('updateSuccess')
          : tMessages('createSuccess')
      );

      // Get the locale from the current pathname
      const pathSegments = pathname.split('/');
      const currentLocale = pathSegments[1] || locale;
      
      console.log("Navigation debug:", {
        pathname,
        pathSegments,
        currentLocale,
        targetPath: `/${currentLocale}/dashboard/units`
      });
      
      // Navigate back to property page if coming from property, otherwise to units list
      if (propertyId && !isEdit) {
        router.push(`/${currentLocale}/dashboard/properties/${data.property_id}`);
      } else {
        router.push(`/${currentLocale}/dashboard/units`);
      }
      router.refresh();
    } catch (error) {
      console.error("Submit error:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : isEdit ? tMessages('updateError') : tMessages('createError')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Collapsible open={isBasicInfoOpen} onOpenChange={setIsBasicInfoOpen}>
          <Card>
            <CardHeader>
              <CollapsibleTrigger asChild>
                <Button 
                  type="button" 
                  variant="ghost" 
                  className="flex items-center justify-between w-full p-0 hover:bg-transparent"
                >
                  <div className="flex-1 text-left">
                    <CardTitle className="text-base">{tSections('basicInformation')}</CardTitle>
                    <CardDescription className="mt-1">
                      {propertyId ? tSections('propertyPreselected') : tSections('basicInformationDescription')}
                    </CardDescription>
                  </div>
                  {isBasicInfoOpen ? <ChevronUp className="h-4 w-4 text-muted-foreground" /> : <ChevronDown className="h-4 w-4 text-muted-foreground" />}
                </Button>
              </CollapsibleTrigger>
            </CardHeader>
            <CollapsibleContent>
              <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="property_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("property")}</FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      defaultValue={field.value?.toString()}
                      disabled={isSubmitting || loadingProperties || isEdit || !!propertyId}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("selectProperty")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {properties.map((property) => (
                          <SelectItem key={property.id} value={property.id.toString()}>
                            {locale === "ar" ? property.name_ar : property.name_en}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                    {propertyId && (
                      <FormDescription>
                        {t("propertyPreselectedDescription")}
                      </FormDescription>
                    )}
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="unit_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("unitNumber")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        value={field.value ?? ""}
                        placeholder={t("unitNumberPlaceholder")}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="unit_name_en"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("unitNameEn")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        value={field.value ?? ""}
                        placeholder={t("unitNameEnPlaceholder")}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="unit_name_ar"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("unitNameAr")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        value={field.value ?? ""}
                        placeholder={t("unitNameArPlaceholder")}
                        disabled={isSubmitting}
                        dir="rtl"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("status")}</FormLabel>
                    <Select 
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("selectStatus")} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="AVAILABLE">{tStatus("available")}</SelectItem>
                        <SelectItem value="RENTED">{tStatus("rented")}</SelectItem>
                        <SelectItem value="UNDER_MAINTENANCE">{tStatus("underMaintenance")}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="floor_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("floorNumber")}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder={t("floorNumberPlaceholder")}
                        disabled={isSubmitting}
                        value={field.value === 0 || field.value === null ? "" : field.value}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
              </CardContent>
            </CollapsibleContent>
          </Card>
        </Collapsible>

        <Card>
          <CardHeader>
            <CardTitle>{tSections('unitDetails')}</CardTitle>
            <CardDescription>
              {tSections('unitDetailsDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <FormField
                control={form.control}
                name="rooms_count"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("roomsCount")}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder={t("roomsCountPlaceholder")}
                        disabled={isSubmitting}
                        value={field.value === 0 || field.value === null ? "" : field.value}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="majalis_count"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("majalisCount")}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder={t("majalisCountPlaceholder")}
                        disabled={isSubmitting}
                        value={field.value === 0 || field.value === null ? "" : field.value}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="bathrooms_count"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("bathroomsCount")}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder={t("bathroomsCountPlaceholder")}
                        disabled={isSubmitting}
                        value={field.value === 0 || field.value === null ? "" : field.value}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="area"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("area")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        value={field.value ?? ""}
                        type="text"
                        placeholder={t("areaPlaceholder")}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      {t("areaDescription")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="rent_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("rentAmount")}</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        value={field.value ?? ""}
                        type="text"
                        placeholder={t("rentAmountPlaceholder")}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      {t("rentAmountDescription")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="description_en"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("descriptionEn")}</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        value={field.value ?? ""}
                        placeholder={t("descriptionEnPlaceholder")}
                        disabled={isSubmitting}
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description_ar"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("descriptionAr")}</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        value={field.value ?? ""}
                        placeholder={t("descriptionArPlaceholder")}
                        disabled={isSubmitting}
                        dir="rtl"
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{tSections('amenities')}</CardTitle>
            <CardDescription>
              {tSections('amenitiesDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AmenitySelector
              selectedIds={selectedAmenities}
              onChange={setSelectedAmenities}
              disabled={isSubmitting}
            />
          </CardContent>
        </Card>

        <div className="flex items-center justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            {tButtons("cancel")}
          </Button>
          <Button type="submit" disabled={isSubmitting || loadingProperties}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEdit ? tButtons("updating") : tButtons("creating")}
              </>
            ) : (
              <>{isEdit ? tButtons("update") : tButtons("create")}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}