import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";

import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { tenantUpdateSchema } from "@/types/tenant";



interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/tenants/:id - Get a single tenant
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for tenants
    // Check permissions
    const userPermissions = await getUserPermissions(decoded.id);
    const canRead = hasPermission(userPermissions, "tenants", "read");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view tenants");
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.badRequest("Invalid tenant ID");
    }

    const tenant = await db.tenant.findUnique({
      where: { id },
      include: {
        documents: {
          include: {
            uploader: {
              select: {
                id: true,
                username: true,
                first_name: true,
                last_name: true,
              },
            },
          },
        },
        emergency_contacts: {
          orderBy: {
            is_primary: "desc",
          },
        },
        creator: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
        contracts: {
          include: {
            contract: {
              select: {
                id: true,
                status: true,
              },
            },
          },
        },
        _count: {
          select: {
            documents: true,
          },
        },
      },
    });

    if (!tenant) {
      return ApiResponseBuilder.notFound("Tenant");
    }

    return ApiResponseBuilder.success(tenant);
  } catch (error) {
    console.error("Get Tenant Error:", error);
    return ApiResponseBuilder.error(
      "Failed to fetch tenant",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// PUT /api/tenants/:id - Update a tenant
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
        // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has UPDATE permission for tenants
    const userPermissions = await getUserPermissions(decoded.id);
    const canUpdate = hasPermission(userPermissions, "tenants", "update");
    if (!canUpdate) {
      return ApiResponseBuilder.forbidden("You don't have permission to update tenants");
    }
    
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.badRequest("Invalid tenant ID");
    }

    const body = await request.json();
    
    // Validate the request body
    const validationResult = tenantUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return ApiResponseBuilder.validationError(validationResult.error);
    }

    const validatedData = validationResult.data;

    // Check if tenant exists
    const existing = await db.tenant.findUnique({
      where: { id },
    });

    if (!existing) {
      return ApiResponseBuilder.notFound("Tenant");
    }

    // Check if email already exists (excluding current tenant)
    if (validatedData.email && validatedData.email !== existing.email) {
      const emailExists = await db.tenant.findUnique({
        where: { email: validatedData.email },
      });

      if (emailExists) {
        return ApiResponseBuilder.conflict(
          "A tenant with this email already exists",
          { email: validatedData.email },
          request.url
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      ...validatedData,
      updated_by: decoded.id,
    };

    // Convert date strings to Date objects if provided
    if (validatedData.national_id_expiry) {
      updateData.national_id_expiry = new Date(validatedData.national_id_expiry);
    }
    if (validatedData.date_of_birth) {
      updateData.date_of_birth = new Date(validatedData.date_of_birth);
    }

    // Update the tenant
    const tenant = await db.tenant.update({
      where: { id },
      data: updateData,
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    return ApiResponseBuilder.success(tenant);
  } catch (error) {
    console.error("Update Tenant Error:", error);
    return ApiResponseBuilder.error(
      "Failed to update tenant",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}

// DELETE /api/tenants/:id - Delete a tenant
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
        // Check authentication and permissions
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has DELETE permission for tenants
    const userPermissions = await getUserPermissions(decoded.id);
    const canDelete = hasPermission(userPermissions, "tenants", "delete");
    if (!canDelete) {
      return ApiResponseBuilder.forbidden("You don't have permission to delete tenants");
    }
    
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return ApiResponseBuilder.badRequest("Invalid tenant ID");
    }

    // Check if tenant exists
    const existing = await db.tenant.findUnique({
      where: { id },
    });

    if (!existing) {
      return ApiResponseBuilder.notFound("Tenant");
    }

    // TODO: Check if tenant has active contracts before deletion
    
    // Delete the tenant (documents and emergency contacts will be cascade deleted)
    await db.tenant.delete({
      where: { id },
    });

    return ApiResponseBuilder.success({ message: "Tenant deleted successfully" });
  } catch (error) {
    console.error("Delete Tenant Error:", error);
    return ApiResponseBuilder.error(
      "Failed to delete tenant",
      "INTERNAL_ERROR",
      500,
      process.env.NODE_ENV === 'development' ? error : undefined,
      request.url
    );
  }
}