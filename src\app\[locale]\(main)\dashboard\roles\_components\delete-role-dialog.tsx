"use client";

import { useState } from "react";
import { toast } from "sonner";
import { Trash2, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import type { RoleWithPermissions } from "@/types/user";

interface DeleteRoleDialogProps {
  role: RoleWithPermissions & { _count: { user_roles: number } };
  children: React.ReactNode;
  onSuccess?: () => void;
}

export function DeleteRoleDialog({ role, children, onSuccess }: DeleteRoleDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations('roles.messages');

  const handleDelete = async () => {
    try {
      setIsDeleting(true);

      const response = await fetch(`/api/roles/${role.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete role");
      }

      toast.success(t('deleteSuccess'));
      setIsOpen(false);
      
      // Refresh the page or call onSuccess callback
      if (onSuccess) {
        onSuccess();
      } else {
        window.location.reload();
      }
    } catch (error) {
      console.error("Error deleting role:", error);
      toast.error(error instanceof Error ? error.message : t('deleteError'));
    } finally {
      setIsDeleting(false);
    }
  };

  const canDelete = !role.is_system && role._count.user_roles === 0;

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        {children}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-destructive" />
            {t('deleteDialogTitle')}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {role.is_system ? (
              <>
                {t('deleteDialogCannotSystem', { name: role.name }).split(role.name)[0]}
                <strong>{role.name}</strong>
                {t('deleteDialogCannotSystem', { name: role.name }).split(role.name)[1]}
                <br />
                <br />
                {t('deleteDialogSystemInfo')}
              </>
            ) : role._count.user_roles > 0 ? (
              <>
                {t('deleteDialogCannotUsers', { name: role.name, count: role._count.user_roles }).split(role.name)[0]}
                <strong>{role.name}</strong>
                {t('deleteDialogCannotUsers', { name: role.name, count: role._count.user_roles }).split(role.name)[1]}
                <br />
                <br />
                {t('deleteDialogUsersInfo')}
              </>
            ) : (
              <>
                {t('deleteDialogConfirm', { name: role.name }).split(role.name)[0]}
                <strong>{role.name}</strong>
                {t('deleteDialogConfirm', { name: role.name }).split(role.name)[1]}
                <br />
                <br />
                {t('deleteDialogWarning')}
                {role.role_permissions.length > 0 && (
                  <>
                    <br />
                    <br />
                    <strong>{t('deleteDialogNote', { count: role.role_permissions.length }).split(':')[0]}:</strong>
                    {t('deleteDialogNote', { count: role.role_permissions.length }).split(':')[1]}
                  </>
                )}
              </>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>{t('cancel')}</AlertDialogCancel>
          {canDelete && (
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('deleting')}
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  {t('deleteDialogTitle')}
                </>
              )}
            </AlertDialogAction>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
