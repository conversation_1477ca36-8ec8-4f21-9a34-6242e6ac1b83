import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { ApiResponseBuilder } from "@/lib/api-response";
import { verifyToken } from "@/lib/auth";
import { hasPermission } from "@/lib/permissions";
import { formatCurrency } from "@/lib/utils";

// GET /api/owners-associations/[id]/receipts/[paymentId] - Generate a receipt for a payment
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; paymentId: string }> }
) {
  try {
    // Check authentication
    const token = request.cookies.get("auth-token")?.value;
    
    if (!token) {
      return ApiResponseBuilder.unauthorized();
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return ApiResponseBuilder.unauthorized("Invalid or expired token");
    }

    // Check if user has READ permission for owners-associations
    const canRead = await hasPermission(decoded.id, "owners-associations", "READ");
    if (!canRead) {
      return ApiResponseBuilder.forbidden("You don't have permission to view receipts");
    }

    const { id: idParam, paymentId: paymentIdParam } = await params;
    const associationId = parseInt(idParam);
    const paymentId = parseInt(paymentIdParam);

    if (isNaN(associationId) || isNaN(paymentId)) {
      return ApiResponseBuilder.error("Invalid ID", "INVALID_ID", 400);
    }

    // Fetch payment with all related data
    const payment = await db.subscriptionPayment.findFirst({
      where: {
        id: paymentId,
        subscription: {
          association_id: associationId,
        },
      },
      include: {
        subscription: {
          include: {
            association: {
              include: {
                property: {
                  select: {
                    name_en: true,
                    name_ar: true,
                    address_en: true,
                    address_ar: true,
                  },
                },
              },
            },
          },
        },
        member: true,
        creator: {
          select: {
            first_name: true,
            last_name: true,
          },
        },
      },
    });

    if (!payment) {
      return ApiResponseBuilder.notFound("Payment not found");
    }

    // Generate receipt number (format: ASSOC-YEAR-PAYMENTID)
    const receiptNumber = `ASSOC-${new Date().getFullYear()}-${payment.id.toString().padStart(6, "0")}`;

    // Prepare receipt data
    const receiptData = {
      receipt_number: receiptNumber,
      issue_date: new Date().toISOString(),
      
      // Association details
      association: {
        id: payment.subscription.association.id,
        name_en: payment.subscription.association.name_en,
        name_ar: payment.subscription.association.name_ar,
        contact_email: payment.subscription.association.contact_email,
        contact_phone: payment.subscription.association.contact_phone,
      },
      
      // Property details
      property: {
        name_en: payment.subscription.association.property.name_en,
        name_ar: payment.subscription.association.property.name_ar,
        address_en: payment.subscription.association.property.address_en,
        address_ar: payment.subscription.association.property.address_ar,
      },
      
      // Member details
      member: {
        id: payment.member.id,
        full_name: payment.member.full_name,
        unit_number: payment.member.unit_number,
        phone: payment.member.phone,
        email: payment.member.email,
      },
      
      // Payment details
      payment: {
        id: payment.id,
        payment_date: payment.payment_date,
        due_date: payment.due_date,
        amount: formatCurrency(payment.amount.toString()),
        amount_raw: payment.amount.toString(),
        status: payment.status,
        payment_method: payment.payment_method,
        reference_number: payment.reference_number,
        notes: payment.notes,
      },
      
      // Subscription details
      subscription: {
        id: payment.subscription.id,
        name_en: payment.subscription.name_en,
        name_ar: payment.subscription.name_ar,
        frequency: payment.subscription.frequency,
      },
      
      // Staff who recorded the payment
      recorded_by: payment.creator ? `${payment.creator.first_name} ${payment.creator.last_name}` : null,
      
      // Receipt metadata
      metadata: {
        generated_at: new Date().toISOString(),
        valid_until: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year validity
      },
    };

    return ApiResponseBuilder.success(receiptData);
  } catch (error: any) {
    console.error("Error generating receipt:", error);
    return ApiResponseBuilder.error("Failed to generate receipt", "INTERNAL_ERROR", 500);
  }
}