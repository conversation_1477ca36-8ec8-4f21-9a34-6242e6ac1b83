const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting simple database seeding...')

  try {
    // Clear existing data
    console.log('🧹 Clearing existing data...')
    await prisma.maintenanceRequest.deleteMany()
    await prisma.expenseAttachment.deleteMany()
    await prisma.expenseApproval.deleteMany()
    await prisma.expense.deleteMany()
    await prisma.expenseCategory.deleteMany()
    await prisma.payment.deleteMany()
    await prisma.invoice.deleteMany()
    await prisma.contractTenant.deleteMany()
    await prisma.contract.deleteMany()
    await prisma.tenant.deleteMany()
    await prisma.unit.deleteMany()
    await prisma.propertyAmenity.deleteMany()
    await prisma.property.deleteMany()
    await prisma.propertyOwner.deleteMany()
    await prisma.amenity.deleteMany()
    await prisma.propertyType.deleteMany()
    await prisma.userRole.deleteMany()
    await prisma.role.deleteMany()
    await prisma.user.deleteMany()

    // 1. Create admin role
    const adminRole = await prisma.role.create({
      data: {
        name: 'Admin',
        description: 'System Administrator',
        is_system: true,
      },
    })

    // 2. Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 10)
    const adminUser = await prisma.user.create({
      data: {
        username: 'admin',
        email: '<EMAIL>',
        password_hash: hashedPassword,
        first_name: 'System',
        last_name: 'Administrator',
        phone: '+968 2123 4567',
        status: 'ACTIVE',
        email_verified: true,
      },
    })

    await prisma.userRole.create({
      data: {
        user_id: adminUser.id,
        role_id: adminRole.id,
      },
    })

    // 3. Create property types
    const villaType = await prisma.propertyType.create({
      data: {
        name_en: 'Villa',
        name_ar: 'فيلا',
        description_en: 'Standalone villa property',
        description_ar: 'عقار فيلا منفصلة',
      },
    })

    const apartmentType = await prisma.propertyType.create({
      data: {
        name_en: 'Apartment',
        name_ar: 'شقة',
        description_en: 'Apartment unit',
        description_ar: 'وحدة شقة',
      },
    })

    // 4. Create amenities
    const poolAmenity = await prisma.amenity.create({
      data: {
        name_en: 'Swimming Pool',
        name_ar: 'مسبح',
        icon: 'waves',
      },
    })

    const parkingAmenity = await prisma.amenity.create({
      data: {
        name_en: 'Parking',
        name_ar: 'موقف سيارات',
        icon: 'car',
      },
    })

    // 5. Create expense categories
    const expenseCategories = []
    
    const utilitiesCategory = await prisma.expenseCategory.create({
      data: {
        name_en: 'Utilities',
        name_ar: 'المرافق',
        description: 'Water, electricity, gas, and other utility expenses',
        is_active: true,
        sort_order: 1,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    expenseCategories.push(utilitiesCategory)

    const maintenanceCategory = await prisma.expenseCategory.create({
      data: {
        name_en: 'Maintenance & Repairs',
        name_ar: 'الصيانة والإصلاحات',
        description: 'Property maintenance, repairs, and upkeep expenses',
        is_active: true,
        sort_order: 2,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    expenseCategories.push(maintenanceCategory)

    const insuranceCategory = await prisma.expenseCategory.create({
      data: {
        name_en: 'Insurance',
        name_ar: 'التأمين',
        description: 'Property insurance premiums and related costs',
        is_active: true,
        sort_order: 3,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    expenseCategories.push(insuranceCategory)

    const cleaningCategory = await prisma.expenseCategory.create({
      data: {
        name_en: 'Cleaning Services',
        name_ar: 'خدمات التنظيف',
        description: 'Professional cleaning and janitorial services',
        is_active: true,
        sort_order: 4,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    expenseCategories.push(cleaningCategory)

    const securityCategory = await prisma.expenseCategory.create({
      data: {
        name_en: 'Security',
        name_ar: 'الأمن',
        description: 'Security services, systems, and equipment',
        is_active: true,
        sort_order: 5,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    expenseCategories.push(securityCategory)

    const landscapingCategory = await prisma.expenseCategory.create({
      data: {
        name_en: 'Landscaping',
        name_ar: 'تنسيق الحدائق',
        description: 'Garden maintenance, landscaping, and outdoor improvements',
        is_active: true,
        sort_order: 6,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    expenseCategories.push(landscapingCategory)

    const administrativeCategory = await prisma.expenseCategory.create({
      data: {
        name_en: 'Administrative',
        name_ar: 'إدارية',
        description: 'Office supplies, legal fees, and administrative costs',
        is_active: true,
        sort_order: 7,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    expenseCategories.push(administrativeCategory)

    const renovationCategory = await prisma.expenseCategory.create({
      data: {
        name_en: 'Renovations',
        name_ar: 'التجديدات',
        description: 'Property improvements and renovation projects',
        is_active: true,
        sort_order: 8,
        created_by: adminUser.id,
        updated_by: adminUser.id,
      },
    })
    expenseCategories.push(renovationCategory)

    // 6. Create property owners
    const owners = []
    for (let i = 1; i <= 5; i++) {
      const owner = await prisma.propertyOwner.create({
        data: {
          name_en: `Owner ${i}`,
          name_ar: `مالك ${i}`,
          email: `owner${i}@email.com`,
          phone: `+968 212${i} 4567`,
          mobile: `+968 912${i} 4567`,
          address_en: `Address ${i}, Muscat, Oman`,
          address_ar: `عنوان ${i}، مسقط، عمان`,
          tax_id: `TAX00${i}`,
          bank_name: 'Bank Muscat',
          bank_account_number: `*********${i}`,
          bank_iban: `OM81BMAG*********012345${i}`,
          management_fee_percentage: 10.00,
          status: 'ACTIVE',
          created_by: adminUser.id,
          updated_by: adminUser.id,
        },
      })
      owners.push(owner)
    }

    // 7. Create properties
    const properties = []
    for (let i = 1; i <= 10; i++) {
      const property = await prisma.property.create({
        data: {
          name_en: `Property ${i}`,
          name_ar: `عقار ${i}`,
          address_en: `Street ${i}, Muscat, Oman`,
          address_ar: `شارع ${i}، مسقط، عمان`,
          property_type_id: i % 2 === 0 ? apartmentType.id : villaType.id,
          owner_id: owners[i % 5].id,
          base_rent: 800.000 + (i * 100),
          status: i % 3 === 0 ? 'RENTED' : 'AVAILABLE',
          total_area: 200.00 + (i * 50),
          floors_count: Math.floor(i / 3) + 1,
          parking_spaces: i % 5 + 1,
          created_by: adminUser.id,
          updated_by: adminUser.id,
        },
      })
      properties.push(property)

      // Add amenities to properties
      await prisma.propertyAmenity.createMany({
        data: [
          { property_id: property.id, amenity_id: poolAmenity.id },
          { property_id: property.id, amenity_id: parkingAmenity.id },
        ],
      })
    }

    // 8. Create units
    const units = []
    for (const property of properties) {
      for (let j = 1; j <= 3; j++) {
        const unit = await prisma.unit.create({
          data: {
            property_id: property.id,
            unit_number: `${property.id}${j.toString().padStart(2, '0')}`,
            unit_name_en: `Unit ${j}`,
            unit_name_ar: `وحدة ${j}`,
            floor_number: j,
            rooms_count: j + 1,
            majalis_count: 1,
            bathrooms_count: j,
            area: 80.00 + (j * 20),
            rent_amount: 400.000 + (j * 100),
            status: j % 2 === 0 ? 'RENTED' : 'AVAILABLE',
            description_en: `${j + 1} bedroom unit`,
            description_ar: `وحدة من ${j + 1} غرف نوم`,
            created_by: adminUser.id,
            updated_by: adminUser.id,
          },
        })
        units.push(unit)
      }
    }

    // 9. Create tenants
    const tenants = []
    for (let i = 1; i <= 15; i++) {
      const tenant = await prisma.tenant.create({
        data: {
          first_name: `Tenant${i}`,
          last_name: `LastName${i}`,
          email: `tenant${i}@email.com`,
          phone: `+968 912${i.toString().padStart(2, '0')} 4567`,
          national_id: `1234567${i.toString().padStart(2, '0')}`,
          national_id_expiry: new Date('2030-12-31'),
          date_of_birth: new Date(`198${i % 10}-0${(i % 12) + 1}-15`),
          nationality: 'Omani',
          occupation: 'Engineer',
          company_name: 'Company Name',
          created_by: adminUser.id,
          updated_by: adminUser.id,
        },
      })
      tenants.push(tenant)
    }

    // 10. Create contracts for rented units
    const rentedUnits = units.filter(unit => unit.status === 'RENTED')
    const contracts = []
    for (let i = 0; i < Math.min(rentedUnits.length, tenants.length); i++) {
      const unit = rentedUnits[i]
      const tenant = tenants[i]
      
      const contract = await prisma.contract.create({
        data: {
          contract_number: `CON-${(i + 1).toString().padStart(4, '0')}`,
          property_id: unit.property_id,
          unit_id: unit.id,
          start_date: new Date('2024-01-01'),
          end_date: new Date('2024-12-31'),
          monthly_rent: unit.rent_amount,
          payment_due_day: 1,
          security_deposit: unit.rent_amount * 2,
          insurance_amount: 100.000,
          insurance_due_date: new Date('2024-06-01'),
          terms_and_conditions: 'Standard rental agreement terms.',
          status: 'ACTIVE',
          auto_renew: false,
          renewal_notice_days: 30,
          notes: `Contract for unit ${unit.unit_number}`,
          created_by: adminUser.id,
          updated_by: adminUser.id,
        },
      })
      contracts.push(contract)

      // Link tenant to contract
      await prisma.contractTenant.create({
        data: {
          contract_id: contract.id,
          tenant_id: tenant.id,
          is_primary: true,
        },
      })
    }

    // 11. Create invoices
    for (const contract of contracts) {
      for (let month = 0; month < 6; month++) {
        const invoiceDate = new Date('2024-01-01')
        invoiceDate.setMonth(invoiceDate.getMonth() + month)
        
        const dueDate = new Date(invoiceDate)
        dueDate.setDate(contract.payment_due_day)
        
        const isPaid = month < 4 // First 4 months are paid
        const totalAmount = contract.monthly_rent
        const paidAmount = isPaid ? totalAmount : 0
        const balanceAmount = totalAmount - paidAmount
        
        const invoice = await prisma.invoice.create({
          data: {
            invoice_number: `INV-${contract.contract_number}-${(month + 1).toString().padStart(2, '0')}`,
            contract_id: contract.id,
            tenant_id: (await prisma.contractTenant.findFirst({ where: { contract_id: contract.id } })).tenant_id,
            property_id: contract.property_id,
            unit_id: contract.unit_id,
            invoice_date: invoiceDate,
            due_date: dueDate,
            original_amount: contract.monthly_rent,
            late_fee: 0.000,
            total_amount: totalAmount,
            paid_amount: paidAmount,
            balance_amount: balanceAmount,
            status: isPaid ? 'PAID' : 'PENDING',
            notes: `Month ${month + 1} rent`,
            created_by: adminUser.id,
            updated_by: adminUser.id,
          },
        })

        // Create payment for paid invoices
        if (isPaid) {
          await prisma.payment.create({
            data: {
              payment_number: `PAY-${invoice.invoice_number}`,
              invoice_id: invoice.id,
              tenant_id: invoice.tenant_id,
              property_id: invoice.property_id,
              unit_id: invoice.unit_id,
              amount: totalAmount,
              payment_method: 'BANK_TRANSFER',
              payment_date: invoiceDate,
              reference_number: `REF${Math.floor(Math.random() * 1000000)}`,
              bank_name: 'Bank Muscat',
              notes: `Payment for ${invoice.invoice_number}`,
              status: 'COMPLETED',
              created_by: adminUser.id,
            },
          })
        }
      }
    }

    // 12. Create maintenance requests
    for (let i = 1; i <= 20; i++) {
      const unit = units[i % units.length]
      const statuses = ['REPORTED', 'ACKNOWLEDGED', 'IN_PROGRESS', 'COMPLETED']
      const priorities = ['LOW', 'MEDIUM', 'HIGH', 'EMERGENCY']
      const categories = ['ELECTRICAL', 'PLUMBING', 'HVAC', 'STRUCTURAL']
      
      await prisma.maintenanceRequest.create({
        data: {
          request_number: `MNT-${i.toString().padStart(4, '0')}`,
          property_id: unit.property_id,
          unit_id: unit.id,
          tenant_id: unit.status === 'RENTED' ? tenants[i % tenants.length].id : null,
          title: `Maintenance Issue ${i}`,
          description: `Description for maintenance issue ${i}`,
          priority: priorities[i % priorities.length],
          category: categories[i % categories.length],
          status: statuses[i % statuses.length],
          reported_by: adminUser.id,
          reported_date: new Date(),
          estimated_cost: 100.000 + (i * 50),
          created_by: adminUser.id,
          updated_by: adminUser.id,
        },
      })
    }

    // 13. Create expense records
    const expenses = []
    const paymentMethods = ['CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'CHECK']
    const expenseStatuses = ['PENDING', 'APPROVED', 'REJECTED']
    
    // Create current year expenses (recent months)
    for (let i = 1; i <= 50; i++) {
      const category = expenseCategories[i % expenseCategories.length]
      const currentDate = new Date()
      
      // Generate dates within the last 6 months
      const expenseDate = new Date()
      expenseDate.setMonth(currentDate.getMonth() - (i % 6))
      expenseDate.setDate(Math.floor(Math.random() * 28) + 1)
      
      const status = expenseStatuses[i % expenseStatuses.length]
      const isApproved = status === 'APPROVED'
      
      const expenseDescriptions = {
        [utilitiesCategory.id]: ['Water bill payment', 'Electricity charges', 'Gas connection fee', 'Internet service'],
        [maintenanceCategory.id]: ['HVAC repair service', 'Plumbing fix for unit', 'Electrical maintenance', 'Door lock replacement'],
        [insuranceCategory.id]: ['Property insurance premium', 'Liability insurance renewal', 'Fire insurance payment'],
        [cleaningCategory.id]: ['Monthly cleaning service', 'Deep cleaning property', 'Window cleaning service', 'Garden cleaning'],
        [securityCategory.id]: ['Security camera maintenance', 'Guard service payment', 'Access control system'],
        [landscapingCategory.id]: ['Garden maintenance', 'Tree trimming service', 'Irrigation system repair', 'Lawn care'],
        [administrativeCategory.id]: ['Legal consultation fee', 'Office supplies', 'Document processing', 'Bank charges'],
        [renovationCategory.id]: ['Kitchen renovation', 'Bathroom upgrade', 'Flooring replacement', 'Painting services']
      }
      
      const descriptions = expenseDescriptions[category.id] || ['General expense']
      const description = descriptions[i % descriptions.length]
      
      // Generate amounts based on category
      let baseAmount = 50.000
      switch (category.name_en) {
        case 'Utilities':
          baseAmount = 80.000 + (Math.random() * 120)
          break
        case 'Maintenance & Repairs':
          baseAmount = 150.000 + (Math.random() * 300)
          break
        case 'Insurance':
          baseAmount = 200.000 + (Math.random() * 500)
          break
        case 'Cleaning Services':
          baseAmount = 30.000 + (Math.random() * 70)
          break
        case 'Security':
          baseAmount = 100.000 + (Math.random() * 200)
          break
        case 'Landscaping':
          baseAmount = 60.000 + (Math.random() * 140)
          break
        case 'Administrative':
          baseAmount = 25.000 + (Math.random() * 75)
          break
        case 'Renovations':
          baseAmount = 500.000 + (Math.random() * 1500)
          break
      }
      
      const expense = await prisma.expense.create({
        data: {
          date: expenseDate,
          description: `${description} - Property maintenance`,
          amount: Number(baseAmount.toFixed(3)),
          category_id: category.id,
          payment_method: paymentMethods[i % paymentMethods.length],
          paid_by: i % 3 === 0 ? 'Property Manager' : `Contractor ${(i % 5) + 1}`,
          notes: i % 4 === 0 ? `Receipt #REC${i.toString().padStart(4, '0')}` : null,
          status: status,
          is_recurring: i % 8 === 0, // Some expenses are recurring
          recurring_frequency: i % 8 === 0 ? (i % 2 === 0 ? 'MONTHLY' : 'QUARTERLY') : null,
          next_due_date: i % 8 === 0 ? new Date(expenseDate.getTime() + (30 * 24 * 60 * 60 * 1000)) : null,
          approved_by: isApproved ? adminUser.id : null,
          approved_at: isApproved ? new Date() : null,
          created_by: adminUser.id,
          updated_by: adminUser.id,
        },
      })
      expenses.push(expense)
      
      // Create approval record for approved/rejected expenses
      if (status !== 'PENDING') {
        await prisma.expenseApproval.create({
          data: {
            expense_id: expense.id,
            user_id: adminUser.id,
            action: status === 'APPROVED' ? 'APPROVED' : 'REJECTED',
            comments: status === 'APPROVED' ? 'Expense approved for payment' : 'Requires additional documentation',
          },
        })
      }
    }

    console.log('🎉 Simple seeding completed successfully!')
    console.log(`✅ Created:`)
    console.log(`   - 1 admin user (admin/admin123)`)
    console.log(`   - 2 property types`)
    console.log(`   - 2 amenities`)
    console.log(`   - ${expenseCategories.length} expense categories`)
    console.log(`   - 5 property owners`)
    console.log(`   - 10 properties`)
    console.log(`   - 30 units`)
    console.log(`   - 15 tenants`)
    console.log(`   - ${contracts.length} contracts`)
    console.log(`   - ${contracts.length * 6} invoices`)
    console.log(`   - ${contracts.length * 4} payments`)
    console.log(`   - 20 maintenance requests`)
    console.log(`   - ${expenses.length} expenses with approvals`)

  } catch (error) {
    console.error('❌ Error during seeding:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
