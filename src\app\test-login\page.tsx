"use client";

import { useState } from "react";

export default function TestLoginPage() {
  const [result, setResult] = useState<string>("");

  const testLogin = async () => {
    try {
      setResult("Starting login test...");
      
      // Step 1: Login
      const loginResponse = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ username: "admin", password: "123456" }),
        credentials: "include",
      });

      if (!loginResponse.ok) {
        throw new Error(`Login failed: ${loginResponse.status}`);
      }

      const loginData = await loginResponse.json();
      setResult(prev => prev + "\n✅ Login successful: " + JSON.stringify(loginData, null, 2));

      // Step 2: Check cookies
      const cookies = document.cookie;
      setResult(prev => prev + "\n📄 Document cookies: " + cookies);

      // Step 3: Test /api/auth/me
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second

      const meResponse = await fetch("/api/auth/me", {
        credentials: "include",
      });

      setResult(prev => prev + "\n🔍 /api/auth/me status: " + meResponse.status);

      if (meResponse.ok) {
        const meData = await meResponse.json();
        setResult(prev => prev + "\n✅ /api/auth/me successful: " + JSON.stringify(meData, null, 2));
      } else {
        const errorData = await meResponse.text();
        setResult(prev => prev + "\n❌ /api/auth/me failed: " + errorData);
      }

    } catch (error) {
      setResult(prev => prev + "\n❌ Error: " + (error instanceof Error ? error.message : String(error)));
    }
  };

  const testDirectAccess = async () => {
    try {
      setResult("Testing direct dashboard access...");
      
      const response = await fetch("/dashboard/default", {
        credentials: "include",
      });
      
      setResult(prev => prev + "\n🏠 Dashboard access status: " + response.status);
      
      if (response.ok) {
        setResult(prev => prev + "\n✅ Dashboard accessible");
      } else {
        setResult(prev => prev + "\n❌ Dashboard not accessible");
      }
    } catch (error) {
      setResult(prev => prev + "\n❌ Error: " + (error instanceof Error ? error.message : String(error)));
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Login Test Page</h1>
      
      <div className="space-x-4 mb-4">
        <button 
          onClick={testLogin}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Test Login Flow
        </button>
        
        <button 
          onClick={testDirectAccess}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Test Dashboard Access
        </button>
        
        <button 
          onClick={() => setResult("")}
          className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
        >
          Clear
        </button>
      </div>
      
      <div className="bg-gray-100 p-4 rounded">
        <h2 className="font-bold mb-2">Test Results:</h2>
        <pre className="whitespace-pre-wrap text-sm">{result}</pre>
      </div>
    </div>
  );
}
