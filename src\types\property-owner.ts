import { z } from "zod";
import type {
  PropertyOwner,
  Property,
  User,
  OwnerPayout,
  OwnerStatus,
} from "@/generated/prisma";

// Property Owner Types
export interface PropertyOwnerWithRelations extends PropertyOwner {
  primary_properties?: Property[];
  payouts?: OwnerPayout[];
  creator?: User | null;
  updater?: User | null;
}

// Property Owner Schemas
export const propertyOwnerSchema = z.object({
  name_en: z.string().min(1, "Name in English is required").max(100),
  name_ar: z.string().min(1, "Name in Arabic is required").max(100),
  email: z.string().email().optional().nullable(),
  phone: z.string().max(20).optional().nullable(),
  mobile: z.string().max(20).optional().nullable(),
  address_en: z.string().optional().nullable(),
  address_ar: z.string().optional().nullable(),
  tax_id: z.string().max(50).optional().nullable(),
  bank_name: z.string().max(100).optional().nullable(),
  bank_account_number: z.string().max(50).optional().nullable(),
  bank_iban: z.string().max(50).optional().nullable(),
  management_fee_percentage: z.coerce.number().min(0).max(100).optional().nullable(),
  notes: z.string().optional().nullable(),
  status: z.enum(["ACTIVE", "INACTIVE"]).default("ACTIVE"),
});

// Property Owner Filter Schema
export const propertyOwnerFilterSchema = z.object({
  search: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE"]).optional(),
  page: z.number().int().positive().default(1),
  pageSize: z.number().int().positive().max(100).default(10),
  sortBy: z.string().default("created_at"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// Type exports
export type PropertyOwnerInput = z.infer<typeof propertyOwnerSchema>;
export type PropertyOwnerFilters = z.infer<typeof propertyOwnerFilterSchema>;

// Helper functions
export function calculateOwnerRevenue(
  totalRentCollected: number | string,
  managementFeePercentage: number | string | null
): { managementFee: number; netAmount: number } {
  const rent = typeof totalRentCollected === "string" ? parseFloat(totalRentCollected) : totalRentCollected;
  const feePercent = managementFeePercentage
    ? typeof managementFeePercentage === "string"
      ? parseFloat(managementFeePercentage)
      : managementFeePercentage
    : 0;

  const managementFee = (rent * feePercent) / 100;
  const netAmount = rent - managementFee;

  return {
    managementFee: Math.round(managementFee * 1000) / 1000, // Round to 3 decimal places
    netAmount: Math.round(netAmount * 1000) / 1000,
  };
}

