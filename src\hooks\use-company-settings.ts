"use client";

import { useState, useEffect } from "react";

export interface CompanySettings {
  id: number;
  company_name: string;
  logo_url: string | null;
  created_at: string;
  updated_at: string;
}

export function useCompanySettings() {
  const [settings, setSettings] = useState<CompanySettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch("/api/company-settings");
      const result = await response.json();

      if (result.success) {
        setSettings(result.data);
      } else {
        setError(result.error || "Failed to load company settings");
      }
    } catch (err) {
      console.error("Error fetching company settings:", err);
      setError("Failed to load company settings");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const refetch = () => {
    fetchSettings();
  };

  return {
    settings,
    isLoading,
    error,
    refetch,
  };
}
