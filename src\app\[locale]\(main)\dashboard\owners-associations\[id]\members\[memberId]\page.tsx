import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { cookies } from "next/headers";
import { verifyToken, getUserPermissions, hasPermission } from "@/lib/auth";
import { db } from "@/lib/db";
import { MemberDetail } from "../../../_components/member-detail";

interface MemberDetailPageProps {
  params: Promise<{
    locale: string;
    id: string;
    memberId: string;
  }>;
}

export async function generateMetadata({ params }: MemberDetailPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const t = await getTranslations("ownersAssociations.memberDetail");
  
  return {
    title: t("title"),
  };
}

export default async function MemberDetailPage({ params }: MemberDetailPageProps) {
  const resolvedParams = await params;
  // Check authentication
  const cookieStore = await cookies();
  const token = cookieStore.get("auth-token")?.value;
  
  if (!token) {
    notFound();
  }

  let decoded;
  try {
    decoded = verifyToken(token);
  } catch (error) {
    notFound();
  }

  // Check permissions
  const userPermissions = await getUserPermissions(decoded.id);
  const canRead = hasPermission(userPermissions, "owners_associations", "read");
  
  if (!canRead) {
    notFound();
  }

  const associationId = parseInt(resolvedParams.id);
  const memberId = parseInt(resolvedParams.memberId);

  if (isNaN(associationId) || isNaN(memberId)) {
    notFound();
  }

  // Fetch association and member details
  const association = await db.ownersAssociation.findUnique({
    where: { id: associationId },
  });

  const member = await db.associationMember.findUnique({
    where: { 
      id: memberId,
      association_id: associationId 
    },
    include: {
      property: true,
    }
  });

  if (!association || !member) {
    notFound();
  }

  return (
    <MemberDetail 
      associationId={associationId}
      memberId={memberId}
      association={association}
      member={member}
    />
  );
}