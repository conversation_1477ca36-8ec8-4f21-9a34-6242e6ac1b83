"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>f<PERSON><PERSON><PERSON>, Wifi, WifiOff } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export interface ErrorStateProps {
  title?: string;
  description?: string;
  error?: Error | string;
  onRetry?: () => void;
  retryLabel?: string;
  showRetry?: boolean;
  variant?: "default" | "network" | "server" | "validation" | "not-found";
  className?: string;
}

const errorVariants = {
  default: {
    icon: AlertCircle,
    title: "Something went wrong",
    description: "An unexpected error occurred. Please try again.",
  },
  network: {
    icon: WifiOff,
    title: "Connection problem",
    description: "Unable to connect to the server. Please check your internet connection and try again.",
  },
  server: {
    icon: AlertCircle,
    title: "Server error",
    description: "The server encountered an error. Please try again in a few moments.",
  },
  validation: {
    icon: AlertCircle,
    title: "Invalid data",
    description: "Please check your input and try again.",
  },
  "not-found": {
    icon: AlertCircle,
    title: "Not found",
    description: "The requested resource could not be found.",
  },
};

export function ErrorState({
  title,
  description,
  error,
  onRetry,
  retryLabel = "Try Again",
  showRetry = true,
  variant = "default",
  className,
}: ErrorStateProps) {
  const variantConfig = errorVariants[variant];
  const Icon = variantConfig.icon;

  const errorMessage = typeof error === "string" ? error : error?.message;
  const displayTitle = title || variantConfig.title;
  const displayDescription = description || variantConfig.description;

  return (
    <Card className={className}>
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
          <Icon className="h-6 w-6 text-destructive" />
        </div>
        <CardTitle className="text-destructive">{displayTitle}</CardTitle>
        <CardDescription>
          {displayDescription}
          {errorMessage && (
            <span className="mt-2 block text-sm text-muted-foreground">
              {errorMessage}
            </span>
          )}
        </CardDescription>
      </CardHeader>
      {(showRetry && onRetry) && (
        <CardContent className="text-center">
          <Button onClick={onRetry} variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            {retryLabel}
          </Button>
        </CardContent>
      )}
    </Card>
  );
}

// Inline error state for smaller spaces
export function InlineErrorState({
  error,
  onRetry,
  retryLabel = "Try Again",
  showRetry = true,
  variant = "default",
  className,
}: Omit<ErrorStateProps, "title" | "description">) {
  const variantConfig = errorVariants[variant];
  const Icon = variantConfig.icon;
  const errorMessage = typeof error === "string" ? error : error?.message;

  return (
    <div className={`flex items-center justify-between rounded-md border border-destructive/20 bg-destructive/5 p-4 ${className}`}>
      <div className="flex items-center space-x-3">
        <Icon className="h-5 w-5 text-destructive" />
        <div>
          <p className="text-sm font-medium text-destructive">
            {variantConfig.title}
          </p>
          {errorMessage && (
            <p className="text-xs text-muted-foreground">
              {errorMessage}
            </p>
          )}
        </div>
      </div>
      {(showRetry && onRetry) && (
        <Button onClick={onRetry} variant="outline" size="sm">
          <RefreshCw className="mr-1 h-3 w-3" />
          {retryLabel}
        </Button>
      )}
    </div>
  );
}

// Helper function to determine error variant based on error type
export function getErrorVariant(error: Error | string | unknown): ErrorStateProps["variant"] {
  const errorMessage = typeof error === "string" ? error : error instanceof Error ? error.message : String(error);
  
  if (errorMessage.toLowerCase().includes("network") || 
      errorMessage.toLowerCase().includes("fetch") ||
      errorMessage.toLowerCase().includes("connection")) {
    return "network";
  }
  
  if (errorMessage.toLowerCase().includes("server") ||
      errorMessage.toLowerCase().includes("500") ||
      errorMessage.toLowerCase().includes("internal")) {
    return "server";
  }
  
  if (errorMessage.toLowerCase().includes("validation") ||
      errorMessage.toLowerCase().includes("invalid") ||
      errorMessage.toLowerCase().includes("400")) {
    return "validation";
  }
  
  if (errorMessage.toLowerCase().includes("not found") ||
      errorMessage.toLowerCase().includes("404")) {
    return "not-found";
  }
  
  return "default";
}
